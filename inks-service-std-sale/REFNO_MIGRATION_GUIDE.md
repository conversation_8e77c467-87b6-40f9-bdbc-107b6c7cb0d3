# RefNo AOP 迁移指南

## 概述

本指南帮助您将现有的手动RefNo编码生成逻辑迁移到基于AOP注解的自动化方案。

## 迁移前准备

### 1. 确认依赖

确保pom.xml中包含AOP依赖：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-aop</artifactId>
</dependency>
```

### 2. 启用AOP

确保配置类中启用了AOP：

```java
@Configuration
@EnableAspectJAutoProxy
public class SaleAutoConfiguration {
    // 配置内容
}
```

### 3. 添加配置（可选）

在application.yml中添加RefNo配置：

```yaml
inks:
  refno:
    enabled: true
    verbose-logging: false
```

## 迁移步骤

### 步骤1：添加注解导入

在需要迁移的Controller类中添加注解导入：

```java
import inks.service.std.sale.annotation.RefNoGeneration;
import inks.service.std.sale.annotation.RefNoCleanup;
```

### 步骤2：迁移Create方法

#### 原有代码：
```java
@RequestMapping(value = "/create", method = RequestMethod.POST)
public R<BusReceiptPojo> create(@RequestBody String json) {
    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    BusReceiptPojo pojo = JSONArray.parseObject(json, BusReceiptPojo.class);
    
    // 手动生成编码
    String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Receipt", null, loginUser.getTenantid());
    pojo.setRefno(refno);
    
    // 设置其他字段...
    pojo.setCreateby(loginUser.getRealname());
    
    BusReceiptPojo result = service.insert(pojo);
    
    // 手动保存缓存
    RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
    
    return R.ok(result);
}
```

#### 迁移后：
```java
@RequestMapping(value = "/create", method = RequestMethod.POST)
@RefNoGeneration(billType = "Bus_Receipt", field = "refno", paramType = RefNoGeneration.ParamType.JSON_STRING)
public R<BusReceiptPojo> create(@RequestBody String json) {
    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    BusReceiptPojo pojo = JSONArray.parseObject(json, BusReceiptPojo.class);
    
    // 编码自动生成，无需手动处理
    // 设置其他字段...
    pojo.setCreateby(loginUser.getRealname());
    
    BusReceiptPojo result = service.insert(pojo);
    // 缓存自动保存，无需手动处理
    
    return R.ok(result);
}
```

### 步骤3：迁移Delete方法

#### 原有代码：
```java
@RequestMapping(value = "/delete", method = RequestMethod.GET)
public R<Integer> delete(String key) {
    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    String refno = service.delete(key, loginUser.getTenantid());
    
    // 手动清理缓存
    RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
    
    return R.ok(1, "删除成功");
}
```

#### 迁移后：
```java
@RequestMapping(value = "/delete", method = RequestMethod.GET)
@RefNoCleanup(afterSuccess = true, ignoreException = true)
public R<Integer> delete(String key) {
    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    String refno = service.delete(key, loginUser.getTenantid());
    
    // 缓存自动清理，无需手动处理
    
    return R.ok(1, "删除成功");
}
```

## 注解参数说明

### @RefNoGeneration 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| billType | String | 无 | 单据类型（必填） |
| field | String | "refno" | 编码字段名 |
| customPrefix | String | "" | 自定义前缀 |
| saveToRedis | boolean | true | 是否保存到Redis |
| paramIndex | int | 0 | 参数索引 |
| paramType | ParamType | JSON_STRING | 参数类型 |

### @RefNoCleanup 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| afterSuccess | boolean | true | 是否在成功后清理 |
| ignoreException | boolean | true | 是否忽略清理异常 |

## 常见场景

### 场景1：JSON字符串参数
```java
@RefNoGeneration(billType = "Bus_Receipt", paramType = RefNoGeneration.ParamType.JSON_STRING)
public R<BusReceiptPojo> create(@RequestBody String json) {
    // 实现
}
```

### 场景2：POJO对象参数
```java
@RefNoGeneration(billType = "Bus_Receipt", paramType = RefNoGeneration.ParamType.POJO)
public R<BusReceiptPojo> create(@RequestBody BusReceiptPojo pojo) {
    // 实现
}
```

### 场景3：自定义前缀
```java
@RefNoGeneration(billType = "Bus_Receipt", customPrefix = "CUSTOM")
public R<BusReceiptPojo> create(@RequestBody String json) {
    // 实现
}
```

### 场景4：不保存缓存
```java
@RefNoGeneration(billType = "Bus_Receipt", saveToRedis = false)
public R<BusReceiptPojo> create(@RequestBody String json) {
    // 实现
}
```

## 验证迁移

### 1. 编译检查
确保项目能够正常编译，没有语法错误。

### 2. 功能测试
- 测试创建操作，确认编码正常生成
- 测试删除操作，确认缓存正常清理
- 检查日志，确认AOP正常工作

### 3. 性能测试
对比迁移前后的性能，AOP会有轻微的性能开销，但通常可以忽略。

## 故障排除

### 问题1：编码没有生成
- 检查AOP是否启用（@EnableAspectJAutoProxy）
- 检查注解参数是否正确
- 检查moduleCode是否能正确提取

### 问题2：参数设置失败
- 检查paramType是否与实际参数类型匹配
- 检查paramIndex是否正确
- 检查field字段名是否存在

### 问题3：缓存清理失败
- 检查Redis连接是否正常
- 检查ignoreException设置
- 查看详细错误日志

## 回滚方案

如果迁移后出现问题，可以快速回滚：

1. 移除注解
2. 恢复原有的手动编码生成逻辑
3. 重新部署

## 最佳实践

1. **分批迁移**：不要一次性迁移所有Controller，建议分批进行
2. **充分测试**：每个迁移的方法都要进行充分测试
3. **保留备份**：迁移前备份原有代码
4. **监控日志**：迁移后密切关注系统日志
5. **性能监控**：关注系统性能变化

## 支持与帮助

如果在迁移过程中遇到问题，可以：

1. 查看详细的错误日志
2. 启用verbose-logging获取更多信息
3. 参考RefNoExampleController示例代码
4. 联系开发团队获取支持
