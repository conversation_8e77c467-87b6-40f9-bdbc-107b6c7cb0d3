pipeline {
  agent {
    node {
      label 'maven'
    }

  }
  stages {
    stage('拉取代码') {
      agent none
      steps {
        container('maven') {
          git(url: 'https://gitee.com/inkstech/inks-service-sale-master.git', credentialsId: 'gtiee-eric', branch: 'master', changelog: true, poll: false)
          sh 'ls -l'
        }

      }
    }

    stage('项目编译') {
      agent none
      steps {
        container('maven') {
          sh 'cd inks-service-std-sale && mvn clean package -Dmaven.test.skip=true'
          sh 'cd inks-service-std-sale && ls -l'
        }

      }
    }

    stage('构建镜像') {
      agent none
      steps {
        container('maven') {
          sh 'docker build -t svc-sale:latest -f inks-service-std-sale/dockerfile ./inks-service-std-sale/'
        }

      }
    }

    stage('推送镜像') {
      agent none
      steps {
        container('maven') {
          withCredentials([usernamePassword(credentialsId : 'aliyun-auth' ,passwordVariable : 'DOCKER_PWD_VAR' ,usernameVariable : 'DOCKER_USER_VAR' ,)]) {
            sh 'echo $DOCKER_PWD_VAR | docker login $REGISTRY --username=$DOCKER_USER_VAR --password-stdin'
            sh 'docker tag svc-sale:latest $REGISTRY/$DOCKERHUB_NAMESPACE/svc-sale:SNAPSHOT-$BUILD_NUMBER'
            sh 'docker push $REGISTRY/$DOCKERHUB_NAMESPACE/svc-sale:SNAPSHOT-$BUILD_NUMBER'
          }

        }

      }
    }

    stage('部署镜像') {
      agent none
      steps {
        container('maven') {
          withCredentials([kubeconfigFile(credentialsId : 'demo-kubeconfig' ,variable : 'KUBECONFIG' ,)]) {
            sh 'envsubst < inks-service-std-sale/deploy.yaml | kubectl apply -f -'
          }

        }

      }
    }

  }
  environment {
    DOCKER_CREDENTIAL_ID = 'dockerhub-id'
    GITHUB_CREDENTIAL_ID = 'github-id'
    KUBECONFIG_CREDENTIAL_ID = 'demo-kubeconfig'
    REGISTRY = 'registry.cn-hangzhou.aliyuncs.com'
    DOCKERHUB_NAMESPACE = 'inksoms'
    GITHUB_ACCOUNT = 'kubesphere'
    APP_NAME = 'devops-java-sample'
  }
  parameters {
    string(name: 'TAG_NAME', defaultValue: '', description: '')
  }
}