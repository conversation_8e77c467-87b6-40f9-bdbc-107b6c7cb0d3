package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusSteppriceobjEntity;
import inks.service.std.sale.domain.pojo.BusSteppriceobjPojo;
import inks.service.std.sale.mapper.BusSteppriceobjMapper;
import inks.service.std.sale.service.BusSteppriceobjService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 阶价对象(BusSteppriceobj)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-31 21:22:30
 */
@Service("busSteppriceobjService")
public class BusSteppriceobjServiceImpl implements BusSteppriceobjService {
    @Resource
    private BusSteppriceobjMapper busSteppriceobjMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusSteppriceobjPojo getEntity(String key,String tid) {
        return this.busSteppriceobjMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusSteppriceobjPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusSteppriceobjPojo> lst = busSteppriceobjMapper.getPageList(queryParam);
            PageInfo<BusSteppriceobjPojo> pageInfo = new PageInfo<BusSteppriceobjPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusSteppriceobjPojo> getList(String Pid,String tid) { 
        try {
            List<BusSteppriceobjPojo> lst = busSteppriceobjMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busSteppriceobjPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusSteppriceobjPojo insert(BusSteppriceobjPojo busSteppriceobjPojo) {
        //初始化item的NULL
        BusSteppriceobjPojo itempojo =this.clearNull(busSteppriceobjPojo);
        BusSteppriceobjEntity busSteppriceobjEntity = new BusSteppriceobjEntity(); 
        BeanUtils.copyProperties(itempojo,busSteppriceobjEntity);
        
          busSteppriceobjEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busSteppriceobjEntity.setRevision(1);  //乐观锁      
          this.busSteppriceobjMapper.insert(busSteppriceobjEntity);
        return this.getEntity(busSteppriceobjEntity.getId(),busSteppriceobjEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busSteppriceobjPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusSteppriceobjPojo update(BusSteppriceobjPojo busSteppriceobjPojo) {
        BusSteppriceobjEntity busSteppriceobjEntity = new BusSteppriceobjEntity(); 
        BeanUtils.copyProperties(busSteppriceobjPojo,busSteppriceobjEntity);
        this.busSteppriceobjMapper.update(busSteppriceobjEntity);
        return this.getEntity(busSteppriceobjEntity.getId(),busSteppriceobjEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busSteppriceobjMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busSteppriceobjPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusSteppriceobjPojo clearNull(BusSteppriceobjPojo busSteppriceobjPojo){
     //初始化NULL字段
     if(busSteppriceobjPojo.getPid()==null) busSteppriceobjPojo.setPid("");
     if(busSteppriceobjPojo.getGoodsid()==null) busSteppriceobjPojo.setGoodsid("");
     if(busSteppriceobjPojo.getItemcode()==null) busSteppriceobjPojo.setItemcode("");
     if(busSteppriceobjPojo.getItemname()==null) busSteppriceobjPojo.setItemname("");
     if(busSteppriceobjPojo.getItemspec()==null) busSteppriceobjPojo.setItemspec("");
     if(busSteppriceobjPojo.getItemunit()==null) busSteppriceobjPojo.setItemunit("");
     if(busSteppriceobjPojo.getRownum()==null) busSteppriceobjPojo.setRownum(0);
     if(busSteppriceobjPojo.getRemark()==null) busSteppriceobjPojo.setRemark("");
     if(busSteppriceobjPojo.getCustom1()==null) busSteppriceobjPojo.setCustom1("");
     if(busSteppriceobjPojo.getCustom2()==null) busSteppriceobjPojo.setCustom2("");
     if(busSteppriceobjPojo.getCustom3()==null) busSteppriceobjPojo.setCustom3("");
     if(busSteppriceobjPojo.getCustom4()==null) busSteppriceobjPojo.setCustom4("");
     if(busSteppriceobjPojo.getCustom5()==null) busSteppriceobjPojo.setCustom5("");
     if(busSteppriceobjPojo.getCustom6()==null) busSteppriceobjPojo.setCustom6("");
     if(busSteppriceobjPojo.getCustom7()==null) busSteppriceobjPojo.setCustom7("");
     if(busSteppriceobjPojo.getCustom8()==null) busSteppriceobjPojo.setCustom8("");
     if(busSteppriceobjPojo.getCustom9()==null) busSteppriceobjPojo.setCustom9("");
     if(busSteppriceobjPojo.getCustom10()==null) busSteppriceobjPojo.setCustom10("");
     if(busSteppriceobjPojo.getTenantid()==null) busSteppriceobjPojo.setTenantid("");
     if(busSteppriceobjPojo.getRevision()==null) busSteppriceobjPojo.setRevision(0);
     return busSteppriceobjPojo;
     }
}
