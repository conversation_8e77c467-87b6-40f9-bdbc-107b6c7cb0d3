package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusSteppricePojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemPojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemdetailPojo;

/**
 * 阶梯单价(BusStepprice)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-02 08:47:12
 */
public interface BusSteppriceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusSteppricePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusSteppriceitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusSteppricePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusSteppricePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusSteppricePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busSteppricePojo 实例对象
     * @return 实例对象
     */
    BusSteppricePojo insert(BusSteppricePojo busSteppricePojo);

    /**
     * 修改数据
     *
     * @param busSteppricepojo 实例对象
     * @return 实例对象
     */
    BusSteppricePojo update(BusSteppricePojo busSteppricepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param busSteppricePojo 实例对象
     * @return 实例对象
     */
    BusSteppricePojo approval(BusSteppricePojo busSteppricePojo);


    /**
     * 根据客户查询售价
     *
     * @return 查询结果
     */
    BusSteppriceitemPojo getStepPriceByGroupid(String goodsid,String groupid,Double qty, String tid);

    /**
     * 根据客户等级查询售价
     *
     * @return 查询结果
     */
    BusSteppriceitemPojo getStepPriceByGroupLevel(String goodsid,String grouplevel,Double qty, String tid);
}
