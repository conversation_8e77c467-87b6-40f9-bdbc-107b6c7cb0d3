package inks.service.std.sale.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.sale.domain.pojo.BusCarryoveritemPojo;
import inks.service.std.sale.domain.BusCarryoveritemEntity;
import inks.service.std.sale.mapper.BusCarryoveritemMapper;
import inks.service.std.sale.service.BusCarryoveritemService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 订单>发货(BusCarryoveritem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-27 08:51:10
 */
@Service("busCarryoveritemService")
public class BusCarryoveritemServiceImpl implements BusCarryoveritemService {
    @Resource
    private BusCarryoveritemMapper busCarryoveritemMapper;

    @Override
    public BusCarryoveritemPojo getEntity(String key,String tid) {
        return this.busCarryoveritemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<BusCarryoveritemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCarryoveritemPojo> lst = busCarryoveritemMapper.getPageList(queryParam);
            PageInfo<BusCarryoveritemPojo> pageInfo = new PageInfo<BusCarryoveritemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<BusCarryoveritemPojo> getList(String Pid,String tid) { 
        try {
            List<BusCarryoveritemPojo> lst = busCarryoveritemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public BusCarryoveritemPojo insert(BusCarryoveritemPojo busCarryoveritemPojo) {
        //初始化item的NULL
        BusCarryoveritemPojo itempojo =this.clearNull(busCarryoveritemPojo);
        BusCarryoveritemEntity busCarryoveritemEntity = new BusCarryoveritemEntity(); 
        BeanUtils.copyProperties(itempojo,busCarryoveritemEntity);
          //生成雪花id
          busCarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busCarryoveritemEntity.setRevision(1);  //乐观锁      
          this.busCarryoveritemMapper.insert(busCarryoveritemEntity);
        return this.getEntity(busCarryoveritemEntity.getId(),busCarryoveritemEntity.getTenantid());
  
    }

    @Override
    public BusCarryoveritemPojo update(BusCarryoveritemPojo busCarryoveritemPojo) {
        BusCarryoveritemEntity busCarryoveritemEntity = new BusCarryoveritemEntity(); 
        BeanUtils.copyProperties(busCarryoveritemPojo,busCarryoveritemEntity);
        this.busCarryoveritemMapper.update(busCarryoveritemEntity);
        return this.getEntity(busCarryoveritemEntity.getId(),busCarryoveritemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.busCarryoveritemMapper.delete(key,tid) ;
    }

     @Override
     public BusCarryoveritemPojo clearNull(BusCarryoveritemPojo busCarryoveritemPojo){
     //初始化NULL字段
     if(busCarryoveritemPojo.getPid()==null) busCarryoveritemPojo.setPid("");
     if(busCarryoveritemPojo.getGoodsid()==null) busCarryoveritemPojo.setGoodsid("");
     if(busCarryoveritemPojo.getItemcode()==null) busCarryoveritemPojo.setItemcode("");
     if(busCarryoveritemPojo.getItemname()==null) busCarryoveritemPojo.setItemname("");
     if(busCarryoveritemPojo.getItemspec()==null) busCarryoveritemPojo.setItemspec("");
     if(busCarryoveritemPojo.getItemunit()==null) busCarryoveritemPojo.setItemunit("");
     if(busCarryoveritemPojo.getOpenqty()==null) busCarryoveritemPojo.setOpenqty(0D);
     if(busCarryoveritemPojo.getOpenamount()==null) busCarryoveritemPojo.setOpenamount(0D);
     if(busCarryoveritemPojo.getInqty()==null) busCarryoveritemPojo.setInqty(0D);
     if(busCarryoveritemPojo.getInamount()==null) busCarryoveritemPojo.setInamount(0D);
     if(busCarryoveritemPojo.getOutqty()==null) busCarryoveritemPojo.setOutqty(0D);
     if(busCarryoveritemPojo.getOutamount()==null) busCarryoveritemPojo.setOutamount(0D);
     if(busCarryoveritemPojo.getCloseqty()==null) busCarryoveritemPojo.setCloseqty(0D);
     if(busCarryoveritemPojo.getCloseamount()==null) busCarryoveritemPojo.setCloseamount(0D);
     if(busCarryoveritemPojo.getSkuid()==null) busCarryoveritemPojo.setSkuid("");
     if(busCarryoveritemPojo.getAttributejson()==null) busCarryoveritemPojo.setAttributejson("");
     if(busCarryoveritemPojo.getRownum()==null) busCarryoveritemPojo.setRownum(0);
     if(busCarryoveritemPojo.getCustom1()==null) busCarryoveritemPojo.setCustom1("");
     if(busCarryoveritemPojo.getCustom2()==null) busCarryoveritemPojo.setCustom2("");
     if(busCarryoveritemPojo.getCustom3()==null) busCarryoveritemPojo.setCustom3("");
     if(busCarryoveritemPojo.getCustom4()==null) busCarryoveritemPojo.setCustom4("");
     if(busCarryoveritemPojo.getCustom5()==null) busCarryoveritemPojo.setCustom5("");
     if(busCarryoveritemPojo.getCustom6()==null) busCarryoveritemPojo.setCustom6("");
     if(busCarryoveritemPojo.getCustom7()==null) busCarryoveritemPojo.setCustom7("");
     if(busCarryoveritemPojo.getCustom8()==null) busCarryoveritemPojo.setCustom8("");
     if(busCarryoveritemPojo.getCustom9()==null) busCarryoveritemPojo.setCustom9("");
     if(busCarryoveritemPojo.getCustom10()==null) busCarryoveritemPojo.setCustom10("");
     if(busCarryoveritemPojo.getTenantid()==null) busCarryoveritemPojo.setTenantid("");
     if(busCarryoveritemPojo.getRevision()==null) busCarryoveritemPojo.setRevision(0);
     return busCarryoveritemPojo;
     }
}
