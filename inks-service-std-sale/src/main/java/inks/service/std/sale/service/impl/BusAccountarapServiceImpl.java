package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusAccountarapEntity;
import inks.service.std.sale.domain.pojo.BusAccountarapPojo;
import inks.service.std.sale.mapper.BusAccountarapMapper;
import inks.service.std.sale.service.BusAccountarapService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 发票to收款(BusAccountarap)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-16 11:31:29
 */
@Service("busAccountarapService")
public class BusAccountarapServiceImpl implements BusAccountarapService {
    @Resource
    private BusAccountarapMapper busAccountarapMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountarapPojo getEntity(String key,String tid) {
        return this.busAccountarapMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountarapPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountarapPojo> lst = busAccountarapMapper.getPageList(queryParam);
            PageInfo<BusAccountarapPojo> pageInfo = new PageInfo<BusAccountarapPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusAccountarapPojo> getList(String Pid,String tid) { 
        try {
            List<BusAccountarapPojo> lst = busAccountarapMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busAccountarapPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountarapPojo insert(BusAccountarapPojo busAccountarapPojo) {
        //初始化item的NULL
        BusAccountarapPojo itempojo =this.clearNull(busAccountarapPojo);
        BusAccountarapEntity busAccountarapEntity = new BusAccountarapEntity(); 
        BeanUtils.copyProperties(itempojo,busAccountarapEntity);
          //生成雪花id
          busAccountarapEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busAccountarapEntity.setRevision(1);  //乐观锁      
          this.busAccountarapMapper.insert(busAccountarapEntity);
        return this.getEntity(busAccountarapEntity.getId(),busAccountarapEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busAccountarapPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountarapPojo update(BusAccountarapPojo busAccountarapPojo) {
        BusAccountarapEntity busAccountarapEntity = new BusAccountarapEntity(); 
        BeanUtils.copyProperties(busAccountarapPojo,busAccountarapEntity);
        this.busAccountarapMapper.update(busAccountarapEntity);
        return this.getEntity(busAccountarapEntity.getId(),busAccountarapEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busAccountarapMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busAccountarapPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusAccountarapPojo clearNull(BusAccountarapPojo busAccountarapPojo){
     //初始化NULL字段
     if(busAccountarapPojo.getPid()==null) busAccountarapPojo.setPid("");
     if(busAccountarapPojo.getDirection()==null) busAccountarapPojo.setDirection("");
     if(busAccountarapPojo.getModulecode()==null) busAccountarapPojo.setModulecode("");
     if(busAccountarapPojo.getBilltype()==null) busAccountarapPojo.setBilltype("");
     if(busAccountarapPojo.getBilldate()==null) busAccountarapPojo.setBilldate(new Date());
     if(busAccountarapPojo.getBilltitle()==null) busAccountarapPojo.setBilltitle("");
     if(busAccountarapPojo.getBilluid()==null) busAccountarapPojo.setBilluid("");
     if(busAccountarapPojo.getBillid()==null) busAccountarapPojo.setBillid("");
     if(busAccountarapPojo.getOpenamount()==null) busAccountarapPojo.setOpenamount(0D);
     if(busAccountarapPojo.getInamount()==null) busAccountarapPojo.setInamount(0D);
     if(busAccountarapPojo.getOutamount()==null) busAccountarapPojo.setOutamount(0D);
     if(busAccountarapPojo.getCloseamount()==null) busAccountarapPojo.setCloseamount(0D);
     if(busAccountarapPojo.getRownum()==null) busAccountarapPojo.setRownum(0);
     if(busAccountarapPojo.getRemark()==null) busAccountarapPojo.setRemark("");
     if(busAccountarapPojo.getCustom1()==null) busAccountarapPojo.setCustom1("");
     if(busAccountarapPojo.getCustom2()==null) busAccountarapPojo.setCustom2("");
     if(busAccountarapPojo.getCustom3()==null) busAccountarapPojo.setCustom3("");
     if(busAccountarapPojo.getCustom4()==null) busAccountarapPojo.setCustom4("");
     if(busAccountarapPojo.getCustom5()==null) busAccountarapPojo.setCustom5("");
     if(busAccountarapPojo.getCustom6()==null) busAccountarapPojo.setCustom6("");
     if(busAccountarapPojo.getCustom7()==null) busAccountarapPojo.setCustom7("");
     if(busAccountarapPojo.getCustom8()==null) busAccountarapPojo.setCustom8("");
     if(busAccountarapPojo.getCustom9()==null) busAccountarapPojo.setCustom9("");
     if(busAccountarapPojo.getCustom10()==null) busAccountarapPojo.setCustom10("");
     if(busAccountarapPojo.getTenantid()==null) busAccountarapPojo.setTenantid("");
     if(busAccountarapPojo.getRevision()==null) busAccountarapPojo.setRevision(0);
     return busAccountarapPojo;
     }
}
