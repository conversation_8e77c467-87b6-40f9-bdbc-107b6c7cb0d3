package inks.service.std.sale.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 优惠券表(BusCoupon)实体类
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
public class BusCouponEntity implements Serializable {
    private static final long serialVersionUID = -37909503632934670L;
     // id
    private String id;
     // 客户id
    private String groupid;
     // 优惠券名称
    private String couponname;
     // 优惠券编码
    private String couponcode;
     // 优惠券类型
    private Integer coupontype;
     // 优惠券总金额
    private Double couponamount;
     // 激活金额
    private Double activeamount;
     // 已使用金额
    private Double usedamount;
     // 单据优惠比例上限(0-1)
    private Double uselimitrate;
     // 商品ID
    private String goodsid;
     // 产品编码
    private String itemcode;
     // 产品名称
    private String itemname;
     // 产品规格
    private String itemspec;
     // 产品单位
    private String itemunit;
     // 备注
    private String remark;
     // 顺序
    private Integer rownum;
     // 作废
    private Integer disannulmark;
     // 作废者
    private String disannulby;
     // 作废者id
    private String disannulbyid;
     // 作废日期
    private Date disannuldate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 客户id
    public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
        
// 优惠券名称
    public String getCouponname() {
        return couponname;
    }
    
    public void setCouponname(String couponname) {
        this.couponname = couponname;
    }
        
// 优惠券编码
    public String getCouponcode() {
        return couponcode;
    }
    
    public void setCouponcode(String couponcode) {
        this.couponcode = couponcode;
    }
        
// 优惠券类型
    public Integer getCoupontype() {
        return coupontype;
    }
    
    public void setCoupontype(Integer coupontype) {
        this.coupontype = coupontype;
    }
        
// 优惠券总金额
    public Double getCouponamount() {
        return couponamount;
    }
    
    public void setCouponamount(Double couponamount) {
        this.couponamount = couponamount;
    }
        
// 激活金额
    public Double getActiveamount() {
        return activeamount;
    }
    
    public void setActiveamount(Double activeamount) {
        this.activeamount = activeamount;
    }
        
// 已使用金额
    public Double getUsedamount() {
        return usedamount;
    }
    
    public void setUsedamount(Double usedamount) {
        this.usedamount = usedamount;
    }
        
// 单据优惠比例上限(0-1)
    public Double getUselimitrate() {
        return uselimitrate;
    }
    
    public void setUselimitrate(Double uselimitrate) {
        this.uselimitrate = uselimitrate;
    }
        
// 商品ID
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
// 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
// 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
// 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
// 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 顺序
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
// 作废者
    public String getDisannulby() {
        return disannulby;
    }
    
    public void setDisannulby(String disannulby) {
        this.disannulby = disannulby;
    }
        
// 作废者id
    public String getDisannulbyid() {
        return disannulbyid;
    }
    
    public void setDisannulbyid(String disannulbyid) {
        this.disannulbyid = disannulbyid;
    }
        
// 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
// 审核员
    public String getAssessor() {
        return assessor;
    }
    
    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
        
// 审核员id
    public String getAssessorid() {
        return assessorid;
    }
    
    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
        
// 审核日期
    public Date getAssessdate() {
        return assessdate;
    }
    
    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

