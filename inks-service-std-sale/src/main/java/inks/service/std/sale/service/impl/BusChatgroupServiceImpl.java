package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusChatgroupEntity;
import inks.service.std.sale.domain.BusChatgroupdmsEntity;
import inks.service.std.sale.domain.BusChatgroupitemEntity;
import inks.service.std.sale.domain.pojo.BusChatgroupPojo;
import inks.service.std.sale.domain.pojo.BusChatgroupdmsPojo;
import inks.service.std.sale.domain.pojo.BusChatgroupitemPojo;
import inks.service.std.sale.domain.pojo.BusChatgroupitemdetailPojo;
import inks.service.std.sale.mapper.BusChatgroupMapper;
import inks.service.std.sale.mapper.BusChatgroupdmsMapper;
import inks.service.std.sale.mapper.BusChatgroupitemMapper;
import inks.service.std.sale.service.BusChatgroupService;
import inks.service.std.sale.service.BusChatgroupdmsService;
import inks.service.std.sale.service.BusChatgroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 客服分组(BusChatgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-08 15:45:53
 */
@Service("busChatgroupService")
public class BusChatgroupServiceImpl implements BusChatgroupService {
    @Resource
    private BusChatgroupMapper busChatgroupMapper;

    @Resource
    private BusChatgroupitemMapper busChatgroupitemMapper;
    @Resource
    private BusChatgroupdmsMapper busChatgroupdmsMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private BusChatgroupitemService busChatgroupitemService;
    @Resource
    private BusChatgroupdmsService busChatgroupdmsService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusChatgroupPojo getEntity(String key, String tid) {
        return this.busChatgroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusChatgroupitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusChatgroupitemdetailPojo> lst = busChatgroupMapper.getPageList(queryParam);
            PageInfo<BusChatgroupitemdetailPojo> pageInfo = new PageInfo<BusChatgroupitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusChatgroupPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusChatgroupPojo busChatgroupPojo = this.busChatgroupMapper.getEntity(key, tid);
            //读取子表
            busChatgroupPojo.setItem(busChatgroupitemMapper.getList(busChatgroupPojo.getId(), busChatgroupPojo.getTenantid()));
            //读取dms子表
            busChatgroupPojo.setDms(busChatgroupdmsMapper.getList(busChatgroupPojo.getId(), busChatgroupPojo.getTenantid()));
            return busChatgroupPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusChatgroupPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusChatgroupPojo> lst = busChatgroupMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表,dms子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busChatgroupitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setDms(busChatgroupdmsMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusChatgroupPojo> pageInfo = new PageInfo<BusChatgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusChatgroupPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusChatgroupPojo> lst = busChatgroupMapper.getPageTh(queryParam);
            PageInfo<BusChatgroupPojo> pageInfo = new PageInfo<BusChatgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busChatgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusChatgroupPojo insert(BusChatgroupPojo busChatgroupPojo) {
//初始化NULL字段
        if (busChatgroupPojo.getParentid() == null) busChatgroupPojo.setParentid("");
        if (busChatgroupPojo.getGrouptype() == null) busChatgroupPojo.setGrouptype("");
        if (busChatgroupPojo.getGroupcode() == null) busChatgroupPojo.setGroupcode("");
        if (busChatgroupPojo.getGroupname() == null) busChatgroupPojo.setGroupname("");
        if (busChatgroupPojo.getGrouplevel() == null) busChatgroupPojo.setGrouplevel(0);
        if (busChatgroupPojo.getStatecode() == null) busChatgroupPojo.setStatecode("");
        if (busChatgroupPojo.getRownum() == null) busChatgroupPojo.setRownum(0);
        if (busChatgroupPojo.getRemark() == null) busChatgroupPojo.setRemark("");
        if (busChatgroupPojo.getCreateby() == null) busChatgroupPojo.setCreateby("");
        if (busChatgroupPojo.getCreatebyid() == null) busChatgroupPojo.setCreatebyid("");
        if (busChatgroupPojo.getCreatedate() == null) busChatgroupPojo.setCreatedate(new Date());
        if (busChatgroupPojo.getLister() == null) busChatgroupPojo.setLister("");
        if (busChatgroupPojo.getListerid() == null) busChatgroupPojo.setListerid("");
        if (busChatgroupPojo.getModifydate() == null) busChatgroupPojo.setModifydate(new Date());
        if (busChatgroupPojo.getCustom1() == null) busChatgroupPojo.setCustom1("");
        if (busChatgroupPojo.getCustom2() == null) busChatgroupPojo.setCustom2("");
        if (busChatgroupPojo.getCustom3() == null) busChatgroupPojo.setCustom3("");
        if (busChatgroupPojo.getCustom4() == null) busChatgroupPojo.setCustom4("");
        if (busChatgroupPojo.getTenantid() == null) busChatgroupPojo.setTenantid("");
        if (busChatgroupPojo.getTenantname() == null) busChatgroupPojo.setTenantname("");
        if (busChatgroupPojo.getRevision() == null) busChatgroupPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusChatgroupEntity busChatgroupEntity = new BusChatgroupEntity();
        BeanUtils.copyProperties(busChatgroupPojo, busChatgroupEntity);

        //设置id和新建日期
        busChatgroupEntity.setId(id);
        busChatgroupEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busChatgroupMapper.insert(busChatgroupEntity);
        //Item子表处理
        List<BusChatgroupitemPojo> lst = busChatgroupPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (BusChatgroupitemPojo busChatgroupitemPojo : lst) {
                //初始化item的NULL
                BusChatgroupitemPojo itemPojo = this.busChatgroupitemService.clearNull(busChatgroupitemPojo);
                BusChatgroupitemEntity busChatgroupitemEntity = new BusChatgroupitemEntity();
                BeanUtils.copyProperties(itemPojo, busChatgroupitemEntity);
                //设置id和Pid
                busChatgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busChatgroupitemEntity.setPid(id);
                busChatgroupitemEntity.setTenantid(busChatgroupPojo.getTenantid());
                busChatgroupitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busChatgroupitemMapper.insert(busChatgroupitemEntity);
            }
        }
        // dms子表处理
        List<BusChatgroupdmsPojo> dms = busChatgroupPojo.getDms();
        if (dms != null) {
            //循环每个dms子表
            for (BusChatgroupdmsPojo busChatgroupdmsPojo : dms) {
                //初始化dms的NULL
                BusChatgroupdmsPojo dmsPojo = this.busChatgroupdmsService.clearNull(busChatgroupdmsPojo);
                BusChatgroupdmsEntity busChatgroupdmsEntity = new BusChatgroupdmsEntity();
                BeanUtils.copyProperties(dmsPojo, busChatgroupdmsEntity);
                //设置id和Pid
                busChatgroupdmsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busChatgroupdmsEntity.setPid(id);
                busChatgroupdmsEntity.setTenantid(busChatgroupPojo.getTenantid());
                busChatgroupdmsEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busChatgroupdmsMapper.insert(busChatgroupdmsEntity);
            }
        }


        //返回Bill实例
        return this.getBillEntity(busChatgroupEntity.getId(), busChatgroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busChatgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusChatgroupPojo update(BusChatgroupPojo busChatgroupPojo) {
        //主表更改
        BusChatgroupEntity busChatgroupEntity = new BusChatgroupEntity();
        BeanUtils.copyProperties(busChatgroupPojo, busChatgroupEntity);
        this.busChatgroupMapper.update(busChatgroupEntity);
        if (busChatgroupPojo.getItem() != null) {
            //Item子表处理
            List<BusChatgroupitemPojo> lst = busChatgroupPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busChatgroupMapper.getDelItemIds(busChatgroupPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.busChatgroupitemMapper.delete(lstDelId, busChatgroupEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (BusChatgroupitemPojo busChatgroupitemPojo : lst) {
                    BusChatgroupitemEntity busChatgroupitemEntity = new BusChatgroupitemEntity();
                    if ("".equals(busChatgroupitemPojo.getId()) || busChatgroupitemPojo.getId() == null) {
                        //初始化item的NULL
                        BusChatgroupitemPojo itemPojo = this.busChatgroupitemService.clearNull(busChatgroupitemPojo);
                        BeanUtils.copyProperties(itemPojo, busChatgroupitemEntity);
                        //设置id和Pid
                        busChatgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busChatgroupitemEntity.setPid(busChatgroupEntity.getId());  // 主表 id
                        busChatgroupitemEntity.setTenantid(busChatgroupPojo.getTenantid());   // 租户id
                        busChatgroupitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busChatgroupitemMapper.insert(busChatgroupitemEntity);
                    } else {
                        BeanUtils.copyProperties(busChatgroupitemPojo, busChatgroupitemEntity);
                        busChatgroupitemEntity.setTenantid(busChatgroupPojo.getTenantid());
                        this.busChatgroupitemMapper.update(busChatgroupitemEntity);
                    }
                }
            }


        }

        if (busChatgroupPojo.getDms() != null) {
            //dms子表处理
            List<BusChatgroupdmsPojo> dms = busChatgroupPojo.getDms();
            //获取被删除的dms
            List<String> lstDelIds = busChatgroupMapper.getDelDmsIds(busChatgroupPojo);
            if (lstDelIds != null) {
                //循环每个删除dms子表
                for (String lstDelId : lstDelIds) {
                    this.busChatgroupdmsMapper.delete(lstDelId, busChatgroupEntity.getTenantid());
                }
            }
            if (dms != null) {
                //循环每个dms子表
                for (BusChatgroupdmsPojo busChatgroupdmsPojo : dms) {
                    BusChatgroupdmsEntity busChatgroupdmsEntity = new BusChatgroupdmsEntity();
                    if ("".equals(busChatgroupdmsPojo.getId()) || busChatgroupdmsPojo.getId() == null) {
                        //初始化dms的NULL
                        BusChatgroupdmsPojo dmsPojo = this.busChatgroupdmsService.clearNull(busChatgroupdmsPojo);
                        BeanUtils.copyProperties(dmsPojo, busChatgroupdmsEntity);
                        //设置id和Pid
                        busChatgroupdmsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // dms id
                        busChatgroupdmsEntity.setPid(busChatgroupEntity.getId());  // 主表 id
                        busChatgroupdmsEntity.setTenantid(busChatgroupPojo.getTenantid());   // 租户id
                        busChatgroupdmsEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busChatgroupdmsMapper.insert(busChatgroupdmsEntity);
                    } else {
                        BeanUtils.copyProperties(busChatgroupdmsPojo, busChatgroupdmsEntity);
                        busChatgroupdmsEntity.setTenantid(busChatgroupPojo.getTenantid());
                        this.busChatgroupdmsMapper.update(busChatgroupdmsEntity);
                    }
                }
            }


        }


        //返回Bill实例
        return this.getBillEntity(busChatgroupEntity.getId(), busChatgroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusChatgroupPojo busChatgroupPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusChatgroupitemPojo> lst = busChatgroupPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusChatgroupitemPojo busChatgroupitemPojo : lst) {
                this.busChatgroupitemMapper.delete(busChatgroupitemPojo.getId(), tid);
            }
        }

        // dms子表处理
        List<BusChatgroupdmsPojo> dms = busChatgroupPojo.getDms();
        if (dms != null) {
            //循环每个删除dms子表
            for (BusChatgroupdmsPojo busChatgroupdmsPojo : dms) {
                this.busChatgroupdmsMapper.delete(busChatgroupdmsPojo.getId(), tid);
            }
        }

        return this.busChatgroupMapper.delete(key, tid);
    }


}
