package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCarryoveritemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 订单>发货(BusCarryoveritem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-27 08:51:10
 */
public interface BusCarryoveritemService {

    BusCarryoveritemPojo getEntity(String key,String tid);

    PageInfo<BusCarryoveritemPojo> getPageList(QueryParam queryParam);

    List<BusCarryoveritemPojo> getList(String Pid,String tid);  

    BusCarryoveritemPojo insert(BusCarryoveritemPojo busCarryoveritemPojo);

    BusCarryoveritemPojo update(BusCarryoveritemPojo busCarryoveritempojo);

    int delete(String key,String tid);

    BusCarryoveritemPojo clearNull(BusCarryoveritemPojo busCarryoveritempojo);
}
