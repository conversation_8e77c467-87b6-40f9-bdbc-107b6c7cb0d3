package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusChatgroupitemEntity;
import inks.service.std.sale.domain.pojo.BusChatgroupitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客服分组客服子表(BusChatgroupitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-08 15:49:08
 */
 @Mapper
public interface BusChatgroupitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusChatgroupitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusChatgroupitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusChatgroupitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busChatgroupitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusChatgroupitemEntity busChatgroupitemEntity);

    
    /**
     * 修改数据
     *
     * @param busChatgroupitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusChatgroupitemEntity busChatgroupitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

