package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusIntendedEntity;
import inks.service.std.sale.domain.pojo.BusIntendedPojo;
import inks.service.std.sale.domain.pojo.BusIntendeditemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 意向订单(BusIntended)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-17 09:12:04
 */
@Mapper
public interface BusIntendedMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusIntendedPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusIntendeditemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusIntendedPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busIntendedEntity 实例对象
     * @return 影响行数
     */
    int insert(BusIntendedEntity busIntendedEntity);

    
    /**
     * 修改数据
     *
     * @param busIntendedEntity 实例对象
     * @return 影响行数
     */
    int update(BusIntendedEntity busIntendedEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param busIntendedPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(BusIntendedPojo busIntendedPojo);
                                                                                                                                                                              /**
     * 修改数据
     *
     * @param busIntendedEntity 实例对象
     * @return 影响行数
     */
    int approval(BusIntendedEntity busIntendedEntity);
                                                                                                          }

