package inks.service.std.sale.utils;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * RefNo AOP 工具类
 * 提供AOP切面中常用的工具方法
 * 
 * <AUTHOR>
 */
public class RefNoAopUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(RefNoAopUtils.class);
    
    /**
     * 从Controller类中提取模块代码
     * 
     * @param target Controller实例
     * @return 模块代码
     */
    public static String extractModuleCode(Object target) {
        if (target == null) {
            return null;
        }
        
        try {
            Class<?> targetClass = target.getClass();
            
            // 方式1：尝试从Controller类中获取moduleCode字段
            try {
                Field moduleCodeField = targetClass.getDeclaredField("moduleCode");
                moduleCodeField.setAccessible(true);
                Object moduleCodeValue = moduleCodeField.get(target);
                if (moduleCodeValue != null && StringUtils.isNotBlank(moduleCodeValue.toString())) {
                    return moduleCodeValue.toString();
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                // 字段不存在或无法访问，继续其他方式
                logger.debug("无法从字段获取moduleCode: {}", e.getMessage());
            }
            
            // 方式2：从类名中提取模块代码（如D01M06B1Controller -> D01M06B1）
            String className = targetClass.getSimpleName();
            if (className.endsWith("Controller")) {
                String moduleCode = className.substring(0, className.length() - "Controller".length());
                if (StringUtils.isNotBlank(moduleCode)) {
                    return moduleCode;
                }
            }
            
            // 方式3：尝试从父类获取
            Class<?> superClass = targetClass.getSuperclass();
            if (superClass != null && !superClass.equals(Object.class)) {
                return extractModuleCode(createInstance(superClass));
            }
            
            logger.warn("无法从Controller类中提取模块代码: {}", className);
            return null;
            
        } catch (Exception e) {
            logger.error("提取模块代码时发生异常", e);
            return null;
        }
    }
    
    /**
     * 设置字段值到对象中
     * 支持JSONObject和普通POJO对象
     * 
     * @param obj 目标对象
     * @param fieldName 字段名
     * @param value 字段值
     * @throws Exception 设置失败时抛出异常
     */
    public static void setFieldValue(Object obj, String fieldName, Object value) throws Exception {
        if (obj == null || StringUtils.isBlank(fieldName)) {
            throw new IllegalArgumentException("对象或字段名不能为空");
        }
        
        if (obj instanceof JSONObject) {
            // JSONObject直接设置
            ((JSONObject) obj).put(fieldName, value);
            logger.debug("通过JSONObject设置字段: {} = {}", fieldName, value);
        } else {
            // 普通对象通过反射设置
            setFieldValueByReflection(obj, fieldName, value);
        }
    }
    
    /**
     * 通过反射设置字段值
     * 
     * @param obj 目标对象
     * @param fieldName 字段名
     * @param value 字段值
     * @throws Exception 设置失败时抛出异常
     */
    private static void setFieldValueByReflection(Object obj, String fieldName, Object value) throws Exception {
        Class<?> clazz = obj.getClass();
        
        // 方式1：直接设置字段
        try {
            Field field = findField(clazz, fieldName);
            if (field != null) {
                field.setAccessible(true);
                field.set(obj, value);
                logger.debug("通过字段反射设置: {} = {}", fieldName, value);
                return;
            }
        } catch (Exception e) {
            logger.debug("通过字段设置失败: {}", e.getMessage());
        }
        
        // 方式2：通过setter方法设置
        try {
            String setterName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            Method setter = findMethod(clazz, setterName, String.class);
            if (setter != null) {
                setter.setAccessible(true);
                setter.invoke(obj, value);
                logger.debug("通过setter方法设置: {} = {}", fieldName, value);
                return;
            }
        } catch (Exception e) {
            logger.debug("通过setter方法设置失败: {}", e.getMessage());
        }
        
        throw new NoSuchFieldException("无法找到字段或对应的setter方法: " + fieldName);
    }
    
    /**
     * 查找字段，包括父类中的字段
     * 
     * @param clazz 类
     * @param fieldName 字段名
     * @return 字段对象，未找到返回null
     */
    private static Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null && !currentClass.equals(Object.class)) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }
    
    /**
     * 查找方法，包括父类中的方法
     * 
     * @param clazz 类
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @return 方法对象，未找到返回null
     */
    private static Method findMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        Class<?> currentClass = clazz;
        while (currentClass != null && !currentClass.equals(Object.class)) {
            try {
                return currentClass.getDeclaredMethod(methodName, parameterTypes);
            } catch (NoSuchMethodException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }
    
    /**
     * 创建类的实例（用于获取父类信息）
     * 
     * @param clazz 类
     * @return 实例对象
     */
    private static Object createInstance(Class<?> clazz) {
        try {
            return clazz.newInstance();
        } catch (Exception e) {
            logger.debug("无法创建类实例: {}", clazz.getName());
            return null;
        }
    }
    
    /**
     * 验证参数索引是否有效
     * 
     * @param args 方法参数数组
     * @param paramIndex 参数索引
     * @return 是否有效
     */
    public static boolean isValidParamIndex(Object[] args, int paramIndex) {
        return args != null && paramIndex >= 0 && paramIndex < args.length;
    }
    
    /**
     * 获取指定索引的参数
     * 
     * @param args 方法参数数组
     * @param paramIndex 参数索引
     * @return 参数对象，无效索引返回null
     */
    public static Object getParameter(Object[] args, int paramIndex) {
        if (!isValidParamIndex(args, paramIndex)) {
            return null;
        }
        return args[paramIndex];
    }
    
    /**
     * 检查字符串是否为有效的编码
     * 
     * @param refno 编码字符串
     * @return 是否有效
     */
    public static boolean isValidRefNo(String refno) {
        return StringUtils.isNotBlank(refno) && refno.length() > 3;
    }
    
    /**
     * 格式化日志消息
     * 
     * @param template 消息模板
     * @param args 参数
     * @return 格式化后的消息
     */
    public static String formatLogMessage(String template, Object... args) {
        try {
            return String.format(template, args);
        } catch (Exception e) {
            return template + " (格式化失败)";
        }
    }
}
