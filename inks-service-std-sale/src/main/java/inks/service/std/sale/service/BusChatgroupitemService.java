package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusChatgroupitemPojo;

import java.util.List;
/**
 * 客服分组客服子表(BusChatgroupitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-08 15:49:08
 */
public interface BusChatgroupitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusChatgroupitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusChatgroupitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusChatgroupitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busChatgroupitemPojo 实例对象
     * @return 实例对象
     */
    BusChatgroupitemPojo insert(BusChatgroupitemPojo busChatgroupitemPojo);

    /**
     * 修改数据
     *
     * @param busChatgroupitempojo 实例对象
     * @return 实例对象
     */
    BusChatgroupitemPojo update(BusChatgroupitemPojo busChatgroupitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busChatgroupitempojo 实例对象
     * @return 实例对象
     */
    BusChatgroupitemPojo clearNull(BusChatgroupitemPojo busChatgroupitempojo);
}
