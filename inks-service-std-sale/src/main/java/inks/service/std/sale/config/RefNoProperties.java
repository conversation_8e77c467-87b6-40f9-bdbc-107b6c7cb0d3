package inks.service.std.sale.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * RefNo相关配置属性
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "inks.refno")
public class RefNoProperties {
    
    /**
     * 是否启用RefNo AOP功能
     */
    private boolean enabled = true;
    
    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;
    
    /**
     * 默认的编码字段名
     */
    private String defaultField = "refno";
    
    /**
     * 是否默认保存到Redis
     */
    private boolean defaultSaveToRedis = true;
    
    /**
     * 是否默认忽略清理异常
     */
    private boolean defaultIgnoreCleanupException = true;
    
    /**
     * 编码生成超时时间（毫秒）
     */
    private long generateTimeout = 5000L;
    
    /**
     * Redis清理超时时间（毫秒）
     */
    private long cleanupTimeout = 3000L;
    
    // Getters and Setters
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public boolean isVerboseLogging() {
        return verboseLogging;
    }
    
    public void setVerboseLogging(boolean verboseLogging) {
        this.verboseLogging = verboseLogging;
    }
    
    public String getDefaultField() {
        return defaultField;
    }
    
    public void setDefaultField(String defaultField) {
        this.defaultField = defaultField;
    }
    
    public boolean isDefaultSaveToRedis() {
        return defaultSaveToRedis;
    }
    
    public void setDefaultSaveToRedis(boolean defaultSaveToRedis) {
        this.defaultSaveToRedis = defaultSaveToRedis;
    }
    
    public boolean isDefaultIgnoreCleanupException() {
        return defaultIgnoreCleanupException;
    }
    
    public void setDefaultIgnoreCleanupException(boolean defaultIgnoreCleanupException) {
        this.defaultIgnoreCleanupException = defaultIgnoreCleanupException;
    }
    
    public long getGenerateTimeout() {
        return generateTimeout;
    }
    
    public void setGenerateTimeout(long generateTimeout) {
        this.generateTimeout = generateTimeout;
    }
    
    public long getCleanupTimeout() {
        return cleanupTimeout;
    }
    
    public void setCleanupTimeout(long cleanupTimeout) {
        this.cleanupTimeout = cleanupTimeout;
    }
    
    @Override
    public String toString() {
        return "RefNoProperties{" +
                "enabled=" + enabled +
                ", verboseLogging=" + verboseLogging +
                ", defaultField='" + defaultField + '\'' +
                ", defaultSaveToRedis=" + defaultSaveToRedis +
                ", defaultIgnoreCleanupException=" + defaultIgnoreCleanupException +
                ", generateTimeout=" + generateTimeout +
                ", cleanupTimeout=" + cleanupTimeout +
                '}';
    }
}
