package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCarryoverPojo;
import inks.service.std.sale.domain.pojo.BusCarryoverinvodetailPojo;
import inks.service.std.sale.domain.pojo.BusCarryoveritemdetailPojo;
import inks.service.std.sale.domain.BusCarryoverEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 货品账单(BusCarryover)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-27 08:50:19
 */
@Mapper
public interface BusCarryoverMapper {

    BusCarryoverPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusCarryoveritemdetailPojo> getPageList(QueryParam queryParam);

    List<BusCarryoverinvodetailPojo> getInvoPageList(QueryParam queryParam);

    List<BusCarryoverPojo> getPageTh(QueryParam queryParam);

    int insert(BusCarryoverEntity busCarryoverEntity);

    int update(BusCarryoverEntity busCarryoverEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(BusCarryoverPojo busCarryoverPojo);

    List<String> getDelInvoIds(BusCarryoverPojo busCarryoverPojo);

    int deleteByMonth(@Param("carryoverIds") List<String> carryoverIds, @Param("tid") String tid);

    List<String> getCarryoverIdsForDeletion(Integer carryyear, Integer carrymonth, String tid, int batchSize);

    int deleteCarryoverItemByMonth(List<String> carryoverIds, String tid);

    int deleteCarryoverInvoByMonth(List<String> carryoverIds, String tid);

    int deleteCarryoverByMonth(List<String> carryoverIds, String tid);

    int deleteInvoCarryoverByRecid(String recid, String tid);

    int deleteDeliCarryoverByRecid(String recid, String tid);
}

