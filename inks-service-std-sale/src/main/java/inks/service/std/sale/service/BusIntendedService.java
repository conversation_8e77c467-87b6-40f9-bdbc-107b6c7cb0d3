package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusIntendedPojo;
import inks.service.std.sale.domain.pojo.BusIntendeditemdetailPojo;

/**
 * 意向订单(BusIntended)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17 09:12:04
 */
public interface BusIntendedService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusIntendedPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusIntendeditemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusIntendedPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusIntendedPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusIntendedPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busIntendedPojo 实例对象
     * @return 实例对象
     */
    BusIntendedPojo insert(BusIntendedPojo busIntendedPojo);

    /**
     * 修改数据
     *
     * @param busIntendedpojo 实例对象
     * @return 实例对象
     */
    BusIntendedPojo update(BusIntendedPojo busIntendedpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key,String tid);

                                                                                                                                                                               /**
     * 审核数据
     *
     * @param busIntendedPojo 实例对象
     * @return 实例对象
     */
     BusIntendedPojo approval(BusIntendedPojo busIntendedPojo);
                                                                                                         }
