package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusDeductionitemEntity;
import inks.service.std.sale.domain.pojo.BusDeductionitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售扣款Item(BusDeductionitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-09 14:37:58
 */
 @Mapper
public interface BusDeductionitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDeductionitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDeductionitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusDeductionitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busDeductionitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusDeductionitemEntity busDeductionitemEntity);

    
    /**
     * 修改数据
     *
     * @param busDeductionitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusDeductionitemEntity busDeductionitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

