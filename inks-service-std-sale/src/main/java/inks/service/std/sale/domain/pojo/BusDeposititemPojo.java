package inks.service.std.sale.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 预收款项目(BusDeposititem)Pojo
 *
 * <AUTHOR>
 * @since 2021-11-20 13:12:51
 */
public class BusDeposititemPojo implements Serializable {
    private static final long serialVersionUID = 164002328119604970L;
    // ID
    @Excel(name = "ID")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 订单id
    @Excel(name = "订单id")
    private String machbillid;
    // 订单单号(备查)
    @Excel(name = "订单单号(备查)")
    private String machbillcode;
    // 发货id
    @Excel(name = "发货id")
    private String delibillid;
    // 发货单号(备查)
    @Excel(name = "发货单号(备查)")
    private String delibillcode;
    // 单据金额
    @Excel(name = "单据金额")
    private Double billtaxamount;
    // 金额
    @Excel(name = "金额")
    private Double amount;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 订单id
    public String getMachbillid() {
        return machbillid;
    }

    public void setMachbillid(String machbillid) {
        this.machbillid = machbillid;
    }

    // 订单单号(备查)
    public String getMachbillcode() {
        return machbillcode;
    }

    public void setMachbillcode(String machbillcode) {
        this.machbillcode = machbillcode;
    }

    // 发货id
    public String getDelibillid() {
        return delibillid;
    }

    public void setDelibillid(String delibillid) {
        this.delibillid = delibillid;
    }

    // 发货单号(备查)
    public String getDelibillcode() {
        return delibillcode;
    }

    public void setDelibillcode(String delibillcode) {
        this.delibillcode = delibillcode;
    }

    // 单据金额
    public Double getBilltaxamount() {
        return billtaxamount;
    }

    public void setBilltaxamount(Double billtaxamount) {
        this.billtaxamount = billtaxamount;
    }

    // 金额
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

