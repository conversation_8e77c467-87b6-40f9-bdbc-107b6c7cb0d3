package inks.service.std.sale.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCouponPojo;
import inks.service.std.sale.service.BusCouponService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 优惠券表(Bus_Coupon)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
//@RestController
//@RequestMapping("busCoupon")
public class BusCouponController {

    @Resource
    private BusCouponService busCouponService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(BusCouponController.class);


    @ApiOperation(value=" 获取优惠券表详细信息", notes="获取优惠券表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Coupon.List")
    public R<BusCouponPojo> getEntity(String key) {
    try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busCouponService.getEntity(key, loginUser.getTenantid()));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Coupon.List")
    public R<PageInfo<BusCouponPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Bus_Coupon.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busCouponService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增优惠券表", notes="新增优惠券表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Coupon.Add")
    public R<BusCouponPojo> create(@RequestBody String json) {
       try {
       BusCouponPojo busCouponPojo = JSONArray.parseObject(json,BusCouponPojo.class);       
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCouponPojo.setCreateby(loginUser.getRealName());   // 创建者
            busCouponPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busCouponPojo.setCreatedate(new Date());   // 创建时间
            busCouponPojo.setLister(loginUser.getRealname());   // 制表
            busCouponPojo.setListerid(loginUser.getUserid());    // 制表id  
            busCouponPojo.setModifydate(new Date());   //修改时间
            busCouponPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.busCouponService.insert(busCouponPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改优惠券表", notes="修改优惠券表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Coupon.Edit")
    public R<BusCouponPojo> update(@RequestBody String json) {
       try {
         BusCouponPojo busCouponPojo = JSONArray.parseObject(json,BusCouponPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCouponPojo.setLister(loginUser.getRealname());   // 制表
            busCouponPojo.setListerid(loginUser.getUserid());    // 制表id  
            busCouponPojo.setTenantid(loginUser.getTenantid());   //租户id
            busCouponPojo.setModifydate(new Date());   //修改时间
//            busCouponPojo.setAssessor(""); // 审核员
//            busCouponPojo.setAssessorid(""); // 审核员id
//            busCouponPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.busCouponService.update(busCouponPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除优惠券表", notes="删除优惠券表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Coupon.Delete")
    public R<Integer> delete(String key) {
    try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busCouponService.delete(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "审核优惠券表", notes = "审核优惠券表", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Coupon.Approval")
    public R<BusCouponPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusCouponPojo busCouponPojo = this.busCouponService.getEntity(key, loginUser.getTenantid());
            if (busCouponPojo.getAssessor().equals(""))
            {
                busCouponPojo.setAssessor(loginUser.getRealname()); //审核员
                busCouponPojo.setAssessorid(loginUser.getUserid()); //审核员id
                }
            else
            {
                busCouponPojo.setAssessor(""); //审核员
                busCouponPojo.setAssessorid(""); //审核员
                }
            busCouponPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busCouponService.approval(busCouponPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    @ApiOperation(value = "作废优惠券", notes = ",?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.Edit")
    public R<BusCouponPojo> disannul(String key, Integer type) {
        try {
            if (type == null) type = 1;
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busCouponService.disannul(key, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Coupon.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusCouponPojo busCouponPojo = this.busCouponService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busCouponPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

