package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDmsgoodsPojo;

/**
 * DMS商品(BusDmsgoods)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-21 15:25:29
 */
public interface BusDmsgoodsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDmsgoodsPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDmsgoodsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDmsgoodsPojo 实例对象
     * @return 实例对象
     */
    BusDmsgoodsPojo insert(BusDmsgoodsPojo busDmsgoodsPojo);

    /**
     * 修改数据
     *
     * @param busDmsgoodspojo 实例对象
     * @return 实例对象
     */
    BusDmsgoodsPojo update(BusDmsgoodsPojo busDmsgoodspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key,String tid);
                                                                                                                                                                               }
