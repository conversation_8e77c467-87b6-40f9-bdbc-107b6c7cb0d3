package inks.service.std.sale.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.sale.domain.pojo.BusCarryoverinvoPojo;
import inks.service.std.sale.domain.BusCarryoverinvoEntity;
import inks.service.std.sale.mapper.BusCarryoverinvoMapper;
import inks.service.std.sale.service.BusCarryoverinvoService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 发货>发票(BusCarryoverinvo)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-27 08:50:35
 */
@Service("busCarryoverinvoService")
public class BusCarryoverinvoServiceImpl implements BusCarryoverinvoService {
    @Resource
    private BusCarryoverinvoMapper busCarryoverinvoMapper;

    @Override
    public BusCarryoverinvoPojo getEntity(String key,String tid) {
        return this.busCarryoverinvoMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<BusCarryoverinvoPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCarryoverinvoPojo> lst = busCarryoverinvoMapper.getPageList(queryParam);
            PageInfo<BusCarryoverinvoPojo> pageInfo = new PageInfo<BusCarryoverinvoPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<BusCarryoverinvoPojo> getList(String Pid,String tid) { 
        try {
            List<BusCarryoverinvoPojo> lst = busCarryoverinvoMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public BusCarryoverinvoPojo insert(BusCarryoverinvoPojo busCarryoverinvoPojo) {
        //初始化item的NULL
        BusCarryoverinvoPojo itempojo =this.clearNull(busCarryoverinvoPojo);
        BusCarryoverinvoEntity busCarryoverinvoEntity = new BusCarryoverinvoEntity(); 
        BeanUtils.copyProperties(itempojo,busCarryoverinvoEntity);
          //生成雪花id
          busCarryoverinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busCarryoverinvoEntity.setRevision(1);  //乐观锁      
          this.busCarryoverinvoMapper.insert(busCarryoverinvoEntity);
        return this.getEntity(busCarryoverinvoEntity.getId(),busCarryoverinvoEntity.getTenantid());
  
    }

    @Override
    public BusCarryoverinvoPojo update(BusCarryoverinvoPojo busCarryoverinvoPojo) {
        BusCarryoverinvoEntity busCarryoverinvoEntity = new BusCarryoverinvoEntity(); 
        BeanUtils.copyProperties(busCarryoverinvoPojo,busCarryoverinvoEntity);
        this.busCarryoverinvoMapper.update(busCarryoverinvoEntity);
        return this.getEntity(busCarryoverinvoEntity.getId(),busCarryoverinvoEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.busCarryoverinvoMapper.delete(key,tid) ;
    }

     @Override
     public BusCarryoverinvoPojo clearNull(BusCarryoverinvoPojo busCarryoverinvoPojo){
     //初始化NULL字段
     if(busCarryoverinvoPojo.getPid()==null) busCarryoverinvoPojo.setPid("");
     if(busCarryoverinvoPojo.getGoodsid()==null) busCarryoverinvoPojo.setGoodsid("");
     if(busCarryoverinvoPojo.getItemcode()==null) busCarryoverinvoPojo.setItemcode("");
     if(busCarryoverinvoPojo.getItemname()==null) busCarryoverinvoPojo.setItemname("");
     if(busCarryoverinvoPojo.getItemspec()==null) busCarryoverinvoPojo.setItemspec("");
     if(busCarryoverinvoPojo.getItemunit()==null) busCarryoverinvoPojo.setItemunit("");
     if(busCarryoverinvoPojo.getOpenqty()==null) busCarryoverinvoPojo.setOpenqty(0D);
     if(busCarryoverinvoPojo.getOpenamount()==null) busCarryoverinvoPojo.setOpenamount(0D);
     if(busCarryoverinvoPojo.getInqty()==null) busCarryoverinvoPojo.setInqty(0D);
     if(busCarryoverinvoPojo.getInamount()==null) busCarryoverinvoPojo.setInamount(0D);
     if(busCarryoverinvoPojo.getOutqty()==null) busCarryoverinvoPojo.setOutqty(0D);
     if(busCarryoverinvoPojo.getOutamount()==null) busCarryoverinvoPojo.setOutamount(0D);
     if(busCarryoverinvoPojo.getCloseqty()==null) busCarryoverinvoPojo.setCloseqty(0D);
     if(busCarryoverinvoPojo.getCloseamount()==null) busCarryoverinvoPojo.setCloseamount(0D);
     if(busCarryoverinvoPojo.getSkuid()==null) busCarryoverinvoPojo.setSkuid("");
     if(busCarryoverinvoPojo.getAttributejson()==null) busCarryoverinvoPojo.setAttributejson("");
     if(busCarryoverinvoPojo.getRownum()==null) busCarryoverinvoPojo.setRownum(0);
     if(busCarryoverinvoPojo.getCustom1()==null) busCarryoverinvoPojo.setCustom1("");
     if(busCarryoverinvoPojo.getCustom2()==null) busCarryoverinvoPojo.setCustom2("");
     if(busCarryoverinvoPojo.getCustom3()==null) busCarryoverinvoPojo.setCustom3("");
     if(busCarryoverinvoPojo.getCustom4()==null) busCarryoverinvoPojo.setCustom4("");
     if(busCarryoverinvoPojo.getCustom5()==null) busCarryoverinvoPojo.setCustom5("");
     if(busCarryoverinvoPojo.getCustom6()==null) busCarryoverinvoPojo.setCustom6("");
     if(busCarryoverinvoPojo.getCustom7()==null) busCarryoverinvoPojo.setCustom7("");
     if(busCarryoverinvoPojo.getCustom8()==null) busCarryoverinvoPojo.setCustom8("");
     if(busCarryoverinvoPojo.getCustom9()==null) busCarryoverinvoPojo.setCustom9("");
     if(busCarryoverinvoPojo.getCustom10()==null) busCarryoverinvoPojo.setCustom10("");
     if(busCarryoverinvoPojo.getTenantid()==null) busCarryoverinvoPojo.setTenantid("");
     if(busCarryoverinvoPojo.getRevision()==null) busCarryoverinvoPojo.setRevision(0);
     return busCarryoverinvoPojo;
     }
}
