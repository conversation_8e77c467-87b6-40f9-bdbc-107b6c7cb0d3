package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusInvocarryoverPojo;
import inks.service.std.sale.service.BusInvocarryoverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 货品账单:发货>发票(Bus_InvoCarryover)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-10 11:30:17
 */
@RestController
@RequestMapping("D01M11B1")
@Api(tags = "D01M11B1:货品账单:发货>发票")
public class D01M11B1Controller extends BusInvocarryoverController {
    @Resource
    private BusInvocarryoverService busInvocarryoverService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "发货单->销售开票 货品账单明细 传入recid(Bus_AccountRec.id)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_InvoCarryover.List")
    public R<PageInfo<BusInvocarryoverPojo>> getPageListByMonth(@RequestBody String json, String recid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (StringUtils.isBlank(recid)) return R.fail("请传入recid(Bus_AccountRec.id)");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_InvoCarryover.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter;
            qpfilter = " and Bus_InvoCarryover.Recid=" + recid;
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvocarryoverService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发货单->销售开票 货品账单明细 传入recid(Bus_AccountRec.id)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageListByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_InvoCarryover.List")
    public R<PageInfo<BusInvocarryoverPojo>> getOnlinePageListByMonth(@RequestBody String json, String recid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (StringUtils.isBlank(recid)) return R.fail("请传入recid(Bus_AccountRec.id)");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_InvoCarryover.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter;
            qpfilter = " and Bus_InvoCarryover.Recid=" + recid;
            //OpenQty, InQty, OutQty, CloseQty 同时都为0的不要
            //qpfilter += " and (Mat_CarryoverItem.OpenQty!=0 and Mat_CarryoverItem.InQty!=0 and Mat_CarryoverItem.OutQty!=0 and Mat_CarryoverItem.CloseQty!=0)";
            qpfilter += " AND NOT (\n" +
                    "    Bus_InvoCarryover.OpenQty = 0\n" +
                    "    AND Bus_InvoCarryover.InQty = 0\n" +
                    "    AND Bus_InvoCarryover.OutQty = 0\n" +
                    "    AND Bus_InvoCarryover.CloseQty = 0\n" +
                    ")";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvocarryoverService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
