package inks.service.std.sale.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 单据编码自动生成注解
 * 用于标记需要自动生成单据编码的方法
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RefNoGeneration {
    
    /**
     * 单据类型，如 "Bus_Receipt", "Bus_Deliery" 等
     */
    String billType();
    
    /**
     * 编码字段名，默认为 "refno"
     */
    String field() default "refno";
    
    /**
     * 自定义前缀，可选
     */
    String customPrefix() default "";
    
    /**
     * 是否保存到Redis缓存，默认为true
     */
    boolean saveToRedis() default true;
    
    /**
     * 参数索引，指定业务对象在方法参数中的位置，默认为0
     * 用于从方法参数中获取需要设置编码的业务对象
     */
    int paramIndex() default 0;
    
    /**
     * 参数类型，用于参数解析
     * 支持：JSON_STRING（JSON字符串）、POJO（直接对象）
     */
    ParamType paramType() default ParamType.JSON_STRING;
    
    /**
     * 参数类型枚举
     */
    enum ParamType {
        JSON_STRING,  // JSON字符串参数
        POJO         // 直接的POJO对象参数
    }
}
