package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.InksConfig;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.constant.MyConstant;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import inks.service.std.sale.domain.pojo.BusAccountrecPojo;
import inks.service.std.sale.service.AppWorkgroupService;
import inks.service.std.sale.service.BusAccountService;
import inks.service.std.sale.service.BusAccountrecService;
import inks.service.std.sale.utils.SqlUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 客户信息(App_Wg_Customer)表控制层
 * Customer 客户 Supplier 供应商 Branch 其他部门  Workshop 生产车间  Factory 外协厂商 Prospects潜在客户
 *
 * <AUTHOR>
 * @since 2021-11-11 09:10:03
 */
@RestController
@RequestMapping("D01M01B1")
@Api(tags = "D01M01B1:往来单位:客户")
public class D01M01B1Controller extends AppWorkgroupController {
    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 服务对象
     */
    @Resource
    private BusAccountService busAccountService;

    /**
     * 服务对象
     */
    @Resource
    private BusAccountrecService busAccountrecService;
    @Resource
    private RedisService redisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取往来单位详细信息", notes = "获取往来单位详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    public R<AppWorkgroupPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    @InksConfig
    public R<PageInfo<AppWorkgroupPojo>> getPageList(@RequestBody String json, @RequestParam(required = false) Integer dept) {//dept=1才开启过滤部门，默认不过滤
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and GroupType='客户'";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            // 部门过滤
            if (Objects.equals(dept, 1)) {
                qpfilter = SqlUtil.filterDeptid(qpfilter, loginUser, "App_Workgroup.Deptid");
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.appWorkgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "修改往来单位的部门id", notes = "修改往来单位", produces = "application/json")
    @RequestMapping(value = "/changeDeptid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Customer.Edit")
    public R changeDeptid(String key, String deptid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.changeDeptid(key, deptid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    @InksConfig
    public R<PageInfo<AppWorkgroupPojo>> getOnlinePageList(@RequestBody String json, @RequestParam(required = false) Integer dept) {//dept=1才开启过滤部门，默认不过滤
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and GroupType='客户' and App_Workgroup.deletemark=0 and App_Workgroup.enabledmark=1 ";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            // 部门过滤
            if (Objects.equals(dept, 1)) {
                qpfilter = SqlUtil.filterDeptid(qpfilter, loginUser, "App_Workgroup.Deptid");
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.appWorkgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增客户信息", notes = "新增客户信息", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Customer.Add")
    public R<AppWorkgroupPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            AppWorkgroupPojo appWorkgroupPojo = JSONArray.parseObject(json, AppWorkgroupPojo.class);


            appWorkgroupPojo.setCreateby(loginUser.getRealname());   // 创建者
            appWorkgroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            appWorkgroupPojo.setCreatedate(new Date());   // 创建时间
            appWorkgroupPojo.setLister(loginUser.getRealname());   // 制表
            appWorkgroupPojo.setListerid(loginUser.getUserid());    // 制表id
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            appWorkgroupPojo.setGrouptype("客户");
            appWorkgroupPojo.setId(""); // 去掉null
            //重名检查
            AppWorkgroupPojo appWorkgroupPojo1 = this.appWorkgroupService.getEntityByUid(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "编码重复");
            }
            appWorkgroupPojo1 = this.appWorkgroupService.getEntityByName(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "名称重复");
            }
            // 加入部门id
            Optional.ofNullable(loginUser.getTenantinfo())
                    .map(TenantInfo::getDeptid)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(appWorkgroupPojo::setDeptid);
            return R.ok(this.appWorkgroupService.insert(appWorkgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改往来单位", notes = "修改往来单位", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Customer.Edit")
    public R<AppWorkgroupPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            AppWorkgroupPojo appWorkgroupPojo = JSONArray.parseObject(json, AppWorkgroupPojo.class);

            appWorkgroupPojo.setLister(loginUser.getRealname());   // 制表
            appWorkgroupPojo.setListerid(loginUser.getUserid());    // 制表id
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            appWorkgroupPojo.setModifydate(new Date());   //修改时间

            //重名检查
            AppWorkgroupPojo appWorkgroupPojo1 = this.appWorkgroupService.getEntityByUid(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "编码重复");
            }
            appWorkgroupPojo1 = this.appWorkgroupService.getEntityByName(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "名称重复");
            }

            return R.ok(this.appWorkgroupService.update(appWorkgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Customer.Delete")
    @OperLog(title = "删除客户信息")
    public R<AppWorkgroupPojo> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            //检查引用
            List<String> lstcite = this.appWorkgroupService.getCiteBillName(key, tid);
            if (!lstcite.isEmpty()) {
                return R.fail(500, "禁止删除,被以下单据引用:" + lstcite);
            }
            // 未删除前先查询
            AppWorkgroupPojo workgroupDB = appWorkgroupService.getEntity(key, tid);
            this.appWorkgroupService.delete(key, tid);
            return R.ok(workgroupDB, workgroupDB.getGroupname() + "删除成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取客户详细信息", notes = "取客户详细信息", produces = "application/json")
    @RequestMapping(value = "/getGeneral", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    public R<AppWorkgroupPojo> getGeneral(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            AppWorkgroupPojo appWorkgroupPojo = this.appWorkgroupService.getCustomerGeneral(key, loginUser.getTenantid());
            return R.ok(appWorkgroupPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "传入业务员名字,获取Sum(客户详细信息,仅返回数据类信息)", notes = "取客户详细信息", produces = "application/json")
    @RequestMapping(value = "/getGeneralBySalesman", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    public R<AppWorkgroupPojo> getGeneralBySalesman(String seller) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            AppWorkgroupPojo appWorkgroupPojo = this.appWorkgroupService.getSumCustomerGeneralBySalesman(seller, loginUser.getTenantid());
            return R.ok(appWorkgroupPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     * SearchPojo.online=1时,查询 TotalAmount!=0的数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取客户应收款列表By销售单(移D01M12)", notes = "取客户应收款列表By销售单", produces = "application/json")
    @RequestMapping(value = "/getPageListBySale", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    public R<PageInfo<AppWorkgroupPojo>> getPageListBySale(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("GroupUid");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            Date startDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy", new Date()) + "-01-01");
            Date endDate = new Date();
            if (busAccountrecPojo != null) {
                startDate = DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1);
            }
            DateRange dateRange = new DateRange("", startDate, endDate);
            queryParam.setDateRange(dateRange);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.appWorkgroupService.getPageListBySale(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取客户应收款列表", notes = "取客户应收款列表", produces = "application/json")
    @RequestMapping(value = "/getPageListByRece", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    public R<PageInfo<AppWorkgroupPojo>> getPageListByRece(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.appWorkgroupService.getPageListByRece(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 刷新销售订单结余额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新销售订单结余额", notes = "刷新销售订单结余额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBusMachRemAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBusMachRemAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBusMachRemAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新销售发货结余额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新销售发货结余额", notes = "刷新销售订单结余额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBusDeliRemAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBusDeliRemAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBusDeliRemAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新销售发票结余额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新销售发票结余额", notes = "刷新销售发票结余额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBusInvoRemAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBusInvoRemAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBusInvoRemAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新销售结转期末额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新销售结转期末额", notes = "刷新销售结转期末额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBusAccoCloseAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBusAccoCloseAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBusAccoCloseAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新销售结转本期额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新销售结转本期额", notes = "刷新销售结转本期额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBusAccoNowAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBusAccoNowAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBusAccoNowAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新采购订单结余额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新采购订单结余额", notes = "刷新采购订单结余额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBuyOrderRemAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBuyOrderRemAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBuyOrderRemAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新采购收货结余额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新采购收货结余额", notes = "刷新采购收货结余额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBuyFiniRemAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBuyFiniRemAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBuyFiniRemAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新采购发票结余额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新采购发票结余额", notes = "刷新采购发票结余额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBuyInvoRemAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBuyInvoRemAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBuyInvoRemAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新采购结转期末额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新采购结转期末额", notes = "刷新采购结转期末额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBuyAccoCloseAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBuyAccoCloseAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBuyAccoCloseAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新采购结转本期额
     * key为客户id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新采购结转本期额", notes = "刷新采购结转本期额", produces = "application/json")
    @RequestMapping(value = "/updateWorkgroupBuyAccoNowAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Edit")
    public R<Integer> updateWorkgroupBuyAccoNowAmt(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.updateWorkgroupBuyAccoNowAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //SearchPojo.online=1时,查询 TotalAmount!=0的数据
    @ApiOperation(value = "Start获取客户应收款列表By销售单(移D01M12)", notes = "SearchPojo.online=1时,查询 TotalAmount!=0的数据", produces = "application/json")
    @RequestMapping(value = "/getPageListBySaleStart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    public R<String> getPageListBySaleStart(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("GroupUid");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            // uuid作为Redis的key
            String uuid = UUID.randomUUID().toString();
            // 开始异步
            this.appWorkgroupService.getPageListBySaleStart(uuid, loginUser, queryParam);
            return R.ok(uuid);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "State获取客户应收款列表By销售单(移D01M12)", notes = "取客户应收款列表By销售单", produces = "application/json")
    @RequestMapping(value = "/getPageListBySaleState", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Customer.List")
    public R<Map<String, Object>> getPageListBySaleState(@RequestParam String key) {
        Map<String, Object> state = this.appWorkgroupService.getPageListBySaleState(key);
        return R.ok(state);
    }

    @ApiOperation(value = "Result获取客户应收款列表By销售单(移D01M12)", notes = "", produces = "application/json")
    @RequestMapping(value = "/getPageListBySaleResult", method = RequestMethod.GET)
    public R<Map<String, Object>> getPrintBatchBillResult(@RequestParam String key) {
        try {
            String cachekey = MyConstant.RECEIPT_PAGES + key;
            Object cacheObject = this.redisService.getCacheObject(cachekey);
            Map map = JSON.parseObject(cacheObject.toString(), Map.class);
            return R.ok(map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}

