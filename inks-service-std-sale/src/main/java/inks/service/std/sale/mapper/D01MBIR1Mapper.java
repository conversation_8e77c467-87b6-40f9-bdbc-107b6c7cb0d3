package inks.service.std.sale.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface D01MBIR1Mapper {
    /*
     *
     * <AUTHOR>
     * @description 货品金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam);
    List<ChartPojo> getSumAmtByGroupMaxMach(QueryParam queryParam);
    List<ChartPojo> getSumAmtByGroupMaxFirstUid(QueryParam queryParam);

    List<ChartPojo> getSumAmtByGroupMaxMachFirstUid(QueryParam queryParam);

    List<ChartPojo> getSumAmtByGroupMaxFirstUid6Month(@Param("queryParam") QueryParam queryParam, @Param("firstuid") String firstuid);

    List<ChartPojo> getSumAmtByGroupMaxMachFirstUid6Month(@Param("queryParam") QueryParam queryParam, @Param("firstuid") String firstuid);

    List<ChartPojo> getSumAmtBySalesmanMach12Month(@Param("queryParam") QueryParam queryParam, @Param("recentMonths") int recentMonths);

    List<ChartPojo> getSumAmtBySalesman12Month(@Param("queryParam") QueryParam queryParam, @Param("recentMonths") int recentMonths);

    List<ChartPojo> getSumAmtByGoodsMaxOnlyMach(QueryParam queryParam);

    List<ChartPojo> getSumAmtByDepositAndReceipt(@Param("queryParam")QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam);



    /**
     * @author: nanno
     * @description: 销售趋势图年度
     * @createTime: 2023/3/9 9:43
     * @params  * @Param: null
     * @return
     */
    List<ChartPojo> getSumAmtByMonthMach(QueryParam queryParam);



    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    ChartPojo getTagSumAmtQtyByDate(QueryParam queryParam);

    ChartPojo getTagSumAmtQtyByDateMach(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    ChartPojo getTagSumAmtQtyByMonth(QueryParam queryParam);



    /*
     *
     * <AUTHOR>
     * @description 根据当前月查询本月开票
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getTagSumAmtByMonth(String tid);


    /*
     *
     * <AUTHOR>
     * @description 客户订单金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 订单逾期
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    ChartPojo getPageList(String tid);
    /*
     *
     * <AUTHOR>
     * @description 热销产品
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumByGoodsMax(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 业务员订单金额占比
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtBySalesman(QueryParam queryParam);

    List<ChartPojo> getSumAmtBySalesmanMach(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 查询在线订单数
     * @date 2021/12/29
     * @param * @param null
     * @return
     */
    Integer getCountMachItemOnline(String tid);

    ChartPojo getSumAmtByGoodsMach(@Param("goodsid")String goodsid, @Param("tid")String tid);

    ChartPojo getMachFinishRate(QueryParam queryParam);

    ChartPojo getDeliFinishRate(QueryParam queryParam);

    ChartPojo getReceiptFinishRate(QueryParam queryParam);

    HashMap<String,String> getAuthByCode(String auth);

    List<ChartPojo> getGroupDistribution(String tenantid);


    List<ChartPojo> getSpuWeightGroupByGroup(QueryParam queryParam);

    List<ChartPojo> getSpuWeightGroupByCaiZhi(QueryParam queryParam);

    List<ChartPojo> getSpuWeightGroupByGongYi(QueryParam queryParam);

    List<Map<String,Object>> getSpuWeightGroupByGuiGe(QueryParam queryParam);

    List<ChartPojo> getSumMachAmtGroupByGoodsUid(QueryParam queryParam);


    List<String> getAllSalesmenNameMach(String tenantid);
    List<String> getAllSalesmenNameDeli(String tenantid);

    int getCountMachOnline(String tid);

    int getCountBuyOnline(String tid);

    int getCountSubOnline(String tid);

    int getCountMachInDay(@Param("day") Integer day, @Param("tid") String tid);

    int getCountSubInDay(@Param("day") Integer day, @Param("tid") String tid);

    int getCountMachOnlinePlanDateToday(String tid);

    int getCountMachOnlinePlanDateWeek(String tid);

    Map<String, Object> getCountMachFinishWeek(String tid);

    int getCountNewMachWeek(String tid);

    int getCountAllMachWeek(String tid);

//    int getCountMachFinishWeek(String tid);
}
