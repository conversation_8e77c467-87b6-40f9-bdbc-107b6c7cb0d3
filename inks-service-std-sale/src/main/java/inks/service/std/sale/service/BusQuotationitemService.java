package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusQuotationitemPojo;

import java.util.List;
/**
 * 报价项目(BusQuotationitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-06 08:21:26
 */
public interface BusQuotationitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusQuotationitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusQuotationitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusQuotationitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busQuotationitemPojo 实例对象
     * @return 实例对象
     */
    BusQuotationitemPojo insert(BusQuotationitemPojo busQuotationitemPojo);

    /**
     * 修改数据
     *
     * @param busQuotationitempojo 实例对象
     * @return 实例对象
     */
    BusQuotationitemPojo update(BusQuotationitemPojo busQuotationitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busQuotationitempojo 实例对象
     * @return 实例对象
     */
    BusQuotationitemPojo clearNull(BusQuotationitemPojo busQuotationitempojo);
}
