package inks.service.std.sale.controller;

import cn.afterturn.easypoi.entity.ImageEntity;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.GoodsFeignService;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.file.ImageUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.FieldFilter;
import inks.common.security.annotation.InksConfig;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.config.InksConfigThreadLocal;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import inks.service.std.sale.domain.pojo.BusMachiningPojo;
import inks.service.std.sale.domain.pojo.BusMachiningitemPojo;
import inks.service.std.sale.domain.pojo.BusMachiningitemdetailPojo;
import inks.service.std.sale.mapper.BusMachiningMapper;
import inks.service.std.sale.service.AppWorkgroupService;
import inks.service.std.sale.service.BusMachiningService;
import inks.service.std.sale.service.BusMachiningitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;
import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;
import static inks.service.std.sale.utils.SqlUtil.filterDeptid;

/**
 * 销售订单(Bus_Machining)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 13:56:18
 */
@RestController
@RequestMapping("D01M03B1")
@Api(tags = "D01M03B1:销售订单")
public class D01M03B1Controller extends BusMachiningController {

    /**
     * 服务对象
     */
    @Resource
    private BusMachiningService busMachiningService;

    /**
     * 服务对象Item
     */
    @Resource
    private BusMachiningitemService busMachiningitemService;
    @Resource
    private BusMachiningMapper busMachiningMapper;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;


    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;

    /**
     * 引用FeignService服务
     */
    @Resource
    private UtilsFeignService utilsFeignService;
    @Resource
    private GoodsFeignService goodsFeignService;


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查结余明细含未审核", notes = "按条件分页查询结余明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    //@InksConfig({"system.bill.amountfilter"})
    @InksConfig
    @FieldFilter(permission = "Bus_Machining.Amount")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            qpfilter = filterDeptid(qpfilter, loginUser, "Bus_Machining.Deptid ");
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "销售订单明细Bom:导出带图模板数据", notes = "导出带单据模板数据", produces = "application/json")
    @RequestMapping(value = "/exportDetailBillAndBom", method = RequestMethod.GET)
    @InksConfig
    public void exportDetailBillAndBom(String key, HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "") String tempUrl) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 获取主表
            QueryParam queryParam = new QueryParam();
            queryParam.setOrderBy("Bus_Machining.CreateDate");
            String qpfilter = "  and Bus_MachiningItem.id='" + key + "'";
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            PageInfo<BusMachiningitemdetailPojo> machiningitemdetailPojoPageInfo = this.busMachiningService.getPageList(queryParam);
            BusMachiningitemdetailPojo machitem = machiningitemdetailPojoPageInfo.getList().get(0);
            Map<String, Object> map = BeanUtils.beanToMap(machitem);

            // 处理主表图片
            String goodsphoto1 = machitem.getGoodsphoto1();
            map.put("image1", "");
            if (StringUtils.isNotBlank(goodsphoto1)) {
                byte[] bytes1 = ImageUtils.readFile(goodsphoto1);
                map.put("image1", new ImageEntity(bytes1, 100, 100));
            }
            String goodsphoto2 = machitem.getGoodsphoto2();
            map.put("image2", "");
            if (StringUtils.isNotBlank(goodsphoto2)) {
                byte[] bytes2 = ImageUtils.readFile(goodsphoto2);
                map.put("image2", new ImageEntity(bytes2, 100, 100));
            }

            // 获取BOM明细，无需图片
            List<MatBomdetailPojo> bomDetail = goodsFeignService.getBomDetailByGoodsid(
                    machitem.getGoodsid(),
                    machitem.getQuantity(),
                    loginUser.getToken()
            ).getData();
            List<Map<String, Object>> lst = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(bomDetail)) {
                // 去掉第一行自己
                bomDetail.remove(0);
                lst = BeanUtils.attrListToMaps(bomDetail);
            }
            // 明细数据，模板用哪个变量名就put哪个
            map.put("list", lst);
            //map.put("list", lst);

            // 模板导出参数
            if (StringUtils.isBlank(tempUrl)) {
                tempUrl = InksConfigThreadLocal.getConfig("module.sale.exportdetailbillandbomtemp");
            }
            //String tempUrl = "D:\\nanno\\模板\\ahhbwork.xls";
            //String tempUrl = "D:\\nanno\\模板\\co1m01b1edit.xls";
            // 下面路径和TemplateExportParams任选其一（建议用URL，oss可直接取）
            // File file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "static/poitemp/co1m01b1edit.xls");
            System.out.println("模板路径tempUrl:" + tempUrl);
            TemplateExportParams exportParams = new TemplateExportParams(tempUrl);
            System.out.println("最终到处map:" + JSON.toJSONString(map));
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, map);
            POIUtil.downloadWorkbook(workbook, request, response, "明细打印" + machitem.getGoodsuid());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "测试导出多Sheet Excel", notes = "导出包含4个Sheet的测试Excel文件，数据完全模拟", produces = "application/json")
    @RequestMapping(value = "/exportTestMultiSheetExcel", method = RequestMethod.GET)
    @InksConfig
    public void exportTestMultiSheetExcel(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            // 创建Sheet1 - 销售订单数据
            createSalesOrderSheet(workbook);
            // 创建Sheet2 - 产品信息数据
            createProductInfoSheet(workbook);
            // 创建Sheet3 - 客户信息数据
            createCustomerInfoSheet(workbook);
            // 创建Sheet4 - 统计汇总数据
            createSummarySheet(workbook);
            // 下载Excel文件
            POIUtil.downloadWorkbook(workbook, request, response, "测试多Sheet数据_" + DateUtils.dateTimeNow());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建销售订单Sheet
     */
    private void createSalesOrderSheet(Workbook workbook) {
        Sheet sheet = workbook.createSheet("销售订单");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"订单编号", "客户名称", "产品名称", "数量", "单价", "金额", "订单日期", "状态"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            // 设置列宽
            sheet.setColumnWidth(i, 4000);
        }

        // 创建模拟数据
        String[][] salesData = {
            {"SO2024001", "北京科技有限公司", "智能手机", "100", "2999.00", "299900.00", "2024-01-15", "已确认"},
            {"SO2024002", "上海贸易公司", "笔记本电脑", "50", "5999.00", "299950.00", "2024-01-16", "待审核"},
            {"SO2024003", "广州电子厂", "平板电脑", "80", "1999.00", "159920.00", "2024-01-17", "已发货"},
            {"SO2024004", "深圳制造商", "智能手表", "200", "899.00", "179800.00", "2024-01-18", "已完成"},
            {"SO2024005", "杭州网络公司", "无线耳机", "300", "299.00", "89700.00", "2024-01-19", "生产中"},
            {"SO2024006", "南京软件公司", "移动电源", "150", "199.00", "29850.00", "2024-01-20", "已确认"},
            {"SO2024007", "成都科技园", "蓝牙音箱", "120", "399.00", "47880.00", "2024-01-21", "待审核"},
            {"SO2024008", "武汉电商", "数据线", "500", "29.00", "14500.00", "2024-01-22", "已发货"}
        };

        for (int i = 0; i < salesData.length; i++) {
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < salesData[i].length; j++) {
                Cell cell = row.createCell(j);
                cell.setCellValue(salesData[i][j]);
            }
        }
    }

    /**
     * 创建产品信息Sheet
     */
    private void createProductInfoSheet(Workbook workbook) {
        Sheet sheet = workbook.createSheet("产品信息");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"产品编码", "产品名称", "规格型号", "单位", "成本价", "销售价", "库存数量", "供应商", "产品类别"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            sheet.setColumnWidth(i, 3500);
        }

        // 创建模拟数据
        String[][] productData = {
            {"P001", "智能手机", "6.1寸 128GB", "台", "2200.00", "2999.00", "850", "华为技术", "电子产品"},
            {"P002", "笔记本电脑", "14寸 i5 8GB", "台", "4500.00", "5999.00", "320", "联想集团", "电脑设备"},
            {"P003", "平板电脑", "10寸 64GB", "台", "1400.00", "1999.00", "560", "苹果公司", "电子产品"},
            {"P004", "智能手表", "1.4寸触屏", "只", "650.00", "899.00", "1200", "小米科技", "智能穿戴"},
            {"P005", "无线耳机", "蓝牙5.0", "副", "180.00", "299.00", "2800", "OPPO", "音频设备"},
            {"P006", "移动电源", "10000mAh", "个", "120.00", "199.00", "1500", "品胜电子", "配件"},
            {"P007", "蓝牙音箱", "立体声", "个", "280.00", "399.00", "680", "JBL", "音频设备"},
            {"P008", "数据线", "Type-C 1米", "根", "15.00", "29.00", "5000", "绿联科技", "配件"},
            {"P009", "充电器", "65W快充", "个", "45.00", "89.00", "3200", "安克创新", "配件"},
            {"P010", "保护壳", "硅胶材质", "个", "8.00", "19.00", "8500", "ESR", "配件"}
        };

        for (int i = 0; i < productData.length; i++) {
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < productData[i].length; j++) {
                Cell cell = row.createCell(j);
                cell.setCellValue(productData[i][j]);
            }
        }
    }

    /**
     * 创建客户信息Sheet
     */
    private void createCustomerInfoSheet(Workbook workbook) {
        Sheet sheet = workbook.createSheet("客户信息");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"客户编码", "客户名称", "联系人", "电话", "地址", "信用等级", "累计采购额", "最后交易日期"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            sheet.setColumnWidth(i, 4500);
        }

        // 创建模拟数据
        String[][] customerData = {
            {"C001", "北京科技有限公司", "张经理", "010-88888888", "北京市海淀区中关村", "A级", "1250000.00", "2024-01-15"},
            {"C002", "上海贸易公司", "李总监", "021-66666666", "上海市浦东新区陆家嘴", "A级", "980000.00", "2024-01-16"},
            {"C003", "广州电子厂", "王主任", "020-77777777", "广州市天河区珠江新城", "B级", "750000.00", "2024-01-17"},
            {"C004", "深圳制造商", "陈厂长", "0755-55555555", "深圳市南山区科技园", "A级", "1680000.00", "2024-01-18"},
            {"C005", "杭州网络公司", "刘总", "0571-44444444", "杭州市西湖区文三路", "B级", "420000.00", "2024-01-19"},
            {"C006", "南京软件公司", "赵经理", "025-33333333", "南京市建邺区河西新城", "C级", "180000.00", "2024-01-20"},
            {"C007", "成都科技园", "孙主管", "028-22222222", "成都市高新区天府大道", "B级", "320000.00", "2024-01-21"},
            {"C008", "武汉电商", "周总", "027-11111111", "武汉市江汉区中央商务区", "A级", "890000.00", "2024-01-22"}
        };

        for (int i = 0; i < customerData.length; i++) {
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < customerData[i].length; j++) {
                Cell cell = row.createCell(j);
                cell.setCellValue(customerData[i][j]);
            }
        }
    }

    /**
     * 创建统计汇总Sheet
     */
    private void createSummarySheet(Workbook workbook) {
        Sheet sheet = workbook.createSheet("统计汇总");

        // 创建标题
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("销售数据统计汇总表");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));

        // 按产品类别统计
        Row categoryHeaderRow = sheet.createRow(2);
        String[] categoryHeaders = {"产品类别", "销售数量", "销售金额", "占比"};
        for (int i = 0; i < categoryHeaders.length; i++) {
            Cell cell = categoryHeaderRow.createCell(i);
            cell.setCellValue(categoryHeaders[i]);
            sheet.setColumnWidth(i, 4000);
        }

        String[][] categoryData = {
            {"电子产品", "230", "759870.00", "45.2%"},
            {"电脑设备", "50", "299950.00", "17.8%"},
            {"智能穿戴", "200", "179800.00", "10.7%"},
            {"音频设备", "420", "137580.00", "8.2%"},
            {"配件", "650", "73850.00", "4.4%"}
        };

        for (int i = 0; i < categoryData.length; i++) {
            Row row = sheet.createRow(i + 3);
            for (int j = 0; j < categoryData[i].length; j++) {
                Cell cell = row.createCell(j);
                cell.setCellValue(categoryData[i][j]);
            }
        }

        // 按月份统计
        Row monthHeaderRow = sheet.createRow(10);
        String[] monthHeaders = {"月份", "订单数量", "销售金额", "环比增长"};
        for (int i = 0; i < monthHeaders.length; i++) {
            Cell cell = monthHeaderRow.createCell(i);
            cell.setCellValue(monthHeaders[i]);
        }

        String[][] monthData = {
            {"2023-10", "45", "680000.00", "+12.5%"},
            {"2023-11", "52", "750000.00", "+10.3%"},
            {"2023-12", "48", "720000.00", "-4.0%"},
            {"2024-01", "58", "890000.00", "+23.6%"}
        };

        for (int i = 0; i < monthData.length; i++) {
            Row row = sheet.createRow(i + 11);
            for (int j = 0; j < monthData[i].length; j++) {
                Cell cell = row.createCell(j);
                cell.setCellValue(monthData[i][j]);
            }
        }
    }

    @ApiOperation(value = "按条件分页查完成的明细含未审核,即取反/getOnlinePageList", notes = "按条件分页查询非结余明细", produces = "application/json")
    @RequestMapping(value = "/getFinishPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getFinishPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and (Bus_MachiningItem.FinishQty>=Bus_MachiningItem.Quantity"; // 完成条件3者其一 注意括号包裹
            qpfilter += " or Bus_MachiningItem.DisannulMark=1 or Bus_MachiningItem.Closed=1) ";  // 关闭、注销的item也算完成
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询生产完成 (生产入库>=数量-库存发货)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInAccessPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getInAccessPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            String qpfilter = " and Bus_MachiningItem.InQuantity>=(Bus_MachiningItem.Quantity-Bus_MachiningItem.StoQty)";
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询生产完成 (生产入库>=数量-库存发货)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineInAccessPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineInAccessPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            String qpfilter = " and Bus_MachiningItem.InQuantity>=(Bus_MachiningItem.Quantity-Bus_MachiningItem.StoQty)";
            qpfilter += " and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待发货Th已审核", notes = "按条件分页查询结余单据含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDeliPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningPojo>> getOnlineDeliPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "and Bus_Machining.FinishCount+Bus_Machining.DisannulCount<Bus_Machining.ItemCount";
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待发货明细", notes = "按条件分页查询待发货明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDeliPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineDeliPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询待发货明细", notes = "按条件分页查询待发货明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDeliPageListToday", method = RequestMethod.POST)
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineDeliPageListToday(@RequestBody(required = false) String json, String groupid) {
        try {
            QueryParam queryParam;
            if (json == null) {
                queryParam = new QueryParam();
            } else {
                queryParam = JSONArray.parseObject(json, QueryParam.class);
            }
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            qpfilter += " and DATE(Bus_Machining.BillPlanDate)=CURDATE()";  // 当天
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "【包括前3天、今天、明天和后天(传入day则查当前时间加上day天数之前的)】按条件分页查询销售订单的待发货明细", notes = "按条件分页查询待发货明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDeliPage3Day", method = RequestMethod.POST)
    public R<PageInfo<Map<String, Object>>> getOnlineDeliPage3Day(@RequestBody(required = false) String json, String groupid, @RequestParam(required = false) Integer day) {
        try {
            QueryParam queryParam;
            if (json == null) {
                queryParam = new QueryParam();
            } else {
                queryParam = JSONArray.parseObject(json, QueryParam.class);
            }
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核

            if (day != null) {
                // 当前时间 至 当前时间加上day天之间
                qpfilter += " and DATE(Bus_Machining.BillPlanDate) between CURDATE() and DATE_ADD(CURDATE(), INTERVAL " + day + " DAY)";
            } else {
                //qpfilter += " and DATE(Bus_Machining.BillPlanDate) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 2 DAY)";// 包括今天、明天和后天
                // 默认 包括前3天、今天、明天和后天
                qpfilter += " and DATE(Bus_Machining.BillPlanDate) BETWEEN DATE_ADD(CURDATE(), INTERVAL -3 DAY) AND DATE_ADD(CURDATE(), INTERVAL 2 DAY)";
            }

            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            PageInfo<BusMachiningitemdetailPojo> onlinePageListPageInfo = this.busMachiningService.getPageList(queryParam);
            // ============对象转Map,并拆解spu,塞回PageInfo============
            List<BusMachiningitemdetailPojo> list = onlinePageListPageInfo.getList();
            // 单据Item. 带属性List转为Map
            List<Map<String, Object>> lst = attrListToMaps(list);
            // 创建新的 PageInfo 对象
            PageInfo<Map<String, Object>> newPageInfo = new PageInfo<>();
            // 复制原 PageInfo 的属性到新的 PageInfo 对象
            BeanUtils.copyProperties(onlinePageListPageInfo, newPageInfo);
            // 设置转换后的列表为新 PageInfo 的内容
            newPageInfo.setList(lst);
            return R.ok(newPageInfo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询(待发货且工序明细", notes = "按条件分页查询待发货明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDeliAndWkFinishPageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineDeliAndWkFinishPageTh(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            qpfilter += " and Bus_Machining.WkFinishCount>0";  // 工序为入库或完成
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待转主计划明细", notes = "按条件分页查询待主计划明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineMpPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineMpPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.MainPlanQty<Bus_MachiningItem.WkQty";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 and Bus_MachiningItem.MainPlanClosed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询待转主计划明细", notes = "按条件分页查询待主计划明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineMpAndMiPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineMpAndMiPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.MainPlanQty<Bus_MachiningItem.WkQty";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 and Bus_MachiningItem.MainPlanClosed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }

            // 加入事务导入资料类型，空和all为不过滤，EditionInfo  new=新单，old=照旧
            String configValue = systemFeignService.getConfigValue("module.goods.engjobinput", loginUser.getTenantid(), loginUser.getToken()).getData();
            if (StringUtils.isBlank(configValue)) {
                qpfilter += " and Bus_MachiningItem.EngStateText = '完成' ";
            }
            if ("new".equals(configValue)) {
                qpfilter += " and ((Bus_MachiningItem.EditionInfo='新单' and Bus_MachiningItem.EngStateText = '完成' ) or (Bus_MachiningItem.EditionInfo='照旧'))";
            } else if ("old".equals(configValue)) {
                qpfilter += " and ((Bus_MachiningItem.EditionInfo='照旧' and Bus_MachiningItem.EngStateText = '完成' ) or (Bus_MachiningItem.EditionInfo='新单'))";
            }

            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询可退货明细", notes = "按条件分页查询可退货明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getRetrunDeliPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getRetrunDeliPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.FinishQty>0";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待生产(采购)明细", notes = "按条件分页查询待生产(采购)明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineWkPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineWkPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.BuyQuantity+Bus_MachiningItem.WkQuantity<Bus_MachiningItem.WkQty";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待转WIP明细", notes = "按条件分页查询待转WIP明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineWipPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineWipPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.WipUsed=0";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待转WIP明细", notes = "按条件分页查询待转WIP明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineMatPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineMatPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.MatUsed=0 and Bus_MachiningItem.MatCode!=''";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待转Mrp明细", notes = "按条件分页查询待转Mrp明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineMrpPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineMrpPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.Mrpid =''";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待拣货明细", notes = "按条件分页查询待拣货明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePickPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlinePickPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_MachiningItem.PickQty<Bus_MachiningItem.Quantity";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查结余明细含未审核", notes = "按条件分页查询结余明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getSpecOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getSpecOnlinePageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and (Bus_MachiningItem.Specid='' or Bus_MachiningItem.Specid is null)";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "销售订单加入getOnlineProjectPageList接口，用于过滤转了事务管理的接口", notes = "按条件分页查询结余明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineProjectPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getOnlineProjectPageList(@RequestBody String json, String groupid) {
        try {
            // logger.info("查询列表");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            queryParam.setTenantid(tid);
            String qpfilter = "  and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
            qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_MachiningItem.id not in (select distinct CiteItemid from Mat_CamProject where  Tenantid = '" + tid + "')";  // 未转事务管理
            qpfilter += " and Bus_MachiningItem.EngStateText not in ('完成','处理')";
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }

            // 加入事务导入资料类型，空和all为不过滤，EditionInfo  new=新单，old=照旧
            String configValue = systemFeignService.getConfigValue("module.goods.engjobinput", loginUser.getTenantid(), loginUser.getToken()).getData();
            if ("new".equals(configValue)) {
                qpfilter += " and Bus_MachiningItem.EditionInfo='新单'";
            } else if ("old".equals(configValue)) {
                qpfilter += " and Bus_MachiningItem.EditionInfo='照旧'";
            }

            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询结余单据含未审核", notes = "按条件分页查询结余单据含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    @InksConfig
    public R<PageInfo<BusMachiningPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "and Bus_Machining.FinishCount+Bus_Machining.DisannulCount<Bus_Machining.ItemCount";
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            qpfilter = filterDeptid(qpfilter, loginUser, "Bus_Machining.Deptid ");
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询可预收款单据 (Bus_Machining.BillTaxAmount>Bus_Machining.AdvaAmount)", notes = "按条件分页查询可预收款单据 ?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineAdvaPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningPojo>> getOnlineAdvaPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Machining.BillTaxAmount>Bus_Machining.AdvaAmount";
            qpfilter += " and Bus_Machining.ItemCount>Bus_Machining.DisannulCount ";  // 未注销
            qpfilter += " and Bus_Machining.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busMachiningService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 检查ITEM引用单据
     *
     * @param key 主键
     * @return ITEM引用单据
     */
    @ApiOperation(value = "检查ITEM引用单据", notes = "检查ITEM引用单据", produces = "application/json")
    @RequestMapping(value = "/chkItemCite", method = RequestMethod.GET)
    public R<List<String>> chkItemCite(String key, String pid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (pid == null) {
                BusMachiningitemPojo busMachiningitemPojo = this.busMachiningitemService.getEntity(key, loginUser.getTenantid());
                pid = busMachiningitemPojo.getPid();
            }
            return R.ok(this.busMachiningService.getItemCiteBillName(key, pid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "检查上一单单据类型", notes = "", produces = "application/json")
    @RequestMapping(value = "/checkHistoryBillType", method = RequestMethod.POST)
    public R<List<Map<String, Object>>> checkHistoryBillType(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<String> goodsids = JSONArray.parseArray(json, String.class);
            return R.ok(this.busMachiningService.checkHistoryBillType(goodsids, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核销售订单", notes = "审核销售订单", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Approval")
    public R<BusMachiningPojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(key, loginUser.getTenantid());
            // 查询系统参数systemFeign
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            Map<String, String> tencfg;
            if (r.getCode() == 200) {
                tencfg = r.getData();
            } else {
                throw new BaseBusinessException("获得系统参数出错" + r.getMsg());
            }
            if (busMachiningPojo.getAssessor().equals("")) {
                AppWorkgroupPojo wgPojo = this.appWorkgroupService.getCustomerGeneral(busMachiningPojo.getGroupid(), loginUser.getTenantid());
                if (tencfg.get("module.sale.machchkcreditlimit") != null && tencfg.get("module.sale.machchkcreditlimit").equals("reject")) {
                    if (wgPojo != null && wgPojo.getTotalamount() > wgPojo.getCreditcquantity()) {
                        return R.fail(wgPojo.getAbbreviate() + " 累积金额" + wgPojo.getTotalamount() + "超过信用额度,审核失败");
                    }
                }
                if (tencfg.get("module.sale.machchkoverinvo") != null && tencfg.get("module.sale.machchkoverinvo").equals("reject")) {
                    if (wgPojo != null && wgPojo.getOverdueinvoice() > 0) {
                        return R.fail(wgPojo.getAbbreviate() + " " + wgPojo.getOverdueinvoice() + "张 逾期发票,审核失败");
                    }
                }
                busMachiningPojo.setAssessor(loginUser.getRealname()); //审核员
                busMachiningPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busMachiningPojo.setAssessor(""); //审核员
                busMachiningPojo.setAssessorid(""); //审核员
            }

            busMachiningPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busMachiningService.approval(busMachiningPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除销售单优惠券(优惠券字段置空，关联优惠券使用删除)", notes = "", produces = "application/json")
    @RequestMapping(value = "/deleteCoupon", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    public R deleteCoupon(String key, String couponid) {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(busMachiningService.deleteCoupon(key, couponid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "发出钉钉审批", notes = "发出钉钉审批", produces = "application/json")
//    @RequestMapping(value = "/dingapprovel", method = RequestMethod.GET)
//    public R<ApprrecPojo> dingapprovel(String key, String apprid) {
//        try {
//
//            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
//            //获取token
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            //创建VM数据对象
//            VelocityContext context = new VelocityContext();
//            //从redis中获取模板对象
//            // Object obj = redisService.getCacheObject(verifyKey);
//            ApprrecPojo apprrecPojo = redisService.getCacheObject(verifyKey);
//            ApprovePojo approvePojo = new ApprovePojo();
//            //获得第三方账号
//            R rja = systemFeignService.getJustauthByUserid(loginUser.getUserid(), "ding", loginUser.getTenantid());
//            JustauthPojo justauthPojo = new JustauthPojo();
//            if (rja.getCode() == 200) {
//                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
//            } else {
//                return R.fail("获得第三方账号出错" + rja.getMsg().toString());
//            }
//            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
//            approvePojo.setUserid(justauthPojo.getAuthuuid());
//            approvePojo.setModelcode(apprrecPojo.getTemplateid());
//            approvePojo.setObject(this.busMachiningService.getBillEntity(key, loginUser.getTenantid()));
//            context.put("approvePojo", approvePojo);
//            String str = apprrecPojo.getDatatemp();
//            // 初始化并取得Velocity引擎
//            VelocityEngine ve = new VelocityEngine();
//            ve.init();
//            // 转换输出
//            StringWriter writer = new StringWriter();
//            ve.evaluate(context, writer, "", str); // 关键方法
//            //写回String
//            str = writer.toString();
//            //新建审批记录
//            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
//            apprrecPojo.setDatatemp(str);
//            apprrecPojo.setApprname("订单审批");
//            apprrecPojo.setResultcode("");
//            apprrecPojo.setBillid(key);    // 单据ID
//            apprrecPojo.setUserid("");
//            apprrecPojo.setApprtype("");
//            apprrecPojo.setCreateby(loginUser.getRealname());
//            apprrecPojo.setCreatebyid(loginUser.getUserid());
//            apprrecPojo.setCreatedate(new Date());
//            apprrecPojo.setLister(loginUser.getRealname());
//            apprrecPojo.setListerid(loginUser.getUserid());
//            apprrecPojo.setModifydate(new Date());
//            apprrecPojo.setTenantid(loginUser.getTenantid());
//            //将企业微信审批信息存入redis
//            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
//            redisService.setCacheObject(CachKey, apprrecPojo, (long) (60 * 12), TimeUnit.MINUTES);
//            R r = oaFeignService.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
//            if (r.getCode() != 200) {
//                return R.fail("发起审批失败" + r.toString());
//            }
//            return R.ok(apprrecPojo);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type, String remark) {
        try {
            if (type == null) type = "wxe";  // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
            //获取token
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //从redis中获取模板对象
            // Object obj = redisService.getCacheObject(verifyKey);
            ApprrecPojo apprrecPojo = redisService.getCacheObject(verifyKey);
            ApprovePojo approvePojo = new ApprovePojo();
            BusMachiningPojo machiningBillEntity = this.busMachiningService.getBillEntity(key, loginUser.getTenantid());
            approvePojo.setObject(machiningBillEntity);

            if ("oms".equals(type)) {
                // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
                if (machiningBillEntity.getOaflowmark() != null && machiningBillEntity.getOaflowmark() == 1) {
                    return R.fail("该单据已发起OA审批");
                }
                //创建VM数据对象
                VelocityContext context = new VelocityContext();
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
//                String data = JSONObject.toJSONString(approvePojo.getObject());
//                apprrecPojo.setDatatemp(data);
            } else {
                //创建VM数据对象
                VelocityContext context = new VelocityContext();

                //获得第三方账号
                R rja = systemFeignService.getJustauthByUserid(loginUser.getUserid(), type, loginUser.getTenantid());
                JustauthPojo justauthPojo = new JustauthPojo();
                if (rja.getCode() == 200) {
                    org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
                } else {
                    return R.fail("获得第三方账号出错" + rja.getMsg());
                }
                approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
                approvePojo.setUserid(justauthPojo.getAuthuuid());
                approvePojo.setModelcode(apprrecPojo.getTemplateid());
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
            }


            //新建审批记录
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            apprrecPojo.setApprname("订单审批");
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key);    // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(new Date());
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(new Date());
            apprrecPojo.setTenantid(loginUser.getTenantid());
            //发起Flowable审批时加入的评论备注
            apprrecPojo.setRemark(remark == null ? "" : remark);
            //将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            redisService.setCacheObject(CachKey, apprrecPojo, (long) (60 * 12), TimeUnit.MINUTES);
            if ("wxe".equals(type)) {
                R r = this.utilsFeignService.wxeapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }
            } else if ("ding".equals(type)) {
                R r = this.utilsFeignService.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }
            } else {//type为oms
                R r = this.utilsFeignService.omsapprovel(apprrecPojo.getId(), loginUser.getTenantid(), loginUser.getToken());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }
                // 发起oms审批成功,需设置OaFlowMark=1 并更新单据
                machiningBillEntity.setOaflowmark(1);
                busMachiningService.update(machiningBillEntity);
            }
            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R<BusMachiningPojo> justapprovel(String key, String type, String approved) {
        try {
            System.out.println("审核通过,写入审核信息");
            //1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = redisService.getCacheObject(verifyKey);
            //2.1 获得单据数据
            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(apprrecPojo.getBillid(), apprrecPojo.getTenantid());
            //3. 写入审核批
            //获得第三方账号
            if (type == null) type = "wxe";
            if ("oms".equals(type)) {
                // oms审批即将完成,需设置OaFlowMark=0
                busMachiningPojo.setOaflowmark(0);
                busMachiningMapper.updateOaflowmark(busMachiningPojo);//只更新oaflowmark
                // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
                if ("false".equals(approved)) {
                    return R.ok();
                }
                // 点击同意审批：审批人字段赋值, if包裹外面的approval方法会进行审核
                busMachiningPojo.setAssessorid(apprrecPojo.getUserid());
                busMachiningPojo.setAssessor(apprrecPojo.getRealname()); //审核员
            } else {
                R rja = systemFeignService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, apprrecPojo.getTenantid());
                JustauthPojo justauthPojo = new JustauthPojo();
                if (rja.getCode() == 200) {
                    org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
                } else {
                    System.out.println("写入审核:获得第三方账号出错：" + rja.getMsg());
                    return R.fail("获得第三方账号出错" + rja.getMsg());
                }
                busMachiningPojo.setAssessorid(justauthPojo.getUserid());
                busMachiningPojo.setAssessor(justauthPojo.getRealname()); //审核员
            }
            busMachiningPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busMachiningService.approval(busMachiningPojo));
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售订单在线明细报表", notes = "打印销售订单在线明细报表", produces = "application/json")
    @RequestMapping(value = "/printOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printOnlinePageList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {

        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_MachiningItem.ItemPlanDate");

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "  and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
        qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
        if (groupid != null) {
            qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusMachiningitemdetailPojo> lst = this.busMachiningService.getPageList(queryParam).getList();

        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusMachiningitemdetailPojo wkWipnoteitemPojo = new BusMachiningitemdetailPojo();
                    lst.add(wkWipnoteitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售订单PageList报表", notes = "打印销售订单PageList报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {

        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_MachiningItem.ItemPlanDate");

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusMachiningitemdetailPojo> lst = this.busMachiningService.getPageList(queryParam).getList();

        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }

        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusMachiningitemdetailPojo wkWipnoteitemPojo = new BusMachiningitemdetailPojo();
                    lst.add(wkWipnoteitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "打印销售订单PageTh报表", notes = "打印销售订单PageTh报表", produces = "application/json")
    @RequestMapping(value = "/printPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printPageTh(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Machining.CreateDate");

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusMachiningPojo> lst = this.busMachiningService.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusMachiningPojo pojo = new BusMachiningPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "(合并打印)销售订单明细", notes = "(合并打印)销售订单明细", produces = "application/json")
    @RequestMapping(value = "/printMergePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printMergePageList(@RequestBody String json, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        map.put("printer", loginUser.getRealname());
        map.put("printdate", org.apache.http.client.utils.DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //=========获取单据Item信息========
        List<BusMachiningitemdetailPojo> lst = JSONArray.parseArray(json, BusMachiningitemdetailPojo.class);
        //合并相同goodsid的货品明细项
        for (int i = 0; i < lst.size(); i++) {
            // 获取当前项的goodsid,attributejson(SPU),quantity
            String goodsid = lst.get(i).getGoodsid();
            String attributejson = lst.get(i).getAttributejson();
            double quantity = lst.get(i).getQuantity();
            // 在当前项之后的所有项中查找是否有相同goodsid的项
            for (int j = i + 1; j < lst.size(); j++) {
                if (lst.get(j).getGoodsid().equals(goodsid) && lst.get(j).getAttributejson().equals(attributejson)) {
                    // 如果存在相同goodsid,attributejson的项，则累加数量和价格到当前项中
                    quantity += lst.get(j).getQuantity();
                    // 删除相同项
                    lst.remove(j);
                    j--;
                }
            }
            // 更新当前项的数量和价格
            lst.get(i).setQuantity(quantity);
        }

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusMachiningitemdetailPojo wkWipnoteitemPojo = new BusMachiningitemdetailPojo();
                    lst.add(wkWipnoteitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "(合并云打印)订单明细", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printMergeWebPageList", method = RequestMethod.POST)
    public R<String> printMergeWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<BusMachiningitemdetailPojo> lstitem = JSONArray.parseArray(json, BusMachiningitemdetailPojo.class);
            //合并相同goodsid和attributejson的货品明细项
            for (int i = 0; i < lstitem.size(); i++) {
                // 获取当前项的goodsid,attributejson(SPU),quantity
                String goodsid = lstitem.get(i).getGoodsid();
                String attributejson = lstitem.get(i).getAttributejson();
                double quantity = lstitem.get(i).getQuantity();
                // 在当前项之后的所有项中查找是否有相同goodsid的项
                for (int j = i + 1; j < lstitem.size(); j++) {
                    if (lstitem.get(j).getGoodsid().equals(goodsid) && lstitem.get(j).getAttributejson().equals(attributejson)) {
                        // 如果存在相同goodsid,attributejson的项，则累加数量和价格到当前项中
                        quantity += lstitem.get(j).getQuantity();
                        // 删除相同项
                        lstitem.remove(j);
                        j--;
                    }
                }
                // 更新当前项的数量和价格
                lstitem.get(i).setQuantity(quantity);
            }
            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "合并打印订单明细");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改销售订单记录Item", notes = "修改销售订单记录Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    public R<BusMachiningitemPojo> updateItem(@RequestBody String json) {
        try {
            BusMachiningitemPojo busMachiningitemPojo = JSONArray.parseObject(json, BusMachiningitemPojo.class);
            if (!busMachiningitemPojo.getPid().isEmpty()) {

                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                busMachiningitemPojo.setTenantid(loginUser.getTenantid());   //租户id
                busMachiningitemPojo = this.busMachiningitemService.update(busMachiningitemPojo);

                BusMachiningPojo wipnotePojo = new BusMachiningPojo();
                wipnotePojo.setItem(null);
                wipnotePojo.setId(busMachiningitemPojo.getPid());
                wipnotePojo.setLister(loginUser.getRealname());   // 制表
                wipnotePojo.setListerid(loginUser.getUserid());    // 制表id
                wipnotePojo.setModifydate(new Date());   //修改时间
                wipnotePojo.setTenantid(loginUser.getTenantid());
                this.busMachiningService.update(wipnotePojo);
                return R.ok(busMachiningitemPojo);
            } else {
                return R.fail("缺少单据ID数据");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "分页云打印订单明细", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, Integer online) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            if (online != null && online == 1) {
                qpfilter = " and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
                qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusMachiningitemdetailPojo> lstitem = this.busMachiningService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && !lstitem.isEmpty()) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            for (Map<String, Object> map2 : lst) {
                if (map2.get("costgroupjson") != null && !map2.get("costgroupjson").toString().isEmpty()) {
                    List<Map<String, Object>> listObjectSec = JSONArray.parseObject(map2.get("costgroupjson").toString(), List.class);
                    for (Map<String, Object> mapList : listObjectSec) {
                        if (mapList.get("key") != null && mapList.get("value") != null)
                            map2.put(mapList.get("key").toString(), mapList.get("value").toString());
                    }
                }
            }

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "订单明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "批量云打印单据", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printBatchWebBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public R<String> printBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");

            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            for (String key : lstkeys) {
                //=========获取单据表头信息========
                BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(key, loginUser.getTenantid());
                if (busMachiningPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                if (printapproved != null && printapproved.equals("true") && busMachiningPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(busMachiningPojo);

                // 获取单据表头.加入公司信息
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                List<BusMachiningitemPojo> lstitem = this.busMachiningitemService.getList(key, loginUser.getTenantid());
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(lstitem);

                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
                ptRefNoMain += busMachiningPojo.getRefno() + ",";

                // 刷入打印Num++
                BusMachiningPojo billPrintPojo = new BusMachiningPojo();
                billPrintPojo.setId(busMachiningPojo.getId());
                billPrintPojo.setPrintcount(busMachiningPojo.getPrintcount() + 1);
                billPrintPojo.setTenantid(busMachiningPojo.getTenantid());
                this.busMachiningService.updatePrintcount(billPrintPojo);
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "销售订单：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "批量云打印单据", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printBatchBill(@RequestBody String json, String ptid) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            String content;
            if (reportsPojo != null) {
                content = reportsPojo.getRptdata();
            } else {
                throw new BaseBusinessException("未找到报表");
            }

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");

            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            //数据填充
            JasperPrint printAll = new JasperPrint();
            for (int a = 0; a < lstkeys.size(); a++) {
                String key = lstkeys.get(a);
                //=========获取单据表头信息========
                BusMachiningPojo busMachiningPojo = this.busMachiningService.getBillEntity(key, loginUser.getTenantid());
                if (busMachiningPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                if (printapproved != null && printapproved.equals("true") && busMachiningPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
                //表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(busMachiningPojo);
                // 加入公司信息
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                // 判定是否需要追行
                if (reportsPojo.getPagerow() > 0) {
                    int index = 0;
                    // 取行余数
                    index = busMachiningPojo.getItem().size() % reportsPojo.getPagerow();
                    if (index > 0) {
                        // 补全空白行
                        for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                            BusMachiningitemPojo busMachiningitemPojo = new BusMachiningitemPojo();
                            busMachiningPojo.getItem().add(busMachiningitemPojo);
                        }
                    }
                }

                // 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(busMachiningPojo.getItem());
                //item转数据源
                JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

                // 刷入打印Num++
//                BusMachiningPojo billPrintPojo = new BusMachiningPojo();
//                billPrintPojo.setId(busMachiningPojo.getId());
//                billPrintPojo.setPrintcount(busMachiningPojo.getPrintcount() + 1);
//                billPrintPojo.setTenantid(busMachiningPojo.getTenantid());
//                this.busMachiningService.update(billPrintPojo);

                //报表生成
                InputStream stream = new ByteArrayInputStream(content.getBytes());
                //编译报表
                JasperReport jasperReport = JasperCompileManager.compileReport(stream);

                //数据填充
                JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                if (a == 0) {
                    printAll = print;
                } else {
                    List<JRPrintPage> pages = print.getPages();
                    for (JRPrintPage page : pages) {
                        printAll.addPage(page);
                    }
                }
            }
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "查询ItemList", notes = "查询ItemList key=id ", produces = "application/json")
    @RequestMapping(value = "/getItemList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<BusMachiningitemPojo>> getItemList(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busMachiningitemService.getList(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "查询ItemEntity", notes = "查询ItemEntity key=id ", produces = "application/json")
    @RequestMapping(value = "/getItemEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<BusMachiningitemPojo> getItemEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busMachiningitemService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "查询ItemList", notes = "查询ItemList key=id ", produces = "application/json")
    @RequestMapping(value = "/getItemSpuList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<Map<String, Object>>> getItemSpuList(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<BusMachiningitemPojo> lst = this.busMachiningitemService.getList(key, loginUser.getTenantid());
            List<Map<String, Object>> lstMap = attrcostListToMaps(lst);
            return R.ok(lstMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "通过客户id查询销售订单总应收款，已收款，未发货金额", notes = "查询ItemList key=id ", produces = "application/json")
    @RequestMapping(value = "/getLastAmtByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<Map<String, Object>> getLastAmtByGroupId(String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busMachiningService.getLastAmtByGroupId(groupid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "传入PageList对象集合,自定义分页云打印订单明细", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printFreeWebPageList", method = RequestMethod.POST)
    public R<String> printFreeWebPageList(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());


            //获取传入的自定义 itemdetailPojo集合
            List<BusMachiningitemdetailPojo> lstitem = JSONArray.parseArray(json, BusMachiningitemdetailPojo.class);

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            for (Map<String, Object> map2 : lst) {
                if (map2.get("costgroupjson") != null && !map2.get("costgroupjson").toString().isEmpty()) {
                    List<Map<String, Object>> listObjectSec = JSONArray.parseObject(map2.get("costgroupjson").toString(), List.class);
                    for (Map<String, Object> mapList : listObjectSec) {
                        if (mapList.get("key") != null && mapList.get("value") != null)
                            map2.put(mapList.get("key").toString(), mapList.get("value").toString());
                    }
                }
            }

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "订单明细自定义勾选打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    // 用来一次性刷新租户下所有订单子表的AttributeStr
//    @RequestMapping(value = "/updateAttrStr", method = RequestMethod.GET)
//    public int updateAttrStr() {
////        String gongShi="${spuchang}*${spukuan}*${spuhou}*${spuwaijing}*${spunajing}*${spugaodu}";
//        String gongShi="${spuff}*${spufd}*${spufdn}*${spul}*${spuh}*${spub}";//立一
////        String tid="ceb5ae04-ea5f-4ba8-81af-0c8ec1ad112c";
//        String tid="b2db6f59-659a-48c6-95be-85a20f0032e2";//立一
////        String tid="b842c7ca-a02b-4dc6-af43-e4d3e84af592";
////        String tid="12138";
//        // id,AttributeJson
//        List<Map<String,String>> lst = this.busMachiningitemMapper.getAllByTid(tid);
//        int count=0;
//        for (Map<String, String> map : lst) {
//            String id = map.get("id");
//            String attributejson = map.get("AttributeJson");
//            System.out.println("id = " + id);
//            System.out.println("attributejson = " + attributejson);
//
//            if (StringUtils.isNotBlank(attributejson)) {
//                String attrStr = BeanUtils.calculateAttrStr(attributejson, gongShi);
//                int i = this.busMachiningitemMapper.upateAttrStr(id, attrStr, tid);
//                count+=i;
//            }
//        }
//        System.out.println("共刷新: " + count);
//        return count;
//    }
}
