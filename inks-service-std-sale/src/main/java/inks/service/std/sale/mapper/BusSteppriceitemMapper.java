package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusSteppriceitemEntity;
import inks.service.std.sale.domain.pojo.BusSteppriceitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶梯项目(BusSteppriceitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-31 21:22:06
 */
 @Mapper
public interface BusSteppriceitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusSteppriceitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusSteppriceitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusSteppriceitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busSteppriceitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusSteppriceitemEntity busSteppriceitemEntity);

    
    /**
     * 修改数据
     *
     * @param busSteppriceitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusSteppriceitemEntity busSteppriceitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

