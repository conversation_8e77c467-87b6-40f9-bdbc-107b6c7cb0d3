package inks.service.std.sale.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 单据编码清理注解
 * 用于标记需要清理Redis中单据编码缓存的方法
 * 通常用于删除操作
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RefNoCleanup {
    
    /**
     * 是否在方法执行成功后清理，默认为true
     * 如果为false，则在方法执行前清理
     */
    boolean afterSuccess() default true;
    
    /**
     * 是否忽略清理异常，默认为true
     * 如果为true，清理失败不会影响主业务逻辑
     */
    boolean ignoreException() default true;
}
