package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusDepositEntity;
import inks.service.std.sale.domain.pojo.BusDepositPojo;
import inks.service.std.sale.domain.pojo.BusDepositcashdetailPojo;
import inks.service.std.sale.domain.pojo.BusDeposititemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预收款(BusDeposit)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:31:25
 */
@Mapper
public interface BusDepositMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDepositPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDeposititemdetailPojo> getPageList(QueryParam queryParam);

    List<BusDepositcashdetailPojo> getCashPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDepositPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDepositEntity 实例对象
     * @return 影响行数
     */
    int insert(BusDepositEntity busDepositEntity);


    /**
     * 修改数据
     *
     * @param busDepositEntity 实例对象
     * @return 影响行数
     */
    int update(BusDepositEntity busDepositEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busDepositPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusDepositPojo busDepositPojo);

    /**
     * 查询 被删除的Cash
     *
     * @param busDepositPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelCashIds(BusDepositPojo busDepositPojo);

    /**
     * 修改数据
     *
     * @param busDepositEntity 实例对象
     * @return 影响行数
     */
    int approval(BusDepositEntity busDepositEntity);


    /**
     * 刷新出纳账户额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateCashAmount(@Param("key") String key, @Param("amount") Double amount, @Param("tid") String tid);

    /**
     * 修改完工记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMachAdvaAmountFirstAmt(@Param("key") String key, @Param("tid") String tid);

    int updateMachItemAvgFirstAmt(@Param("machid") String machbillid, @Param("tid") String tid);
}

