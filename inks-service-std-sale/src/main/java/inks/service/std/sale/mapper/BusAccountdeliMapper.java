package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusAccountdeliPojo;
import inks.service.std.sale.domain.BusAccountdeliEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 销售订单to发货单(BusAccountdeli)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-13 16:40:26
 */
 @Mapper
public interface BusAccountdeliMapper {

    BusAccountdeliPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusAccountdeliPojo> getPageList(QueryParam queryParam);

    List<BusAccountdeliPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(BusAccountdeliEntity busAccountdeliEntity);

    int update(BusAccountdeliEntity busAccountdeliEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

