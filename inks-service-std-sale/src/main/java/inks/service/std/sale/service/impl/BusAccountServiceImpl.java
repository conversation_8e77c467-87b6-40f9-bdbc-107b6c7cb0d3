package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.*;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.mapper.*;
import inks.service.std.sale.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售账单(BusAccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-03 14:28:09
 */
@Service("busAccountService")
public class BusAccountServiceImpl implements BusAccountService {
    @Resource
    private BusAccountrecMapper busAccountrecMapper;
    @Resource
    private BusInvocarryoverMapper busInvocarryoverMapper;
    @Resource
    private BusCarryoverinvoMapper busCarryoverinvoMapper;
    @Resource
    private BusCarryoveritemMapper busCarryoveritemMapper;
    @Resource
    private BusAccountdeliService busAccountdeliService;
    @Resource
    private BusAccountMapper busAccountMapper;

    @Resource
    private BusAccountitemMapper busAccountitemMapper;
    @Resource
    private BusAccountitemService busAccountitemService;
    @Resource
    private BusAccountinvoMapper busAccountinvoMapper;
    @Resource
    private BusAccountinvoService busAccountinvoService;
    @Resource
    private BusAccountarapMapper busAccountarapMapper;
    @Resource
    private BusAccountdeliMapper busAccountdeliMapper;
    @Resource
    private BusAccountarapService busAccountarapService;
    @Resource
    private BusAccountrecService busAccountrecService;
    @Resource
    private RedisService redisService;

    @Resource
    private TokenService tokenService;

    private static final Logger log = LoggerFactory.getLogger(BusAccountServiceImpl.class);
    @Autowired
    private BusInvocarryoverServiceImpl busInvocarryoverService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountPojo getEntity(String key, String tid) {
        return this.busAccountMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountitemdetailPojo> lst = busAccountMapper.getPageList(queryParam);
            PageInfo<BusAccountitemdetailPojo> pageInfo = new PageInfo<BusAccountitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountPojo getBillEntity(String key, String tid) {
        try {
            // 读取主表
            BusAccountPojo busAccountPojo = this.busAccountMapper.getEntity(key, tid);
            // 读取子表
            busAccountPojo.setItem(busAccountitemMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
            // 读取发货to发票
            busAccountPojo.setInvo(busAccountinvoMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
            // 读取应收应付
            busAccountPojo.setArap(busAccountarapMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
            // 读取销售订单to发货
            busAccountPojo.setDeli(busAccountdeliMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));

            return busAccountPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountPojo> lst = busAccountMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busAccountitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setInvo(busAccountinvoMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setArap(busAccountarapMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setDeli(busAccountdeliMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusAccountPojo> pageInfo = new PageInfo<BusAccountPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountPojo> lst = busAccountMapper.getPageTh(queryParam);
            PageInfo<BusAccountPojo> pageInfo = new PageInfo<BusAccountPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusAccountPojo insert(BusAccountPojo busAccountPojo) {
//初始化NULL字段
        cleanNull(busAccountPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusAccountEntity busAccountEntity = new BusAccountEntity();
        BeanUtils.copyProperties(busAccountPojo, busAccountEntity);
        //设置id和新建日期
        busAccountEntity.setId(id);
        busAccountEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busAccountMapper.insert(busAccountEntity);
        //Item子表处理
        List<BusAccountitemPojo> lst = busAccountPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (BusAccountitemPojo busAccountitemPojo : lst) {
                //初始化item的NULL
                BusAccountitemPojo itemPojo = this.busAccountitemService.clearNull(busAccountitemPojo);
                BusAccountitemEntity busAccountitemEntity = new BusAccountitemEntity();
                BeanUtils.copyProperties(itemPojo, busAccountitemEntity);
                //设置id和Pid
                busAccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busAccountitemEntity.setPid(id);
                busAccountitemEntity.setTenantid(busAccountPojo.getTenantid());
                busAccountitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busAccountitemMapper.insert(busAccountitemEntity);
            }
        }
        //Invo子表处理
        List<BusAccountinvoPojo> lstInvo = busAccountPojo.getInvo();
        if (lstInvo != null) {
            //循环每个Invo子表
            for (BusAccountinvoPojo busAccountinvoPojo : lstInvo) {
                //初始化Invo的NULL
                BusAccountinvoPojo invoPojo = this.busAccountinvoService.clearNull(busAccountinvoPojo);
                BusAccountinvoEntity busAccountinvoEntity = new BusAccountinvoEntity();
                BeanUtils.copyProperties(invoPojo, busAccountinvoEntity);
                //设置id和Pid
                busAccountinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busAccountinvoEntity.setPid(id);
                busAccountinvoEntity.setTenantid(busAccountPojo.getTenantid());
                busAccountinvoEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busAccountinvoMapper.insert(busAccountinvoEntity);
            }
        }
        //Arap子表处理
        List<BusAccountarapPojo> lstArap = busAccountPojo.getArap();
        if (lstArap != null) {
            //循环每个Arap子表
            for (BusAccountarapPojo busAccountarapPojo : lstArap) {
                //初始化Arap的NULL
                BusAccountarapPojo arapPojo = this.busAccountarapService.clearNull(busAccountarapPojo);
                BusAccountarapEntity busAccountarapEntity = new BusAccountarapEntity();
                BeanUtils.copyProperties(arapPojo, busAccountarapEntity);
                //设置id和Pid
                busAccountarapEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busAccountarapEntity.setPid(id);
                busAccountarapEntity.setTenantid(busAccountPojo.getTenantid());
                busAccountarapEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busAccountarapMapper.insert(busAccountarapEntity);
            }
        }
        //Deli子表处理
        List<BusAccountdeliPojo> lstDeli = busAccountPojo.getDeli();
        if (lstDeli != null) {
            //循环每个Deli子表
            for (BusAccountdeliPojo busAccountdeliPojo : lstDeli) {
                //初始化Deli的NULL
                BusAccountdeliPojo deliPojo = this.busAccountdeliService.clearNull(busAccountdeliPojo);
                BusAccountdeliEntity busAccountdeliEntity = new BusAccountdeliEntity();  // 销售订单to发货
                BeanUtils.copyProperties(deliPojo, busAccountdeliEntity);
                //设置id和Pid
                busAccountdeliEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busAccountdeliEntity.setPid(id);
                busAccountdeliEntity.setTenantid(busAccountPojo.getTenantid());
                busAccountdeliEntity.setRevision(1);  // 乐观锁
                //插入子表
                this.busAccountdeliMapper.insert(busAccountdeliEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(busAccountEntity.getId(), busAccountEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusAccountPojo update(BusAccountPojo busAccountPojo) {
        //主表更改
        BusAccountEntity busAccountEntity = new BusAccountEntity();
        BeanUtils.copyProperties(busAccountPojo, busAccountEntity);
        this.busAccountMapper.update(busAccountEntity);
        //Item子表处理
        if (busAccountPojo.getItem() != null) {
            //Item子表处理
            List<BusAccountitemPojo> lst = busAccountPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busAccountMapper.getDelItemIds(busAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.busAccountitemMapper.delete(lstDelId, busAccountEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (BusAccountitemPojo busAccountitemPojo : lst) {
                    BusAccountitemEntity busAccountitemEntity = new BusAccountitemEntity();
                    if ("".equals(busAccountitemPojo.getId()) || busAccountitemPojo.getId() == null) {
                        //初始化item的NULL
                        BusAccountitemPojo itemPojo = this.busAccountitemService.clearNull(busAccountitemPojo);
                        BeanUtils.copyProperties(itemPojo, busAccountitemEntity);
                        //设置id和Pid
                        busAccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busAccountitemEntity.setPid(busAccountEntity.getId());  // 主表 id
                        busAccountitemEntity.setTenantid(busAccountPojo.getTenantid());   // 租户id
                        busAccountitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busAccountitemMapper.insert(busAccountitemEntity);
                    } else {
                        BeanUtils.copyProperties(busAccountitemPojo, busAccountitemEntity);
                        busAccountitemEntity.setTenantid(busAccountPojo.getTenantid());
                        this.busAccountitemMapper.update(busAccountitemEntity);
                    }
                }
            }
        }

        //Invo子表处理
        if (busAccountPojo.getInvo() != null) {
            //Invo子表处理
            List<BusAccountinvoPojo> lstInvo = busAccountPojo.getInvo();
            //获取被删除的Invo
            List<String> lstDelIds = busAccountMapper.getDelInvoIds(busAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除Invo子表
                for (String lstDelId : lstDelIds) {
                    this.busAccountinvoMapper.delete(lstDelId, busAccountEntity.getTenantid());
                }
            }
            if (lstInvo != null) {
                //循环每个Invo子表
                for (BusAccountinvoPojo busAccountinvoPojo : lstInvo) {
                    BusAccountinvoEntity busAccountinvoEntity = new BusAccountinvoEntity();
                    if ("".equals(busAccountinvoPojo.getId()) || busAccountinvoPojo.getId() == null) {
                        //初始化Invo的NULL
                        BusAccountinvoPojo invoPojo = this.busAccountinvoService.clearNull(busAccountinvoPojo);
                        BeanUtils.copyProperties(invoPojo, busAccountinvoEntity);
                        //设置id和Pid
                        busAccountinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // invo id
                        busAccountinvoEntity.setPid(busAccountEntity.getId());  // 主表 id
                        busAccountinvoEntity.setTenantid(busAccountPojo.getTenantid());   // 租户id
                        busAccountinvoEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busAccountinvoMapper.insert(busAccountinvoEntity);
                    } else {
                        BeanUtils.copyProperties(busAccountinvoPojo, busAccountinvoEntity);
                        busAccountinvoEntity.setTenantid(busAccountPojo.getTenantid());
                        this.busAccountinvoMapper.update(busAccountinvoEntity);
                    }
                }
            }
        }

        //ArAp子表处理
        if (busAccountPojo.getArap() != null) {
            //ArAp子表处理
            List<BusAccountarapPojo> lstArap = busAccountPojo.getArap();
            //获取被删除的ArAp
            List<String> lstDelIds = busAccountMapper.getDelArapIds(busAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除ArAp子表
                for (String lstDelId : lstDelIds) {
                    this.busAccountarapMapper.delete(lstDelId, busAccountEntity.getTenantid());
                }
            }
            if (lstArap != null) {
                //循环每个ArAp子表
                for (BusAccountarapPojo busAccountarapPojo : lstArap) {
                    BusAccountarapEntity busAccountarapEntity = new BusAccountarapEntity();
                    if ("".equals(busAccountarapPojo.getId()) || busAccountarapPojo.getId() == null) {
                        //初始化ArAp的NULL
                        BusAccountarapPojo arapPojo = this.busAccountarapService.clearNull(busAccountarapPojo);
                        BeanUtils.copyProperties(arapPojo, busAccountarapEntity);
                        //设置id和Pid
                        busAccountarapEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // arap id
                        busAccountarapEntity.setPid(busAccountEntity.getId());  // 主表 id
                        busAccountarapEntity.setTenantid(busAccountPojo.getTenantid());   // 租户id
                        busAccountarapEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busAccountarapMapper.insert(busAccountarapEntity);
                    } else {
                        BeanUtils.copyProperties(busAccountarapPojo, busAccountarapEntity);
                        busAccountarapEntity.setTenantid(busAccountPojo.getTenantid());
                        this.busAccountarapMapper.update(busAccountarapEntity);
                    }
                }
            }
        }

        //Deli子表处理
        if (busAccountPojo.getDeli() != null) {
            //Deli子表处理
            List<BusAccountdeliPojo> lstDeli = busAccountPojo.getDeli();
            //获取被删除的Deli
            List<String> lstDelIds = busAccountMapper.getDelDeliIds(busAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除Deli子表
                for (String lstDelId : lstDelIds) {
                    this.busAccountdeliMapper.delete(lstDelId, busAccountEntity.getTenantid());
                }
            }
            if (lstDeli != null) {
                //循环每个Deli子表
                for (BusAccountdeliPojo busAccountdeliPojo : lstDeli) {
                    BusAccountdeliEntity busAccountdeliEntity = new BusAccountdeliEntity();  // 销售订单to发货
                    if ("".equals(busAccountdeliPojo.getId()) || busAccountdeliPojo.getId() == null) {
                        //初始化Deli的NULL
                        BusAccountdeliPojo deliPojo = this.busAccountdeliService.clearNull(busAccountdeliPojo);
                        BeanUtils.copyProperties(deliPojo, busAccountdeliEntity);
                    } else {
                        BeanUtils.copyProperties(busAccountdeliPojo, busAccountdeliEntity);
                    }
                    busAccountdeliEntity.setPid(busAccountEntity.getId());  // 主表 id
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(busAccountEntity.getId(), busAccountEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        BusAccountPojo busAccountPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusAccountitemPojo> lst = busAccountPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusAccountitemPojo busAccountitemPojo : lst) {
                this.busAccountitemMapper.delete(busAccountitemPojo.getId(), tid);
            }
        }
        //Invo子表处理
        List<BusAccountinvoPojo> lstInvo = busAccountPojo.getInvo();
        if (lstInvo != null) {
            //循环每个删除invo子表
            for (BusAccountinvoPojo busAccountinvoPojo : lstInvo) {
                this.busAccountinvoMapper.delete(busAccountinvoPojo.getId(), tid);
            }
        }
        //ArAp子表处理
        List<BusAccountarapPojo> lstArap = busAccountPojo.getArap();
        if (lstArap != null) {
            //循环每个删除arap子表
            for (BusAccountarapPojo busAccountarapPojo : lstArap) {
                this.busAccountarapMapper.delete(busAccountarapPojo.getId(), tid);
            }
        }
        //Deli子表处理
        List<BusAccountdeliPojo> lstDeli = busAccountPojo.getDeli();
        if (lstDeli != null) {
            //循环每个删除deli子表
            for (BusAccountdeliPojo busAccountdeliPojo : lstDeli) {
                this.busAccountdeliMapper.delete(busAccountdeliPojo.getId(), tid);
            }
        }
        //删除主表
        this.busAccountMapper.delete(key, tid);
        return busAccountPojo.getRefno();
    }

    private static void cleanNull(BusAccountPojo busAccountPojo) {
        if (busAccountPojo.getRefno() == null) busAccountPojo.setRefno("");
        if (busAccountPojo.getBilltype() == null) busAccountPojo.setBilltype("");
        if (busAccountPojo.getBilldate() == null) busAccountPojo.setBilldate(new Date());
        if (busAccountPojo.getBilltitle() == null) busAccountPojo.setBilltitle("");
        if (busAccountPojo.getGroupid() == null) busAccountPojo.setGroupid("");
        if (busAccountPojo.getCarryyear() == null) busAccountPojo.setCarryyear(0);
        if (busAccountPojo.getCarrymonth() == null) busAccountPojo.setCarrymonth(0);
        if (busAccountPojo.getStartdate() == null) busAccountPojo.setStartdate(new Date());
        if (busAccountPojo.getEnddate() == null) busAccountPojo.setEnddate(new Date());
        if (busAccountPojo.getOperator() == null) busAccountPojo.setOperator("");
        if (busAccountPojo.getOperatorid() == null) busAccountPojo.setOperatorid("");
        if (busAccountPojo.getRownum() == null) busAccountPojo.setRownum(0);
        if (busAccountPojo.getSummary() == null) busAccountPojo.setSummary("");
        if (busAccountPojo.getCreateby() == null) busAccountPojo.setCreateby("");
        if (busAccountPojo.getCreatebyid() == null) busAccountPojo.setCreatebyid("");
        if (busAccountPojo.getCreatedate() == null) busAccountPojo.setCreatedate(new Date());
        if (busAccountPojo.getLister() == null) busAccountPojo.setLister("");
        if (busAccountPojo.getListerid() == null) busAccountPojo.setListerid("");
        if (busAccountPojo.getModifydate() == null) busAccountPojo.setModifydate(new Date());
        if (busAccountPojo.getBillopenamount() == null) busAccountPojo.setBillopenamount(0D);
        if (busAccountPojo.getBillinamount() == null) busAccountPojo.setBillinamount(0D);
        if (busAccountPojo.getBilloutamount() == null) busAccountPojo.setBilloutamount(0D);
        if (busAccountPojo.getBillcloseamount() == null) busAccountPojo.setBillcloseamount(0D);
        if (busAccountPojo.getInvoopenamount() == null) busAccountPojo.setInvoopenamount(0D);
        if (busAccountPojo.getInvoinamount() == null) busAccountPojo.setInvoinamount(0D);
        if (busAccountPojo.getInvooutamount() == null) busAccountPojo.setInvooutamount(0D);
        if (busAccountPojo.getInvocloseamount() == null) busAccountPojo.setInvocloseamount(0D);
        if (busAccountPojo.getArapopenamount() == null) busAccountPojo.setArapopenamount(0D);
        if (busAccountPojo.getArapinamount() == null) busAccountPojo.setArapinamount(0D);
        if (busAccountPojo.getArapoutamount() == null) busAccountPojo.setArapoutamount(0D);
        if (busAccountPojo.getArapcloseamount() == null) busAccountPojo.setArapcloseamount(0D);
        if (busAccountPojo.getDeliopenamount() == null) busAccountPojo.setDeliopenamount(0D);
        if (busAccountPojo.getDeliinamount() == null) busAccountPojo.setDeliinamount(0D);
        if (busAccountPojo.getDelioutamount() == null) busAccountPojo.setDelioutamount(0D);
        if (busAccountPojo.getDelicloseamount() == null) busAccountPojo.setDelicloseamount(0D);
        if (busAccountPojo.getPrintcount() == null) busAccountPojo.setPrintcount(0);
        if (busAccountPojo.getCustom1() == null) busAccountPojo.setCustom1("");
        if (busAccountPojo.getCustom2() == null) busAccountPojo.setCustom2("");
        if (busAccountPojo.getCustom3() == null) busAccountPojo.setCustom3("");
        if (busAccountPojo.getCustom4() == null) busAccountPojo.setCustom4("");
        if (busAccountPojo.getCustom5() == null) busAccountPojo.setCustom5("");
        if (busAccountPojo.getCustom6() == null) busAccountPojo.setCustom6("");
        if (busAccountPojo.getCustom7() == null) busAccountPojo.setCustom7("");
        if (busAccountPojo.getCustom8() == null) busAccountPojo.setCustom8("");
        if (busAccountPojo.getCustom9() == null) busAccountPojo.setCustom9("");
        if (busAccountPojo.getCustom10() == null) busAccountPojo.setCustom10("");
        if (busAccountPojo.getTenantid() == null) busAccountPojo.setTenantid("");
        if (busAccountPojo.getTenantname() == null) busAccountPojo.setTenantname("");
        if (busAccountPojo.getRevision() == null) busAccountPojo.setRevision(0);
    }

    /**
     * 新增数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    public List<BusAccountitemPojo> pullItemList(BusAccountPojo busAccountPojo) {
        //生成id
//        AppWorkgroupPojo wgPojo = this.appWorkgroupService.getEntity(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
//        if (wgPojo == null) {
//            throw new RuntimeException("未找到对应客户信息");
//        }

        // 查询当前客户之前的销售账单
        BusAccountPojo busAccountMaxPojo = this.busAccountMapper.getMaxEntityByGroup(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (busAccountMaxPojo != null) {
//            throw new RuntimeException("请先初始化客户账单");
            dtStart = busAccountMaxPojo.getEnddate();
            openAmount = busAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(busAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + busAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(2000);
        queryParam.setTenantid(busAccountPojo.getTenantid());
        List<BusAccountitemPojo> lstitem = this.busAccountMapper.pullItemList(queryParam);
        if (lstitem.isEmpty()) {
            //  throw new RuntimeException("未找到销售与收款信息");
            BusAccountitemPojo newitem = new BusAccountitemPojo();
            newitem.setOpenamount(0D);
            newitem.setInamount(0D);
            newitem.setOutamount(0D);
            newitem.setCloseamount(0D);
            lstitem.add(newitem);
        }


        // 改为BigDec
        BigDecimal decinAmt = BigDecimal.valueOf(0);
        BigDecimal decoutAmt = BigDecimal.valueOf(0);
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.valueOf(0);

        for (int i = 0; i < lstitem.size(); i++) {
            lstitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }

        // 填入 初始，初末
        lstitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.valueOf(0);
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);

        lstitem.get(lstitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());

        //返回Bill实例
        return lstitem;

    }

    @Override
    public List<BusAccountinvoPojo> pullInvoList(BusAccountPojo busAccountPojo) {
        // 查询当前客户之前的销售账单
        BusAccountPojo busAccountMaxPojo = this.busAccountMapper.getMaxEntityByGroup(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (busAccountMaxPojo != null) {
            dtStart = busAccountMaxPojo.getEnddate();
            openAmount = busAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(busAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + busAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(2000);
        queryParam.setTenantid(busAccountPojo.getTenantid());
        List<BusAccountinvoPojo> lstinvoitem = this.busAccountMapper.pullInvoList(queryParam);
        if (lstinvoitem.isEmpty()) {
            BusAccountinvoPojo newinvoitem = new BusAccountinvoPojo();
            newinvoitem.setOpenamount(0D);
            newinvoitem.setInamount(0D);
            newinvoitem.setOutamount(0D);
            newinvoitem.setCloseamount(0D);
            lstinvoitem.add(newinvoitem);
        }

        BigDecimal decinAmt = BigDecimal.ZERO;
        BigDecimal decoutAmt = BigDecimal.ZERO;
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.ZERO;

        for (int i = 0; i < lstinvoitem.size(); i++) {
            lstinvoitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstinvoitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstinvoitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstinvoitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }
        // 填入 初始，初末
        lstinvoitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.ZERO;
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);
        // 最后一行 填入期末
        lstinvoitem.get(lstinvoitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());
        return lstinvoitem;
    }


    @Override
    public List<BusAccountarapPojo> pullArapList(BusAccountPojo busAccountPojo) {
        // 查询当前客户之前的销售账单
        BusAccountPojo busAccountMaxPojo = this.busAccountMapper.getMaxEntityByGroup(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (busAccountMaxPojo != null) {
            dtStart = busAccountMaxPojo.getEnddate();
            openAmount = busAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(busAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + busAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(2000);
        queryParam.setTenantid(busAccountPojo.getTenantid());
        List<BusAccountarapPojo> lstarapitem = this.busAccountMapper.pullArapList(queryParam);
        if (lstarapitem.isEmpty()) {
            BusAccountarapPojo newarapitem = new BusAccountarapPojo();
            newarapitem.setOpenamount(0D);
            newarapitem.setInamount(0D);
            newarapitem.setOutamount(0D);
            newarapitem.setCloseamount(0D);
            lstarapitem.add(newarapitem);
        }

        BigDecimal decinAmt = BigDecimal.ZERO;
        BigDecimal decoutAmt = BigDecimal.ZERO;
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.ZERO;

        for (int i = 0; i < lstarapitem.size(); i++) {
            lstarapitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstarapitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstarapitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstarapitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }
        // 第一行填入初始，初末
        lstarapitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.ZERO;
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);
        // 最后一行 填入期末
        lstarapitem.get(lstarapitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());
        return lstarapitem;
    }


    private List<BusAccountdeliPojo> pullDeliList(BusAccountPojo newPojo) {
        // 查询当前客户之前的销售账单
        BusAccountPojo busAccountMaxPojo = this.busAccountMapper.getMaxEntityByGroup(newPojo.getGroupid(), newPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (busAccountMaxPojo != null) {
            dtStart = busAccountMaxPojo.getEnddate();
            openAmount = busAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(newPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + newPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(2000);
        queryParam.setTenantid(newPojo.getTenantid());
        List<BusAccountdeliPojo> lstdeliitem = this.busAccountMapper.pullDeliList(queryParam);
        if (lstdeliitem.isEmpty()) {
            BusAccountdeliPojo newdeliitem = new BusAccountdeliPojo();
            newdeliitem.setOpenamount(0D);
            newdeliitem.setInamount(0D);
            newdeliitem.setOutamount(0D);
            newdeliitem.setCloseamount(0D);
            lstdeliitem.add(newdeliitem);
        }
        BigDecimal decinAmt = BigDecimal.ZERO;
        BigDecimal decoutAmt = BigDecimal.ZERO;
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.ZERO;

        for (int i = 0; i < lstdeliitem.size(); i++) {
            lstdeliitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstdeliitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstdeliitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstdeliitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }
        // 第一行填入初始，初末
        lstdeliitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.ZERO;
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);
        // 最后一行 填入期末
        lstdeliitem.get(lstdeliitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());
        return lstdeliitem;
    }


    // 批量生成账单
    @Override
    @Transactional
    public int batchCreate(BusAccountPojo busAccountPojo) {
        try {
            int num = 0;
            String tid = busAccountPojo.getTenantid();
            // 获取所有客户
            List<String> lstwgIds = this.busAccountMapper.getCustomerIds(tid);

            // item改为BigDec
            BigDecimal accountopenAmt = BigDecimal.valueOf(0);
            BigDecimal accountinAmt = BigDecimal.valueOf(0);
            BigDecimal accountoutAmt = BigDecimal.valueOf(0);
            BigDecimal accountcloseAmt = BigDecimal.valueOf(0);
            // invo改为BigDec
            BigDecimal accountInvoopenAmt = BigDecimal.valueOf(0);
            BigDecimal accountInvoinAmt = BigDecimal.valueOf(0);
            BigDecimal accountInvooutAmt = BigDecimal.valueOf(0);
            BigDecimal accountInvocloseAmt = BigDecimal.valueOf(0);
            // arap改为BigDec
            BigDecimal accountArapopenAmt = BigDecimal.valueOf(0);
            BigDecimal accountArapinAmt = BigDecimal.valueOf(0);
            BigDecimal accountArapoutAmt = BigDecimal.valueOf(0);
            BigDecimal accountArapcloseAmt = BigDecimal.valueOf(0);
            //deli改为BigDec
            BigDecimal accountDeliopenAmt = BigDecimal.valueOf(0);
            BigDecimal accountDeliinAmt = BigDecimal.valueOf(0);
            BigDecimal accountDelioutAmt = BigDecimal.valueOf(0);
            BigDecimal accountDelicloseAmt = BigDecimal.valueOf(0);
            // 遍历计算每个客户账单
            for (String wgid : lstwgIds) {
                BusAccountPojo newPojo = new BusAccountPojo();
                BeanUtils.copyProperties(busAccountPojo, newPojo);
                newPojo.setGroupid(wgid);
                // 1.送货单to收款
                newPojo.setItem(pullItemList(newPojo));
                // 改为BigDec
                BigDecimal decopenAmt = BigDecimal.valueOf(0);
                BigDecimal decinAmt = BigDecimal.valueOf(0);
                BigDecimal decoutAmt = BigDecimal.valueOf(0);
                BigDecimal deccloseAmt = BigDecimal.valueOf(0);
                if (!newPojo.getItem().isEmpty()) {
                    decopenAmt = decopenAmt.add(BigDecimal.valueOf(newPojo.getItem().get(0).getOpenamount()));
                    deccloseAmt = deccloseAmt.add(BigDecimal.valueOf(newPojo.getItem().get(newPojo.getItem().size() - 1).getCloseamount()));
                }
                for (BusAccountitemPojo itemPojo : newPojo.getItem()) {
                    decinAmt = decinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decoutAmt = decoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setBillopenamount(decopenAmt.doubleValue());
                newPojo.setBillinamount(decinAmt.doubleValue());
                newPojo.setBilloutamount(decoutAmt.doubleValue());
                newPojo.setBillcloseamount(deccloseAmt.doubleValue());

                // 2.送货单to发票
                newPojo.setInvo(pullInvoList(newPojo));
                // 改为BigDec
                BigDecimal decInvoopenAmt = BigDecimal.valueOf(0);
                BigDecimal decInvoinAmt = BigDecimal.valueOf(0);
                BigDecimal decInvooutAmt = BigDecimal.valueOf(0);
                BigDecimal decInvocloseAmt = BigDecimal.valueOf(0);
                if (!newPojo.getInvo().isEmpty()) {
                    decInvoopenAmt = decInvoopenAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(0).getOpenamount()));
                    decInvocloseAmt = decInvocloseAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(newPojo.getInvo().size() - 1).getCloseamount()));
                }
                for (BusAccountinvoPojo itemPojo : newPojo.getInvo()) {
                    decInvoinAmt = decInvoinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decInvooutAmt = decInvooutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setInvoopenamount(decInvoopenAmt.doubleValue());
                newPojo.setInvoinamount(decInvoinAmt.doubleValue());
                newPojo.setInvooutamount(decInvooutAmt.doubleValue());
                newPojo.setInvocloseamount(decInvocloseAmt.doubleValue());

                // 3.发票to收款
                newPojo.setArap(pullArapList(newPojo));
                // 改为BigDec
                BigDecimal decArapopenAmt = BigDecimal.valueOf(0);
                BigDecimal decArapinAmt = BigDecimal.valueOf(0);
                BigDecimal decArapoutAmt = BigDecimal.valueOf(0);
                BigDecimal decArapcloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getArap().size() > 0) {
                    decArapopenAmt = decArapopenAmt.add(BigDecimal.valueOf(newPojo.getArap().get(0).getOpenamount()));
                    decArapcloseAmt = decArapcloseAmt.add(BigDecimal.valueOf(newPojo.getArap().get(newPojo.getArap().size() - 1).getCloseamount()));
                }
                for (BusAccountarapPojo itemPojo : newPojo.getArap()) {
                    decArapinAmt = decArapinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decArapoutAmt = decArapoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setArapopenamount(decArapopenAmt.doubleValue());
                newPojo.setArapinamount(decArapinAmt.doubleValue());
                newPojo.setArapoutamount(decArapoutAmt.doubleValue());
                newPojo.setArapcloseamount(decArapcloseAmt.doubleValue());

                // 4.销售订单to发货
                newPojo.setDeli(pullDeliList(newPojo));
                // 改为BigDec
                BigDecimal decDeliopenAmt = BigDecimal.valueOf(0);
                BigDecimal decDeliinAmt = BigDecimal.valueOf(0);
                BigDecimal decDelioutAmt = BigDecimal.valueOf(0);
                BigDecimal decDelicloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getDeli().size() > 0) {
                    decDeliopenAmt = decDeliopenAmt.add(BigDecimal.valueOf(newPojo.getDeli().get(0).getOpenamount()));
                    decDelicloseAmt = decDelicloseAmt.add(BigDecimal.valueOf(newPojo.getDeli().get(newPojo.getDeli().size() - 1).getCloseamount()));
                }
                for (BusAccountdeliPojo itemPojo : newPojo.getDeli()) {
                    decDeliinAmt = decDeliinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decDelioutAmt = decDelioutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setDeliopenamount(decDeliopenAmt.doubleValue());
                newPojo.setDeliinamount(decDeliinAmt.doubleValue());
                newPojo.setDelioutamount(decDelioutAmt.doubleValue());
                newPojo.setDelicloseamount(decDelicloseAmt.doubleValue());

                insert(newPojo);
                // 当前客户item累加
                accountopenAmt = accountopenAmt.add(decopenAmt);
                accountinAmt = accountinAmt.add(decinAmt);
                accountoutAmt = accountoutAmt.add(decoutAmt);
                accountcloseAmt = accountcloseAmt.add(deccloseAmt);
                // 当前客户invo累加
                accountInvoopenAmt = accountInvoopenAmt.add(decInvoopenAmt);
                accountInvoinAmt = accountInvoinAmt.add(decInvoinAmt);
                accountInvooutAmt = accountInvooutAmt.add(decInvooutAmt);
                accountInvocloseAmt = accountInvocloseAmt.add(decInvocloseAmt);
                // 当前客户arap累加
                accountArapopenAmt = accountArapopenAmt.add(decArapopenAmt);
                accountArapinAmt = accountArapinAmt.add(decArapinAmt);
                accountArapoutAmt = accountArapoutAmt.add(decArapoutAmt);
                accountArapcloseAmt = accountArapcloseAmt.add(decArapcloseAmt);
                // 当前客户deli累加
                accountDeliopenAmt = accountDeliopenAmt.add(decDeliopenAmt);
                accountDeliinAmt = accountDeliinAmt.add(decDeliinAmt);
                accountDelioutAmt = accountDelioutAmt.add(decDelioutAmt);
                accountDelicloseAmt = accountDelicloseAmt.add(decDelicloseAmt);
                // 下一位客户
                num++;
            }
            BusAccountrecPojo busAccountrecPojo = new BusAccountrecPojo();
            BeanUtils.copyProperties(busAccountPojo, busAccountrecPojo);
            // 设置item该月累加账单
            busAccountrecPojo.setBillopenamount(accountopenAmt.doubleValue());
            busAccountrecPojo.setBillinamount(accountinAmt.doubleValue());
            busAccountrecPojo.setBilloutamount(accountoutAmt.doubleValue());
            busAccountrecPojo.setBillcloseamount(accountcloseAmt.doubleValue());
            // 设置invo该月累加账单
            busAccountrecPojo.setInvoopenamount(accountInvoopenAmt.doubleValue());
            busAccountrecPojo.setInvoinamount(accountInvoinAmt.doubleValue());
            busAccountrecPojo.setInvooutamount(accountInvooutAmt.doubleValue());
            busAccountrecPojo.setInvocloseamount(accountInvocloseAmt.doubleValue());
            // 设置arap该月累加账单
            busAccountrecPojo.setArapopenamount(accountArapopenAmt.doubleValue());
            busAccountrecPojo.setArapinamount(accountArapinAmt.doubleValue());
            busAccountrecPojo.setArapoutamount(accountArapoutAmt.doubleValue());
            busAccountrecPojo.setArapcloseamount(accountArapcloseAmt.doubleValue());
            // 设置deli该月累加账单
            busAccountrecPojo.setDeliopenamount(accountDeliopenAmt.doubleValue());
            busAccountrecPojo.setDeliinamount(accountDeliinAmt.doubleValue());
            busAccountrecPojo.setDelioutamount(accountDelioutAmt.doubleValue());
            busAccountrecPojo.setDelicloseamount(accountDelicloseAmt.doubleValue());
            this.busAccountrecService.insert(busAccountrecPojo);
            return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }


    // 批量生成账单
    @Override
    @Transactional
    public int batchInit(BusAccountPojo busAccountPojo) {
        try {
            int num = 0;
            String tid = busAccountPojo.getTenantid();
            List<String> lstwgIds = this.busAccountMapper.getCustomerIds(tid);
            for (String wgid : lstwgIds) {
                BusAccountPojo dbPojo = getMaxEntityByGroup(wgid, tid);
                // 是否已有初始化
                if (dbPojo == null) {
                    BusAccountPojo newPojo = new BusAccountPojo();
                    BeanUtils.copyProperties(busAccountPojo, newPojo);
                    newPojo.setGroupid(wgid);
                    insert(newPojo);
                    num++;
                }
            }
            return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }

    /**
     * 通过GroupID查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountPojo getMaxEntityByGroup(String key, String tid) {
        return this.busAccountMapper.getMaxEntityByGroup(key, tid);
    }

    /**
     * 通过GroupID查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountPojo getMaxBillEntityByGroup(String key, String tid) {
        BusAccountPojo busAccountPojo = this.busAccountMapper.getMaxEntityByGroup(key, tid);
        //读取子表
        busAccountPojo.setItem(busAccountitemMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
        return busAccountPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusAccountitemPojo> getMultItemList(QueryParam queryParam) {
        return this.busAccountMapper.getMultItemList(queryParam);
    }

    /**
     * 分页查询实时报表
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountPojo> getNowPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountPojo> lst = this.busAccountMapper.getNowPageList(queryParam);
            PageInfo<BusAccountPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //销售结转redis状态key
    private final String BUSBATCHCREATE_CODE = "busbatchcreate_codes:";

    @Async
    @Override
    @Transactional
    public void batchCreateStart(BusAccountPojo busAccountPojo, String uuid, boolean isCreateGoodsCarryover) {
        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(BUSBATCHCREATE_CODE, uuid, missionMsg);

            String tid = busAccountPojo.getTenantid();
            // 获取所有客户
            List<String> groupIds = this.busAccountMapper.getCustomerIds(tid);


            // 单据账单 account:
            // item改为BigDec
            BigDecimal accountItemopenAmt = BigDecimal.valueOf(0);
            BigDecimal accountIteminAmt = BigDecimal.valueOf(0);
            BigDecimal accountItemoutAmt = BigDecimal.valueOf(0);
            BigDecimal accountItemcloseAmt = BigDecimal.valueOf(0);
            // invo改为BigDec
            BigDecimal accountInvoopenAmt = BigDecimal.valueOf(0);
            BigDecimal accountInvoinAmt = BigDecimal.valueOf(0);
            BigDecimal accountInvooutAmt = BigDecimal.valueOf(0);
            BigDecimal accountInvocloseAmt = BigDecimal.valueOf(0);
            // arap改为BigDec
            BigDecimal accountArapopenAmt = BigDecimal.valueOf(0);
            BigDecimal accountArapinAmt = BigDecimal.valueOf(0);
            BigDecimal accountArapoutAmt = BigDecimal.valueOf(0);
            BigDecimal accountArapcloseAmt = BigDecimal.valueOf(0);
            // deli改为BigDec
            BigDecimal accountDeliopenAmt = BigDecimal.valueOf(0);
            BigDecimal accountDeliinAmt = BigDecimal.valueOf(0);
            BigDecimal accountDelioutAmt = BigDecimal.valueOf(0);
            BigDecimal accountDelicloseAmt = BigDecimal.valueOf(0);


            // 遍历计算每个客户账单
            for (String groupId : groupIds) {
                BusAccountPojo newPojo = new BusAccountPojo();
                BeanUtils.copyProperties(busAccountPojo, newPojo);
                newPojo.setGroupid(groupId);
                // 1.送货单to收款
                newPojo.setItem(pullItemList(newPojo));
                // 改为BigDec
                BigDecimal decopenAmt = BigDecimal.valueOf(0);
                BigDecimal decinAmt = BigDecimal.valueOf(0);
                BigDecimal decoutAmt = BigDecimal.valueOf(0);
                BigDecimal deccloseAmt = BigDecimal.valueOf(0);
                if (!newPojo.getItem().isEmpty()) {
                    decopenAmt = decopenAmt.add(BigDecimal.valueOf(newPojo.getItem().get(0).getOpenamount()));
                    deccloseAmt = deccloseAmt.add(BigDecimal.valueOf(newPojo.getItem().get(newPojo.getItem().size() - 1).getCloseamount()));
                }
                for (BusAccountitemPojo itemPojo : newPojo.getItem()) {
                    decinAmt = decinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decoutAmt = decoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setBillopenamount(decopenAmt.doubleValue());
                newPojo.setBillinamount(decinAmt.doubleValue());
                newPojo.setBilloutamount(decoutAmt.doubleValue());
                newPojo.setBillcloseamount(deccloseAmt.doubleValue());

                // 2.送货单to发票
                newPojo.setInvo(pullInvoList(newPojo));
                // 改为BigDec
                BigDecimal decInvoopenAmt = BigDecimal.valueOf(0);
                BigDecimal decInvoinAmt = BigDecimal.valueOf(0);
                BigDecimal decInvooutAmt = BigDecimal.valueOf(0);
                BigDecimal decInvocloseAmt = BigDecimal.valueOf(0);
                if (!newPojo.getInvo().isEmpty()) {
                    decInvoopenAmt = decInvoopenAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(0).getOpenamount()));
                    decInvocloseAmt = decInvocloseAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(newPojo.getInvo().size() - 1).getCloseamount()));
                }
                for (BusAccountinvoPojo itemPojo : newPojo.getInvo()) {
                    decInvoinAmt = decInvoinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decInvooutAmt = decInvooutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setInvoopenamount(decInvoopenAmt.doubleValue());
                newPojo.setInvoinamount(decInvoinAmt.doubleValue());
                newPojo.setInvooutamount(decInvooutAmt.doubleValue());
                newPojo.setInvocloseamount(decInvocloseAmt.doubleValue());

                // 3.发票to收款
                newPojo.setArap(pullArapList(newPojo));
                // 改为BigDec
                BigDecimal decArapopenAmt = BigDecimal.valueOf(0);
                BigDecimal decArapinAmt = BigDecimal.valueOf(0);
                BigDecimal decArapoutAmt = BigDecimal.valueOf(0);
                BigDecimal decArapcloseAmt = BigDecimal.valueOf(0);
                if (!newPojo.getArap().isEmpty()) {
                    decArapopenAmt = decArapopenAmt.add(BigDecimal.valueOf(newPojo.getArap().get(0).getOpenamount()));
                    decArapcloseAmt = decArapcloseAmt.add(BigDecimal.valueOf(newPojo.getArap().get(newPojo.getArap().size() - 1).getCloseamount()));
                }
                for (BusAccountarapPojo itemPojo : newPojo.getArap()) {
                    decArapinAmt = decArapinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decArapoutAmt = decArapoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setArapopenamount(decArapopenAmt.doubleValue());
                newPojo.setArapinamount(decArapinAmt.doubleValue());
                newPojo.setArapoutamount(decArapoutAmt.doubleValue());
                newPojo.setArapcloseamount(decArapcloseAmt.doubleValue());

                // 4.发票to付款
                newPojo.setDeli(pullDeliList(newPojo));
                // 改为BigDec
                BigDecimal decDeliopenAmt = BigDecimal.valueOf(0);
                BigDecimal decDeliinAmt = BigDecimal.valueOf(0);
                BigDecimal decDelioutAmt = BigDecimal.valueOf(0);
                BigDecimal decDelicloseAmt = BigDecimal.valueOf(0);
                if (!newPojo.getDeli().isEmpty()) {
                    decDeliopenAmt = decDeliopenAmt.add(BigDecimal.valueOf(newPojo.getDeli().get(0).getOpenamount()));
                    decDelicloseAmt = decDelicloseAmt.add(BigDecimal.valueOf(newPojo.getDeli().get(newPojo.getDeli().size() - 1).getCloseamount()));
                }
                for (BusAccountdeliPojo itemPojo : newPojo.getDeli()) {
                    decDeliinAmt = decDeliinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decDelioutAmt = decDelioutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setDeliopenamount(decDeliopenAmt.doubleValue());
                newPojo.setDeliinamount(decDeliinAmt.doubleValue());
                newPojo.setDelioutamount(decDelioutAmt.doubleValue());
                newPojo.setDelicloseamount(decDelicloseAmt.doubleValue());

                // 主表+3子表保存
                insert(newPojo);
                // 当前客户item累加
                accountItemopenAmt = accountItemopenAmt.add(decopenAmt);
                accountIteminAmt = accountIteminAmt.add(decinAmt);
                accountItemoutAmt = accountItemoutAmt.add(decoutAmt);
                accountItemcloseAmt = accountItemcloseAmt.add(deccloseAmt);
                // 当前客户invo累加
                accountInvoopenAmt = accountInvoopenAmt.add(decInvoopenAmt);
                accountInvoinAmt = accountInvoinAmt.add(decInvoinAmt);
                accountInvooutAmt = accountInvooutAmt.add(decInvooutAmt);
                accountInvocloseAmt = accountInvocloseAmt.add(decInvocloseAmt);
                // 当前客户arap累加
                accountArapopenAmt = accountArapopenAmt.add(decArapopenAmt);
                accountArapinAmt = accountArapinAmt.add(decArapinAmt);
                accountArapoutAmt = accountArapoutAmt.add(decArapoutAmt);
                accountArapcloseAmt = accountArapcloseAmt.add(decArapcloseAmt);
                // 当前客户deli累加
                accountDeliopenAmt = accountDeliopenAmt.add(decDeliopenAmt);
                accountDeliinAmt = accountDeliinAmt.add(decDeliinAmt);
                accountDelioutAmt = accountDelioutAmt.add(decDelioutAmt);
                accountDelicloseAmt = accountDelicloseAmt.add(decDelicloseAmt);
                // 下一位客户
            }
            BusAccountrecPojo busAccountrecPojo = new BusAccountrecPojo();
            BeanUtils.copyProperties(busAccountPojo, busAccountrecPojo);
            // 设置item该月累加账单
            busAccountrecPojo.setBillopenamount(accountItemopenAmt.doubleValue());
            busAccountrecPojo.setBillinamount(accountIteminAmt.doubleValue());
            busAccountrecPojo.setBilloutamount(accountItemoutAmt.doubleValue());
            busAccountrecPojo.setBillcloseamount(accountItemcloseAmt.doubleValue());
            // 设置invo该月累加账单
            busAccountrecPojo.setInvoopenamount(accountInvoopenAmt.doubleValue());
            busAccountrecPojo.setInvoinamount(accountInvoinAmt.doubleValue());
            busAccountrecPojo.setInvooutamount(accountInvooutAmt.doubleValue());
            busAccountrecPojo.setInvocloseamount(accountInvocloseAmt.doubleValue());
            // 设置arap该月累加账单
            busAccountrecPojo.setArapopenamount(accountArapopenAmt.doubleValue());
            busAccountrecPojo.setArapinamount(accountArapinAmt.doubleValue());
            busAccountrecPojo.setArapoutamount(accountArapoutAmt.doubleValue());
            busAccountrecPojo.setArapcloseamount(accountArapcloseAmt.doubleValue());
            // 设置deli该月累加账单
            busAccountrecPojo.setDeliopenamount(accountDeliopenAmt.doubleValue());
            busAccountrecPojo.setDeliinamount(accountDeliinAmt.doubleValue());
            busAccountrecPojo.setDelioutamount(accountDelioutAmt.doubleValue());
            busAccountrecPojo.setDelicloseamount(accountDelicloseAmt.doubleValue());
            BusAccountrecPojo recInsert = this.busAccountrecService.insert(busAccountrecPojo);

            // 生成货品账单（功能同下面单据账单）  读取指定系统参数"module.sale.goodscarryover"：是否生成货品账单
            if (isCreateGoodsCarryover) { //busAccountPojo有本次想生成的Year和Month
                batchCreateStart_Carryover(busAccountPojo.getCarryyear(), busAccountPojo.getCarrymonth(), recInsert.getId(), tid);
            }

            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(BUSBATCHCREATE_CODE, uuid, missionMsg);
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }

    //protected void batchCreateStart_Carryover(Integer carryYear, Integer carryMonth, String tid) {
    //    // busAccountPojo有本次想生成的Year和Month
    //    // 货品账单(全部客户)
    //
    //    // 1.销售订单to发货单
    //    List<BusInvocarryoverPojo> busInvocarryoverPojos = pullInvoCarryList(carryYear, carryMonth, tid);
    //    // 批量插入
    //    int count = busInvocarryoverMapper.batchInsert(busInvocarryoverPojos);
    //    log.info(" 货品账单Bus_InvoCarryover共生成" + count + "条记录 来自D01M14B1/batchCreateStartbatch的CreateStart_Carryover");
    //
    //    //// 2.发货单to销售开票
    //    //newCarry.setInvo(pullCarryInvoList(newCarry));
    //
    //}


    private static final int BATCH_SIZE = 200; // 每批次插入X条

    protected void batchCreateStart_Carryover(Integer carryYear, Integer carryMonth, String recidNew, String tid) {
        // 获取货品账单数据
        List<BusInvocarryoverPojo> busInvocarryoverPojos = pullInvoCarryList(carryYear, carryMonth, recidNew, tid);
        // 批量插入数据
        batchInsertData(busInvocarryoverPojos);

        // TODO 销售订单To发货单 BuDeliocarryoverPojo

        log.info("货品账单Bus_InvoCarryover共生成 " + busInvocarryoverPojos.size() + " 条记录 来自D01M14B1/batchCreateStartbatch的CreateStart_Carryover");
    }

    private void batchInsertData(List<BusInvocarryoverPojo> dataList) {
        int totalSize = dataList.size();
        int batchCount = 0; // 批次数
        int totalInserted = 0; // 总插入数量

        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            // 获取当前批次数据
            List<BusInvocarryoverPojo> batchList = dataList.subList(i, Math.min(i + BATCH_SIZE, totalSize));
            int insertedCount = busInvocarryoverMapper.batchInsert(batchList);
            batchCount++;
            totalInserted += insertedCount;
            log.info("第 " + batchCount + " 次插入，本批次插入数量：" + insertedCount + " 条，累计插入数量：" + totalInserted + " 条");
        }
    }


    @Override
    public Map<String, Object> batchCreateState(String key) {
        return this.redisService.getCacheMapValue(BUSBATCHCREATE_CODE, key);
    }


    // 本次想生成的Year和Month 2024,10
    // 假设上一期是9月，想要结账10月
    // in发货单-->out销售开票
    public List<BusInvocarryoverPojo> pullInvoCarryList(Integer carryYear, Integer carryMonth, String recidNew, String tid) {
        // 拿到关联的9月的Bus_AccountRec  carryMonth-1 上个月
        BusAccountrecPojo accountRecLastMonth = busAccountrecMapper.getEntityByYearMonth(
                carryMonth == 1 ? carryYear - 1 : carryYear,
                carryMonth == 1 ? 12 : carryMonth - 1,
                tid
        );
        String accountRecid = accountRecLastMonth.getId();
        // 先获取9月已生成货品账单(客户下) 从表Bus_InvoCarryover查出来的数据
        List<BusInvocarryoverPojo> lastMonthDB = busInvocarryoverMapper.getListByMonth(accountRecid, null, tid);

        // 获取10月还未生成货品账单 从 in发货单-->out销售开票 两表实时查出的数据
        QueryParam queryParam = new QueryParam();
        //String strFilter = " and zhu.Groupid='" + groupid + "'";
        //queryParam.setFilterstr(strFilter);
        // 该月的开始日期和结束日期 10.1 00:00:00 -10.31 23:59:59.999
        Date[] startAndEndDateOfMonth = getStartAndEndDateOfMonth(carryYear, carryMonth);
        Date dtStart = startAndEndDateOfMonth[0];
        Date dtEnd = startAndEndDateOfMonth[1];
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(10000);
        queryParam.setTenantid(tid);
        List<BusInvocarryoverPojo> thisMonthList = this.busAccountMapper.pullCarryInvoList(queryParam);


        // 存储最终结果的列表
        List<BusInvocarryoverPojo> resultNewList = new ArrayList<>();

        // 将10月的数据按goodsid存入Map，避免使用嵌套循环
        Map<String, BusInvocarryoverPojo> thisMonthMap = new HashMap<>();
        for (BusInvocarryoverPojo thisMonth : thisMonthList) {
            String goodsId = thisMonth.getGoodsid();
            if (thisMonthMap.containsKey(goodsId)) {//非首次出现此goodsid，累加出入账数量，金额
                BusInvocarryoverPojo existing = thisMonthMap.get(goodsId);
                existing.setInqty(BigDecimal.valueOf(existing.getInqty()).add(BigDecimal.valueOf(thisMonth.getInqty())).doubleValue());
                existing.setInamount(BigDecimal.valueOf(existing.getInamount()).add(BigDecimal.valueOf(thisMonth.getInamount())).doubleValue());
                existing.setOutqty(BigDecimal.valueOf(existing.getOutqty()).add(BigDecimal.valueOf(thisMonth.getOutqty())).doubleValue());
                existing.setOutamount(BigDecimal.valueOf(existing.getOutamount()).add(BigDecimal.valueOf(thisMonth.getOutamount())).doubleValue());
            } else {// 首次出现此goodsid，直接放入
                thisMonthMap.put(goodsId, thisMonth);
            }
        }

        // 遍历9月数据，基于9月的数据生成新记录
        if (lastMonthDB != null) {
            for (BusInvocarryoverPojo lastMonth : lastMonthDB) {
                // 新建一个新的记录，复制9月的closeqty和closeamount作为openqty和openamount
                BusInvocarryoverPojo newInvo = new BusInvocarryoverPojo();
                newInvo.setId(inksSnowflake.getSnowflake().nextIdStr());
                newInvo.setRecid(recidNew);
                newInvo.setTenantid(tid);
                // 期初数量和金额为9月数据的期末数量和金额
                newInvo.setOpenqty(lastMonth.getCloseqty());
                newInvo.setOpenamount(lastMonth.getCloseamount());
                // 延用货品信息、包括货品表里的客户
                newInvo.setGroupid(lastMonth.getGroupid());
                newInvo.setGroupname(lastMonth.getGroupname());
                newInvo.setGroupuid(lastMonth.getGroupuid());
                newInvo.setGoodsid(lastMonth.getGoodsid());
                newInvo.setItemcode(lastMonth.getItemcode());
                newInvo.setItemname(lastMonth.getItemname());
                newInvo.setItemspec(lastMonth.getItemspec());
                newInvo.setItemunit(lastMonth.getItemunit());
                newInvo.setSkuid(lastMonth.getSkuid());
                newInvo.setAttributejson(lastMonth.getAttributejson());

                // 初始化入账数量、入账金额、出账数量和出账金额为0
                newInvo.setInqty(0.0);
                newInvo.setInamount(0.0);
                newInvo.setOutqty(0.0);
                newInvo.setOutamount(0.0);

                // 查找对应的10月数据并更新入账和出账数据  remove() 方法会根据提供的键移除 Map 中对应的键值对，并返回被移除的值。如果键不存在，返回 null。
                BusInvocarryoverPojo thisMonth = thisMonthMap.remove(lastMonth.getGoodsid());
                if (thisMonth != null) {
                    // 设置更新后的入账和出账数据
                    newInvo.setInqty(thisMonth.getInqty());
                    newInvo.setInamount(thisMonth.getInamount());
                    newInvo.setOutqty(thisMonth.getOutqty());
                    newInvo.setOutamount(thisMonth.getOutamount());

                    // 更新期末数量和金额 加上in 减去out
                    BigDecimal closeQty = BigDecimal.valueOf(lastMonth.getCloseqty())
                            .add(BigDecimal.valueOf(thisMonth.getInqty()))
                            .subtract(BigDecimal.valueOf(thisMonth.getOutqty()));
                    BigDecimal closeAmount = BigDecimal.valueOf(lastMonth.getCloseamount())
                            .add(BigDecimal.valueOf(thisMonth.getInamount()))
                            .subtract(BigDecimal.valueOf(thisMonth.getOutamount()));


                    newInvo.setCloseqty(closeQty.doubleValue());
                    newInvo.setCloseamount(closeAmount.doubleValue());
                } else {
                    // 如果本月该货品未产生任何记录，则期末数量和金额仍然延续为9月的期末数量和金额
                    newInvo.setCloseqty(lastMonth.getCloseqty());
                    newInvo.setCloseamount(lastMonth.getCloseamount());
                }
                // 去除空值
                BusInvocarryoverServiceImpl.cleanNull(newInvo);
                // 添加到新的列表中
                resultNewList.add(newInvo);
            }
        }

        // 处理10月中有但9月中没有的额外数据
        for (BusInvocarryoverPojo extraThisMonth : thisMonthMap.values()) {
            BusInvocarryoverPojo newInvo = new BusInvocarryoverPojo();
            newInvo.setId(inksSnowflake.getSnowflake().nextIdStr());
            newInvo.setRecid(recidNew);
            newInvo.setTenantid(tid);
            // 延用货品信息
            newInvo.setGroupid(extraThisMonth.getGroupid());
            newInvo.setGroupname(extraThisMonth.getGroupname());
            newInvo.setGroupuid(extraThisMonth.getGroupuid());
            newInvo.setGoodsid(extraThisMonth.getGoodsid());
            newInvo.setItemcode(extraThisMonth.getItemcode());
            newInvo.setItemname(extraThisMonth.getItemname());
            newInvo.setItemspec(extraThisMonth.getItemspec());
            newInvo.setItemunit(extraThisMonth.getItemunit());
            newInvo.setSkuid(extraThisMonth.getSkuid());
            newInvo.setAttributejson(extraThisMonth.getAttributejson());
            // 期初数量和金额为0
            newInvo.setOpenqty(0.0);
            newInvo.setOpenamount(0.0);
            // 设置更新后的入账和出账数据
            newInvo.setInqty(extraThisMonth.getInqty());
            newInvo.setInamount(extraThisMonth.getInamount());
            newInvo.setOutqty(extraThisMonth.getOutqty());
            newInvo.setOutamount(extraThisMonth.getOutamount());
            // 更新期末数量和金额
            BigDecimal closeQty = BigDecimal.valueOf(extraThisMonth.getInqty())
                    .subtract(BigDecimal.valueOf(extraThisMonth.getOutqty()));
            BigDecimal closeAmount = BigDecimal.valueOf(extraThisMonth.getInamount())
                    .subtract(BigDecimal.valueOf(extraThisMonth.getOutamount()));

            newInvo.setCloseqty(closeQty.doubleValue());
            newInvo.setCloseamount(closeAmount.doubleValue());
            // 去除空值
            BusInvocarryoverServiceImpl.cleanNull(newInvo);
            // 添加到新的列表中
            resultNewList.add(newInvo);
        }
        // resultNewList中需要过滤掉OpenQty, InQty, OutQty, CloseQty 同时都为0的不要
        return resultNewList.stream()
                .filter(pojo -> !(pojo.getOpenqty() == 0
                        && pojo.getInqty() == 0
                        && pojo.getOutqty() == 0
                        && pojo.getCloseqty() == 0))
                .collect(Collectors.toList());
    }


    public List<BusInvocarryoverPojo> updateInvoCarryByids(List<String> invocarryids, Integer carryYear, Integer carryMonth, String tid) {
        // 拿到上一个月Bus_AccountRec  carryMonth-1 上个月
        BusAccountrecPojo accountRecLastMonth = busAccountrecMapper.getEntityByYearMonth(
                carryMonth == 1 ? carryYear - 1 : carryYear,
                carryMonth == 1 ? 12 : carryMonth - 1,
                tid
        );
        if (accountRecLastMonth == null) {
            throw new RuntimeException("上个月没有生成账单");
        }
        // 获取上个月生成的账单id
        String lastMonth_Recid = accountRecLastMonth.getId();

        // 拿到当前选择Items数据
        List<BusInvocarryoverPojo> selMonthDB = busInvocarryoverMapper.getListByids(invocarryids, tid);
        // 勾选的invocarryids更新后的最终数据
        List<BusInvocarryoverPojo> selMonthUpdate = new ArrayList<>();
        // 获取Items in发货单-->out销售开票 两表实时查出的数据
        for (BusInvocarryoverPojo selMonthDBPojo : selMonthDB) {
            // 获取10月还未生成货品账单 从 in发货单-->out销售开票 两表实时查出的数据 【此处只查1个货品】
            QueryParam queryParam = new QueryParam();
            // 该月的开始日期和结束日期 10.1 00:00:00 -10.31 23:59:59.999
            Date[] startAndEndDateOfMonth = getStartAndEndDateOfMonth(carryYear, carryMonth);
            Date dtStart = startAndEndDateOfMonth[0];
            Date dtEnd = startAndEndDateOfMonth[1];
            queryParam.setOrderBy("BillDate");
            queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
            queryParam.setOrderType(0);
            queryParam.setPageNum(1);
            queryParam.setPageSize(10000);
            queryParam.setTenantid(tid);
            queryParam.setFilterstr(" and Mat_Goods.id='" + selMonthDBPojo.getGoodsid() + "'");//【此处只查1个货品】
            List<BusInvocarryoverPojo> thisMonthList = this.busAccountMapper.pullCarryInvoList(queryParam);

            // 合并返回记录数量和金额
            BigDecimal decInQty = BigDecimal.valueOf(0);
            BigDecimal decInAmt = BigDecimal.valueOf(0);
            BigDecimal decOutQty = BigDecimal.valueOf(0);
            BigDecimal decOutAmt = BigDecimal.valueOf(0);
            for (BusInvocarryoverPojo thisMonth : thisMonthList) {
                decInQty=  decInQty.add(thisMonth.getInqty()== null ? BigDecimal.valueOf(0) : BigDecimal.valueOf(thisMonth.getInqty()));
                decInAmt=  decInAmt.add(thisMonth.getInamount()== null ? BigDecimal.valueOf(0) : BigDecimal.valueOf(thisMonth.getInamount()));
                decOutQty=  decOutQty.add(thisMonth.getOutqty()== null ? BigDecimal.valueOf(0) : BigDecimal.valueOf(thisMonth.getOutqty()));
                decOutAmt=  decOutAmt.add(thisMonth.getOutamount()== null ? BigDecimal.valueOf(0) : BigDecimal.valueOf(thisMonth.getOutamount()));
            }

            // 获取上个月期末数量和金额
            BigDecimal decOpenQty = BigDecimal.valueOf(0);
            BigDecimal decOpenAmt = BigDecimal.valueOf(0);
            BusInvocarryoverPojo invocarryoverDB = busInvocarryoverMapper.getEntityByRecidAndGoodsid(lastMonth_Recid, selMonthDBPojo.getGoodsid(), tid);
            if (invocarryoverDB != null) {
                decOpenQty= BigDecimal.valueOf(invocarryoverDB.getCloseqty());
                decOpenAmt= BigDecimal.valueOf(invocarryoverDB.getCloseamount());
            }

            // 计算期末数量和金额 加上in 减去out
            BigDecimal closeQty =  decOpenQty
                    .add(decInQty )
                    .subtract(decOutQty);
            BigDecimal closeAmount = decOpenAmt
                    .add(decInAmt)
                    .subtract(decOutAmt);
            // 生成新的数据POJO

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusInvocarryoverPojo newInvo = new BusInvocarryoverPojo();
            newInvo.setId(selMonthDBPojo.getId());
            newInvo.setOpenqty(decOpenQty.doubleValue());
            newInvo.setOpenamount(decOpenAmt.doubleValue());
            newInvo.setInqty(decInQty.doubleValue());
            newInvo.setInamount(decInAmt.doubleValue());
            newInvo.setOutqty(decOutQty.doubleValue());
            newInvo.setOutamount(decOutAmt.doubleValue());
            newInvo.setCloseqty(closeQty.doubleValue());
            newInvo.setCloseamount(closeAmount.doubleValue());
            newInvo.setTenantid(tid);
            newInvo.setLister(loginUser.getRealname());
            newInvo.setListerid(loginUser.getUserid());
            newInvo.setModifydate(new Date());
            // 更新数据库
            BusInvocarryoverPojo update = busInvocarryoverService.update(newInvo);
            selMonthUpdate.add(update);
        }
        // 返回更新后的数据
        return selMonthUpdate;
    }


    /**
     * 根据传入的年份和月份，返回该月的开始日期（1号00:00:00）和结束日期（最后一天23:59:59.999）。
     * Date startDate = startAndEndDates[0];
     * Date endDate = startAndEndDates[1];
     *
     * @param year  指定的年份（如 2024）。
     * @param month 指定的月份（如 12，对应12月）。
     * @return 返回一个包含开始日期和结束日期的数组，
     * 第一个元素是该月的开始日期（00:00:00），
     * 第二个元素是该月的结束日期（23:59:59.999）。
     */
    public static Date[] getStartAndEndDateOfMonth(Integer year, Integer month) {
        // 参数检查
        if (year == null || month == null || month < 1 || month > 12) {
            throw new IllegalArgumentException("年份和月份必须有效");
        }
        Calendar calendar = Calendar.getInstance();
        // 设置为该年该月的1号，时分秒设为00:00:00
        calendar.set(year, month - 1, 1, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0); // 确保毫秒为0
        Date startDate = calendar.getTime();
        // 设置为该月的最后一天，时分秒设为23:59:59.999
        calendar.set(year, month - 1, calendar.getActualMaximum(Calendar.DAY_OF_MONTH), 23, 59, 59);
        calendar.set(Calendar.MILLISECOND, 999); // 设置毫秒为999
        Date endDate = calendar.getTime();
        return new Date[]{startDate, endDate};
    }


}
