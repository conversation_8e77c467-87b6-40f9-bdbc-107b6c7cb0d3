package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusDeposititemEntity;
import inks.service.std.sale.domain.pojo.BusDeposititemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预收款项目(BusDeposititem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-20 13:12:52
 */
 @Mapper
public interface BusDeposititemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDeposititemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDeposititemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusDeposititemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busDeposititemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusDeposititemEntity busDeposititemEntity);

    
    /**
     * 修改数据
     *
     * @param busDeposititemEntity 实例对象
     * @return 影响行数
     */
    int update(BusDeposititemEntity busDeposititemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

