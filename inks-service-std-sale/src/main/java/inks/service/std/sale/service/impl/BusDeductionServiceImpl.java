package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.sale.domain.BusDeductionEntity;
import inks.service.std.sale.domain.BusDeductionitemEntity;
import inks.service.std.sale.domain.pojo.BusAccountrecPojo;
import inks.service.std.sale.domain.pojo.BusDeductionPojo;
import inks.service.std.sale.domain.pojo.BusDeductionitemPojo;
import inks.service.std.sale.domain.pojo.BusDeductionitemdetailPojo;
import inks.service.std.sale.mapper.BusAccountrecMapper;
import inks.service.std.sale.mapper.BusDeductionMapper;
import inks.service.std.sale.mapper.BusDeductionitemMapper;
import inks.service.std.sale.service.BusDeductionService;
import inks.service.std.sale.service.BusDeductionitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static inks.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 销售扣款(BusDeduction)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-16 16:57:09
 */
@Service("busDeductionService")
public class BusDeductionServiceImpl implements BusDeductionService {
    @Resource
    private BusDeductionMapper busDeductionMapper;

    @Resource
    private BusDeductionitemMapper busDeductionitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusDeductionitemService busDeductionitemService;

    @Resource
    private BusAccountrecMapper busAccountrecMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDeductionPojo getEntity(String key, String tid) {
        return this.busDeductionMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDeductionitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeductionitemdetailPojo> lst = busDeductionMapper.getPageList(queryParam);
            PageInfo<BusDeductionitemdetailPojo> pageInfo = new PageInfo<BusDeductionitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDeductionPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusDeductionPojo busDeductionPojo = this.busDeductionMapper.getEntity(key, tid);
            //读取子表
            busDeductionPojo.setItem(busDeductionitemMapper.getList(busDeductionPojo.getId(), busDeductionPojo.getTenantid()));
            return busDeductionPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDeductionPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeductionPojo> lst = busDeductionMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busDeductionitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusDeductionPojo> pageInfo = new PageInfo<BusDeductionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDeductionPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeductionPojo> lst = busDeductionMapper.getPageTh(queryParam);
            PageInfo<BusDeductionPojo> pageInfo = new PageInfo<BusDeductionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busDeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDeductionPojo insert(BusDeductionPojo busDeductionPojo) {
//初始化NULL字段
        if (busDeductionPojo.getRefno() == null) busDeductionPojo.setRefno("");
        if (busDeductionPojo.getBilltype() == null) busDeductionPojo.setBilltype("");
        if (busDeductionPojo.getBilldate() == null) busDeductionPojo.setBilldate(new Date());
        if (busDeductionPojo.getBilltitle() == null) busDeductionPojo.setBilltitle("");
        if (busDeductionPojo.getGroupid() == null) busDeductionPojo.setGroupid("");
        if (busDeductionPojo.getOperator() == null) busDeductionPojo.setOperator("");
        if (busDeductionPojo.getTaxrate() == null) busDeductionPojo.setTaxrate(0);
        if (busDeductionPojo.getBilltaxamount() == null) busDeductionPojo.setBilltaxamount(0D);
        if (busDeductionPojo.getBillamount() == null) busDeductionPojo.setBillamount(0D);
        if (busDeductionPojo.getBilltaxtotal() == null) busDeductionPojo.setBilltaxtotal(0D);
        if (busDeductionPojo.getSummary() == null) busDeductionPojo.setSummary("");
        if (busDeductionPojo.getCreateby() == null) busDeductionPojo.setCreateby("");
        if (busDeductionPojo.getCreatebyid() == null) busDeductionPojo.setCreatebyid("");
        if (busDeductionPojo.getCreatedate() == null) busDeductionPojo.setCreatedate(new Date());
        if (busDeductionPojo.getLister() == null) busDeductionPojo.setLister("");
        if (busDeductionPojo.getListerid() == null) busDeductionPojo.setListerid("");
        if (busDeductionPojo.getModifydate() == null) busDeductionPojo.setModifydate(new Date());
        if (busDeductionPojo.getAssessor() == null) busDeductionPojo.setAssessor("");
        if (busDeductionPojo.getAssessorid() == null) busDeductionPojo.setAssessorid("");
        if (busDeductionPojo.getAssessdate() == null) busDeductionPojo.setAssessdate(new Date());
        if (busDeductionPojo.getItemcount() == null) busDeductionPojo.setItemcount(busDeductionPojo.getItem().size());
        if (busDeductionPojo.getDisannulcount() == null) busDeductionPojo.setDisannulcount(0);
        if (busDeductionPojo.getFinishcount() == null) busDeductionPojo.setFinishcount(0);
        if (busDeductionPojo.getPrintcount() == null) busDeductionPojo.setPrintcount(0);
        if (busDeductionPojo.getCustom1() == null) busDeductionPojo.setCustom1("");
        if (busDeductionPojo.getCustom2() == null) busDeductionPojo.setCustom2("");
        if (busDeductionPojo.getCustom3() == null) busDeductionPojo.setCustom3("");
        if (busDeductionPojo.getCustom4() == null) busDeductionPojo.setCustom4("");
        if (busDeductionPojo.getCustom5() == null) busDeductionPojo.setCustom5("");
        if (busDeductionPojo.getCustom6() == null) busDeductionPojo.setCustom6("");
        if (busDeductionPojo.getCustom7() == null) busDeductionPojo.setCustom7("");
        if (busDeductionPojo.getCustom8() == null) busDeductionPojo.setCustom8("");
        if (busDeductionPojo.getCustom9() == null) busDeductionPojo.setCustom9("");
        if (busDeductionPojo.getCustom10() == null) busDeductionPojo.setCustom10("");
        if (busDeductionPojo.getTenantid() == null) busDeductionPojo.setTenantid("");
        if (busDeductionPojo.getTenantname() == null) busDeductionPojo.setTenantname("");
        if (busDeductionPojo.getRevision() == null) busDeductionPojo.setRevision(0);

        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busDeductionPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDeductionPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止新建结账前单据");
        }
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusDeductionEntity busDeductionEntity = new BusDeductionEntity();
        BeanUtils.copyProperties(busDeductionPojo, busDeductionEntity);
        //设置id和新建日期
        busDeductionEntity.setId(id);
        busDeductionEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busDeductionMapper.insert(busDeductionEntity);
        //Item子表处理
        List<BusDeductionitemPojo> lst = busDeductionPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (BusDeductionitemPojo busDeductionitemPojo : lst) {
                //初始化item的NULL
                BusDeductionitemPojo itemPojo = this.busDeductionitemService.clearNull(busDeductionitemPojo);
                BusDeductionitemEntity busDeductionitemEntity = new BusDeductionitemEntity();
                BeanUtils.copyProperties(itemPojo, busDeductionitemEntity);
                //设置id和Pid
                busDeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busDeductionitemEntity.setPid(id);
                busDeductionitemEntity.setTenantid(busDeductionPojo.getTenantid());
                busDeductionitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busDeductionitemMapper.insert(busDeductionitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(busDeductionEntity.getId(), busDeductionEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busDeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDeductionPojo update(BusDeductionPojo busDeductionPojo) {

        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busDeductionPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDeductionPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止修改结账前单据");
        }
        BusDeductionPojo orgPojo = this.busDeductionMapper.getEntity(busDeductionPojo.getId(), busDeductionPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止修改结账前单据");
        }
        //主表更改
        BusDeductionEntity busDeductionEntity = new BusDeductionEntity();
        busDeductionPojo.setItemcount(busDeductionPojo.getItem().size());
        BeanUtils.copyProperties(busDeductionPojo, busDeductionEntity);
        this.busDeductionMapper.update(busDeductionEntity);
        if (busDeductionPojo.getItem() != null) {
            //Item子表处理
            List<BusDeductionitemPojo> lst = busDeductionPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busDeductionMapper.getDelItemIds(busDeductionPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    //读取待删除项目
                    BusDeductionitemPojo busDeductionitemPojo = this.busDeductionitemMapper.getEntity(lstDelIds.get(i), busDeductionEntity.getTenantid());
                    List<String> lstcite = getItemCiteBillName(lstDelIds.get(i), busDeductionitemPojo.getPid(), busDeductionEntity.getTenantid());
                    if (lstcite.size() > 0) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite.toString());
                    }
                    this.busDeductionitemMapper.delete(lstDelIds.get(i), busDeductionEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BusDeductionitemEntity busDeductionitemEntity = new BusDeductionitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        BusDeductionitemPojo itemPojo = this.busDeductionitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, busDeductionitemEntity);
                        //设置id和Pid
                        busDeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busDeductionitemEntity.setPid(busDeductionEntity.getId());  // 主表 id
                        busDeductionitemEntity.setTenantid(busDeductionPojo.getTenantid());   // 租户id
                        busDeductionitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busDeductionitemMapper.insert(busDeductionitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), busDeductionitemEntity);
                        busDeductionitemEntity.setTenantid(busDeductionPojo.getTenantid());
                        this.busDeductionitemMapper.update(busDeductionitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busDeductionEntity.getId(), busDeductionEntity.getTenantid());
    }


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        BusDeductionPojo busDeductionPojo = this.getBillEntity(key, tid);
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busDeductionPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDeductionPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            DateUtils.dateTime();
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止删除结账前单据");
        }
        //Item子表处理
        List<BusDeductionitemPojo> lst = busDeductionPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusDeductionitemPojo busDeductionitemPojo : lst) {
                // 检查是否被引用
                List<String> lstcite = getItemCiteBillName(busDeductionitemPojo.getId(), busDeductionitemPojo.getPid(), tid);
                if (!lstcite.isEmpty()) {
                    throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                }
                this.busDeductionitemMapper.delete(busDeductionitemPojo.getId(), tid);
            }
        }
         this.busDeductionMapper.delete(key, tid);
        return busDeductionPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param busDeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDeductionPojo approval(BusDeductionPojo busDeductionPojo) {
        //主表更改
        BusDeductionEntity busDeductionEntity = new BusDeductionEntity();
        BeanUtils.copyProperties(busDeductionPojo, busDeductionEntity);
        this.busDeductionMapper.approval(busDeductionEntity);
        //返回Bill实例
        return this.getBillEntity(busDeductionEntity.getId(), busDeductionEntity.getTenantid());
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.busDeductionMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public void updatePrintcount(BusDeductionPojo billPrintPojo) {
        this.busDeductionMapper.updatePrintcount(billPrintPojo);
    }
}
