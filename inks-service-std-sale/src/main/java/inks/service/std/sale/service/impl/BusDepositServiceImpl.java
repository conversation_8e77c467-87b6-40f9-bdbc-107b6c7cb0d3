package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.security.annotation.InksConfig;
import inks.service.std.sale.domain.BusDepositEntity;
import inks.service.std.sale.domain.BusDepositcashEntity;
import inks.service.std.sale.domain.BusDeposititemEntity;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.mapper.*;
import inks.service.std.sale.service.BusDepositService;
import inks.service.std.sale.service.BusDepositcashService;
import inks.service.std.sale.service.BusDeposititemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 预收款(BusDeposit)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 15:31:25
 */
@Service("busDepositService")
public class BusDepositServiceImpl implements BusDepositService {
    @Resource
    private BusDepositMapper busDepositMapper;

    @Resource
    private BusDeposititemMapper busDeposititemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusDeposititemService busDeposititemService;

    @Resource
    private BusDepositcashMapper busDepositcashMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusDepositcashService busDepositcashService;
    @Resource
    private BusMachiningMapper busMachiningMapper;

    @Resource
    private BusAccountrecMapper busAccountrecMapper;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDepositPojo getEntity(String key, String tid) {
        return this.busDepositMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDeposititemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeposititemdetailPojo> lst = busDepositMapper.getPageList(queryParam);
            PageInfo<BusDeposititemdetailPojo> pageInfo = new PageInfo<BusDeposititemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<BusDepositcashdetailPojo> getCashPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDepositcashdetailPojo> lst = busDepositMapper.getCashPageList(queryParam);
            PageInfo<BusDepositcashdetailPojo> pageInfo = new PageInfo<BusDepositcashdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDepositPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusDepositPojo busDepositPojo = this.busDepositMapper.getEntity(key, tid);
            //读取子表
            busDepositPojo.setItem(busDeposititemMapper.getList(busDepositPojo.getId(), busDepositPojo.getTenantid()));
            //读取Cash
            busDepositPojo.setCash(busDepositcashMapper.getList(busDepositPojo.getId(), busDepositPojo.getTenantid()));
            return busDepositPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDepositPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDepositPojo> lst = busDepositMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (BusDepositPojo busDepositPojo : lst) {
                busDepositPojo.setItem(busDeposititemMapper.getList(busDepositPojo.getId(), busDepositPojo.getTenantid()));
                busDepositPojo.setCash(busDepositcashMapper.getList(busDepositPojo.getId(), busDepositPojo.getTenantid()));
            }
            PageInfo<BusDepositPojo> pageInfo = new PageInfo<BusDepositPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDepositPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDepositPojo> lst = busDepositMapper.getPageTh(queryParam);
            PageInfo<BusDepositPojo> pageInfo = new PageInfo<BusDepositPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busDepositPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    @InksConfig
    public BusDepositPojo insert(BusDepositPojo busDepositPojo, String token) {
        String tid = busDepositPojo.getTenantid();
//初始化NULL字段
        if (busDepositPojo.getRefno() == null) busDepositPojo.setRefno("");
        if (busDepositPojo.getBilltype() == null) busDepositPojo.setBilltype("");
        if (busDepositPojo.getBilltitle() == null) busDepositPojo.setBilltitle("");
        if (busDepositPojo.getBilldate() == null) busDepositPojo.setBilldate(new Date());
        if (busDepositPojo.getGroupid() == null) busDepositPojo.setGroupid("");
        if (busDepositPojo.getBillamount() == null) busDepositPojo.setBillamount(0D);
        if (busDepositPojo.getOperator() == null) busDepositPojo.setOperator("");
        if (busDepositPojo.getCitecode() == null) busDepositPojo.setCitecode("");
        if (busDepositPojo.getOutamount() == null) busDepositPojo.setOutamount(0D);
        if (busDepositPojo.getReturnuid() == null) busDepositPojo.setReturnuid("");
        if (busDepositPojo.getOrguid() == null) busDepositPojo.setOrguid("");
        if (busDepositPojo.getSummary() == null) busDepositPojo.setSummary("");
        if (busDepositPojo.getCreateby() == null) busDepositPojo.setCreateby("");
        if (busDepositPojo.getCreatebyid() == null) busDepositPojo.setCreatebyid("");
        if (busDepositPojo.getCreatedate() == null) busDepositPojo.setCreatedate(new Date());
        if (busDepositPojo.getLister() == null) busDepositPojo.setLister("");
        if (busDepositPojo.getListerid() == null) busDepositPojo.setListerid("");
        if (busDepositPojo.getModifydate() == null) busDepositPojo.setModifydate(new Date());
        if (busDepositPojo.getAssessor() == null) busDepositPojo.setAssessor("");
        if (busDepositPojo.getAssessorid() == null) busDepositPojo.setAssessorid("");
        if (busDepositPojo.getAssessdate() == null) busDepositPojo.setAssessdate(new Date());
        if (busDepositPojo.getFmdocmark() == null) busDepositPojo.setFmdocmark(0);
        if (busDepositPojo.getFmdoccode() == null) busDepositPojo.setFmdoccode("");
        if (busDepositPojo.getLockedamount()==null) busDepositPojo.setLockedamount(0D);
        if (busDepositPojo.getCustom1() == null) busDepositPojo.setCustom1("");
        if (busDepositPojo.getWorkdate() == null) busDepositPojo.setWorkdate(new Date());
        if (busDepositPojo.getCustom2() == null) busDepositPojo.setCustom2("");
        if (busDepositPojo.getCustom3() == null) busDepositPojo.setCustom3("");
        if (busDepositPojo.getCustom4() == null) busDepositPojo.setCustom4("");
        if (busDepositPojo.getCustom5() == null) busDepositPojo.setCustom5("");
        if (busDepositPojo.getCustom6() == null) busDepositPojo.setCustom6("");
        if (busDepositPojo.getCustom7() == null) busDepositPojo.setCustom7("");
        if (busDepositPojo.getCustom8() == null) busDepositPojo.setCustom8("");
        if (busDepositPojo.getCustom9() == null) busDepositPojo.setCustom9("");
        if (busDepositPojo.getCustom10() == null) busDepositPojo.setCustom10("");
        if (tid == null) busDepositPojo.setTenantid("");
        if (busDepositPojo.getRevision() == null) busDepositPojo.setRevision(0);
        //Item子表处理
        List<BusDeposititemPojo> lst = busDepositPojo.getItem();
        // 结账检查
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDepositPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止新建结账前单据");
        }
        //销售订单预收额不能大于销售订单金额
        // 读取指定系统参数 是否允许超订单预收参数 不为true时才检查
        String configValue = systemFeignService.getConfigValue("module.sale.depositverflow", tid, token).getData();
        if (!Objects.equals(configValue, "true") && lst != null) {
            for (BusDeposititemPojo item : lst) {
                if (StringUtils.isNotBlank(item.getMachbillid())) {
                    BusMachiningPojo machDB = this.busMachiningMapper.getEntity(item.getMachbillid(), tid);
                    if (machDB != null) {
                        BigDecimal itemAmount = BigDecimal.valueOf(item.getAmount());
                        BigDecimal remainingPreAmount = BigDecimal.valueOf(machDB.getBilltaxamount()).subtract(BigDecimal.valueOf(machDB.getAdvaamount()));
                        if (itemAmount.compareTo(remainingPreAmount) > 0) {
                            throw new RuntimeException("预收金额" + item.getAmount() + "不能大于销售订单剩余可预收金额" + remainingPreAmount);
                        }
                    }
                }
            }
        }


        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusDepositEntity busDepositEntity = new BusDepositEntity();
        BeanUtils.copyProperties(busDepositPojo, busDepositEntity);
        //设置id和新建日期
        busDepositEntity.setId(id);
        busDepositEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busDepositMapper.insert(busDepositEntity);


        if (lst != null) {
            //循环每个item子表
            for (BusDeposititemPojo busDeposititemPojo : lst) {
                //初始化item的NULL
                BusDeposititemPojo itemPojo = this.busDeposititemService.clearNull(busDeposititemPojo);
                BusDeposititemEntity busDeposititemEntity = new BusDeposititemEntity();
                BeanUtils.copyProperties(itemPojo, busDeposititemEntity);
                //设置id和Pid
                busDeposititemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busDeposititemEntity.setPid(id);
                busDeposititemEntity.setTenantid(tid);
                busDeposititemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busDeposititemMapper.insert(busDeposititemEntity);
                if (!"".equals(busDeposititemPojo.getMachbillid())) {
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(busDeposititemPojo.getMachbillid(), tid);
                    this.busDepositMapper.updateMachItemAvgFirstAmt(busDeposititemPojo.getMachbillid(), tid);
                }
            }
        }

        //Cash子表处理
        List<BusDepositcashPojo> lstcash = busDepositPojo.getCash();
        if (lstcash != null) {
            //循环每个item子表
            for (BusDepositcashPojo busDepositcashPojo : lstcash) {
                //初始化item的NULL
                BusDepositcashPojo cashPojo = this.busDepositcashService.clearNull(busDepositcashPojo);
                BusDepositcashEntity busDepositcashEntity = new BusDepositcashEntity();
                BeanUtils.copyProperties(cashPojo, busDepositcashEntity);
                //设置id和Pid
                busDepositcashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busDepositcashEntity.setPid(id);
                busDepositcashEntity.setTenantid(tid);
                busDepositcashEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busDepositcashMapper.insert(busDepositcashEntity);
                // 同步出纳账户金额
                this.busDepositMapper.updateCashAmount(busDepositcashEntity.getCashaccid(), busDepositcashEntity.getAmount(), tid);
            }
        }

        //返回Bill实例
        return this.getBillEntity(busDepositEntity.getId(), busDepositEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busDepositPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDepositPojo update(BusDepositPojo busDepositPojo, String token) {
        String tid = busDepositPojo.getTenantid();
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDepositPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止修改结账前单据");
        }
        BusDepositPojo orgPojo = this.busDepositMapper.getEntity(busDepositPojo.getId(), tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止修改结账前单据");
        }
        //Item子表处理
        List<BusDeposititemPojo> lst = busDepositPojo.getItem();
        //销售订单预收额不能大于销售订单金额
        // 读取指定系统参数 是否允许超订单预收参数 不为true时才检查
        String configValue = systemFeignService.getConfigValue("module.sale.depositverflow", tid, token).getData();
        if (!Objects.equals(configValue, "true") && lst != null) {
            for (BusDeposititemPojo item : lst) {
                BigDecimal orgAmount = BigDecimal.ZERO;
                if (StringUtils.isNotBlank(item.getId())) {
                    BusDeposititemPojo orgItem = this.busDeposititemMapper.getEntity(item.getId(), tid);
                    orgAmount = BigDecimal.valueOf(orgItem.getAmount());
                }
                if (StringUtils.isNotBlank(item.getMachbillid())) {
                    BusMachiningPojo machDB = this.busMachiningMapper.getEntity(item.getMachbillid(), tid);
                    if (machDB != null) {
                        BigDecimal itemAmount = BigDecimal.valueOf(item.getAmount());
                        BigDecimal remainingPreAmount = BigDecimal.valueOf(machDB.getBilltaxamount()).subtract(BigDecimal.valueOf(machDB.getAdvaamount()));
                        if (itemAmount.subtract(orgAmount).compareTo(remainingPreAmount) > 0) {
                            throw new RuntimeException("预收金额" + item.getAmount() + "不能大于销售订单剩余可预收金额" + remainingPreAmount.add(orgAmount));
                        }
                    }
                }
            }
        }

        //主表更改
        BusDepositEntity busDepositEntity = new BusDepositEntity();
        BeanUtils.copyProperties(busDepositPojo, busDepositEntity);
        this.busDepositMapper.update(busDepositEntity);

        //获取被删除的Item
        List<String> lstDelIds = busDepositMapper.getDelItemIds(busDepositPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                BusDeposititemPojo dpPojo = this.busDeposititemMapper.getEntity(lstDelIds.get(i), tid);
                this.busDeposititemMapper.delete(lstDelIds.get(i), busDepositEntity.getTenantid());
                if (!"".equals(dpPojo.getMachbillid())) {
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(dpPojo.getMachbillid(), tid);
                    this.busDepositMapper.updateMachItemAvgFirstAmt(lst.get(i).getMachbillid(), tid);
                }
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (BusDeposititemPojo busDeposititemPojo : lst) {
                BusDeposititemEntity busDeposititemEntity = new BusDeposititemEntity();
                if ("".equals(busDeposititemPojo.getId()) || busDeposititemPojo.getId() == null) {
                    //初始化item的NULL
                    BusDeposititemPojo itemPojo = this.busDeposititemService.clearNull(busDeposititemPojo);
                    BeanUtils.copyProperties(itemPojo, busDeposititemEntity);
                    //设置id和Pid
                    busDeposititemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busDeposititemEntity.setPid(busDepositEntity.getId());  // 主表 id
                    busDeposititemEntity.setTenantid(tid);   // 租户id
                    busDeposititemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busDeposititemMapper.insert(busDeposititemEntity);
                } else {
                    BeanUtils.copyProperties(busDeposititemPojo, busDeposititemEntity);
                    busDeposititemEntity.setTenantid(tid);
                    this.busDeposititemMapper.update(busDeposititemEntity);
                }
                if (!"".equals(busDeposititemPojo.getMachbillid())) {
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(busDeposititemPojo.getMachbillid(), tid);
                    this.busDepositMapper.updateMachItemAvgFirstAmt(busDeposititemPojo.getMachbillid(), tid);
                }
            }
        }

        //Cash子表处理
        List<BusDepositcashPojo> lstcash = busDepositPojo.getCash();
        //获取被删除的Item
        List<String> lstcashDelIds = busDepositMapper.getDelCashIds(busDepositPojo);
        if (lstcashDelIds != null) {
            //循环每个删除item子表
            for (String lstcashDelId : lstcashDelIds) {
                BusDepositcashPojo delPojo = this.busDepositcashMapper.getEntity(lstcashDelId, busDepositEntity.getTenantid());
                this.busDepositcashMapper.delete(lstcashDelId, busDepositEntity.getTenantid());
                // 同步出纳账户金额
                this.busDepositMapper.updateCashAmount(delPojo.getCashaccid(), 0 - delPojo.getAmount(), tid);
            }
        }
        if (lstcash != null) {
            //循环每个item子表
            for (BusDepositcashPojo busDepositcashPojo : lstcash) {
                BusDepositcashEntity busDepositcashEntity = new BusDepositcashEntity();
                if (Objects.equals(busDepositcashPojo.getId(), "") || busDepositcashPojo.getId() == null) {
                    //初始化item的NULL
                    BusDepositcashPojo cashPojo = this.busDepositcashService.clearNull(busDepositcashPojo);
                    BeanUtils.copyProperties(cashPojo, busDepositcashEntity);
                    //设置id和Pid
                    busDepositcashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busDepositcashEntity.setPid(busDepositEntity.getId());  // 主表 id
                    busDepositcashEntity.setTenantid(tid);   // 租户id
                    busDepositcashEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busDepositcashMapper.insert(busDepositcashEntity);
                    // 同步出纳账户金额
                    this.busDepositMapper.updateCashAmount(busDepositcashEntity.getCashaccid(), busDepositcashEntity.getAmount(), tid);
                } else {
                    BeanUtils.copyProperties(busDepositcashPojo, busDepositcashEntity);
                    busDepositcashEntity.setTenantid(tid);
                    BusDepositcashPojo depositCashDB = this.busDepositcashMapper.getEntity(busDepositcashPojo.getId(), busDepositEntity.getTenantid());
                    this.busDepositcashMapper.update(busDepositcashEntity);
                    // 同步出纳账户金额 先减少之前金额，再加上本次修改后的
                    this.busDepositMapper.updateCashAmount(busDepositcashEntity.getCashaccid(), busDepositcashEntity.getAmount() - depositCashDB.getAmount(), tid);
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(busDepositEntity.getId(), busDepositEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        BusDepositPojo busDepositPojo = this.getBillEntity(key, tid);
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busDepositPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDepositPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止删除结账前单据");
        }
        //Item子表处理
        List<BusDeposititemPojo> lst = busDepositPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusDeposititemPojo busDeposititemPojo : lst) {
                BusDeposititemPojo delPojo = this.busDeposititemMapper.getEntity(busDeposititemPojo.getId(), busDepositPojo.getTenantid());
                this.busDeposititemMapper.delete(busDeposititemPojo.getId(), tid);
                if (!"".equals(delPojo.getMachbillid())) {
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(delPojo.getMachbillid(), busDepositPojo.getTenantid());
                    this.busDepositMapper.updateMachItemAvgFirstAmt(busDeposititemPojo.getMachbillid(), busDepositPojo.getTenantid());
                }
            }
        }

        //Cash子表处理  Eric ********
        List<BusDepositcashPojo> lstcash = busDepositPojo.getCash();
        if (lstcash != null) {
            //循环每个删除item子表
            for (BusDepositcashPojo busDepositcashPojo : lstcash) {
                BusDepositcashPojo delPojo = this.busDepositcashMapper.getEntity(busDepositcashPojo.getId(), tid);
                this.busDepositcashMapper.delete(busDepositcashPojo.getId(), tid);
                // 同步出纳账户金额
                this.busDepositMapper.updateCashAmount(delPojo.getCashaccid(), 0 - delPojo.getAmount(), busDepositPojo.getTenantid());
            }
        }
        this.busDepositMapper.delete(key, tid);
        return busDepositPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param busDepositPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDepositPojo approval(BusDepositPojo busDepositPojo) {
        //主表更改
        BusDepositEntity busDepositEntity = new BusDepositEntity();
        BeanUtils.copyProperties(busDepositPojo, busDepositEntity);
        this.busDepositMapper.approval(busDepositEntity);
        //返回Bill实例
        return this.getBillEntity(busDepositEntity.getId(), busDepositEntity.getTenantid());
    }

}
