package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusReceiptitemPojo;

import java.util.List;
/**
 * 相关发票(BusReceiptitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-20 13:13:56
 */
public interface BusReceiptitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceiptitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusReceiptitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusReceiptitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busReceiptitemPojo 实例对象
     * @return 实例对象
     */
    BusReceiptitemPojo insert(BusReceiptitemPojo busReceiptitemPojo);

    /**
     * 修改数据
     *
     * @param busReceiptitempojo 实例对象
     * @return 实例对象
     */
    BusReceiptitemPojo update(BusReceiptitemPojo busReceiptitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busReceiptitempojo 实例对象
     * @return 实例对象
     */
    BusReceiptitemPojo clearNull(BusReceiptitemPojo busReceiptitempojo);
}
