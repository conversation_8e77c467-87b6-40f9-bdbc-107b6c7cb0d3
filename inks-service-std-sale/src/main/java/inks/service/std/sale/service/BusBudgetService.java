package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusBudgetPojo;
import inks.service.std.sale.domain.pojo.BusBudgetitemdetailPojo;

/**
 * 成本预算(BusBudget)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-02 09:26:57
 */
public interface BusBudgetService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusBudgetPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusBudgetitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusBudgetPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusBudgetPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusBudgetPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busBudgetPojo 实例对象
     * @return 实例对象
     */
    BusBudgetPojo insert(BusBudgetPojo busBudgetPojo);

    /**
     * 修改数据
     *
     * @param busBudgetpojo 实例对象
     * @return 实例对象
     */
    BusBudgetPojo update(BusBudgetPojo busBudgetpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key,String tid);

                                                                                                                                       /**
     * 审核数据
     *
     * @param busBudgetPojo 实例对象
     * @return 实例对象
     */
     BusBudgetPojo approval(BusBudgetPojo busBudgetPojo);
                                                                                }
