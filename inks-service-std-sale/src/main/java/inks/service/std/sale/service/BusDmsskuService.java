package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDmsskuPojo;

/**
 * Dms商品Sku(BusDmssku)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-29 13:08:57
 */
public interface BusDmsskuService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDmsskuPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDmsskuPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDmsskuPojo 实例对象
     * @return 实例对象
     */
    BusDmsskuPojo insert(BusDmsskuPojo busDmsskuPojo);

    /**
     * 修改数据
     *
     * @param busDmsskupojo 实例对象
     * @return 实例对象
     */
    BusDmsskuPojo update(BusDmsskuPojo busDmsskupojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                                                                                                                         }
