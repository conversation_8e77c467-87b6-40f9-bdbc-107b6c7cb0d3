package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusCostpartgroupPojo;
import inks.service.std.sale.service.BusCostpartgroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 成本组件(Bus_CostPart)表控制层
 *
 * <AUTHOR>
 * @since 2022-06-06 07:53:21
 */
@RestController
@RequestMapping("D01M02S1")
@Api(tags = "D01M02S1:核价单组件")
public class D01M02S1Controller extends BusCostpartController {

    /**
     * 服务对象
     */
    @Resource
    private BusCostpartgroupService busCostpartgroupService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取组件分组详细信息", notes = "获取组件分组详细信息", produces = "application/json")
    @RequestMapping(value = "/getGroupEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_CostPart.List")
    public R<BusCostpartgroupPojo> getGroupEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busCostpartgroupService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getGroupPageList", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Bus_CostPart.List")
    public R<PageInfo<BusCostpartgroupPojo>> getGroupPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_CostPartGroup.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busCostpartgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询全部分组", notes = "查询全部分组", produces = "application/json")
    @RequestMapping(value = "/getGroupList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_CostPart.List")
    public R<List<BusCostpartgroupPojo>> getGroupList() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busCostpartgroupService.getList(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增组件分组", notes = "新增组件分组", produces = "application/json")
    @RequestMapping(value = "/createGroup", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_CostPart.Add")
    public R<BusCostpartgroupPojo> createGroup(@RequestBody String json) {
        try {
            BusCostpartgroupPojo busCostpartgroupPojo = JSONArray.parseObject(json, BusCostpartgroupPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCostpartgroupPojo.setCreateby(loginUser.getRealName());   // 创建者
            busCostpartgroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busCostpartgroupPojo.setCreatedate(new Date());   // 创建时间
            busCostpartgroupPojo.setLister(loginUser.getRealname());   // 制表
            busCostpartgroupPojo.setListerid(loginUser.getUserid());    // 制表id
            busCostpartgroupPojo.setModifydate(new Date());   //修改时间
            busCostpartgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busCostpartgroupService.insert(busCostpartgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改组件分组", notes = "修改组件分组", produces = "application/json")
    @RequestMapping(value = "/updateGroup", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_CostPart.Edit")
    public R<BusCostpartgroupPojo> updateGroup(@RequestBody String json) {
        try {
            BusCostpartgroupPojo busCostpartgroupPojo = JSONArray.parseObject(json, BusCostpartgroupPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCostpartgroupPojo.setLister(loginUser.getRealname());   // 制表
            busCostpartgroupPojo.setListerid(loginUser.getUserid());    // 制表id
            busCostpartgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            busCostpartgroupPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.busCostpartgroupService.update(busCostpartgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除组件分组", notes = "删除组件分组", produces = "application/json")
    @RequestMapping(value = "/deleteGroup", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_CostPart.Delete")
    @OperLog(title = "删除组件分组")
    public R<Integer> deleteGroup(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busCostpartgroupService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
