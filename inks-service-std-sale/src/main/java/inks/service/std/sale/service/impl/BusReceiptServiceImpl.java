package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.sale.domain.BusReceiptEntity;
import inks.service.std.sale.domain.BusReceiptcashEntity;
import inks.service.std.sale.domain.BusReceiptitemEntity;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.mapper.*;
import inks.service.std.sale.service.BusReceiptService;
import inks.service.std.sale.service.BusReceiptcashService;
import inks.service.std.sale.service.BusReceiptitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 收款单据(BusReceipt)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 15:34:47
 */
@Service("busReceiptService")
public class BusReceiptServiceImpl implements BusReceiptService {
    @Resource
    private BusReceiptMapper busReceiptMapper;

    @Resource
    private BusReceiptitemMapper busReceiptitemMapper;

    @Resource
    private BusDelieryMapper busDelieryMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusReceiptitemService busReceiptitemService;


    @Resource
    private BusReceiptcashMapper busReceiptcashMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusReceiptcashService busReceiptcashService;
    @Resource
    private BusInvoiceMapper busInvoiceMapper;

    @Resource
    private BusAccountrecMapper busAccountrecMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusReceiptPojo getEntity(String key, String tid) {
        return this.busReceiptMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceiptitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceiptitemdetailPojo> lst = busReceiptMapper.getPageList(queryParam);
            PageInfo<BusReceiptitemdetailPojo> pageInfo = new PageInfo<BusReceiptitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusReceiptPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusReceiptPojo busReceiptPojo = this.busReceiptMapper.getEntity(key, tid);
            //读取子表
            busReceiptPojo.setItem(busReceiptitemMapper.getList(busReceiptPojo.getId(), busReceiptPojo.getTenantid()));
            //读取Cash
            busReceiptPojo.setCash(busReceiptcashMapper.getList(busReceiptPojo.getId(), busReceiptPojo.getTenantid()));
            return busReceiptPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceiptPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceiptPojo> lst = busReceiptMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busReceiptitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setCash(busReceiptcashMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusReceiptPojo> pageInfo = new PageInfo<BusReceiptPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceiptPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceiptPojo> lst = busReceiptMapper.getPageTh(queryParam);
            PageInfo<BusReceiptPojo> pageInfo = new PageInfo<BusReceiptPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busReceiptPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusReceiptPojo insert(BusReceiptPojo busReceiptPojo) {
//初始化NULL字段
        cleanNull(busReceiptPojo);
        String tid = busReceiptPojo.getTenantid();
        // 结账检查
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busReceiptPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止新建结账前单据");
        }
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusReceiptEntity busReceiptEntity = new BusReceiptEntity();
        BeanUtils.copyProperties(busReceiptPojo, busReceiptEntity);

        String billType = busReceiptEntity.getBilltype();
        //设置id和新建日期
        busReceiptEntity.setId(id);
        busReceiptEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busReceiptMapper.insert(busReceiptEntity);

        // 如果是红冲单 同步原始单据的红冲标志
        if (billType.contains("红冲")) {
            this.busReceiptMapper.updateOrgReturn(busReceiptPojo.getOrguid(), busReceiptPojo.getRefno(), tid);
        }

        //Item子表处理
        List<BusReceiptitemPojo> lst = busReceiptPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                BusReceiptitemPojo busReceiptitemPojo = lst.get(i);
                //初始化item的NULL
                BusReceiptitemPojo itemPojo = this.busReceiptitemService.clearNull(busReceiptitemPojo);
                BusReceiptitemEntity busReceiptitemEntity = new BusReceiptitemEntity();
                BeanUtils.copyProperties(itemPojo, busReceiptitemEntity);
                //设置id和Pid
                busReceiptitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busReceiptitemEntity.setPid(id);
                busReceiptitemEntity.setTenantid(tid);
                busReceiptitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busReceiptitemMapper.insert(busReceiptitemEntity);
                // 销售收款
                if (billType.equals("销售收款")) {
                    // 同步销售发票,销售订单的收款,预收款,总收款等(主表Receipted共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(busReceiptitemPojo.getInvoid(), busReceiptitemPojo.getInvobillcode(), tid);
//                    this.busReceiptMapper.updateBusInvoFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), busReceiptPojo.getTenantid());
                    // 超数检查
                    BusInvoicePojo busInvoicePojo = this.busInvoiceMapper.getEntity(busReceiptitemPojo.getInvoid(), tid);
                    if (busInvoicePojo != null) {
                        if (busInvoicePojo.getReceipted() > busInvoicePojo.getTaxamount()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,收款总额:" + busInvoicePojo.getReceipted() + "超出应收额:" + busInvoicePojo.getTaxamount());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + busReceiptitemPojo.getInvobillcode());
                    }
                } else if (billType.equals("单据收款") || billType.equals("收款红冲")) {
                    this.busReceiptMapper.updateDeliFinish(busReceiptitemPojo.getInvoid(), busReceiptitemPojo.getInvobillcode(), tid);
                    // 超数检查
                    BusDelieryPojo busDelieryPojo = this.busDelieryMapper.getEntity(busReceiptitemPojo.getInvoid(), tid);
                    if (busDelieryPojo != null) {
                        if (busDelieryPojo.getBillreceived() > busDelieryPojo.getBilltaxamount()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,收款总额:" + busDelieryPojo.getBillreceived() + "超出单据应收额:" + busDelieryPojo.getBilltaxamount());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + busReceiptitemPojo.getInvobillcode());
                    }
                }
            }
        }

        //Cash子表处理
        List<BusReceiptcashPojo> lstcash = busReceiptPojo.getCash();
        if (lstcash != null) {
            //循环每个item子表
            for (BusReceiptcashPojo busReceiptcashPojo : lstcash) {
                //初始化item的NULL
                BusReceiptcashPojo cashPojo = this.busReceiptcashService.clearNull(busReceiptcashPojo);
                BusReceiptcashEntity busReceiptcashEntity = new BusReceiptcashEntity();
                BeanUtils.copyProperties(cashPojo, busReceiptcashEntity);
                //设置id和Pid
                busReceiptcashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busReceiptcashEntity.setPid(id);
                busReceiptcashEntity.setTenantid(tid);
                busReceiptcashEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busReceiptcashMapper.insert(busReceiptcashEntity);
                // 同步出纳账户金额
                this.busReceiptMapper.updateCashAmount(busReceiptcashEntity.getCashaccid(), busReceiptcashEntity.getAmount(), tid);

            }
        }

        //返回Bill实例
        return this.getBillEntity(busReceiptEntity.getId(), busReceiptEntity.getTenantid());

    }


    /**
     * 修改数据
     *
     * @param busReceiptPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusReceiptPojo update(BusReceiptPojo busReceiptPojo) {
        String tid = busReceiptPojo.getTenantid();
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busReceiptPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止修改结账前单据");
        }
        BusReceiptPojo orgPojo = this.busReceiptMapper.getEntity(busReceiptPojo.getId(), tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止修改结账前单据");
        }
        //主表更改
        BusReceiptEntity busReceiptEntity = new BusReceiptEntity();
        BeanUtils.copyProperties(busReceiptPojo, busReceiptEntity);
        this.busReceiptMapper.update(busReceiptEntity);
        //Item子表处理
        List<BusReceiptitemPojo> lst = busReceiptPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = busReceiptMapper.getDelItemIds(busReceiptPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (String lstDelId : lstDelIds) {
                BusReceiptitemPojo busReceiptitemPojo = this.busReceiptitemMapper.getEntity(lstDelId, busReceiptEntity.getTenantid());
                this.busReceiptitemMapper.delete(lstDelId, busReceiptEntity.getTenantid());
                if (busReceiptEntity.getBilltype().equals("销售收款")) {
                    // 同步销售发票,销售订单的收款,预收款,总收款等(主表Receipted共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(busReceiptitemPojo.getInvoid(), busReceiptitemPojo.getInvobillcode(), tid);
//                    this.busReceiptMapper.updateBusInvoFinish(busReceiptitemPojo.getInvoid(), busReceiptitemPojo.getInvobillcode(), busReceiptPojo.getTenantid());
                }
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                BusReceiptitemEntity busReceiptitemEntity = new BusReceiptitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    BusReceiptitemPojo itemPojo = this.busReceiptitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, busReceiptitemEntity);
                    //设置id和Pid
                    busReceiptitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busReceiptitemEntity.setPid(busReceiptEntity.getId());  // 主表 id
                    busReceiptitemEntity.setTenantid(tid);   // 租户id
                    busReceiptitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busReceiptitemMapper.insert(busReceiptitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), busReceiptitemEntity);
                    busReceiptitemEntity.setTenantid(tid);
                    this.busReceiptitemMapper.update(busReceiptitemEntity);
                }
                // 销售收款
                if (busReceiptEntity.getBilltype().equals("销售收款")) {
                    // 同步销售发票,销售订单的收款,预收款,总收款等(主表Receipted共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
//                    // 同步发票金额
//                    this.busReceiptMapper.updateBusInvoFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), busReceiptPojo.getTenantid());
                    // 超数检查
                    BusInvoicePojo busInvoicePojo = this.busInvoiceMapper.getEntity(lst.get(i).getInvoid(), tid);
                    if (busInvoicePojo != null) {
                        if (busInvoicePojo.getReceipted() > busInvoicePojo.getTaxamount()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,收款总额:" + busInvoicePojo.getReceipted() + "超出应收额:" + busInvoicePojo.getTaxamount());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getInvobillcode());
                    }
                } else if (busReceiptEntity.getBilltype().equals("单据收款") || busReceiptEntity.getBilltype().equals("收款红冲")) {
                    this.busReceiptMapper.updateDeliFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
                    // 超数检查
                    BusDelieryPojo busDelieryPojo = this.busDelieryMapper.getEntity(lst.get(i).getInvoid(), tid);
                    if (busDelieryPojo != null) {
                        if (busDelieryPojo.getBillreceived() > busDelieryPojo.getBilltaxamount()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,收款总额:" + busDelieryPojo.getBillreceived() + "超出单据应收额:" + busDelieryPojo.getBilltaxamount());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getInvobillcode());
                    }
                }
            }
        }

        //Cash子表处理
        List<BusReceiptcashPojo> lstcash = busReceiptPojo.getCash();
        //获取被删除的Item
        List<String> lstcashDelIds = busReceiptMapper.getDelCashIds(busReceiptPojo);
        if (lstcashDelIds != null) {
            //循环每个删除item子表
            for (String lstcashDelId : lstcashDelIds) {
                BusReceiptcashPojo delPojo = this.busReceiptcashMapper.getEntity(lstcashDelId, tid);
                this.busReceiptcashMapper.delete(lstcashDelId, tid);
                // 同步出纳账户金额
                this.busReceiptMapper.updateCashAmount(delPojo.getCashaccid(), 0 - delPojo.getAmount(), tid);
            }
        }
        if (lstcash != null) {
            //循环每个item子表
            for (int i = 0; i < lstcash.size(); i++) {
                BusReceiptcashEntity busReceiptcashEntity = new BusReceiptcashEntity();
                if (Objects.equals(lstcash.get(i).getId(), "") || lstcash.get(i).getId() == null) {
                    //初始化item的NULL
                    BusReceiptcashPojo cashPojo = this.busReceiptcashService.clearNull(lstcash.get(i));
                    BeanUtils.copyProperties(cashPojo, busReceiptcashEntity);
                    //设置id和Pid
                    busReceiptcashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busReceiptcashEntity.setPid(busReceiptEntity.getId());  // 主表 id
                    busReceiptcashEntity.setTenantid(tid);   // 租户id
                    busReceiptcashEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busReceiptcashMapper.insert(busReceiptcashEntity);
                    this.busReceiptMapper.updateCashAmount(busReceiptcashEntity.getCashaccid(), busReceiptcashEntity.getAmount(), tid);
                } else {
                    BeanUtils.copyProperties(lstcash.get(i), busReceiptcashEntity);
                    busReceiptcashEntity.setTenantid(tid);
                    // 先减少之前金额
                    BusReceiptcashPojo receiptCashBD = this.busReceiptcashMapper.getEntity(lstcash.get(i).getId(), tid);
                    this.busReceiptcashMapper.update(busReceiptcashEntity);
                    // 同步出纳账户金额 先减少之前金额，再加上本次修改后的
                    this.busReceiptMapper.updateCashAmount(busReceiptcashEntity.getCashaccid(), busReceiptcashEntity.getAmount() - receiptCashBD.getAmount(), tid);
                    // 出纳账户余额检查
                    Double cashAmount = this.busReceiptMapper.getCashAmount(busReceiptcashEntity.getCashaccid(), tid);
                    if (cashAmount < 0) {
                        int Rowno = i + 1;
                        throw new RuntimeException(Rowno + "行,账户余额不足");
                    }
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(busReceiptEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        BusReceiptPojo busReceiptPojo = this.getBillEntity(key, tid);
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busReceiptPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busReceiptPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止删除结账前单据");
        }
        // 如果是红冲单 同步去掉原始单据的红冲标志
        if (busReceiptPojo.getBilltype().contains("红冲")) {
            this.busReceiptMapper.updateOrgReturn(busReceiptPojo.getOrguid(), "", tid);
        }
        //Item子表处理
        List<BusReceiptitemPojo> lst = busReceiptPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusReceiptitemPojo busReceiptitemPojo : lst) {
                this.busReceiptitemMapper.delete(busReceiptitemPojo.getId(), tid);
                if (busReceiptPojo.getBilltype().equals("销售收款")) {
                    // 同步销售发票,销售订单的收款,预收款,总收款等(主表Receipted共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(busReceiptitemPojo.getInvoid(), busReceiptitemPojo.getInvobillcode(), tid);
//                    this.busReceiptMapper.updateBusInvoFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), busReceiptPojo.getTenantid());
                } else if (busReceiptPojo.getBilltype().equals("单据收款")) {
                    this.busReceiptMapper.updateDeliFinish(busReceiptitemPojo.getInvoid(), busReceiptitemPojo.getInvobillcode(), tid);
                }
            }
        }
        //Cash子表处理
        List<BusReceiptcashPojo> lstCash = busReceiptPojo.getCash();
        if (lstCash != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstCash.size(); i++) {
                BusReceiptcashPojo delPojo = this.busReceiptcashMapper.getEntity(lstCash.get(i).getId(), tid);
                this.busReceiptcashMapper.delete(lstCash.get(i).getId(), tid);
                // 同步出纳账户金额
                this.busReceiptMapper.updateCashAmount(delPojo.getCashaccid(), 0 - delPojo.getAmount(), tid);
                // 出纳账户余额检查
                Double cashAmount = this.busReceiptMapper.getCashAmount(delPojo.getCashaccid(), tid);
                if (cashAmount < 0) {
                    int Rowno = i + 1;
                    throw new RuntimeException(Rowno + "行,账户余额不足");
                }
            }
        }
        this.busReceiptMapper.delete(key, tid);
        return busReceiptPojo.getRefno();
    }

    private static void cleanNull(BusReceiptPojo busReceiptPojo) {
        if (busReceiptPojo.getRefno() == null) busReceiptPojo.setRefno("");
        if (busReceiptPojo.getBilltype() == null) busReceiptPojo.setBilltype("");
        if (busReceiptPojo.getBilltitle() == null) busReceiptPojo.setBilltitle("");
        if (busReceiptPojo.getBilldate() == null) busReceiptPojo.setBilldate(new Date());
        if (busReceiptPojo.getGroupid() == null) busReceiptPojo.setGroupid("");
        if (busReceiptPojo.getBillamount() == null) busReceiptPojo.setBillamount(0D);
        if (busReceiptPojo.getOperator() == null) busReceiptPojo.setOperator("");
        if (busReceiptPojo.getCitecode() == null) busReceiptPojo.setCitecode("");
        if (busReceiptPojo.getReturnuid() == null) busReceiptPojo.setReturnuid("");
        if (busReceiptPojo.getOrguid() == null) busReceiptPojo.setOrguid("");
        if (busReceiptPojo.getSummary() == null) busReceiptPojo.setSummary("");
        if (busReceiptPojo.getCreateby() == null) busReceiptPojo.setCreateby("");
        if (busReceiptPojo.getCreatebyid() == null) busReceiptPojo.setCreatebyid("");
        if (busReceiptPojo.getCreatedate() == null) busReceiptPojo.setCreatedate(new Date());
        if (busReceiptPojo.getLister() == null) busReceiptPojo.setLister("");
        if (busReceiptPojo.getListerid() == null) busReceiptPojo.setListerid("");
        if (busReceiptPojo.getModifydate() == null) busReceiptPojo.setModifydate(new Date());
        if (busReceiptPojo.getAssessor() == null) busReceiptPojo.setAssessor("");
        if (busReceiptPojo.getAssessorid() == null) busReceiptPojo.setAssessorid("");
        if (busReceiptPojo.getAssessdate() == null) busReceiptPojo.setAssessdate(new Date());
        if (busReceiptPojo.getFmdocmark() == null) busReceiptPojo.setFmdocmark(0);
        if (busReceiptPojo.getFmdoccode() == null) busReceiptPojo.setFmdoccode("");
        if (busReceiptPojo.getCustom1() == null) busReceiptPojo.setCustom1("");
        if (busReceiptPojo.getWorkdate() == null) busReceiptPojo.setWorkdate(new Date());

        if (busReceiptPojo.getCustom2() == null) busReceiptPojo.setCustom2("");
        if (busReceiptPojo.getCustom3() == null) busReceiptPojo.setCustom3("");
        if (busReceiptPojo.getCustom4() == null) busReceiptPojo.setCustom4("");
        if (busReceiptPojo.getCustom5() == null) busReceiptPojo.setCustom5("");
        if (busReceiptPojo.getCustom6() == null) busReceiptPojo.setCustom6("");
        if (busReceiptPojo.getCustom7() == null) busReceiptPojo.setCustom7("");
        if (busReceiptPojo.getCustom8() == null) busReceiptPojo.setCustom8("");
        if (busReceiptPojo.getCustom9() == null) busReceiptPojo.setCustom9("");
        if (busReceiptPojo.getCustom10() == null) busReceiptPojo.setCustom10("");
        if (busReceiptPojo.getTenantid() == null) busReceiptPojo.setTenantid("");
        if (busReceiptPojo.getRevision() == null) busReceiptPojo.setRevision(0);
    }

    /**
     * 审核数据
     *
     * @param busReceiptPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusReceiptPojo approval(BusReceiptPojo busReceiptPojo) {
        //主表更改
        BusReceiptEntity busReceiptEntity = new BusReceiptEntity();
        BeanUtils.copyProperties(busReceiptPojo, busReceiptEntity);
        this.busReceiptMapper.approval(busReceiptEntity);
        //返回Bill实例
        return this.getBillEntity(busReceiptEntity.getId(), busReceiptEntity.getTenantid());
    }

    // sale，fm服务都也有这个方法，必须同时修改！！！！！！！
    private void syncFirstAndLastAmt(String invobillid, String invobillcode, String tid) {
        // 1更新Bus_Invoice的Receipted共收,FirstAmt预收,LastAmt收款 (FirstAmt+LastAmt=Receipted)
        this.busReceiptMapper.updateBusInvoFinish(invobillid, invobillcode, tid);
        // 2更新Bus_InvoiceItem的AvgFirstAmt, AvgLastAmt
        this.busReceiptMapper.updateBusInvoItemAvgAmt(invobillid, invobillcode, tid);
        // 3更新关联销售订单Bus_MachiningItem的AvgFirstAmt, AvgLastAmt
        this.busReceiptMapper.updateBusMachingItemAvgAmt(invobillid, invobillcode, tid);
        // 4更新关联销售订单Bus_Machining的FirstAmt, LastAmt
        this.busReceiptMapper.updateBusMachingFirstLastAmt(invobillid, invobillcode, tid);
    }
}
