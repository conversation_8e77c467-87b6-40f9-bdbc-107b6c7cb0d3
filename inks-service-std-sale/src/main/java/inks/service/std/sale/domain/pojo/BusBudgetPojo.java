package inks.service.std.sale.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 成本预算(BusBudget)实体类
 *
 * <AUTHOR>
 * @since 2023-08-02 09:26:56
 */
public class BusBudgetPojo implements Serializable {
    private static final long serialVersionUID = -77809267354379030L;
         // id
          @Excel(name = "id")
    private String id;
         // 编码
          @Excel(name = "编码")
    private String refno;
         // 单据类型
          @Excel(name = "单据类型")
    private String billtype;
         // 单据日期
          @Excel(name = "单据日期")
    private Date billdate;
         // 单据标题
          @Excel(name = "单据标题")
    private String billtitle;
         // 订单id
          @Excel(name = "订单id")
    private String machbillid;
         // 订单单号(备查)
          @Excel(name = "订单单号(备查)")
    private String machbillcode;
         // 客户
          @Excel(name = "客户")
    private String groupid;
         // 生产车间
          @Excel(name = "生产车间")
    private String workshop;
         // 材料预算
          @Excel(name = "材料预算")
    private Double billmatcost;
         // 人工预算
          @Excel(name = "人工预算")
    private Double billlaborcost;
         // 直接费用预算
          @Excel(name = "直接费用预算")
    private Double billdirectcost;
         // 间接费用预算
          @Excel(name = "间接费用预算")
    private Double billindirectcost;
         // 直接材料
          @Excel(name = "直接材料")
    private String billmatamt;
         // 直接人工
          @Excel(name = "直接人工")
    private Double billlaboramt;
         // 直接费用
          @Excel(name = "直接费用")
    private Double billdirectamt;
         // 间接费用
          @Excel(name = "间接费用")
    private Double billindirectamt;
         // 经办人
          @Excel(name = "经办人")
    private String operator;
         // 摘要
          @Excel(name = "摘要")
    private String summary;
         // 创建者
          @Excel(name = "创建者")
    private String createby;
         // 创建者id
          @Excel(name = "创建者id")
    private String createbyid;
         // 新建日期
          @Excel(name = "新建日期")
    private Date createdate;
         // 制表
          @Excel(name = "制表")
    private String lister;
         // 制表id
          @Excel(name = "制表id")
    private String listerid;
         // 修改日期
          @Excel(name = "修改日期")
    private Date modifydate;
         // 审核员
          @Excel(name = "审核员")
    private String assessor;
         // 审核员id
          @Excel(name = "审核员id")
    private String assessorid;
         // 审核日期
          @Excel(name = "审核日期")
    private Date assessdate;
         // 自定义1
          @Excel(name = "自定义1")
    private String custom1;
         // 自定义2
          @Excel(name = "自定义2")
    private String custom2;
         // 自定义3
          @Excel(name = "自定义3")
    private String custom3;
         // 自定义4
          @Excel(name = "自定义4")
    private String custom4;
         // 自定义5
          @Excel(name = "自定义5")
    private String custom5;
         // 自定义6
          @Excel(name = "自定义6")
    private String custom6;
         // 自定义7
          @Excel(name = "自定义7")
    private String custom7;
         // 自定义8
          @Excel(name = "自定义8")
    private String custom8;
         // 自定义9
          @Excel(name = "自定义9")
    private String custom9;
         // 自定义10
          @Excel(name = "自定义10")
    private String custom10;
         // 租户id
          @Excel(name = "租户id")
    private String tenantid;
         // 租户名称
          @Excel(name = "租户名称")
    private String tenantname;
         // 乐观锁
          @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<BusBudgetitemPojo> item;

    private String groupuid;
    private String groupname;
    private String abbreviate;

    public String getGroupuid() {
        return groupuid;
    }

    public void setGroupuid(String groupuid) {
        this.groupuid = groupuid;
    }

    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    public String getAbbreviate() {
        return abbreviate;
    }

    public void setAbbreviate(String abbreviate) {
        this.abbreviate = abbreviate;
    }

    // id
     public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 编码
     public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
   // 单据类型
     public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
   // 单据日期
     public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
   // 单据标题
     public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
   // 订单id
     public String getMachbillid() {
        return machbillid;
    }
    
    public void setMachbillid(String machbillid) {
        this.machbillid = machbillid;
    }
        
   // 订单单号(备查)
     public String getMachbillcode() {
        return machbillcode;
    }
    
    public void setMachbillcode(String machbillcode) {
        this.machbillcode = machbillcode;
    }
        
   // 客户
     public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
        
   // 生产车间
     public String getWorkshop() {
        return workshop;
    }
    
    public void setWorkshop(String workshop) {
        this.workshop = workshop;
    }
        
   // 材料预算
     public Double getBillmatcost() {
        return billmatcost;
    }
    
    public void setBillmatcost(Double billmatcost) {
        this.billmatcost = billmatcost;
    }
        
   // 人工预算
     public Double getBilllaborcost() {
        return billlaborcost;
    }
    
    public void setBilllaborcost(Double billlaborcost) {
        this.billlaborcost = billlaborcost;
    }
        
   // 直接费用预算
     public Double getBilldirectcost() {
        return billdirectcost;
    }
    
    public void setBilldirectcost(Double billdirectcost) {
        this.billdirectcost = billdirectcost;
    }
        
   // 间接费用预算
     public Double getBillindirectcost() {
        return billindirectcost;
    }
    
    public void setBillindirectcost(Double billindirectcost) {
        this.billindirectcost = billindirectcost;
    }
        
   // 直接材料


    public String getBillmatamt() {
        return billmatamt;
    }

    public void setBillmatamt(String billmatamt) {
        this.billmatamt = billmatamt;
    }

    // 直接人工
     public Double getBilllaboramt() {
        return billlaboramt;
    }
    
    public void setBilllaboramt(Double billlaboramt) {
        this.billlaboramt = billlaboramt;
    }
        
   // 直接费用
     public Double getBilldirectamt() {
        return billdirectamt;
    }
    
    public void setBilldirectamt(Double billdirectamt) {
        this.billdirectamt = billdirectamt;
    }
        
   // 间接费用
     public Double getBillindirectamt() {
        return billindirectamt;
    }
    
    public void setBillindirectamt(Double billindirectamt) {
        this.billindirectamt = billindirectamt;
    }
        
   // 经办人
     public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
        
   // 摘要
     public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
   // 创建者
     public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
     public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
     public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
     public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
     public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
     public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 审核员
     public String getAssessor() {
        return assessor;
    }
    
    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
        
   // 审核员id
     public String getAssessorid() {
        return assessorid;
    }
    
    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
        
   // 审核日期
     public Date getAssessdate() {
        return assessdate;
    }
    
    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
        
   // 自定义1
     public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
     public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
     public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
     public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
     public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
     public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
     public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
     public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
     public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
     public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
     public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
     public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
     public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

    public List<BusBudgetitemPojo> getItem() {
        return item;
    }

    public void setItem(List<BusBudgetitemPojo> item) {
        this.item = item;
    }


}

