package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCostpartgroupPojo;

import java.util.List;
import java.util.Map;

/**
 * 组件分组(BusCostpartgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-07 13:29:47
 */
public interface BusCostpartgroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostpartgroupPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusCostpartgroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busCostpartgroupPojo 实例对象
     * @return 实例对象
     */
    BusCostpartgroupPojo insert(BusCostpartgroupPojo busCostpartgroupPojo);

    /**
     * 修改数据
     *
     * @param busCostpartgrouppojo 实例对象
     * @return 实例对象
     */
    BusCostpartgroupPojo update(BusCostpartgroupPojo busCostpartgrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 分页查询
     *

     * @return 查询结果
     */
    List<BusCostpartgroupPojo> getList(String tid);

    void splitGroupjson(List<Map<String, Object>> lst,String tid);
}
