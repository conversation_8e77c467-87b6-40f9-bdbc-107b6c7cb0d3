package inks.service.std.sale.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCouponactivePojo;
import inks.service.std.sale.service.BusCouponactiveService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 优惠券激活记录(Bus_CouponActive)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
//@RestController
//@RequestMapping("busCouponactive")
public class BusCouponactiveController {

    @Resource
    private BusCouponactiveService busCouponactiveService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(BusCouponactiveController.class);


    @ApiOperation(value=" 获取优惠券激活记录详细信息", notes="获取优惠券激活记录详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_CouponActive.List")
    public R<BusCouponactivePojo> getEntity(String key) {
    try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busCouponactiveService.getEntity(key, loginUser.getTenantid()));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_CouponActive.List")
    public R<PageInfo<BusCouponactivePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Bus_CouponActive.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busCouponactiveService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增优惠券激活记录", notes="新增优惠券激活记录", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_CouponActive.Add")
    public R<BusCouponactivePojo> create(@RequestBody String json) {
       try {
       BusCouponactivePojo busCouponactivePojo = JSONArray.parseObject(json,BusCouponactivePojo.class);       
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCouponactivePojo.setCreateby(loginUser.getRealName());   // 创建者
            busCouponactivePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busCouponactivePojo.setCreatedate(new Date());   // 创建时间
            busCouponactivePojo.setLister(loginUser.getRealname());   // 制表
            busCouponactivePojo.setListerid(loginUser.getUserid());    // 制表id  
            busCouponactivePojo.setModifydate(new Date());   //修改时间
            busCouponactivePojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.busCouponactiveService.insert(busCouponactivePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改优惠券激活记录", notes="修改优惠券激活记录", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_CouponActive.Edit")
    public R<BusCouponactivePojo> update(@RequestBody String json) {
       try {
         BusCouponactivePojo busCouponactivePojo = JSONArray.parseObject(json,BusCouponactivePojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCouponactivePojo.setLister(loginUser.getRealname());   // 制表
            busCouponactivePojo.setListerid(loginUser.getUserid());    // 制表id  
            busCouponactivePojo.setTenantid(loginUser.getTenantid());   //租户id
            busCouponactivePojo.setModifydate(new Date());   //修改时间
//            busCouponactivePojo.setAssessor(""); // 审核员
//            busCouponactivePojo.setAssessorid(""); // 审核员id
//            busCouponactivePojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.busCouponactiveService.update(busCouponactivePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除优惠券激活记录", notes="删除优惠券激活记录", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_CouponActive.Delete")
    public R<Integer> delete(String key) {
    try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busCouponactiveService.delete(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "审核优惠券激活记录", notes = "审核优惠券激活记录", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_CouponActive.Approval")
    public R<BusCouponactivePojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusCouponactivePojo busCouponactivePojo = this.busCouponactiveService.getEntity(key, loginUser.getTenantid());
            if (busCouponactivePojo.getAssessor().equals(""))
            {
                busCouponactivePojo.setAssessor(loginUser.getRealname()); //审核员
                busCouponactivePojo.setAssessorid(loginUser.getUserid()); //审核员id
                }
            else
            {
                busCouponactivePojo.setAssessor(""); //审核员
                busCouponactivePojo.setAssessorid(""); //审核员
                }
            busCouponactivePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busCouponactiveService.approval(busCouponactivePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_CouponActive.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusCouponactivePojo busCouponactivePojo = this.busCouponactiveService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busCouponactivePojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

