package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusCostmodelitemEntity;
import inks.service.std.sale.domain.pojo.BusCostmodelitemPojo;
import inks.service.std.sale.mapper.BusCostmodelitemMapper;
import inks.service.std.sale.service.BusCostmodelitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 模型项目(BusCostmodelitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-07 13:30:44
 */
@Service("busCostmodelitemService")
public class BusCostmodelitemServiceImpl implements BusCostmodelitemService {
    @Resource
    private BusCostmodelitemMapper busCostmodelitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusCostmodelitemPojo getEntity(String key,String tid) {
        return this.busCostmodelitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusCostmodelitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCostmodelitemPojo> lst = busCostmodelitemMapper.getPageList(queryParam);
            PageInfo<BusCostmodelitemPojo> pageInfo = new PageInfo<BusCostmodelitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusCostmodelitemPojo> getList(String Pid,String tid) { 
        try {
            List<BusCostmodelitemPojo> lst = busCostmodelitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busCostmodelitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusCostmodelitemPojo insert(BusCostmodelitemPojo busCostmodelitemPojo) {
        //初始化item的NULL
        BusCostmodelitemPojo itempojo =this.clearNull(busCostmodelitemPojo);
        BusCostmodelitemEntity busCostmodelitemEntity = new BusCostmodelitemEntity(); 
        BeanUtils.copyProperties(itempojo,busCostmodelitemEntity);
        
          busCostmodelitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busCostmodelitemEntity.setRevision(1);  //乐观锁      
          this.busCostmodelitemMapper.insert(busCostmodelitemEntity);
        return this.getEntity(busCostmodelitemEntity.getId(),busCostmodelitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busCostmodelitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusCostmodelitemPojo update(BusCostmodelitemPojo busCostmodelitemPojo) {
        BusCostmodelitemEntity busCostmodelitemEntity = new BusCostmodelitemEntity(); 
        BeanUtils.copyProperties(busCostmodelitemPojo,busCostmodelitemEntity);
        this.busCostmodelitemMapper.update(busCostmodelitemEntity);
        return this.getEntity(busCostmodelitemEntity.getId(),busCostmodelitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busCostmodelitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busCostmodelitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusCostmodelitemPojo clearNull(BusCostmodelitemPojo busCostmodelitemPojo){
     //初始化NULL字段
     if(busCostmodelitemPojo.getPid()==null) busCostmodelitemPojo.setPid("");
     if(busCostmodelitemPojo.getPartgroupid()==null) busCostmodelitemPojo.setPartgroupid("");
     if(busCostmodelitemPojo.getItemtype()==null) busCostmodelitemPojo.setItemtype("");
     if(busCostmodelitemPojo.getItemname()==null) busCostmodelitemPojo.setItemname("");
     if(busCostmodelitemPojo.getItemspec()==null) busCostmodelitemPojo.setItemspec("");
     if(busCostmodelitemPojo.getItemunit()==null) busCostmodelitemPojo.setItemunit("");
     if(busCostmodelitemPojo.getBaseprice()==null) busCostmodelitemPojo.setBaseprice(0D);
     if(busCostmodelitemPojo.getQuantity()==null) busCostmodelitemPojo.setQuantity(0D);
     if(busCostmodelitemPojo.getRebate()==null) busCostmodelitemPojo.setRebate(0D);
     if(busCostmodelitemPojo.getRebatesec()==null) busCostmodelitemPojo.setRebatesec(0D);
     if(busCostmodelitemPojo.getPrice()==null) busCostmodelitemPojo.setPrice(0D);
     if(busCostmodelitemPojo.getAmount()==null) busCostmodelitemPojo.setAmount(0D);
     if(busCostmodelitemPojo.getPricemin()==null) busCostmodelitemPojo.setPricemin(0D);
     if(busCostmodelitemPojo.getPricemax()==null) busCostmodelitemPojo.setPricemax(0D);
     if(busCostmodelitemPojo.getRemark()==null) busCostmodelitemPojo.setRemark("");
     if(busCostmodelitemPojo.getRownum()==null) busCostmodelitemPojo.setRownum(0);
     if(busCostmodelitemPojo.getAllowdelete()==null) busCostmodelitemPojo.setAllowdelete(0);
     if(busCostmodelitemPojo.getAllowedit()==null) busCostmodelitemPojo.setAllowedit(0);
     if(busCostmodelitemPojo.getCustom1()==null) busCostmodelitemPojo.setCustom1("");
     if(busCostmodelitemPojo.getCustom2()==null) busCostmodelitemPojo.setCustom2("");
     if(busCostmodelitemPojo.getCustom3()==null) busCostmodelitemPojo.setCustom3("");
     if(busCostmodelitemPojo.getCustom4()==null) busCostmodelitemPojo.setCustom4("");
     if(busCostmodelitemPojo.getCustom5()==null) busCostmodelitemPojo.setCustom5("");
     if(busCostmodelitemPojo.getCustom6()==null) busCostmodelitemPojo.setCustom6("");
     if(busCostmodelitemPojo.getCustom7()==null) busCostmodelitemPojo.setCustom7("");
     if(busCostmodelitemPojo.getCustom8()==null) busCostmodelitemPojo.setCustom8("");
     if(busCostmodelitemPojo.getCustom9()==null) busCostmodelitemPojo.setCustom9("");
     if(busCostmodelitemPojo.getCustom10()==null) busCostmodelitemPojo.setCustom10("");
     if(busCostmodelitemPojo.getTenantid()==null) busCostmodelitemPojo.setTenantid("");
     if(busCostmodelitemPojo.getRevision()==null) busCostmodelitemPojo.setRevision(0);
     return busCostmodelitemPojo;
     }
}
