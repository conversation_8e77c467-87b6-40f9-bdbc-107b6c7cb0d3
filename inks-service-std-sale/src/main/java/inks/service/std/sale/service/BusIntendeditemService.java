package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusIntendeditemPojo;

import java.util.List;
/**
 * 意向项目(BusIntendeditem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17 09:12:51
 */
public interface BusIntendeditemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusIntendeditemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusIntendeditemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusIntendeditemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busIntendeditemPojo 实例对象
     * @return 实例对象
     */
    BusIntendeditemPojo insert(BusIntendeditemPojo busIntendeditemPojo);

    /**
     * 修改数据
     *
     * @param busIntendeditempojo 实例对象
     * @return 实例对象
     */
    BusIntendeditemPojo update(BusIntendeditemPojo busIntendeditempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busIntendeditempojo 实例对象
     * @return 实例对象
     */
    BusIntendeditemPojo clearNull(BusIntendeditemPojo busIntendeditempojo);
}
