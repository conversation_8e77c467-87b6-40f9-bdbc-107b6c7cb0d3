package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.service.std.sale.constant.MyConstant;
import inks.service.std.sale.domain.*;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.mapper.BusDeductionitemMapper;
import inks.service.std.sale.mapper.BusDelieryitemMapper;
import inks.service.std.sale.mapper.BusInvoiceMapper;
import inks.service.std.sale.mapper.BusInvoiceitemMapper;
import inks.service.std.sale.service.BusInvoiceService;
import inks.service.std.sale.service.BusInvoiceitemService;
import inks.service.std.sale.utils.PrintColor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 销售开票(BusInvoice)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 15:30:29
 */
@Service("busInvoiceService")
public class BusInvoiceServiceImpl implements BusInvoiceService {
    private static final Logger log = LoggerFactory.getLogger(BusInvoiceServiceImpl.class);

    @Resource
    private BusInvoiceMapper busInvoiceMapper;

    @Resource
    private BusInvoiceitemMapper busInvoiceitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusInvoiceitemService busInvoiceitemService;

    @Resource
    private BusDelieryitemMapper busDelieryitemMapper;

    @Resource
    private BusDeductionitemMapper busDeductionitemMapper;
    @Resource
    private RedisService redisService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusInvoicePojo getEntity(String key, String tid) {
        return this.busInvoiceMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusInvoiceitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusInvoiceitemdetailPojo> lst = busInvoiceMapper.getPageList(queryParam);
            PageInfo<BusInvoiceitemdetailPojo> pageInfo = new PageInfo<BusInvoiceitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusInvoicePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusInvoicePojo busInvoicePojo = this.busInvoiceMapper.getEntity(key, tid);
            //读取子表
            busInvoicePojo.setItem(busInvoiceitemMapper.getList(busInvoicePojo.getId(), busInvoicePojo.getTenantid()));
            return busInvoicePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusInvoicePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusInvoicePojo> lst = busInvoiceMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (BusInvoicePojo busInvoicePojo : lst) {
                busInvoicePojo.setItem(busInvoiceitemMapper.getList(busInvoicePojo.getId(), busInvoicePojo.getTenantid()));
            }
            PageInfo<BusInvoicePojo> pageInfo = new PageInfo<BusInvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusInvoicePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusInvoicePojo> lst = busInvoiceMapper.getPageTh(queryParam);
            PageInfo<BusInvoicePojo> pageInfo = new PageInfo<BusInvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo insert(BusInvoicePojo busInvoicePojo) {
//初始化NULL字段
        cleanNull(busInvoicePojo);
        String tid = busInvoicePojo.getTenantid();
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        BeanUtils.copyProperties(busInvoicePojo, busInvoiceEntity);
        //设置id和新建日期
        busInvoiceEntity.setId(id);
        busInvoiceEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busInvoiceMapper.insert(busInvoiceEntity);
        if (busInvoicePojo.getItem() != null) {
            //销售开票时 有明细 Eric 20220109
            //Item子表处理
            List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BusInvoiceitemPojo item = lst.get(i);
                    //初始化item的NULL
                    BusInvoiceitemPojo itemPojo = this.busInvoiceitemService.clearNull(item);
                    BusInvoiceitemEntity busInvoiceitemEntity = new BusInvoiceitemEntity();
                    BeanUtils.copyProperties(itemPojo, busInvoiceitemEntity);
                    //设置id和Pid
                    busInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    busInvoiceitemEntity.setPid(id);
                    busInvoiceitemEntity.setTenantid(tid);
                    busInvoiceitemEntity.setRevision(1);  //乐观锁
                    //插入子表
                    this.busInvoiceitemMapper.insert(busInvoiceitemEntity);
                    // 单据类型这里不叫BillType，叫DeliType
                    String deliType = item.getDelitype();
                    //更新订单已发货 Eric20211213
                    String deliitemid = item.getDeliitemid();
                    String deliuid = item.getDeliuid();
                    if (!deliitemid.isEmpty() && !deliuid.isEmpty()) {
                        if (deliType.equals("发出商品") || deliType.equals("订单退货") ||
                                deliType.equals("其他发货") || deliType.equals("其他退货") ||
                                deliType.equals("退货返工") || deliType.equals("返工补发")) {
                            this.busInvoiceMapper.updateDeliInvoFinish(deliitemid, tid);
                            // 超数检查
                            BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(deliitemid, tid);
                            if (busDelieryitemPojo != null) {
                                if (busDelieryitemPojo.getInvoqty() > busDelieryitemPojo.getQuantity()) {
                                    int Rowno = i + 1;
                                    throw new RuntimeException(Rowno + "行,开票总数:" + busDelieryitemPojo.getInvoqty() + "超出销售数:" + busDelieryitemPojo.getQuantity());
                                }
                            } else {
                                throw new RuntimeException("关联单据丢失:" + deliuid);
                            }
                            this.busInvoiceMapper.updateDeliInvoCount(deliitemid, tid);
                        } else {
                            this.busInvoiceMapper.updateDeduInvoFinish(deliitemid, tid);
                            // 超数检查
                            BusDeductionitemPojo busDeductionitemPojo = this.busDeductionitemMapper.getEntity(deliitemid, tid);
                            if (busDeductionitemPojo != null) {
                                if (busDeductionitemPojo.getInvoqty() > busDeductionitemPojo.getQuantity()) {
                                    int Rowno = i + 1;
                                    throw new RuntimeException(Rowno + "行,开票总数:" + busDeductionitemPojo.getInvoqty() + "超出扣款数:" + busDeductionitemPojo.getQuantity());
                                }
                            } else {
                                throw new RuntimeException("关联单据丢失:" + deliuid);
                            }
                            this.busInvoiceMapper.updateDeduInvoCount(deliitemid, tid);
                        }
                    }
                    // 如果有销售订单id就同步更新订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                    if (isNotBlank(item.getMachitemid()) && isNotBlank(item.getMachuid())) {
                        // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                        syncMachAboutInvo(item.getMachitemid(), item.getMachuid(), tid);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());

    }


    @Override
    @Transactional
    public String insertStart(BusInvoicePojo busInvoicePojo,boolean isMachSyncAllowed) {
        String tid = busInvoicePojo.getTenantid();
        String state_redisKey = MyConstant.ASYNC_BUSINVOIC_STATE + tid + busInvoicePojo.getListerid();  // 获取进度条的redisKey
        // 进度条提示信息：移动到方法开始处
        Map<String, Object> missionMsg = null;
        // 收集所有增删改的中delitype、DeliItemid、Machitemid、Machuid,最后同意做批量同步
        List<Map<String, String>> changeItems = new ArrayList<>();
        try {
            // 有锁，查看进度
            missionMsg = redisService.getCacheObject(state_redisKey);
            if (missionMsg != null) {
                throw new RuntimeException("发票" + missionMsg.get("type") + "任务正在处理中...进度：" + missionMsg.get("finish") + "/" + missionMsg.get("total"));
            }
            //初始化NULL字段
            cleanNull(busInvoicePojo);
            //生成id
            String id = inksSnowflake.getSnowflake().nextIdStr();
            BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
            BeanUtils.copyProperties(busInvoicePojo, busInvoiceEntity);
            //设置id和新建日期
            busInvoiceEntity.setId(id);
            busInvoiceEntity.setRevision(1);  //乐观锁
            //插入主表
            this.busInvoiceMapper.insert(busInvoiceEntity);
            if (busInvoicePojo.getItem() == null) {
                return null;
            }
            //销售开票时 有明细 Eric 20220109
            //Item子表处理
            List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();


            // 初始化批量插入子表集合
            List<BusInvoiceitemEntity> insertList = new ArrayList<>();
            //循环每个item子表
            for (BusInvoiceitemPojo item : lst) {
                //初始化item的NULL
                BusInvoiceitemPojo itemPojo = this.busInvoiceitemService.clearNull(item);
                BusInvoiceitemEntity busInvoiceitemEntity = new BusInvoiceitemEntity();
                BeanUtils.copyProperties(itemPojo, busInvoiceitemEntity);
                //设置id和Pid
                busInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busInvoiceitemEntity.setPid(id);
                busInvoiceitemEntity.setTenantid(tid);
                busInvoiceitemEntity.setRevision(1);  //乐观锁
                // 将实体添加到批处理列表
                insertList.add(busInvoiceitemEntity);

                // 收集所有要插入的delitype、DeliItemid、Machitemid、Machuid
                Map<String, String> delItemData = new HashMap<>();
                delItemData.put("delitype", item.getDelitype());
                delItemData.put("deliitemid", item.getDeliitemid());
                delItemData.put("deliuid", item.getDeliuid());
                delItemData.put("machitemid", item.getMachitemid());
                delItemData.put("machuid", item.getMachuid());
                delItemData.put("rownum", String.valueOf(item.getRownum() + 1));
                changeItems.add(delItemData);
            }

            // 批量插入数据
            this.batchInsertData(insertList);

            // 拿到所有的deliItemIds (delitype为发货单或扣款单的)
            List<String> deliItemIds = new ArrayList<>();
            List<String> deliItemIdInDedus = new ArrayList<>();
            List<String> machitemids = new ArrayList<>();

            for (Map<String, String> changeItem : changeItems) {
                String delitype = changeItem.get("delitype");
                String deliitemid = changeItem.get("deliitemid");
                String machitemid = changeItem.get("machitemid");
                machitemids.add(machitemid);

                if (!deliitemid.isEmpty()) {
                    // 根据delitype类型进行分类
                    if (delitype.equals("发出商品") || delitype.equals("订单退货") ||
                            delitype.equals("其他发货") || delitype.equals("其他退货") ||
                            delitype.equals("退货返工") || delitype.equals("返工补发")) {
                        deliItemIds.add(deliitemid);  // 加入deliItemIds集合
                    } else {
                        deliItemIdInDedus.add(deliitemid);  // 加入deliItemIdInDedus集合
                    }
                }
            }


            // 总共需要更新的数量 （+1防止下面超数检查还没进行就finish=total完成了）
            int totalTasks;
            if (isMachSyncAllowed) {
                totalTasks = deliItemIds.size() + deliItemIdInDedus.size() + machitemids.size();
            } else {
                totalTasks = deliItemIds.size() + deliItemIdInDedus.size();
            }            AtomicInteger currentCount = new AtomicInteger(0);

            //------------1设置当前计算任务进度，类型，总数，已完成数
            missionMsg = new HashMap<>();// 初始化任务信息
            missionMsg.put("total", totalTasks);
            missionMsg.put("finish", 0);
            missionMsg.put("type", busInvoicePojo.getRefno() + "新建");
            this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);

            // 批量更新操作 关联发货单（分批处理，打印进度）
            if (!deliItemIds.isEmpty()) {
                syncDeliAboutInvoBatch(deliItemIds, tid, state_redisKey, totalTasks, currentCount, missionMsg);
            }
            // 批量更新操作 关联扣款单（分批处理，打印进度）
            if (!deliItemIdInDedus.isEmpty()) {
                syncDeduAboutInvoBatch(deliItemIdInDedus, tid, state_redisKey, totalTasks, currentCount, missionMsg);
            }
            // （通过系统参数判断是否进行销售订单同步）批量更新操作 关联销售订单（很慢，在这里面打印进度）
            if (isMachSyncAllowed && CollectionUtils.isNotEmpty(machitemids)) {
                syncMachAboutInvoBatch(machitemids, tid, state_redisKey, totalTasks, currentCount, missionMsg);
            }

            // 检查==============
            PrintColor.red("超数检查 开始");
            int checkCount = 0;
            for (Map<String, String> changeItem : changeItems) {
                String delitype = changeItem.get("delitype");
                String deliitemid = changeItem.get("deliitemid");
                String deliuid = changeItem.get("deliuid");
                String rownum = changeItem.get("rownum");

                if (!deliitemid.isEmpty() && !deliuid.isEmpty()) {
                    if (delitype.equals("发出商品") || delitype.equals("订单退货") ||
                            delitype.equals("其他发货") || delitype.equals("其他退货") ||
                            delitype.equals("退货返工") || delitype.equals("返工补发")) {
                        // 超数检查
                        BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(deliitemid, tid);
                        if (busDelieryitemPojo != null) {
                            if (busDelieryitemPojo.getInvoqty() > busDelieryitemPojo.getQuantity()) {
                                //int Rowno = i + 1;
                                throw new RuntimeException(rownum + "行,开票总数:" + busDelieryitemPojo.getInvoqty() + "超出销售数:" + busDelieryitemPojo.getQuantity());
                            }
                        } else {
                            throw new RuntimeException("关联单据丢失:" + deliuid);
                        }
                    } else {
                        // 超数检查
                        BusDeductionitemPojo busDeductionitemPojo = this.busDeductionitemMapper.getEntity(deliitemid, tid);
                        if (busDeductionitemPojo != null) {
                            if (busDeductionitemPojo.getInvoqty() > busDeductionitemPojo.getQuantity()) {
                                //int Rowno = i + 1;
                                throw new RuntimeException(rownum + "行,开票总数:" + busDeductionitemPojo.getInvoqty() + "超出扣款数:" + busDeductionitemPojo.getQuantity());
                            }
                        } else {
                            throw new RuntimeException("关联单据丢失:" + deliuid);
                        }
                    }
                }
                checkCount++;
                if (checkCount == changeItems.size()) {
                    missionMsg.put("finish", totalTasks);
                    missionMsg.put("id", id); //返回下最终创建成功的主表id
                    this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
                }
            }
            return state_redisKey;
        } catch (Exception e) {
            // 捕获所有异常并记录错误信息
            missionMsg.put("error", "处理发票时发生错误: " + e.getMessage());
            this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
            throw new RuntimeException("处理发票时发生错误: " + e.getMessage(), e);
        }
    }

    private static final int BATCH_SIZE = 200; // 每批次插入X条

    private void batchInsertData(List<BusInvoiceitemEntity> insertList) {
        int totalSize = insertList.size();
        int batchCount = 0; // 批次数
        int totalInserted = 0; // 总插入数量

        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            // 获取当前批次数据
            List<BusInvoiceitemEntity> batchList = insertList.subList(i, Math.min(i + BATCH_SIZE, totalSize));
            int insertedCount = busInvoiceitemMapper.batchInsert(batchList);
            batchCount++;
            totalInserted += insertedCount;
            log.info("第 " + batchCount + " 次插入，本批次插入数量：" + insertedCount + " 条，累计插入数量：" + totalInserted + " 条");
        }
        log.info("批量插入完毕！");
    }


    // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
    private void syncMachAboutInvo(String machitemid, String machuid, String tid) {
        String machId = busInvoiceMapper.getMachId(machitemid, tid);
        this.busInvoiceMapper.updateMachItemInvoQty(machitemid, tid);
        this.busInvoiceMapper.updateMachInvoCountInvoAmt(machitemid, machuid, tid);
        this.busInvoiceMapper.updateMachItemAvgInvoAmt(machId, tid);
    }

    private void syncMachAboutInvoBatch(List<String> machitemids, String tid, String state_redisKey, int totalTasks, AtomicInteger currentCount, Map<String, Object> missionMsg) {
        if (machitemids == null || machitemids.isEmpty()) {
            return;
        }

        // 设置每批处理的数量
        int batchSize = 50;
        int totalSize = machitemids.size();

        // 将大的列表分割成多个小批次
        for (int i = 0; i < totalSize; i += batchSize) {
            // 获取当前批次的子列表
            List<String> batchMachitemids = machitemids.subList(
                    i,
                    Math.min(i + batchSize, totalSize)
            );

            // 获取当前批次的machIds
            List<String> batchMachIds = busInvoiceMapper.getMachIds(batchMachitemids, tid);

            // 执行当前批次的更新
            this.busInvoiceMapper.updateMachItemInvoQtyBatch(batchMachitemids, tid);
            if (CollectionUtils.isNotEmpty(batchMachIds)) {
                this.busInvoiceMapper.updateMachInvoCountInvoAmtBatch(batchMachIds, tid);
                //this.busInvoiceMapper.updateMachItemAvgInvoAmtBatch(batchMachIds, tid);
            }

            // 更新已完成数并打印进度
            currentCount.addAndGet(batchMachitemids.size());
            PrintColor.red("销售订单更新 已处理任务数量：" + currentCount + " / " + totalTasks + "进度：" + currentCount.get() * 100 / totalTasks + "%");
            missionMsg.put("finish", currentCount.get() - 2);// 减去1，499/500不能再这里完成，后面检查超数没有异常才算真正完成！
            this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
        }
    }

    private void syncDeliAboutInvoBatch(List<String> deliItemIds, String tid, String state_redisKey, int totalTasks, AtomicInteger currentCount, Map<String, Object> missionMsg) {
        if (deliItemIds == null || deliItemIds.isEmpty()) {
            return;
        }

        int batchSize = 50;
        int totalSize = deliItemIds.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            List<String> batchDeliItemIds = deliItemIds.subList(i, Math.min(i + batchSize, totalSize));

            // 更新发货单
            this.busInvoiceMapper.updateDeliInvoFinishBatch(batchDeliItemIds, tid);
            this.busInvoiceMapper.updateDeliInvoCountBatch(batchDeliItemIds, tid);

            currentCount.addAndGet(batchDeliItemIds.size());
            PrintColor.red("发货单更新 已处理任务数量：" + currentCount + " / " + totalTasks + "进度：" + currentCount.get() * 100 / totalTasks + "%");
            if (currentCount.get() % 50 == 0 || currentCount.get() == totalTasks) {
                missionMsg.put("finish", currentCount.get() - 2);// 减去1，499/500不能再这里完成，后面检查超数没有异常才算真正完成！
                this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
            }
        }
    }

    private void syncDeduAboutInvoBatch(List<String> deliItemIdInDedus, String tid, String state_redisKey, int totalTasks, AtomicInteger currentCount, Map<String, Object> missionMsg) {
        if (deliItemIdInDedus == null || deliItemIdInDedus.isEmpty()) {
            return;
        }

        int batchSize = 50;
        int totalSize = deliItemIdInDedus.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            List<String> batchDeduItemIds = deliItemIdInDedus.subList(i, Math.min(i + batchSize, totalSize));

            // 更新扣款单
            this.busInvoiceMapper.updateDeduInvoFinishBatch(batchDeduItemIds, tid);
            this.busInvoiceMapper.updateDeduInvoCountBatch(batchDeduItemIds, tid);

            currentCount.addAndGet(batchDeduItemIds.size());
            PrintColor.red("扣款单更新 已处理任务数量：" + currentCount + " / " + totalTasks + "进度：" + currentCount.get() * 100 / totalTasks + "%");
            if (currentCount.get() % 50 == 0 || currentCount.get() == totalTasks) {
                missionMsg.put("finish", currentCount.get() - 2);// 减去1，499/500不能再这里完成，后面检查超数没有异常才算真正完成！
                this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
            }
        }
    }


    /**
     * 修改数据
     *
     * @param busInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo update(BusInvoicePojo busInvoicePojo) {
        String tid = busInvoicePojo.getTenantid();
        //主表更改
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        BeanUtils.copyProperties(busInvoicePojo, busInvoiceEntity);
        BusInvoicePojo dbPojo = this.busInvoiceMapper.getEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
        if (busInvoiceEntity.getAmount() != null && busInvoiceEntity.getAmount() != dbPojo.getAmount()) {
            List<String> lstcite = getCiteBillName(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
            if (!lstcite.isEmpty()) {
                throw new RuntimeException("发票已被引用,禁止修改:" + lstcite);
            }
        }
        this.busInvoiceMapper.update(busInvoiceEntity);
        if (busInvoicePojo.getItem() != null) {
            //销售开票时 有明细 Eric 20220109
            //Item子表处理
            List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busInvoiceMapper.getDelItemIds(busInvoicePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    BusInvoiceitemPojo delPojo = this.busInvoiceitemMapper.getEntity(lstDelId, busInvoiceEntity.getTenantid());
                    // 加上引用检测
                    List<String> lstcite = getCiteBillName(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("发票已被引用,禁止删除:" + lstcite);
                    }

                    this.busInvoiceitemMapper.delete(lstDelId, busInvoiceEntity.getTenantid());
                    //更新订单已发货 Eric20211213
                    String delitype = delPojo.getDelitype();
                    if (delitype.equals("发出商品") || delitype.equals("订单退货") ||
                            delitype.equals("其他发货") || delitype.equals("其他退货") ||
                            delitype.equals("退货返工") || delitype.equals("返工补发")) {
                        this.busInvoiceMapper.updateDeliInvoFinish(delPojo.getDeliitemid(), tid);

                        this.busInvoiceMapper.updateDeliInvoCount(delPojo.getDeliitemid(), tid);
                    } else {
                        this.busInvoiceMapper.updateDeduInvoFinish(delPojo.getDeliitemid(), tid);

                        this.busInvoiceMapper.updateDeduInvoCount(delPojo.getDeliitemid(), tid);
                    }
                    if (isNotBlank(delPojo.getMachitemid()) && isNotBlank(delPojo.getMachuid())) {
                        // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                        syncMachAboutInvo(delPojo.getMachitemid(), delPojo.getMachuid(), tid);
                    }

                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BusInvoiceitemPojo busInvoiceitemPojo = lst.get(i);
                    BusInvoiceitemEntity busInvoiceitemEntity = new BusInvoiceitemEntity();
                    if ("".equals(busInvoiceitemPojo.getId()) || busInvoiceitemPojo.getId() == null) {
                        //初始化item的NULL
                        BusInvoiceitemPojo itemPojo = this.busInvoiceitemService.clearNull(busInvoiceitemPojo);
                        BeanUtils.copyProperties(itemPojo, busInvoiceitemEntity);
                        //设置id和Pid
                        busInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busInvoiceitemEntity.setPid(busInvoiceEntity.getId());  // 主表 id
                        busInvoiceitemEntity.setTenantid(tid);   // 租户id
                        busInvoiceitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busInvoiceitemMapper.insert(busInvoiceitemEntity);
                    } else {
                        BeanUtils.copyProperties(busInvoiceitemPojo, busInvoiceitemEntity);
                        busInvoiceitemEntity.setTenantid(tid);
                        this.busInvoiceitemMapper.update(busInvoiceitemEntity);
                    }

                    //更新订单已发货 Eric20211213
                    String delitype = busInvoiceitemPojo.getDelitype();
                    String deliitemid = busInvoiceitemPojo.getDeliitemid();
                    String deliuid = busInvoiceitemPojo.getDeliuid();
                    if (!deliitemid.isEmpty() && !deliuid.isEmpty()) {
                        if (delitype.equals("发出商品") || delitype.equals("订单退货") ||
                                delitype.equals("其他发货") || delitype.equals("其他退货") ||
                                delitype.equals("退货返工") || delitype.equals("返工补发")) {
                            this.busInvoiceMapper.updateDeliInvoFinish(deliitemid, tid);
                            // 超数检查
                            BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(deliitemid, tid);
                            if (busDelieryitemPojo != null) {
                                if (busDelieryitemPojo.getInvoqty() > busDelieryitemPojo.getQuantity()) {
                                    int Rowno = i + 1;
                                    throw new RuntimeException(Rowno + "行,开票总数:" + busDelieryitemPojo.getInvoqty() + "超出销售数:" + busDelieryitemPojo.getQuantity());
                                }
                            } else {
                                throw new RuntimeException("关联单据丢失:" + deliuid);
                            }
                            this.busInvoiceMapper.updateDeliInvoCount(deliitemid, tid);

                        } else {
                            this.busInvoiceMapper.updateDeduInvoFinish(deliitemid, tid);
                            // 超数检查
                            BusDeductionitemPojo busDeductionitemPojo = this.busDeductionitemMapper.getEntity(deliitemid, tid);
                            if (busDeductionitemPojo != null) {
                                if (busDeductionitemPojo.getInvoqty() > busDeductionitemPojo.getQuantity()) {
                                    int Rowno = i + 1;
                                    throw new RuntimeException(Rowno + "行,开票总数:" + busDeductionitemPojo.getInvoqty() + "超出扣款数:" + busDeductionitemPojo.getQuantity());
                                }
                            } else {
                                throw new RuntimeException("关联单据丢失:" + deliuid);
                            }
                            this.busInvoiceMapper.updateDeduInvoCount(deliitemid, tid);
                        }
                        if (isNotBlank(busInvoiceitemPojo.getMachitemid()) && isNotBlank(busInvoiceitemPojo.getMachuid())) {
                            // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                            syncMachAboutInvo(busInvoiceitemPojo.getMachitemid(), busInvoiceitemPojo.getMachuid(), tid);
                        }
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
    }


    @Override
    @Transactional
    public String updateStart(BusInvoicePojo busInvoicePojo,boolean isMachSyncAllowed) {
        String tid = busInvoicePojo.getTenantid();
        String state_redisKey = MyConstant.ASYNC_BUSINVOIC_STATE + tid + busInvoicePojo.getListerid(); // 获取进度条的redisKey
        // 进度条提示信息：移动到方法开始处
        Map<String, Object> missionMsg = null;
        // 收集所有增删改的中delitype、DeliItemid、Machitemid、Machuid,最后同意做批量同步
        List<Map<String, String>> changeItems = new ArrayList<>();
        try {
            // 有锁，查看进度
            missionMsg = redisService.getCacheObject(state_redisKey);
            if (missionMsg != null) {
                throw new RuntimeException("发票" + missionMsg.get("type") + "任务正在处理中...进度：" + missionMsg.get("finish") + "/" + missionMsg.get("total"));
            }
            //主表更改
            BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
            BeanUtils.copyProperties(busInvoicePojo, busInvoiceEntity);
            BusInvoicePojo dbPojo = this.busInvoiceMapper.getEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
            if (busInvoiceEntity.getAmount() != null && !Objects.equals(busInvoiceEntity.getAmount(), dbPojo.getAmount())) {
                List<String> lstcite = getCiteBillName(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
                if (!lstcite.isEmpty()) {
                    throw new RuntimeException("发票已被引用,禁止修改:" + lstcite);
                }
            }
            this.busInvoiceMapper.update(busInvoiceEntity);

            if (busInvoicePojo.getItem() == null) {
                return state_redisKey;
            }

            //销售开票时 有明细 Eric 20220109
            //Item子表处理
            List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();

            //获取被删除的Item
            List<String> lstDelIds = busInvoiceMapper.getDelItemIds(busInvoicePojo);


            // 处理需要删除的items
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    BusInvoiceitemPojo delPojo = this.busInvoiceitemMapper.getEntity(lstDelId, busInvoiceEntity.getTenantid());
                    // 加上引用检测
                    List<String> lstcite = getCiteBillName(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("发票已被引用,禁止删除:" + lstcite);
                    }
                    String delitype = delPojo.getDelitype();
                    // 收集删除项的相关信息
                    Map<String, String> delItemData = new HashMap<>();
                    delItemData.put("delitype", delitype);
                    delItemData.put("deliitemid", delPojo.getDeliitemid());
                    delItemData.put("deliuid", delPojo.getDeliuid());
                    delItemData.put("machitemid", delPojo.getMachitemid());
                    delItemData.put("machuid", delPojo.getMachuid());
                    delItemData.put("rownum", String.valueOf(delPojo.getRownum() + 1));
                    changeItems.add(delItemData);
                }
                //批量删除
                if (CollectionUtils.isNotEmpty(lstDelIds)) {

                    busInvoiceitemMapper.batchDelete(lstDelIds, tid);
                }
            }


            // 初始化批量插入子表集合
            List<BusInvoiceitemEntity> insertList = new ArrayList<>();
            // 处理新的和更新的items
            if (lst != null) {
                //循环每个item子表
                for (BusInvoiceitemPojo busInvoiceitemPojo : lst) {
                    BusInvoiceitemEntity busInvoiceitemEntity = new BusInvoiceitemEntity();

                    if ("".equals(busInvoiceitemPojo.getId()) || busInvoiceitemPojo.getId() == null) {
                        //初始化item的NULL
                        BusInvoiceitemPojo itemPojo = this.busInvoiceitemService.clearNull(busInvoiceitemPojo);
                        BeanUtils.copyProperties(itemPojo, busInvoiceitemEntity);
                        //设置id和Pid
                        busInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                        busInvoiceitemEntity.setPid(busInvoiceEntity.getId());
                        busInvoiceitemEntity.setTenantid(tid);
                        busInvoiceitemEntity.setRevision(1);
                        // 将实体添加到批处理列表
                        insertList.add(busInvoiceitemEntity);
                    } else {
                        BeanUtils.copyProperties(busInvoiceitemPojo, busInvoiceitemEntity);
                        busInvoiceitemEntity.setTenantid(tid);
                        this.busInvoiceitemMapper.update(busInvoiceitemEntity);
                    }

                    //更新订单已发货 Eric20211213
                    String delitype = busInvoiceitemPojo.getDelitype();
                    String deliitemid = busInvoiceitemPojo.getDeliitemid();
                    // 收集新增或更新项的相关信息
                    Map<String, String> updatedItemData = new HashMap<>();
                    updatedItemData.put("delitype", delitype);
                    updatedItemData.put("deliitemid", deliitemid);
                    updatedItemData.put("deliuid", busInvoiceitemPojo.getDeliuid());
                    updatedItemData.put("machitemid", busInvoiceitemPojo.getMachitemid());
                    updatedItemData.put("machuid", busInvoiceitemPojo.getMachuid());
                    updatedItemData.put("rownum", String.valueOf(busInvoiceitemPojo.getRownum() + 1));
                    changeItems.add(updatedItemData);
                }

            }
            // 批量插入数据
            this.batchInsertData(insertList);


            // 拿到所有的deliItemIds (delitype为发货单或扣款单的)
            List<String> deliItemIds = new ArrayList<>();
            List<String> deliItemIdInDedus = new ArrayList<>();
            List<String> machitemids = new ArrayList<>();

            for (Map<String, String> changeItem : changeItems) {
                String delitype = changeItem.get("delitype");
                String deliitemid = changeItem.get("deliitemid");
                String machitemid = changeItem.get("machitemid");
                machitemids.add(machitemid);
                if (!deliitemid.isEmpty()) {
                    // 根据delitype类型进行分类
                    if (delitype.equals("发出商品") || delitype.equals("订单退货") ||
                            delitype.equals("其他发货") || delitype.equals("其他退货") ||
                            delitype.equals("退货返工") || delitype.equals("返工补发")) {
                        deliItemIds.add(deliitemid);  // 加入deliItemIds集合
                    } else {
                        deliItemIdInDedus.add(deliitemid);  // 加入deliItemIdInDedus集合
                    }
                }
            }

            // 总共需要更新的数量
            AtomicInteger currentCount = new AtomicInteger(0);
            // 总共需要更新的数量 （+1防止下面超数检查还没进行就finish=total完成了）
            int totalTasks;
            if (isMachSyncAllowed) {
                totalTasks = deliItemIds.size() + deliItemIdInDedus.size() + machitemids.size();
            } else {
                totalTasks = deliItemIds.size() + deliItemIdInDedus.size();
            }            //------------1设置当前计算任务进度，类型，总数，已完成数
            missionMsg = new HashMap<>();// 初始化任务信息
            missionMsg.put("total", totalTasks);
            missionMsg.put("finish", 0);
            missionMsg.put("type", busInvoicePojo.getRefno() + "修改");
            this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);

            // 批量更新操作 关联发货单（分批处理，打印进度）
            if (!deliItemIds.isEmpty()) {
                syncDeliAboutInvoBatch(deliItemIds, tid, state_redisKey, totalTasks, currentCount, missionMsg);
            }
            PrintColor.zi("发货单更新完成 currentCount=" + currentCount);
            // 批量更新操作 关联扣款单（分批处理，打印进度）
            if (!deliItemIdInDedus.isEmpty()) {
                syncDeduAboutInvoBatch(deliItemIdInDedus, tid, state_redisKey, totalTasks, currentCount, missionMsg);
            }
            PrintColor.zi("扣款单更新完成 currentCount=" + currentCount);
            // （通过系统参数判断是否进行销售订单同步）批量更新操作 关联销售订单（很慢，在这里面打印进度）
            if (isMachSyncAllowed && CollectionUtils.isNotEmpty(machitemids)) {
                syncMachAboutInvoBatch(machitemids, tid, state_redisKey, totalTasks, currentCount, missionMsg);
            }
            PrintColor.zi("销售订单更新完成 currentCount=" + currentCount);


            // 检查==============
            PrintColor.red("超数检查 开始");
            int checkCount = 0;
            for (Map<String, String> changeItem : changeItems) {
                String delitype = changeItem.get("delitype");
                String deliitemid = changeItem.get("deliitemid");
                String deliuid = changeItem.get("deliuid");
                String rownum = changeItem.get("rownum");

                if (!deliitemid.isEmpty() && !deliuid.isEmpty()) {
                    if (delitype.equals("发出商品") || delitype.equals("订单退货") ||
                            delitype.equals("其他发货") || delitype.equals("其他退货") ||
                            delitype.equals("退货返工") || delitype.equals("返工补发")) {
                        // 超数检查
                        BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(deliitemid, tid);
                        if (busDelieryitemPojo != null) {
                            if (busDelieryitemPojo.getInvoqty() > busDelieryitemPojo.getQuantity()) {
                                //int Rowno = i + 1;
                                throw new RuntimeException(rownum + "行,开票总数:" + busDelieryitemPojo.getInvoqty() + "超出销售数:" + busDelieryitemPojo.getQuantity());
                            }
                        } else {
                            throw new RuntimeException("关联单据丢失:" + deliuid);
                        }
                    } else {
                        // 超数检查
                        BusDeductionitemPojo busDeductionitemPojo = this.busDeductionitemMapper.getEntity(deliitemid, tid);
                        if (busDeductionitemPojo != null) {
                            if (busDeductionitemPojo.getInvoqty() > busDeductionitemPojo.getQuantity()) {
                                //int Rowno = i + 1;
                                throw new RuntimeException(rownum + "行,开票总数:" + busDeductionitemPojo.getInvoqty() + "超出扣款数:" + busDeductionitemPojo.getQuantity());
                            }
                        } else {
                            throw new RuntimeException("关联单据丢失:" + deliuid);
                        }
                    }
                }
                checkCount++;
                if (checkCount == changeItems.size()) {
                    missionMsg.put("finish", totalTasks);
                    missionMsg.put("id", busInvoicePojo.getId()); //返回下最终创建成功的主表id
                    this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
                }
            }
            return state_redisKey;
        } catch (Exception e) {
            // 捕获所有异常，并记录错误信息
            missionMsg.put("error", "更新发票失败: " + e.getMessage());
            this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
            throw e; // 重新抛出异常
        }
    }


    @Override
    @Transactional
    public String delete(String key, String tid) {
        BusInvoicePojo busInvoicePojo = this.getBillEntity(key, tid);
        List<String> lstcite = getCiteBillName(busInvoicePojo.getId(), busInvoicePojo.getTenantid());
        if (!lstcite.isEmpty()) {
            throw new RuntimeException("发票已被引用,禁止删:" + lstcite);
        }
        //Item子表处理
        List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusInvoiceitemPojo busInvoiceitemPojo : lst) {
                this.busInvoiceitemMapper.delete(busInvoiceitemPojo.getId(), tid);
                String delitype = busInvoiceitemPojo.getDelitype();
                String deliitemid = busInvoiceitemPojo.getDeliitemid();
                String deliuid = busInvoiceitemPojo.getDeliuid();
                if (delitype.equals("发出商品") || delitype.equals("订单退货") ||
                        delitype.equals("其他发货") || delitype.equals("其他退货") ||
                        delitype.equals("退货返工") || delitype.equals("返工补发")) {
                    this.busInvoiceMapper.updateDeliInvoFinish(deliitemid, tid);
                    this.busInvoiceMapper.updateDeliInvoCount(deliitemid, tid);

                } else {
                    this.busInvoiceMapper.updateDeduInvoFinish(deliitemid, tid);
                    this.busInvoiceMapper.updateDeduInvoCount(deliitemid, tid);
                }
                //更新采购单的发票数量和开票行数 20231226
                if (isNotBlank(busInvoiceitemPojo.getMachitemid()) && isNotBlank(busInvoiceitemPojo.getMachuid())) {
                    // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                    syncMachAboutInvo(busInvoiceitemPojo.getMachitemid(), busInvoiceitemPojo.getMachuid(), tid);
                }
            }
        }
        this.busInvoiceMapper.delete(key, tid);
        return busInvoicePojo.getRefno();
    }

    @Override
    @Transactional
    public String deleteStart(String key, String tid, String userid, boolean isMachSyncAllowed) {
        String state_redisKey = MyConstant.ASYNC_BUSINVOIC_STATE + tid + userid; // 获取进度条的redisKey
        Map<String, Object> missionMsg = new HashMap<>(); // 进度条提示信息：移动到方法开始处
        // 收集所有增删改的中delitype、DeliItemid、Machitemid、Machuid,最后同意做批量同步
        List<Map<String, String>> changeItems = new ArrayList<>();
        try {
            // 有锁，查看进度
            missionMsg = redisService.getCacheObject(state_redisKey);
            if (missionMsg != null) {
                throw new RuntimeException("发票" + missionMsg.get("type") + "任务正在处理中...进度：" + missionMsg.get("finish") + "/" + missionMsg.get("total"));
            }

            BusInvoicePojo busInvoicePojo = this.getBillEntity(key, tid);
            List<String> lstcite = getCiteBillName(busInvoicePojo.getId(), busInvoicePojo.getTenantid());
            if (!lstcite.isEmpty()) {
                throw new RuntimeException("发票已被引用,禁止删:" + lstcite);
            }

            // Item子表处理
            List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();
            if (CollectionUtils.isNotEmpty(lst)) {
                // 所有要删除的子表id集合
                List<String> delidList = lst.stream()
                        .map(BusInvoiceitemPojo::getId) // 假设 BusInvoiceitemPojo 有 getId() 方法
                        .collect(Collectors.toList());
                // 循环每个删除item子表
                for (BusInvoiceitemPojo delitem : lst) {

                    // 收集删除项的相关信息
                    Map<String, String> delItemData = new HashMap<>();
                    delItemData.put("delitype", delitem.getDelitype());
                    delItemData.put("deliitemid", delitem.getDeliitemid());
                    delItemData.put("deliuid", delitem.getDeliuid());
                    delItemData.put("machitemid", delitem.getMachitemid());
                    delItemData.put("machuid", delitem.getMachuid());
                    delItemData.put("rownum", String.valueOf(delitem.getRownum() + 1));
                    changeItems.add(delItemData);

                }
                //批量删除
                busInvoiceitemMapper.batchDelete(delidList, tid);


                // 拿到所有的deliItemIds (delitype为发货单或扣款单的)
                List<String> deliItemIds = new ArrayList<>();
                List<String> deliItemIdInDedus = new ArrayList<>();
                List<String> machitemids = new ArrayList<>();

                for (Map<String, String> changeItem : changeItems) {
                    String delitype = changeItem.get("delitype");
                    String deliitemid = changeItem.get("deliitemid");
                    String machitemid = changeItem.get("machitemid");
                    String machuid = changeItem.get("machuid");
                    machitemids.add(machitemid);
                    if (!deliitemid.isEmpty()) {
                        // 根据delitype类型进行分类
                        if (delitype.equals("发出商品") || delitype.equals("订单退货") ||
                                delitype.equals("其他发货") || delitype.equals("其他退货") ||
                                delitype.equals("退货返工") || delitype.equals("返工补发")) {
                            deliItemIds.add(deliitemid);  // 加入deliItemIds集合
                        } else {
                            deliItemIdInDedus.add(deliitemid);  // 加入deliItemIdInDedus集合
                        }
                    }
                }

                // 总共需要更新的数量 （+1防止下面超数检查还没进行就finish=total完成了）
                int totalTasks;
                if (isMachSyncAllowed) {
                    totalTasks = deliItemIds.size() + deliItemIdInDedus.size() + machitemids.size();
                } else {
                    totalTasks = deliItemIds.size() + deliItemIdInDedus.size();
                }
                AtomicInteger currentCount = new AtomicInteger(0);

                //------------1设置当前计算任务进度，类型，总数，已完成数
                missionMsg = new HashMap<>();// 初始化任务信息
                missionMsg.put("total", totalTasks);
                missionMsg.put("finish", 0);
                missionMsg.put("type", busInvoicePojo.getRefno() + "删除");
                this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
                // 批量更新操作 关联发货单（分批处理，打印进度）
                if (!deliItemIds.isEmpty()) {
                    syncDeliAboutInvoBatch(deliItemIds, tid, state_redisKey, totalTasks, currentCount, missionMsg);
                }
                // 批量更新操作 关联扣款单（分批处理，打印进度）
                if (!deliItemIdInDedus.isEmpty()) {
                    syncDeduAboutInvoBatch(deliItemIdInDedus, tid, state_redisKey, totalTasks, currentCount, missionMsg);
                }
                // （通过系统参数判断是否进行销售订单同步）批量更新操作 关联销售订单（很慢，在这里面打印进度）
                if (isMachSyncAllowed && CollectionUtils.isNotEmpty(machitemids)) {
                    syncMachAboutInvoBatch(machitemids, tid, state_redisKey, totalTasks, currentCount, missionMsg);
                }
                // 删除是一次性批量删掉的，看不到进度条的
                currentCount.incrementAndGet();
                missionMsg.put("finish", totalTasks); // 加1 500/500 不用检查超数，直接算完成
                missionMsg.put("id", busInvoicePojo.getId()); //返回下最终创建成功的id
                this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
            } else {
                missionMsg = new HashMap<>();// 初始化任务信息
                missionMsg.put("total", 1);
                missionMsg.put("finish", 1); // 加1 500/500 不用检查超数，直接算完成
                missionMsg.put("id", busInvoicePojo.getId()); //返回下最终创建成功的id
                this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
            }
            this.busInvoiceMapper.delete(key, tid);
            return busInvoicePojo.getRefno();
        } catch (Exception e) {
            // 捕获所有异常，包括自定义抛出的 RuntimeException
            missionMsg.put("error", "删除发票失败: " + e.getMessage());
            this.redisService.setCacheObject(state_redisKey, missionMsg, 3L, TimeUnit.SECONDS);
            throw e; // 重新抛出异常
        }
    }


    private static void cleanNull(BusInvoicePojo busInvoicePojo) {
        if (busInvoicePojo.getRefno() == null) busInvoicePojo.setRefno("");
        if (busInvoicePojo.getBilltype() == null) busInvoicePojo.setBilltype("");
        if (busInvoicePojo.getBilltitle() == null) busInvoicePojo.setBilltitle("");
        if (busInvoicePojo.getBilldate() == null) busInvoicePojo.setBilldate(new Date());
        if (busInvoicePojo.getGroupid() == null) busInvoicePojo.setGroupid("");
        if (busInvoicePojo.getTaxamount() == null) busInvoicePojo.setTaxamount(0D);
        if (busInvoicePojo.getAmount() == null) busInvoicePojo.setAmount(0D);
        if (busInvoicePojo.getTaxtotal() == null) busInvoicePojo.setTaxtotal(0D);
        if (busInvoicePojo.getTaxrate() == null) busInvoicePojo.setTaxrate(0);
        if (busInvoicePojo.getInvocode() == null) busInvoicePojo.setInvocode("");
        if (busInvoicePojo.getInvodate() == null) busInvoicePojo.setInvodate(new Date());
        if (busInvoicePojo.getAimdate() == null) busInvoicePojo.setAimdate(new Date());
        if (busInvoicePojo.getReceipted() == null) busInvoicePojo.setReceipted(0D);
        if (busInvoicePojo.getSummary() == null) busInvoicePojo.setSummary("");
        if (busInvoicePojo.getCreateby() == null) busInvoicePojo.setCreateby("");
        if (busInvoicePojo.getCreatebyid() == null) busInvoicePojo.setCreatebyid("");
        if (busInvoicePojo.getCreatedate() == null) busInvoicePojo.setCreatedate(new Date());
        if (busInvoicePojo.getLister() == null) busInvoicePojo.setLister("");
        if (busInvoicePojo.getListerid() == null) busInvoicePojo.setListerid("");
        if (busInvoicePojo.getModifydate() == null) busInvoicePojo.setModifydate(new Date());
        if (busInvoicePojo.getAssessor() == null) busInvoicePojo.setAssessor("");
        if (busInvoicePojo.getAssessorid() == null) busInvoicePojo.setAssessorid("");
        if (busInvoicePojo.getAssessdate() == null) busInvoicePojo.setAssessdate(new Date());
        if (busInvoicePojo.getStatecode() == null) busInvoicePojo.setStatecode("");
        if (busInvoicePojo.getStatedate() == null) busInvoicePojo.setStatedate(new Date());
        if (busInvoicePojo.getClosed() == null) busInvoicePojo.setClosed(0);
        if (busInvoicePojo.getDisannulmark() == null) busInvoicePojo.setDisannulmark(0);
        if (busInvoicePojo.getFmdocmark() == null) busInvoicePojo.setFmdocmark(0);
        if (busInvoicePojo.getFmdoccode() == null) busInvoicePojo.setFmdoccode("");
        if (busInvoicePojo.getOperator() == null) busInvoicePojo.setOperator("");
        if (busInvoicePojo.getFirstamt() == null) busInvoicePojo.setFirstamt(0D);
        if (busInvoicePojo.getLastamt() == null) busInvoicePojo.setLastamt(0D);
        if (busInvoicePojo.getBilltaxamount() == null) busInvoicePojo.setBilltaxamount(0D);
        if (busInvoicePojo.getBilltaxtotal() == null) busInvoicePojo.setBilltaxtotal(0D);
        if (busInvoicePojo.getBillamount() == null) busInvoicePojo.setBillamount(0D);
        if (busInvoicePojo.getCustom1() == null) busInvoicePojo.setCustom1("");
        if (busInvoicePojo.getCustom2() == null) busInvoicePojo.setCustom2("");
        if (busInvoicePojo.getCustom3() == null) busInvoicePojo.setCustom3("");
        if (busInvoicePojo.getCustom4() == null) busInvoicePojo.setCustom4("");
        if (busInvoicePojo.getCustom5() == null) busInvoicePojo.setCustom5("");
        if (busInvoicePojo.getCustom6() == null) busInvoicePojo.setCustom6("");
        if (busInvoicePojo.getCustom7() == null) busInvoicePojo.setCustom7("");
        if (busInvoicePojo.getCustom8() == null) busInvoicePojo.setCustom8("");
        if (busInvoicePojo.getCustom9() == null) busInvoicePojo.setCustom9("");
        if (busInvoicePojo.getCustom10() == null) busInvoicePojo.setCustom10("");
        if (busInvoicePojo.getRevision() == null) busInvoicePojo.setRevision(0);
        if (busInvoicePojo.getTenantid() == null) busInvoicePojo.setTenantid("");
    }


    /**
     * 审核数据
     *
     * @param busInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo approval(BusInvoicePojo busInvoicePojo) {
        //主表更改
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        BeanUtils.copyProperties(busInvoicePojo, busInvoiceEntity);
        this.busInvoiceMapper.approval(busInvoiceEntity);
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param key 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo disannul(String key, Integer type, LoginUser loginUser) {
        String strType = type == 1 ? "作废" : "恢复";
        BusInvoicePojo dbPojo = this.busInvoiceMapper.getEntity(key, loginUser.getTenantid());
        if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
            if (dbPojo.getClosed() == 1) {
                throw new RuntimeException(dbPojo.getRefno() + "已关闭,禁止作废操作");
            }
            if (dbPojo.getReceipted() > 0) {
                throw new RuntimeException(dbPojo.getRefno() + "已被引用,禁止作废操作");
            }
        } else {
            throw new RuntimeException(dbPojo.getRefno() + "已" + strType + ",无需操作");
        }
        //主表更改
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        busInvoiceEntity.setId(key);
        busInvoiceEntity.setDisannulmark(type);
        busInvoiceEntity.setLister(loginUser.getRealname());
        busInvoiceEntity.setListerid(loginUser.getUserid());
        busInvoiceEntity.setModifydate(new Date());
        busInvoiceEntity.setTenantid(loginUser.getTenantid());
        this.busInvoiceMapper.update(busInvoiceEntity);
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());

    }

    /**
     * 作废数据
     *
     * @param key 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo closed(String key, Integer type, LoginUser loginUser) {
        String strType = type == 1 ? "关闭" : "开启";
        BusInvoicePojo dbPojo = this.busInvoiceMapper.getEntity(key, loginUser.getTenantid());
        if (!Objects.equals(dbPojo.getClosed(), type)) {
            if (dbPojo.getDisannulmark() == 1) {
                throw new RuntimeException(dbPojo.getRefno() + "已作废,禁止操作");
            }
        } else {
            throw new RuntimeException(dbPojo.getRefno() + "已" + strType + ",无需操作");
        }

        //主表更改
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        busInvoiceEntity.setId(key);
        busInvoiceEntity.setClosed(type);
        busInvoiceEntity.setLister(loginUser.getRealname());
        busInvoiceEntity.setListerid(loginUser.getUserid());
        busInvoiceEntity.setModifydate(new Date());
        busInvoiceEntity.setTenantid(loginUser.getTenantid());
        this.busInvoiceMapper.update(busInvoiceEntity);
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
    }


    @Override
    // 查询Item是否被引用
    public List<String> getCiteBillName(String key, String tid) {
        return this.busInvoiceMapper.getCiteBillName(key, tid);
    }

    @Override
    // 拉取某个客户所有待对账项目，送货+扣款
    public List<BusInvoiceitemPojo> pullItem(QueryParam queryParam, String groupid) {
        return this.busInvoiceMapper.pullItem(queryParam.getDateRange().getStartDate(), queryParam.getDateRange().getEndDate(), groupid, queryParam.getTenantid());
    }

}
