package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusChatgroupitemEntity;
import inks.service.std.sale.domain.pojo.BusChatgroupitemPojo;
import inks.service.std.sale.mapper.BusChatgroupitemMapper;
import inks.service.std.sale.service.BusChatgroupitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 客服分组客服子表(BusChatgroupitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-08 15:49:08
 */
@Service("busChatgroupitemService")
public class BusChatgroupitemServiceImpl implements BusChatgroupitemService {
    @Resource
    private BusChatgroupitemMapper busChatgroupitemMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusChatgroupitemPojo getEntity(String key,String tid) {
        return this.busChatgroupitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusChatgroupitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusChatgroupitemPojo> lst = busChatgroupitemMapper.getPageList(queryParam);
            PageInfo<BusChatgroupitemPojo> pageInfo = new PageInfo<BusChatgroupitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusChatgroupitemPojo> getList(String Pid,String tid) { 
        try {
            List<BusChatgroupitemPojo> lst = busChatgroupitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busChatgroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusChatgroupitemPojo insert(BusChatgroupitemPojo busChatgroupitemPojo) {
        //初始化item的NULL
        BusChatgroupitemPojo itempojo =this.clearNull(busChatgroupitemPojo);
        BusChatgroupitemEntity busChatgroupitemEntity = new BusChatgroupitemEntity(); 
        BeanUtils.copyProperties(itempojo,busChatgroupitemEntity);
          //生成雪花id
          busChatgroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busChatgroupitemEntity.setRevision(1);  //乐观锁      
          this.busChatgroupitemMapper.insert(busChatgroupitemEntity);
        return this.getEntity(busChatgroupitemEntity.getId(),busChatgroupitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busChatgroupitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusChatgroupitemPojo update(BusChatgroupitemPojo busChatgroupitemPojo) {
        BusChatgroupitemEntity busChatgroupitemEntity = new BusChatgroupitemEntity(); 
        BeanUtils.copyProperties(busChatgroupitemPojo,busChatgroupitemEntity);
        this.busChatgroupitemMapper.update(busChatgroupitemEntity);
        return this.getEntity(busChatgroupitemEntity.getId(),busChatgroupitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busChatgroupitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busChatgroupitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusChatgroupitemPojo clearNull(BusChatgroupitemPojo busChatgroupitemPojo){
     //初始化NULL字段
     if(busChatgroupitemPojo.getPid()==null) busChatgroupitemPojo.setPid("");
     if(busChatgroupitemPojo.getChatterid()==null) busChatgroupitemPojo.setChatterid("");
     if(busChatgroupitemPojo.getChattername()==null) busChatgroupitemPojo.setChattername("");
     if(busChatgroupitemPojo.getUserid()==null) busChatgroupitemPojo.setUserid("");
     if(busChatgroupitemPojo.getRownum()==null) busChatgroupitemPojo.setRownum(0);
     if(busChatgroupitemPojo.getRemark()==null) busChatgroupitemPojo.setRemark("");
     if(busChatgroupitemPojo.getCreateby()==null) busChatgroupitemPojo.setCreateby("");
     if(busChatgroupitemPojo.getCreatebyid()==null) busChatgroupitemPojo.setCreatebyid("");
     if(busChatgroupitemPojo.getCreatedate()==null) busChatgroupitemPojo.setCreatedate(new Date());
     if(busChatgroupitemPojo.getLister()==null) busChatgroupitemPojo.setLister("");
     if(busChatgroupitemPojo.getListerid()==null) busChatgroupitemPojo.setListerid("");
     if(busChatgroupitemPojo.getModifydate()==null) busChatgroupitemPojo.setModifydate(new Date());
     if(busChatgroupitemPojo.getCustom1()==null) busChatgroupitemPojo.setCustom1("");
     if(busChatgroupitemPojo.getCustom2()==null) busChatgroupitemPojo.setCustom2("");
     if(busChatgroupitemPojo.getCustom3()==null) busChatgroupitemPojo.setCustom3("");
     if(busChatgroupitemPojo.getCustom4()==null) busChatgroupitemPojo.setCustom4("");
     if(busChatgroupitemPojo.getTenantid()==null) busChatgroupitemPojo.setTenantid("");
     if(busChatgroupitemPojo.getTenantname()==null) busChatgroupitemPojo.setTenantname("");
     if(busChatgroupitemPojo.getRevision()==null) busChatgroupitemPojo.setRevision(0);
     return busChatgroupitemPojo;
     }
}
