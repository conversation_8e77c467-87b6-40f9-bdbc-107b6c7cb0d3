package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusReceaccountitemPojo;

import java.util.List;
/**
 * 应收账单Item(BusReceaccountitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-23 13:32:16
 */
public interface BusReceaccountitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceaccountitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusReceaccountitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusReceaccountitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busReceaccountitemPojo 实例对象
     * @return 实例对象
     */
    BusReceaccountitemPojo insert(BusReceaccountitemPojo busReceaccountitemPojo);

    /**
     * 修改数据
     *
     * @param busReceaccountitempojo 实例对象
     * @return 实例对象
     */
    BusReceaccountitemPojo update(BusReceaccountitemPojo busReceaccountitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busReceaccountitempojo 实例对象
     * @return 实例对象
     */
    BusReceaccountitemPojo clearNull(BusReceaccountitemPojo busReceaccountitempojo);
}
