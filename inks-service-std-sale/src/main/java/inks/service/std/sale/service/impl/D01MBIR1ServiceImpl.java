package inks.service.std.sale.service.impl; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/3
 * @param 销售大屏接口实现
 */

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.service.std.sale.mapper.D01MBIR1Mapper;
import inks.service.std.sale.service.D01MBIR1Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class D01MBIR1ServiceImpl implements D01MBIR1Service {
    @Resource
    private D01MBIR1Mapper d01MBIR1Mapper;

    /*
     *
     * <AUTHOR>
     * @description 获取客户订单金额排名
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam) {
        try {
            List<ChartPojo> lst = d01MBIR1Mapper.getSumAmtByGroupMax(queryParam);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumAmtByGroupMaxMach(QueryParam queryParam) {
        try {
            List<ChartPojo> lst = d01MBIR1Mapper.getSumAmtByGroupMaxMach(queryParam);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    //可以优化:可以改成先构建一个客户名的Map,销售,发货,收款金额数量都是0,然后再遍历数据库数据,如果有对应的客户名,则更新Map中的数据,最后将Map转换成List返回
    public List<Map<String, Object>> getSumAmtByGroupMachAndDeliAndReceipt(QueryParam queryParam, String groupname) {
        try {
            List<ChartPojo> lstDeli = d01MBIR1Mapper.getSumAmtByGroupMax(queryParam);
            List<ChartPojo> lstMach = d01MBIR1Mapper.getSumAmtByGroupMaxMach(queryParam);
            List<ChartPojo> lstReceipt = d01MBIR1Mapper.getSumAmtByDepositAndReceipt(queryParam);
//            lstReceipt.removeIf(chartPojo -> chartPojo.getName() == null);


            // 初始化结果集
            List<Map<String, Object>> resultList = new ArrayList<>();

            // 遍历发货单数据
            for (ChartPojo pojo : lstDeli) {
                // 获取客户名
                String name = pojo.getName();

                // 初始化客户数据
                Map<String, Object> customerData = new HashMap<>();
                customerData.put("name", name);
                customerData.put("deliamt", pojo.getValue());
                customerData.put("deliqty", pojo.getValueb());
                customerData.put("delisumqty", pojo.getValuec());
                customerData.put("receiptamt", 0d);

                // 遍历销售订单数据
                for (ChartPojo machPojo : lstMach) {
                    // 如果客户名相同
                    if (machPojo.getName().equals(name)) {
                        // 更新客户数据
                        customerData.put("machamt", machPojo.getValue());
                        customerData.put("machqty", machPojo.getValueb());
                        customerData.put("machsumqty", machPojo.getValuec());
                        break;
                    }
                }

                // 遍历收款数据
                for (ChartPojo receiptPojo : lstReceipt) {
                    // 如果客户名相同
                    if (receiptPojo.getName() != null && receiptPojo.getName().equals(name)) {
                        // 更新客户数据
                        customerData.put("receiptamt", receiptPojo.getValue());
                        break;
                    }
                }

                // 添加客户数据到结果集
                resultList.add(customerData);
            }

            // 补充没有对应上的客户数据
            for (ChartPojo pojo : lstMach) {
                // 获取客户名
                String name = pojo.getName();

                // 判断客户数据是否存在
                boolean exist = false;
                for (Map<String, Object> customerData : resultList) {
                    if (customerData.get("name").equals(name)) {
                        exist = true;
                        break;
                    }
                }

                // 如果客户数据不存在，则添加
                if (!exist && name != null) {
                    Map<String, Object> customerData = new HashMap<>();
                    customerData.put("name", name);
                    customerData.put("deliamt", 0d);
                    customerData.put("deliqty", 0d);
                    customerData.put("delisumqty", 0d);
                    customerData.put("machamt", pojo.getValue());
                    customerData.put("machqty", pojo.getValueb());
                    customerData.put("machsumqty", pojo.getValuec());
                    customerData.put("receiptamt", 0d);
                    resultList.add(customerData);
                }
            }

            // 遍历所有客户数据，如果客户数据不存在，则添加一个新的 Map 对象，金额和数量都为 0
            for (ChartPojo pojo : lstReceipt) {
                // 获取客户名
                String name = pojo.getName();

                // 判断客户数据是否存在
                boolean exist = false;
                for (Map<String, Object> customerData : resultList) {
                    if (name != null && customerData.get("name").equals(name)) {
                        exist = true;
                        break;
                    }
                }

                // 如果客户数据不存在，则添加
                if (!exist && name != null) {
                    Map<String, Object> customerData = new HashMap<>();
                    customerData.put("name", name);
                    customerData.put("deliamt", 0d);
                    customerData.put("deliqty", 0d);
                    customerData.put("delisumqty", 0d);
                    customerData.put("machamt", 0d);
                    customerData.put("machqty", 0d);
                    customerData.put("machsumqty", 0d);
                    customerData.put("receiptamt", pojo.getValue());
                    resultList.add(customerData);
                }
            }
            //resultList按machamt降序排序
            resultList = resultList.stream()
                    .sorted((o1, o2) -> {
                        Double machamt1 = o1.get("machamt") == null ? 0d : (Double) o1.get("machamt");
                        Double machamt2 = o2.get("machamt") == null ? 0d : (Double) o2.get("machamt");
                        return machamt2.compareTo(machamt1);
                    })
                    .collect(Collectors.toList());
            // 使用 Stream 过滤出符合模糊查询条件的客户数据
            if (StringUtils.isNotBlank(groupname)) {
                resultList = resultList.stream()
                        .filter(customerData -> {
                            String customerName = (String) customerData.get("name");
                            // 使用正则表达式进行模糊匹配，类似 SQL 中的 LIKE
                            return customerName != null && customerName.matches(".*" + groupname + ".*");
                        })
                        .collect(Collectors.toList());
            }

            return resultList;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 货品金额排名
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam) {
        try {
            List<ChartPojo> sumAmtByGoodsMax = this.d01MBIR1Mapper.getSumAmtByGoodsMax(queryParam);
            for (ChartPojo amtByGoodsMax : sumAmtByGoodsMax) {
                String goodsid = amtByGoodsMax.getCode();
                //获取当前货品在销售单的总金额
                ChartPojo sumAmtByGoodsMach = d01MBIR1Mapper.getSumAmtByGoodsMach(goodsid, queryParam.getTenantid());
                amtByGoodsMax.setValueb(sumAmtByGoodsMach.getValue());
            }
            return sumAmtByGoodsMax;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumAmtByGoodsMaxMach(QueryParam queryParam) {
        try {
            List<ChartPojo> sumAmtByGoodsMax = this.d01MBIR1Mapper.getSumAmtByGoodsMaxOnlyMach(queryParam);
            return sumAmtByGoodsMax;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 业务员订单金额占比
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtBySalesman(QueryParam queryParam) {
        try {
            return d01MBIR1Mapper.getSumAmtBySalesman(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumAmtBySalesmanMach(QueryParam queryParam) {
        try {
            return d01MBIR1Mapper.getSumAmtBySalesmanMach(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年度
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByYear(QueryParam queryParam, Integer trend) {
        try {
            //将 SearchPojo对象转换成json对象
            // JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            String dateprefix = DateUtils.parseDateToStr("yyyy", queryParam.getDateRange().getStartDate());
            List<String> list = new ArrayList<>();
            //从SearchPojo 对象中取出时间
            list.add(dateprefix + "-01");
            list.add(dateprefix + "-02");
            list.add(dateprefix + "-03");
            list.add(dateprefix + "-04");
            list.add(dateprefix + "-05");
            list.add(dateprefix + "-06");
            list.add(dateprefix + "-07");
            list.add(dateprefix + "-08");
            list.add(dateprefix + "-09");
            list.add(dateprefix + "-10");
            list.add(dateprefix + "-11");
            list.add(dateprefix + "-12");
            //统计值，趋势时为累加，普通时常态为0.0
            Double sum = 0.0;
            queryParam.setPageSize(100);
            List<ChartPojo> DbPojoList = d01MBIR1Mapper.getSumAmtByYear(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            //循环月list
            for (int i = 0; i < list.size(); i++) {
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                chartPojo.setValue(sum);   // 先赋值为最大值
                for (int j = 0; j < DbPojoList.size(); j++) {
                    if (DbPojoList.get(j) != null) {
                        //判断如果月等于数据库返回月那么值就等于数据库返回值 否则为0
                        if (DbPojoList.get(j).getName().equals(list.get(i))) {
                            //因为是趋势图 所以采用累加的形式
                            if (trend != null && trend == 1) {
                                sum += DbPojoList.get(j).getValue();
                                chartPojo.setValue(sum);
                            } else {
                                chartPojo.setValue(DbPojoList.get(j).getValue());
                            }
                            break;
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByMonth(QueryParam queryParam, Integer trend) {
        try {
            //将object对象转行成json对象
            //JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            //  System.out.println(resultStr.getString("StartDate"));
            String strStartDate = DateUtils.dateTime(queryParam.getDateRange().getStartDate());
            ;
            List<String> dateList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(strStartDate));
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
            String nowDate = dateSdf.format(new Date());
            // 到下个月不在累计
            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(strStartDate.split("-")[1])) {
                // 至本年月日,不在计算
                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
                    dateList.add(dateSdf.format(calendar.getTime()));
                    calendar.add(Calendar.DATE, 1);
                    break;
                }
                dateList.add(dateSdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
            List<ChartPojo> DbPojoList = d01MBIR1Mapper.getSumAmtByMonth(queryParam);
            //统计值，趋势时为累加，普通时常态为0.0
            Double sum = 0.0;
            Double sumb = 0.0;
            List<ChartPojo> pojoList = new ArrayList<>();
            for (String s : dateList) {
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(s);
                chartPojo.setValue(sum);   // 先赋值为最大值
                chartPojo.setValueb(sumb);   // 先赋值为最大值
                for (ChartPojo pojo : DbPojoList) {
                    if (pojo != null) {
                        if (pojo.getName().equals(s)) {
                            if (trend != null && trend == 1) {
                                sum += pojo.getValue();
                                chartPojo.setValue(sum);
                                sumb += pojo.getValueb();
                                chartPojo.setValueb(sumb);
                            } else {
                                chartPojo.setValue(pojo.getValue());
                                chartPojo.setValueb(pojo.getValueb());
                            }
                            break;
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * @return
     * @author: nanno
     * @description: 销售趋势图月
     * @createTime: 2023/3/9 9:42
     * @params * @Param: null
     */
    @Override
    public List<ChartPojo> getSumAmtByMonthMach(QueryParam queryParam, Integer trend) {
        try {
            //将object对象转行成json对象
            //JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            //  System.out.println(resultStr.getString("StartDate"));
            String strStartDate = DateUtils.dateTime(queryParam.getDateRange().getStartDate());
            ;
            List<String> dateList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(strStartDate));
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
            String nowDate = dateSdf.format(new Date());
            // 到下个月不在累计
            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(strStartDate.split("-")[1])) {
                // 至本年月日,不在计算
                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
                    dateList.add(dateSdf.format(calendar.getTime()));
                    calendar.add(Calendar.DATE, 1);
                    break;
                }
                dateList.add(dateSdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
            List<ChartPojo> DbPojoList = d01MBIR1Mapper.getSumAmtByMonthMach(queryParam);
            //统计值，趋势时为累加，普通时常态为0.0
            Double sum = 0.0;
            List<ChartPojo> pojoList = new ArrayList<>();
            for (int i = 0; i < dateList.size(); i++) {
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(dateList.get(i));
                chartPojo.setValue(sum);   // 先赋值为最大值
                for (int j = 0; j < DbPojoList.size(); j++) {
                    if (DbPojoList.get(j) != null) {
                        if (DbPojoList.get(j).getName().equals(dateList.get(i))) {
                            if (trend != null && trend == 1) {
                                sum += DbPojoList.get(j).getValue();
                                chartPojo.setValue(sum);
                            } else {
                                chartPojo.setValue(DbPojoList.get(j).getValue());
                            }
                            break;
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByDay(QueryParam queryParam, Integer trend) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 创建日期列表
            List<String> dateList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            for (int i = 6; i >= 0; i--) {
                calendar.add(Calendar.DATE, -1);
                dateList.add(format.format(calendar.getTime()));
            }

            // 设置查询参数的日期范围
            Date startDate = dateTimeFormat.parse(dateList.get(0) + " 00:00:00");
            Date endDate = dateTimeFormat.parse(dateList.get(dateList.size() - 1) + " 23:59:59");
            queryParam.setDateRange(new DateRange("", startDate, endDate));

            // 查询数据库
            List<ChartPojo> dbPojoList = d01MBIR1Mapper.getSumAmtByDay(queryParam);

            // 创建结果列表
            List<ChartPojo> pojoList = new ArrayList<>();
            BigDecimal sum = BigDecimal.ZERO;
            for (String date : dateList) {
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(date);
                chartPojo.setValue(sum.doubleValue()); // 先设置为累计值

                for (ChartPojo dbPojo : dbPojoList) {
                    if (dbPojo != null && dbPojo.getName().equals(date)) {
                        BigDecimal dbValue = BigDecimal.valueOf(dbPojo.getValue());
                        if (trend != null && trend == 1) {
                            sum = sum.add(dbValue);
                            chartPojo.setValue(sum.doubleValue());
                        } else {
                            chartPojo.setValue(dbValue.doubleValue());
                        }
                        break;
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public ChartPojo getTagSumAmtQtyByDate(QueryParam queryParam) {
        try {
            ChartPojo chartPojo = d01MBIR1Mapper.getTagSumAmtQtyByDate(queryParam);
            chartPojo.setName(DateUtils.dateTime(queryParam.getDateRange().getStartDate()) + "~" + DateUtils.dateTime(queryParam.getDateRange().getEndDate()));
            return chartPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public ChartPojo getTagSumAmtQtyByDateMach(QueryParam queryParam) {
        try {
            ChartPojo chartPojo = d01MBIR1Mapper.getTagSumAmtQtyByDateMach(queryParam);
            chartPojo.setName(DateUtils.dateTime(queryParam.getDateRange().getStartDate()) + "~" + DateUtils.dateTime(queryParam.getDateRange().getEndDate()));
            return chartPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * @return
     * @author: nanno
     * @description: 订单完成率
     * @createTime: 2023/3/8 16:35
     * @params * @Param: null
     */
    @Override
    public ChartPojo getMachFinishRate(QueryParam queryParam) {
        try {
            return d01MBIR1Mapper.getMachFinishRate(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public ChartPojo getDeliFinishRate(QueryParam queryParam) {
        try {
            return d01MBIR1Mapper.getDeliFinishRate(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public ChartPojo getReceiptFinishRate(QueryParam queryParam) {
        try {
            return d01MBIR1Mapper.getReceiptFinishRate(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getGroupDistribution(String tid) {
        try {
            return d01MBIR1Mapper.getGroupDistribution(tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<ChartPojo> getSpuWeightGroupByGroup(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        List<ChartPojo> lst = d01MBIR1Mapper.getSpuWeightGroupByGroup(queryParam);
        PageInfo<ChartPojo> pageInfo = new PageInfo<ChartPojo>(lst);
        return pageInfo;
    }

    @Override
    public List<ChartPojo> getSpuWeightGroupByCaiZhi(QueryParam queryParam) {
        return d01MBIR1Mapper.getSpuWeightGroupByCaiZhi(queryParam);
    }

    @Override
    public List<ChartPojo> getSpuWeightGroupByGongYi(QueryParam queryParam) {
        return d01MBIR1Mapper.getSpuWeightGroupByGongYi(queryParam);
    }

    @Override
    public PageInfo<Map<String, Object>> getSpuWeightGroupByGuiGe(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        List<Map<String, Object>> spuWeightGroupByGuiGe = d01MBIR1Mapper.getSpuWeightGroupByGuiGe(queryParam);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(spuWeightGroupByGuiGe);

        return pageInfo;
    }


    //    /*
//     *
//     * <AUTHOR>
//     * @description 本月销售额
//     * @date 2022/1/3
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public ChartPojo getTagSumAmtQtyByMonth(QueryParam queryParam) {
//        try {
//            return d01MBIR1Mapper.getTagSumAmtQtyByMonth(queryParam);
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//    /*
//     *
//     * <AUTHOR>
//     * @description 根据当前月查询本月开票
//     * @date 2022/1/3
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getTagSumAmtByMonth(String tid) {
//        try {
//            return d01MBIR1Mapper.getTagSumAmtByMonth(tid);
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//
//     /*
//      *
//      * <AUTHOR>
//      * @description 订单逾期
//      * @date 2022/1/3
//      * @param * @param null
//      * @return
//      */
//    @Override
//    public ChartPojo getPageList(String tid) {
//        try {
//            return d01MBIR1Mapper.getPageList(tid);
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//    /*
//     *
//     * <AUTHOR>
//     * @description 热销产品
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getSumByGoodsMax(QueryParam queryParam) {
//        try {
//            return d01MBIR1Mapper.getSumAmtByGoodsMax(queryParam);
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }

//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图年
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getSumAmtByYearMax(QueryParam queryParam) {
//        try {
//            //将 SearchPojo对象转换成json对象
//            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
//            List<String> list = new ArrayList<>();
//            //从SearchPojo 对象中取出时间
//            list.add(resultStr.getString("StartDate")+"-01");
//            list.add(resultStr.getString("StartDate")+"-02");
//            list.add(resultStr.getString("StartDate")+"-03");
//            list.add(resultStr.getString("StartDate")+"-04");
//            list.add(resultStr.getString("StartDate")+"-05");
//            list.add(resultStr.getString("StartDate")+"-06");
//            list.add(resultStr.getString("StartDate")+"-07");
//            list.add(resultStr.getString("StartDate")+"-08");
//            list.add(resultStr.getString("StartDate")+"-09");
//            list.add(resultStr.getString("StartDate")+"-10");
//            list.add(resultStr.getString("StartDate")+"-11");
//            list.add(resultStr.getString("StartDate")+"-12");
//            List<ChartPojo> chartPojoList = d01MBIR1Mapper.getSumAmtByYear(queryParam);
//            List<ChartPojo> pojoList = new ArrayList<>();
//            //循环月list
//            for(int i=0;i< list.size();i++){
//                ChartPojo chartPojo = new ChartPojo();
//                chartPojo.setName(list.get(i));
//                for(int j=0;j< chartPojoList.size();j++){
//                    if(chartPojoList.get(j)!=null){
//                        //判断如果月等于数据库返回月那么值就等于数据库返回值 否则为0
//                        if(chartPojoList.get(j).getName().equals(list.get(i))){
//                            //因为是趋势图 所以采用累加的形式
//                            chartPojo.setValue(chartPojoList.get(j).getValue());
//                            break;
//                        }
//                        else{
//                            chartPojo.setValue(0.0);
//                        }
//                    }
//                }
//                pojoList.add(chartPojo);
//            }
//            return pojoList;
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图月
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getSumAmtByMonthMax(QueryParam queryParam) {
//        try {
//            //将object对象转行成json对象
//            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
//            List<String> dateList = new ArrayList<>();
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(resultStr.getString("StartDate")));
//            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
//            String nowDate = dateSdf.format(new Date());
//            // 到下个月不在累计
//            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(resultStr.getString("StartDate").split("-")[1])) {
//                // 至本年月日,不在计算
//                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
//                    dateList.add(dateSdf.format(calendar.getTime()));
//                    calendar.add(Calendar.DATE, 1);
//                    break;
//                }
//                dateList.add(dateSdf.format(calendar.getTime()));
//                calendar.add(Calendar.DATE, 1);
//            }
//            List<ChartPojo> chartPojoList = d01MBIR1Mapper.getSumAmtByMonth(queryParam);
//            List<ChartPojo> pojoList = new ArrayList<>();
//            for(int i=0;i< dateList.size();i++){
//                ChartPojo chartPojo = new ChartPojo();
//                chartPojo.setName(dateList.get(i));
//                for(int j=0;j< chartPojoList.size();j++){
//                    if(chartPojoList.get(j)!=null){
//                        if(chartPojoList.get(j).getName().equals(dateList.get(i))){
//                            chartPojo.setValue(chartPojoList.get(j).getValue());
//                            break;
//                        }
//                        else{
//                            chartPojo.setValue(0.0);
//                        }
//                    }
//                }
//                pojoList.add(chartPojo);
//            }
//            return pojoList;
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图周
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getSumAmtByDayMax(QueryParam queryParam) {
//        try {
//            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
//            List<String> list = new ArrayList<>();
//            for(int i=7-1;i>=0;i--){
//                Calendar calendar = Calendar.getInstance();
//                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - i);
//                Date today = calendar.getTime();
//                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                list.add(format.format(today));
//            }
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//            DateRange dateRange = new DateRange("",simpleDateFormat.parse(list.get(0)),simpleDateFormat.parse(list.get(list.size()-1)));
//            queryParam.setDateRange(dateRange);
//            List<ChartPojo> chartPojoList = d01MBIR1Mapper.getSumAmtByDay(queryParam);
//            List<ChartPojo> pojoList = new ArrayList<>();
//            for(int i=0;i< list.size();i++){
//                ChartPojo chartPojo = new ChartPojo();
//                chartPojo.setName(list.get(i));
//                for(int j=0;j< chartPojoList.size();j++){
//                    if(chartPojoList.get(j)!=null){
//                        if(chartPojoList.get(j).getName().equals(list.get(i))){
//                            chartPojo.setValue(chartPojoList.get(j).getValue());
//                            break;
//                        }
//                        else{
//                            chartPojo.setValue(0.0);
//                        }
//                    }
//                }
//                pojoList.add(chartPojo);
//            }
//            return pojoList;
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//
//    @Override
//    public Integer getCountMachItemOnline(String tid) {
//        try {
//            return d01MBIR1Mapper.getCountMachItemOnline(tid);
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
}
