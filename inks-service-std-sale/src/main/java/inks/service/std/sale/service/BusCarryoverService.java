package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCarryoverPojo;
import inks.service.std.sale.domain.pojo.BusCarryoverinvoPojo;
import inks.service.std.sale.domain.pojo.BusCarryoverinvodetailPojo;
import inks.service.std.sale.domain.pojo.BusCarryoveritemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 货品账单(BusCarryover)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-27 08:50:19
 */
public interface BusCarryoverService {

    BusCarryoverPojo getEntity(String key,String tid);

    PageInfo<BusCarryoveritemdetailPojo> getPageList(QueryParam queryParam);

    BusCarryoverPojo getBillEntity(String key,String tid);

    PageInfo<BusCarryoverPojo> getBillList(QueryParam queryParam);

    PageInfo<BusCarryoverPojo> getPageTh(QueryParam queryParam);

    BusCarryoverPojo insert(BusCarryoverPojo busCarryoverPojo);

    BusCarryoverPojo update(BusCarryoverPojo busCarryoverpojo);

    int delete(String key,String tid);

    PageInfo<BusCarryoverinvodetailPojo> getInvoPageList(QueryParam queryParam);
}
