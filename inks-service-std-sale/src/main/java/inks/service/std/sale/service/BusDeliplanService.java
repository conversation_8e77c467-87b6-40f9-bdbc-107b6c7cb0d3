package inks.service.std.sale.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.*;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 发货计划(BusDeliplan)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:04
 */
public interface BusDeliplanService {

    BusDeliplanPojo getEntity(String key,String tid);

    PageInfo<BusDeliplanitemdetailPojo> getPageList(QueryParam queryParam);

    BusDeliplanPojo getBillEntity(String key,String tid);

    PageInfo<BusDeliplanPojo> getBillList(QueryParam queryParam);

    PageInfo<BusDeliplanPojo> getPageTh(QueryParam queryParam);

    BusDeliplanPojo insert(BusDeliplanPojo busDeliplanPojo);

    BusDeliplanPojo update(BusDeliplanPojo busDeliplanpojo);

    int delete(String key,String tid);

     BusDeliplanPojo approval(BusDeliplanPojo busDeliplanPojo);

    BusDeliplanPojo disannul(List<BusDeliplanitemPojo> lst, Integer type, LoginUser loginUser);

    BusDeliplanPojo closed(List<BusDeliplanitemPojo> lst, Integer type, LoginUser loginUser);

    PageInfo<Map<String, Object>> getPageListByMach(QueryParam queryParam);
}
