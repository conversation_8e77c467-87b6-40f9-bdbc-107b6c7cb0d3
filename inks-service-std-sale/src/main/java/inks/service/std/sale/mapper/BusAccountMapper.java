package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusAccountEntity;
import inks.service.std.sale.domain.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售账单(BusAccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-03 14:28:09
 */
@Mapper
public interface BusAccountMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusAccountitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusAccountPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busAccountEntity 实例对象
     * @return 影响行数
     */
    int insert(BusAccountEntity busAccountEntity);


    /**
     * 修改数据
     *
     * @param busAccountEntity 实例对象
     * @return 影响行数
     */
    int update(BusAccountEntity busAccountEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteByMonth(@Param("accountIds") List<String> accountIds, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busAccountPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusAccountPojo busAccountPojo);

    List<String> getDelInvoIds(BusAccountPojo busAccountPojo);

    List<String> getDelArapIds(BusAccountPojo busAccountPojo);

    List<String> getDelDeliIds(BusAccountPojo busAccountPojo);
    /**
     * 查询 被删除的Item
     *
     * @param tid 筛选条件
     * @return 查询结果
     */
    List<String> getCustomerIds(String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountPojo getMaxEntityByGroup(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询 所有Item
     *
     * @return 查询结果
     */
    List<BusAccountitemPojo> getMultItemList(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusAccountPojo> getNowPageList(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusAccountitemPojo> pullItemList(QueryParam queryParam);

    List<BusAccountinvoPojo> pullInvoList(QueryParam queryParam);

    List<BusAccountarapPojo> pullArapList(QueryParam queryParam);

    List<BusAccountdeliPojo> pullDeliList(QueryParam queryParam);

    List<BusCarryoveritemPojo> pullCarryItemList(QueryParam queryParam);

    List<BusInvocarryoverPojo> pullCarryInvoList(QueryParam queryParam);

    List<String> getAccountIdsForDeletion(Integer carryyear, Integer carrymonth, String tid, int batchSize);

    int deleteBusAccountItemByMonth(List<String> accountIds, String tid);

    int deleteBusAccountArapByMonth(List<String> accountIds, String tid);

    int deleteBusAccountDeliByMonth(List<String> accountIds, String tid);

    int deleteBusAccountInvoByMonth(List<String> accountIds, String tid);

    int deleteBusAccountByMonth(List<String> accountIds, String tid);
}

