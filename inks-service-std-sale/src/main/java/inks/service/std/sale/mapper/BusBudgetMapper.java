package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusBudgetEntity;
import inks.service.std.sale.domain.pojo.BusBudgetPojo;
import inks.service.std.sale.domain.pojo.BusBudgetitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成本预算(BusBudget)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-02 09:26:56
 */
@Mapper
public interface BusBudgetMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusBudgetPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusBudgetitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusBudgetPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busBudgetEntity 实例对象
     * @return 影响行数
     */
    int insert(BusBudgetEntity busBudgetEntity);

    
    /**
     * 修改数据
     *
     * @param busBudgetEntity 实例对象
     * @return 影响行数
     */
    int update(BusBudgetEntity busBudgetEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param busBudgetPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(BusBudgetPojo busBudgetPojo);
                                                                                                                                      /**
     * 修改数据
     *
     * @param busBudgetEntity 实例对象
     * @return 影响行数
     */
    int approval(BusBudgetEntity busBudgetEntity);

    int checkMachbillid(@Param("machbillid") String machbillid,@Param("tid") String tid);

    List<String> checkMachItemIds(@Param("machitemIds") List<String> machitemIds, @Param("tid") String tenantid);
}

