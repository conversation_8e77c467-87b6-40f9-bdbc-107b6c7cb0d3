package inks.service.std.sale.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.sale.domain.pojo.BusAccountdeliPojo;
import inks.service.std.sale.domain.BusAccountdeliEntity;
import inks.service.std.sale.mapper.BusAccountdeliMapper;
import inks.service.std.sale.service.BusAccountdeliService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 销售订单to发货单(BusAccountdeli)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-13 16:40:26
 */
@Service("busAccountdeliService")
public class BusAccountdeliServiceImpl implements BusAccountdeliService {
    @Resource
    private BusAccountdeliMapper busAccountdeliMapper;

    @Override
    public BusAccountdeliPojo getEntity(String key,String tid) {
        return this.busAccountdeliMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<BusAccountdeliPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountdeliPojo> lst = busAccountdeliMapper.getPageList(queryParam);
            PageInfo<BusAccountdeliPojo> pageInfo = new PageInfo<BusAccountdeliPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<BusAccountdeliPojo> getList(String Pid,String tid) { 
        try {
            List<BusAccountdeliPojo> lst = busAccountdeliMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public BusAccountdeliPojo insert(BusAccountdeliPojo busAccountdeliPojo) {
        //初始化item的NULL
        BusAccountdeliPojo itempojo =this.clearNull(busAccountdeliPojo);
        BusAccountdeliEntity busAccountdeliEntity = new BusAccountdeliEntity(); 
        BeanUtils.copyProperties(itempojo,busAccountdeliEntity);
          //生成雪花id
          busAccountdeliEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busAccountdeliEntity.setRevision(1);  //乐观锁      
          this.busAccountdeliMapper.insert(busAccountdeliEntity);
        return this.getEntity(busAccountdeliEntity.getId(),busAccountdeliEntity.getTenantid());
  
    }

    @Override
    public BusAccountdeliPojo update(BusAccountdeliPojo busAccountdeliPojo) {
        BusAccountdeliEntity busAccountdeliEntity = new BusAccountdeliEntity(); 
        BeanUtils.copyProperties(busAccountdeliPojo,busAccountdeliEntity);
        this.busAccountdeliMapper.update(busAccountdeliEntity);
        return this.getEntity(busAccountdeliEntity.getId(),busAccountdeliEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.busAccountdeliMapper.delete(key,tid) ;
    }

     @Override
     public BusAccountdeliPojo clearNull(BusAccountdeliPojo busAccountdeliPojo){
     //初始化NULL字段
     if(busAccountdeliPojo.getPid()==null) busAccountdeliPojo.setPid("");
     if(busAccountdeliPojo.getDirection()==null) busAccountdeliPojo.setDirection("");
     if(busAccountdeliPojo.getModulecode()==null) busAccountdeliPojo.setModulecode("");
     if(busAccountdeliPojo.getBilltype()==null) busAccountdeliPojo.setBilltype("");
     if(busAccountdeliPojo.getBilldate()==null) busAccountdeliPojo.setBilldate(new Date());
     if(busAccountdeliPojo.getBilltitle()==null) busAccountdeliPojo.setBilltitle("");
     if(busAccountdeliPojo.getBilluid()==null) busAccountdeliPojo.setBilluid("");
     if(busAccountdeliPojo.getBillid()==null) busAccountdeliPojo.setBillid("");
     if(busAccountdeliPojo.getOpenamount()==null) busAccountdeliPojo.setOpenamount(0D);
     if(busAccountdeliPojo.getInamount()==null) busAccountdeliPojo.setInamount(0D);
     if(busAccountdeliPojo.getOutamount()==null) busAccountdeliPojo.setOutamount(0D);
     if(busAccountdeliPojo.getCloseamount()==null) busAccountdeliPojo.setCloseamount(0D);
     if(busAccountdeliPojo.getRownum()==null) busAccountdeliPojo.setRownum(0);
     if(busAccountdeliPojo.getRemark()==null) busAccountdeliPojo.setRemark("");
     if(busAccountdeliPojo.getCustom1()==null) busAccountdeliPojo.setCustom1("");
     if(busAccountdeliPojo.getCustom2()==null) busAccountdeliPojo.setCustom2("");
     if(busAccountdeliPojo.getCustom3()==null) busAccountdeliPojo.setCustom3("");
     if(busAccountdeliPojo.getCustom4()==null) busAccountdeliPojo.setCustom4("");
     if(busAccountdeliPojo.getCustom5()==null) busAccountdeliPojo.setCustom5("");
     if(busAccountdeliPojo.getCustom6()==null) busAccountdeliPojo.setCustom6("");
     if(busAccountdeliPojo.getCustom7()==null) busAccountdeliPojo.setCustom7("");
     if(busAccountdeliPojo.getCustom8()==null) busAccountdeliPojo.setCustom8("");
     if(busAccountdeliPojo.getCustom9()==null) busAccountdeliPojo.setCustom9("");
     if(busAccountdeliPojo.getCustom10()==null) busAccountdeliPojo.setCustom10("");
     if(busAccountdeliPojo.getTenantid()==null) busAccountdeliPojo.setTenantid("");
     if(busAccountdeliPojo.getRevision()==null) busAccountdeliPojo.setRevision(0);
     return busAccountdeliPojo;
     }
}
