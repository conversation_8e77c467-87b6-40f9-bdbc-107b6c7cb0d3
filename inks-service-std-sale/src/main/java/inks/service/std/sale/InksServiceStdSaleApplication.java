package inks.service.std.sale;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;

@EnableAsync
@SpringBootApplication(scanBasePackages={"inks.api.feign.*","inks.common.log.*","inks.common.redis.*","inks.common.security.*","inks.common.core.*","inks.service.std.sale.*"})
@EnableSwagger2
@EnableDiscoveryClient
@EnableFeignClients(basePackages={"inks.api"})
public class InksServiceStdSaleApplication {

   public static void main(String[] args) throws UnknownHostException {
           ConfigurableApplicationContext application = SpringApplication.run(InksServiceStdSaleApplication.class, args);
           Environment env = application.getEnvironment();
           String ip = InetAddress.getLocalHost().getHostAddress();
           String port = env.getProperty("server.port");
           String property = env.getProperty("spring.application.name");
           String path = property == null ? "" : property;
           System.out.println(
                   "\n\t" +
                           "----------------------------------------------------------\n\t" +
                           "Application Sailrui-Boot is running! Access URLs:\n\t" +
                           "Local: \t\thttp://localhost:" + port + "/swagger-ui.html\n\t" +
                           "External: \thttp://" + ip + ":" + port + "/swagger-ui.html\n\t" +
                           "Nacos: \t\thttp://dev.inksyun.com:31080/" + path + "/swagger-ui.html\n\t" +
                           "------------------------------------------------------------");
       }

}
