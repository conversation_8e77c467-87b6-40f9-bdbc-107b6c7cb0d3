package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusSteppriceobjPojo;

import java.util.List;
/**
 * 阶价对象(BusSteppriceobj)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-31 21:22:30
 */
public interface BusSteppriceobjService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusSteppriceobjPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusSteppriceobjPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusSteppriceobjPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busSteppriceobjPojo 实例对象
     * @return 实例对象
     */
    BusSteppriceobjPojo insert(BusSteppriceobjPojo busSteppriceobjPojo);

    /**
     * 修改数据
     *
     * @param busSteppriceobjpojo 实例对象
     * @return 实例对象
     */
    BusSteppriceobjPojo update(BusSteppriceobjPojo busSteppriceobjpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busSteppriceobjpojo 实例对象
     * @return 实例对象
     */
    BusSteppriceobjPojo clearNull(BusSteppriceobjPojo busSteppriceobjpojo);
}
