package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusSteppriceitemPojo;

import java.util.List;
/**
 * 阶梯项目(BusSteppriceitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-31 21:22:06
 */
public interface BusSteppriceitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusSteppriceitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusSteppriceitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusSteppriceitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busSteppriceitemPojo 实例对象
     * @return 实例对象
     */
    BusSteppriceitemPojo insert(BusSteppriceitemPojo busSteppriceitemPojo);

    /**
     * 修改数据
     *
     * @param busSteppriceitempojo 实例对象
     * @return 实例对象
     */
    BusSteppriceitemPojo update(BusSteppriceitemPojo busSteppriceitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busSteppriceitempojo 实例对象
     * @return 实例对象
     */
    BusSteppriceitemPojo clearNull(BusSteppriceitemPojo busSteppriceitempojo);
}
