package inks.service.std.sale.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.sale.domain.pojo.BusDeliplanitemPojo;
import inks.service.std.sale.domain.BusDeliplanitemEntity;
import inks.service.std.sale.mapper.BusDeliplanitemMapper;
import inks.service.std.sale.service.BusDeliplanitemService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 发货计划明细(BusDeliplanitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:19
 */
@Service("busDeliplanitemService")
public class BusDeliplanitemServiceImpl implements BusDeliplanitemService {
    @Resource
    private BusDeliplanitemMapper busDeliplanitemMapper;

    @Override
    public BusDeliplanitemPojo getEntity(String key,String tid) {
        return this.busDeliplanitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<BusDeliplanitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeliplanitemPojo> lst = busDeliplanitemMapper.getPageList(queryParam);
            PageInfo<BusDeliplanitemPojo> pageInfo = new PageInfo<BusDeliplanitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<BusDeliplanitemPojo> getList(String Pid,String tid) { 
        try {
            List<BusDeliplanitemPojo> lst = busDeliplanitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public BusDeliplanitemPojo insert(BusDeliplanitemPojo busDeliplanitemPojo) {
        //初始化item的NULL
        BusDeliplanitemPojo itempojo =this.clearNull(busDeliplanitemPojo);
        BusDeliplanitemEntity busDeliplanitemEntity = new BusDeliplanitemEntity(); 
        BeanUtils.copyProperties(itempojo,busDeliplanitemEntity);
          //生成雪花id
          busDeliplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busDeliplanitemEntity.setRevision(1);  //乐观锁      
          this.busDeliplanitemMapper.insert(busDeliplanitemEntity);
        return this.getEntity(busDeliplanitemEntity.getId(),busDeliplanitemEntity.getTenantid());
  
    }

    @Override
    public BusDeliplanitemPojo update(BusDeliplanitemPojo busDeliplanitemPojo) {
        BusDeliplanitemEntity busDeliplanitemEntity = new BusDeliplanitemEntity(); 
        BeanUtils.copyProperties(busDeliplanitemPojo,busDeliplanitemEntity);
        this.busDeliplanitemMapper.update(busDeliplanitemEntity);
        return this.getEntity(busDeliplanitemEntity.getId(),busDeliplanitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.busDeliplanitemMapper.delete(key,tid) ;
    }

     @Override
     public BusDeliplanitemPojo clearNull(BusDeliplanitemPojo busDeliplanitemPojo){
     //初始化NULL字段
     if(busDeliplanitemPojo.getPid()==null) busDeliplanitemPojo.setPid("");
     if(busDeliplanitemPojo.getGoodsid()==null) busDeliplanitemPojo.setGoodsid("");
     if(busDeliplanitemPojo.getItemcode()==null) busDeliplanitemPojo.setItemcode("");
     if(busDeliplanitemPojo.getItemname()==null) busDeliplanitemPojo.setItemname("");
     if(busDeliplanitemPojo.getItemspec()==null) busDeliplanitemPojo.setItemspec("");
     if(busDeliplanitemPojo.getItemunit()==null) busDeliplanitemPojo.setItemunit("");
     if(busDeliplanitemPojo.getQuantity()==null) busDeliplanitemPojo.setQuantity(0D);
     if(busDeliplanitemPojo.getTaxprice()==null) busDeliplanitemPojo.setTaxprice(0D);
     if(busDeliplanitemPojo.getTaxamount()==null) busDeliplanitemPojo.setTaxamount(0D);
     if(busDeliplanitemPojo.getPrice()==null) busDeliplanitemPojo.setPrice(0D);
     if(busDeliplanitemPojo.getAmount()==null) busDeliplanitemPojo.setAmount(0D);
     if(busDeliplanitemPojo.getItemtaxrate()==null) busDeliplanitemPojo.setItemtaxrate(0);
     if(busDeliplanitemPojo.getTaxtotal()==null) busDeliplanitemPojo.setTaxtotal(0D);
     if(busDeliplanitemPojo.getStdprice()==null) busDeliplanitemPojo.setStdprice(0D);
     if(busDeliplanitemPojo.getStdamount()==null) busDeliplanitemPojo.setStdamount(0D);
     if(busDeliplanitemPojo.getRebate()==null) busDeliplanitemPojo.setRebate(0);
     if(busDeliplanitemPojo.getFreeqty()==null) busDeliplanitemPojo.setFreeqty(0D);
     if(busDeliplanitemPojo.getPickqty()==null) busDeliplanitemPojo.setPickqty(0D);
     if(busDeliplanitemPojo.getFinishqty()==null) busDeliplanitemPojo.setFinishqty(0D);
     if(busDeliplanitemPojo.getClosed()==null) busDeliplanitemPojo.setClosed(0);
     if(busDeliplanitemPojo.getRownum()==null) busDeliplanitemPojo.setRownum(0);
     if(busDeliplanitemPojo.getRemark()==null) busDeliplanitemPojo.setRemark("");
     if(busDeliplanitemPojo.getCiteuid()==null) busDeliplanitemPojo.setCiteuid("");
     if(busDeliplanitemPojo.getCiteitemid()==null) busDeliplanitemPojo.setCiteitemid("");
     if(busDeliplanitemPojo.getCustpo()==null) busDeliplanitemPojo.setCustpo("");
     if(busDeliplanitemPojo.getStatecode()==null) busDeliplanitemPojo.setStatecode("");
     if(busDeliplanitemPojo.getStatedate()==null) busDeliplanitemPojo.setStatedate(new Date());
     if(busDeliplanitemPojo.getMachtype()==null) busDeliplanitemPojo.setMachtype("");
     if(busDeliplanitemPojo.getVirtualitem()==null) busDeliplanitemPojo.setVirtualitem(0);
     if(busDeliplanitemPojo.getLocation()==null) busDeliplanitemPojo.setLocation("");
     if(busDeliplanitemPojo.getBatchno()==null) busDeliplanitemPojo.setBatchno("");
     if(busDeliplanitemPojo.getMachuid()==null) busDeliplanitemPojo.setMachuid("");
     if(busDeliplanitemPojo.getMachitemid()==null) busDeliplanitemPojo.setMachitemid("");
     if(busDeliplanitemPojo.getDisannulmark()==null) busDeliplanitemPojo.setDisannulmark(0);
     if(busDeliplanitemPojo.getDisannullister()==null) busDeliplanitemPojo.setDisannullister("");
     if(busDeliplanitemPojo.getDisannuldate()==null) busDeliplanitemPojo.setDisannuldate(new Date());
     if(busDeliplanitemPojo.getAttributejson()==null) busDeliplanitemPojo.setAttributejson("");
     if(busDeliplanitemPojo.getMachdate()==null) busDeliplanitemPojo.setMachdate(new Date());
     if(busDeliplanitemPojo.getSourcetype()==null) busDeliplanitemPojo.setSourcetype(0);
     if(busDeliplanitemPojo.getItemplandate()==null) busDeliplanitemPojo.setItemplandate(new Date());
     if(busDeliplanitemPojo.getCustom1()==null) busDeliplanitemPojo.setCustom1("");
     if(busDeliplanitemPojo.getCustom2()==null) busDeliplanitemPojo.setCustom2("");
     if(busDeliplanitemPojo.getCustom3()==null) busDeliplanitemPojo.setCustom3("");
     if(busDeliplanitemPojo.getCustom4()==null) busDeliplanitemPojo.setCustom4("");
     if(busDeliplanitemPojo.getCustom5()==null) busDeliplanitemPojo.setCustom5("");
     if(busDeliplanitemPojo.getCustom6()==null) busDeliplanitemPojo.setCustom6("");
     if(busDeliplanitemPojo.getCustom7()==null) busDeliplanitemPojo.setCustom7("");
     if(busDeliplanitemPojo.getCustom8()==null) busDeliplanitemPojo.setCustom8("");
     if(busDeliplanitemPojo.getCustom9()==null) busDeliplanitemPojo.setCustom9("");
     if(busDeliplanitemPojo.getCustom10()==null) busDeliplanitemPojo.setCustom10("");
     if(busDeliplanitemPojo.getTenantid()==null) busDeliplanitemPojo.setTenantid("");
     if(busDeliplanitemPojo.getRevision()==null) busDeliplanitemPojo.setRevision(0);
     return busDeliplanitemPojo;
     }
}
