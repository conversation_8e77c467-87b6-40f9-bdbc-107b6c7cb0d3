package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusDelicarryoverPojo;
import inks.service.std.sale.service.BusDelicarryoverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 货品账单:订单>发货(Bus_DeliCarryover)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-10 12:50:16
 */
@RestController
@RequestMapping("D01M11B2")
@Api(tags = "D01M11B2:货品账单:订单>发货")
public class D01M11B2Controller extends BusDelicarryoverController {
    @Resource
    private BusDelicarryoverService busDelicarryoverService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "销售订单>发货 货品账单明细 传入recid(Bus_AccountRec.id)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliCarryover.List")
    public R<PageInfo<BusDelicarryoverPojo>> getPageListByMonth(@RequestBody String json, String recid) {
        try {
            if (StringUtils.isBlank(recid)) return R.fail("请传入recid(Bus_AccountRec.id)");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliCarryover.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter;
            qpfilter = " and Bus_DeliCarryover.Recid=" + recid;
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelicarryoverService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
