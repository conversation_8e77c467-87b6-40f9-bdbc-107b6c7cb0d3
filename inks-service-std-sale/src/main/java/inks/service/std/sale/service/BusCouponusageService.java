package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCouponusagePojo;
import com.github.pagehelper.PageInfo;

/**
 * 优惠券使用记录(Bus_CouponUsage)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
public interface BusCouponusageService {

    BusCouponusagePojo getEntity(String key,String tid);

    PageInfo<BusCouponusagePojo> getPageList(QueryParam queryParam);

    BusCouponusagePojo insert(BusCouponusagePojo busCouponusagePojo);

    BusCouponusagePojo update(BusCouponusagePojo busCouponusagepojo);

    int delete(String key,String tid);

    void deleteByCouponidAndCiteid(String couponid, String id, String tid);
    void deleteByCouponCodeAndCiteid(String couponcode, String id, String tid);
}
