package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCarryoverinvoPojo;
import inks.service.std.sale.domain.BusCarryoverinvoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 发货>发票(BusCarryoverinvo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-27 08:50:35
 */
 @Mapper
public interface BusCarryoverinvoMapper {

    BusCarryoverinvoPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusCarryoverinvoPojo> getPageList(QueryParam queryParam);

    List<BusCarryoverinvoPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);

    int insert(BusCarryoverinvoEntity busCarryoverinvoEntity);

    int update(BusCarryoverinvoEntity busCarryoverinvoEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<BusCarryoverinvoPojo> getListByMonth(Date dtStart,String groupid, String tid);
}

