package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusReceaccountEntity;
import inks.service.std.sale.domain.pojo.BusReceaccountPojo;
import inks.service.std.sale.domain.pojo.BusReceaccountitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应收账单(BusReceaccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-23 13:32:01
 */
@Mapper
public interface BusReceaccountMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceaccountPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusReceaccountitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusReceaccountPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busReceaccountEntity 实例对象
     * @return 影响行数
     */
    int insert(BusReceaccountEntity busReceaccountEntity);

    
    /**
     * 修改数据
     *
     * @param busReceaccountEntity 实例对象
     * @return 影响行数
     */
    int update(BusReceaccountEntity busReceaccountEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param busReceaccountPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(BusReceaccountPojo busReceaccountPojo);
                                                                                                                                                                                         }

