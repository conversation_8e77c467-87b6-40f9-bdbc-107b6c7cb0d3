package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusReceaccountPojo;
import inks.service.std.sale.domain.pojo.BusReceaccountitemdetailPojo;

/**
 * 应收账单(BusReceaccount)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-23 13:32:01
 */
public interface BusReceaccountService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceaccountPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusReceaccountitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceaccountPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusReceaccountPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusReceaccountPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busReceaccountPojo 实例对象
     * @return 实例对象
     */
    BusReceaccountPojo insert(BusReceaccountPojo busReceaccountPojo);

    /**
     * 修改数据
     *
     * @param busReceaccountpojo 实例对象
     * @return 实例对象
     */
    BusReceaccountPojo update(BusReceaccountPojo busReceaccountpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key,String tid);

                                                                                                                                                                                         }
