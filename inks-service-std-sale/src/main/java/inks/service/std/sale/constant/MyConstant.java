package inks.service.std.sale.constant;

/**
 * 常量
 */
public interface MyConstant {

    //客户应收款报表
    String RECEIPT_PAGES = "receipt_pages:";
    //"refno_lock:" 生成(释放)编码的Redis锁   String redisLock_Key = MyConstant.REFNO_LOCK + moduleCode + tid;
    String REFNO_LOCK = "refno_lock:";
    //invoice_lock:
    String INVOICE_LOCK = "invoice_lock:";
    //ASYNC_BusInvoic_STATE
    String ASYNC_BUSINVOIC_STATE = "businvoic_state:";

}
