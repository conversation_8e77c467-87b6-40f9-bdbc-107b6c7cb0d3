package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusChatgroupEntity;
import inks.service.std.sale.domain.pojo.BusChatgroupPojo;
import inks.service.std.sale.domain.pojo.BusChatgroupitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客服分组(BusChatgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-08 15:45:53
 */
@Mapper
public interface BusChatgroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusChatgroupPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusChatgroupitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusChatgroupPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busChatgroupEntity 实例对象
     * @return 影响行数
     */
    int insert(BusChatgroupEntity busChatgroupEntity);

    
    /**
     * 修改数据
     *
     * @param busChatgroupEntity 实例对象
     * @return 影响行数
     */
    int update(BusChatgroupEntity busChatgroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param busChatgroupPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(BusChatgroupPojo busChatgroupPojo);

    List<String> getDelDmsIds(BusChatgroupPojo busChatgroupPojo);
}

