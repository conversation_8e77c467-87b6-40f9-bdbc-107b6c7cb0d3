package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.service.std.sale.annotation.RefNoCleanup;
import inks.service.std.sale.annotation.RefNoGeneration;
import inks.service.std.sale.domain.pojo.BusReceiptPojo;
import inks.service.std.sale.service.BusReceiptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * RefNo注解使用示例Controller
 * 展示如何使用@RefNoGeneration和@RefNoCleanup注解
 * 
 * <AUTHOR>
 */
@Api(tags = "RefNo注解示例")
@RestController
@RequestMapping("/example/refno")
public class RefNoExampleController {
    
    // 模块代码，AOP会自动提取此字段
    private final String moduleCode = "EXAMPLE";
    
    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private BusReceiptService busReceiptService;
    
    /**
     * 示例1：JSON字符串参数的编码生成
     * 最常见的使用场景
     */
    @ApiOperation(value = "创建单据-JSON参数", notes = "使用JSON字符串参数自动生成编码")
    @PostMapping("/create-json")
    @RefNoGeneration(
        billType = "Bus_Receipt", 
        field = "refno", 
        paramType = RefNoGeneration.ParamType.JSON_STRING,
        paramIndex = 0
    )
    public R<BusReceiptPojo> createWithJsonParam(@RequestBody String json) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusReceiptPojo pojo = JSONArray.parseObject(json, BusReceiptPojo.class);
            
            // 编码已通过AOP自动生成并设置到pojo中
            // 设置其他必要字段
            pojo.setCreateby(loginUser.getRealname());
            pojo.setCreatebyid(loginUser.getUserid());
            pojo.setCreatedate(new Date());
            pojo.setTenantid(loginUser.getTenantid());
            
            BusReceiptPojo result = busReceiptService.insert(pojo);
            return R.ok(result);
            
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    /**
     * 示例2：POJO对象参数的编码生成
     * 直接传递对象的场景
     */
    @ApiOperation(value = "创建单据-POJO参数", notes = "使用POJO对象参数自动生成编码")
    @PostMapping("/create-pojo")
    @RefNoGeneration(
        billType = "Bus_Receipt", 
        field = "refno", 
        paramType = RefNoGeneration.ParamType.POJO,
        paramIndex = 0
    )
    public R<BusReceiptPojo> createWithPojoParam(@RequestBody BusReceiptPojo pojo) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            
            // 编码已通过AOP自动生成并设置到pojo中
            // 设置其他必要字段
            pojo.setCreateby(loginUser.getRealname());
            pojo.setCreatebyid(loginUser.getUserid());
            pojo.setCreatedate(new Date());
            pojo.setTenantid(loginUser.getTenantid());
            
            BusReceiptPojo result = busReceiptService.insert(pojo);
            return R.ok(result);
            
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    /**
     * 示例3：自定义前缀的编码生成
     */
    @ApiOperation(value = "创建单据-自定义前缀", notes = "使用自定义前缀生成编码")
    @PostMapping("/create-custom-prefix")
    @RefNoGeneration(
        billType = "Bus_Receipt", 
        field = "refno", 
        customPrefix = "CUSTOM",
        paramType = RefNoGeneration.ParamType.JSON_STRING
    )
    public R<BusReceiptPojo> createWithCustomPrefix(@RequestBody String json) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusReceiptPojo pojo = JSONArray.parseObject(json, BusReceiptPojo.class);
            
            // 编码已通过AOP自动生成（带自定义前缀）
            pojo.setCreateby(loginUser.getRealname());
            pojo.setCreatebyid(loginUser.getUserid());
            pojo.setCreatedate(new Date());
            pojo.setTenantid(loginUser.getTenantid());
            
            BusReceiptPojo result = busReceiptService.insert(pojo);
            return R.ok(result);
            
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    /**
     * 示例4：不保存到Redis的编码生成
     */
    @ApiOperation(value = "创建单据-不缓存", notes = "生成编码但不保存到Redis缓存")
    @PostMapping("/create-no-cache")
    @RefNoGeneration(
        billType = "Bus_Receipt", 
        field = "refno", 
        saveToRedis = false,
        paramType = RefNoGeneration.ParamType.JSON_STRING
    )
    public R<BusReceiptPojo> createWithoutCache(@RequestBody String json) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusReceiptPojo pojo = JSONArray.parseObject(json, BusReceiptPojo.class);
            
            // 编码已生成但未保存到Redis
            pojo.setCreateby(loginUser.getRealname());
            pojo.setCreatebyid(loginUser.getUserid());
            pojo.setCreatedate(new Date());
            pojo.setTenantid(loginUser.getTenantid());
            
            BusReceiptPojo result = busReceiptService.insert(pojo);
            return R.ok(result);
            
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    /**
     * 示例5：删除操作的缓存清理
     * 方法执行成功后清理缓存
     */
    @ApiOperation(value = "删除单据-成功后清理", notes = "删除单据并在成功后清理缓存")
    @DeleteMapping("/delete-after-success/{key}")
    @RefNoCleanup(afterSuccess = true, ignoreException = true)
    public R<Integer> deleteAfterSuccess(@PathVariable String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String refno = busReceiptService.delete(key, loginUser.getTenantid());
            
            // Redis缓存会在方法成功执行后自动清理
            return R.ok(1, "删除成功，refno: " + refno);
            
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    /**
     * 示例6：删除操作的缓存清理
     * 方法执行前清理缓存
     */
    @ApiOperation(value = "删除单据-执行前清理", notes = "删除单据并在执行前清理缓存")
    @DeleteMapping("/delete-before/{key}")
    @RefNoCleanup(afterSuccess = false, ignoreException = true)
    public R<Integer> deleteBefore(@PathVariable String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            
            // Redis缓存会在方法执行前自动清理
            String refno = busReceiptService.delete(key, loginUser.getTenantid());
            
            return R.ok(1, "删除成功，refno: " + refno);
            
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    /**
     * 示例7：严格模式的缓存清理
     * 清理失败会抛出异常
     */
    @ApiOperation(value = "删除单据-严格模式", notes = "删除单据，清理失败会抛出异常")
    @DeleteMapping("/delete-strict/{key}")
    @RefNoCleanup(afterSuccess = true, ignoreException = false)
    public R<Integer> deleteStrict(@PathVariable String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String refno = busReceiptService.delete(key, loginUser.getTenantid());
            
            // 如果Redis缓存清理失败，会抛出异常
            return R.ok(1, "删除成功，refno: " + refno);
            
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
