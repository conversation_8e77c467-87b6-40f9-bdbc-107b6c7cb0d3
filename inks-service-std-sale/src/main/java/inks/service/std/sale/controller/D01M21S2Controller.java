package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusDmsskuPojo;
import inks.service.std.sale.service.impl.BusDmsskuServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Dms商品Sku(Bus_DmsSku)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-29 13:08:57
 */
@RestController
@RequestMapping("D01M21S2")
@Api(tags = "D01M21S2:Dms商品Sku")
public class D01M21S2Controller extends BusDmsskuController {
    @Resource
    private BusDmsskuServiceImpl busDmsskuService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "通过Spu按条件分页查询DMS商品(注意传入的SPU必须经过ASCII编码!!编码前的SPU格式为[{\"key\":\"spuchang\",\"value\":\"500\"},{\"key\":\"spuhou\",\"value\":\"1.5\"}])", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListBySpu", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_DmsGoods.List")
    public R<PageInfo<BusDmsskuPojo>> getPageListBySpu(@RequestBody String json, @RequestParam() String spu) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DmsSku.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            // 将额外的WHERE条件拼接到qpfilter中
//            qpfilter += " AND JSON_CONTAINS(Bus_DmsGoods.AttributeJson, '{\"key\":\"spuchang\",\"value\":\"500\"}', '$')";
//            qpfilter += " AND JSON_CONTAINS(Bus_DmsGoods.AttributeJson, '{\"key\":\"spuhou\",\"value\":\"1.5\"}', '$')";
            // 动态构建JSON_CONTAINS条件
            if (StringUtils.isNotBlank(spu)) {
                // 将spu解析为JSON数组
                JSONArray spuArray = JSONArray.parseArray(spu);
                // 首先过滤到必须是JSON格式！！否则报错 (把AttributeJson字段改为JSON类型了)
//                qpfilter += " AND JSON_VALID(Bus_DmsGoods.AttributeJson)";
                // 遍历spu数组，构建条件
                for (Object o : spuArray) {
                    JSONObject spuObj = (JSONObject) o;
                    String key = spuObj.getString("key");
                    String value = spuObj.getString("value");
                    // 构建JSON_CONTAINS条件，并添加到qpfilter中
                    qpfilter += " AND JSON_CONTAINS(Bus_DmsSku.AttributeJson, '{\"key\":\"" + key + "\",\"value\":\"" + value + "\"}', '$')";
                }
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDmsskuService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
