package inks.service.std.sale.mapper;

import inks.common.core.domain.GoodsPojo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusDelieryEntity;
import inks.service.std.sale.domain.pojo.BusDelieryPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 发出商品(BusDeliery)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-13 14:07:44
 */
@Mapper
public interface BusDelieryMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDelieryPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDelieryitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDelieryPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDelieryEntity 实例对象
     * @return 影响行数
     */
    int insert(BusDelieryEntity busDelieryEntity);


    /**
     * 修改数据
     *
     * @param busDelieryEntity 实例对象
     * @return 影响行数
     */
    int update(BusDelieryEntity busDelieryEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busDelieryPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusDelieryPojo busDelieryPojo);

    /**
     * 修改数据
     *
     * @param busDelieryEntity 实例对象
     * @return 影响行数
     */
    int approval(BusDelieryEntity busDelieryEntity);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCountAndAmount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改完工记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMachItemFinishQty(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMachFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);


    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);



    /**
     * 查询 所有Item
     *
     * @param ids 筛选条件
     * @return 查询结果
     */
    List<BusDelieryitemPojo> getItemListByIds(@Param("ids")String ids, @Param("pid")String pid, @Param("tid")String tid);

    void updateDeliItemReturnQty(String citeitemid, String tid);

    void updateDeliReturnCount(String citeitemid, String tid);


    void syncDeliPlanFinishCount(String deliplanitemid, String tid);

    void syncDeliPlanItemFinishQty(String deliplanitemid, String tid);
    void syncDeliPlanItemFinishQtyByPlan( String machitemid, Date billdate, String tid);

    void updateMachFinishAmount(String machitemid, String tid);

    Map<String, Object> getGoodsInfo(String goodsid, String tid);

    List<Map<String, Object>> getAttrList(String tid);

    List<Map<String, String>> getListByGoodsAttr(@Param("goodsid") String goodsid, @Param("lst") List<Map<String, Object>> lst, @Param("tid") String tid);

    Double getSumInventoryQuantity(String goodsid, String skuid, String tid);
}

