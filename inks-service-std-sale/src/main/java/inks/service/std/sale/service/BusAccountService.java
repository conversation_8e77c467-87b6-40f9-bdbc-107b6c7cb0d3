package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.*;

import java.util.List;
import java.util.Map;

/**
 * 销售账单(BusAccount)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-03 14:28:09
 */
public interface BusAccountService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusAccountitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusAccountPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusAccountPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    BusAccountPojo insert(BusAccountPojo busAccountPojo);

    /**
     * 修改数据
     *
     * @param busAccountpojo 实例对象
     * @return 实例对象
     */
    BusAccountPojo update(BusAccountPojo busAccountpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);



    /**
     * 新增数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    List<BusAccountitemPojo> pullItemList(BusAccountPojo busAccountPojo);
    List<BusAccountinvoPojo> pullInvoList(BusAccountPojo busAccountPojo);
    List<BusAccountarapPojo> pullArapList(BusAccountPojo busAccountPojo);

    //List<BusCarryoveritemPojo> pullCarryItemList(BusCarryoverPojo busCarryoverPojo);
    //List<BusCarryoverinvoPojo> pullCarryInvoList(BusCarryoverPojo busCarryoverPojo);
    // 批量生产账单
    int batchCreate(BusAccountPojo busAccountPojo);

    // 批量生产账单
    int batchInit(BusAccountPojo busAccountPojo);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountPojo getMaxEntityByGroup(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountPojo getMaxBillEntityByGroup(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<BusAccountitemPojo> getMultItemList(QueryParam queryParam);


    /**
     * 分页查询实时报表
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusAccountPojo> getNowPageList(QueryParam queryParam);

    void batchCreateStart(BusAccountPojo busAccountPojo, String uuid,boolean isCreateGoodsCarryover);

    Map<String, Object> batchCreateState(String key);

    List<BusInvocarryoverPojo> updateInvoCarryByids(List<String> invocarryids, Integer year, Integer month, String tenantid);
}
