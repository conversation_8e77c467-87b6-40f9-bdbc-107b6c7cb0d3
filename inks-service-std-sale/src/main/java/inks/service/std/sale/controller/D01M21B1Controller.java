package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusDmsgoodsPojo;
import inks.service.std.sale.service.impl.BusDmsgoodsServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * DMS商品(Bus_DmsGoods)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-21 15:25:29
 */
@RestController
@RequestMapping("D01M21B1")
@Api(tags = "D01M21B1:DMS商品")
public class D01M21B1Controller extends BusDmsgoodsController {
    @Resource
    private BusDmsgoodsServiceImpl busDmsgoodsServiceImpl;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "get新编码", notes = "get新编码", produces = "application/json")
    @GetMapping("/getMaxCode")
    public R<String> getNextCode() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 读取最大数
            //生成最大
            String nextCode = this.busDmsgoodsServiceImpl.getNextCode(loginUser.getTenantid());
            return R.ok(nextCode);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "通过Spu按条件分页查询DMS商品(注意传入的SPU必须经过ASCII编码!!编码前的SPU格式为[{\"key\":\"spuchang\",\"value\":\"500\"},{\"key\":\"spuhou\",\"value\":\"1.5\"}])", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListBySpu", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_DmsGoods.List")
    public R<PageInfo<BusDmsgoodsPojo>> getPageListBySpu(@RequestBody String json, @RequestParam() String spu) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DmsGoods.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            // 将额外的WHERE条件拼接到qpfilter中
//            qpfilter += " AND JSON_CONTAINS(Bus_DmsGoods.AttributeJson, '{\"key\":\"spuchang\",\"value\":\"500\"}', '$')";
//            qpfilter += " AND JSON_CONTAINS(Bus_DmsGoods.AttributeJson, '{\"key\":\"spuhou\",\"value\":\"1.5\"}', '$')";
            // 动态构建JSON_CONTAINS条件
            if (StringUtils.isNotBlank(spu)) {
                // 将spu解析为JSON数组
                JSONArray spuArray = JSONArray.parseArray(spu);
                // 首先过滤到必须是JSON格式！！否则报错 (把AttributeJson字段改为JSON类型了)
//                qpfilter += " AND JSON_VALID(Bus_DmsGoods.AttributeJson)";
                // 遍历spu数组，构建条件
                for (Object o : spuArray) {
                    JSONObject spuObj = (JSONObject) o;
                    String key = spuObj.getString("key");
                    String value = spuObj.getString("value");
                    // 构建JSON_CONTAINS条件，并添加到qpfilter中
                    qpfilter += " AND JSON_CONTAINS(Bus_DmsGoods.AttributeJson, '{\"key\":\"" + key + "\",\"value\":\"" + value + "\"}', '$')";
                }
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDmsgoodsServiceImpl.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
