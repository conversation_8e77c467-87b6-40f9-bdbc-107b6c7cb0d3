package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.ReportsPojo;
import inks.service.std.sale.domain.pojo.BusDelieryPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo;

import java.util.List;
import java.util.Map;

/**
 * 发出商品(BusDeliery)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-13 14:07:45
 */
public interface BusDelieryService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDelieryPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDelieryitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDelieryPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDelieryPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDelieryPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDelieryPojo 实例对象
     * @return 实例对象
     */
    BusDelieryPojo insert(BusDelieryPojo busDelieryPojo, Integer warn);

    /**
     * 修改数据
     *
     * @param busDelierypojo 实例对象
     * @return 实例对象
     */
    BusDelieryPojo update(BusDelieryPojo busDelierypojo, Integer warn);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param busDelieryPojo 实例对象
     * @return 实例对象
     */
    BusDelieryPojo approval(BusDelieryPojo busDelieryPojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BusDelieryPojo disannul(List<BusDelieryitemPojo> lst, Integer type, LoginUser loginUser);
    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BusDelieryPojo closed(List<BusDelieryitemPojo> lst, Integer type, LoginUser loginUser);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    // 开始批量打印
   void printBatchBillStart(List<String> ids, String uuid, String printapproved, ReportsPojo reportsPojo , LoginUser loginUser);

    /**
     * 获取批量打印状态
     * @param key
     * @return
     */
    Map<String, Object> getPrintBatchBillState(String key);

    /**
     * 查询 所有Item
     *
     * @param ids 筛选条件
     * @return 查询结果
     */
    List<BusDelieryitemPojo> getItemListByIds(String ids, String pid, String tid);
}
