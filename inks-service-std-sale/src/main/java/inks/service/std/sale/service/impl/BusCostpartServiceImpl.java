package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusCostpartEntity;
import inks.service.std.sale.domain.pojo.BusCostpartPojo;
import inks.service.std.sale.mapper.BusCostpartMapper;
import inks.service.std.sale.service.BusCostpartService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 成本组件(BusCostpart)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-07 13:29:31
 */
@Service("busCostpartService")
public class BusCostpartServiceImpl implements BusCostpartService {
    @Resource
    private BusCostpartMapper busCostpartMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusCostpartPojo getEntity(String key, String tid) {
        return this.busCostpartMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusCostpartPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCostpartPojo> lst = busCostpartMapper.getPageList(queryParam);
            PageInfo<BusCostpartPojo> pageInfo = new PageInfo<BusCostpartPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param busCostpartPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusCostpartPojo insert(BusCostpartPojo busCostpartPojo) {
    //初始化NULL字段
     if(busCostpartPojo.getPartgroupid()==null) busCostpartPojo.setPartgroupid("");
     if(busCostpartPojo.getItemtype()==null) busCostpartPojo.setItemtype("");
     if(busCostpartPojo.getItemname()==null) busCostpartPojo.setItemname("");
     if(busCostpartPojo.getItemspec()==null) busCostpartPojo.setItemspec("");
     if(busCostpartPojo.getItemunit()==null) busCostpartPojo.setItemunit("");
     if(busCostpartPojo.getBaseprice()==null) busCostpartPojo.setBaseprice(0D);
     if(busCostpartPojo.getRebate()==null) busCostpartPojo.setRebate(0D);
     if(busCostpartPojo.getRebatesec()==null) busCostpartPojo.setRebatesec(0D);
     if(busCostpartPojo.getPrice()==null) busCostpartPojo.setPrice(0D);
     if(busCostpartPojo.getPricemin()==null) busCostpartPojo.setPricemin(0D);
     if(busCostpartPojo.getPricemax()==null) busCostpartPojo.setPricemax(0D);
     if(busCostpartPojo.getEnabledmark()==null) busCostpartPojo.setEnabledmark(0);
     if(busCostpartPojo.getAllowdelete()==null) busCostpartPojo.setAllowdelete(0);
     if(busCostpartPojo.getAllowedit()==null) busCostpartPojo.setAllowedit(0);
     if(busCostpartPojo.getRownum()==null) busCostpartPojo.setRownum(0);
     if(busCostpartPojo.getRemark()==null) busCostpartPojo.setRemark("");
     if(busCostpartPojo.getCreateby()==null) busCostpartPojo.setCreateby("");
     if(busCostpartPojo.getCreatebyid()==null) busCostpartPojo.setCreatebyid("");
     if(busCostpartPojo.getCreatedate()==null) busCostpartPojo.setCreatedate(new Date());
     if(busCostpartPojo.getLister()==null) busCostpartPojo.setLister("");
     if(busCostpartPojo.getListerid()==null) busCostpartPojo.setListerid("");
     if(busCostpartPojo.getModifydate()==null) busCostpartPojo.setModifydate(new Date());
     if(busCostpartPojo.getCustom1()==null) busCostpartPojo.setCustom1("");
     if(busCostpartPojo.getCustom2()==null) busCostpartPojo.setCustom2("");
     if(busCostpartPojo.getCustom3()==null) busCostpartPojo.setCustom3("");
     if(busCostpartPojo.getCustom4()==null) busCostpartPojo.setCustom4("");
     if(busCostpartPojo.getCustom5()==null) busCostpartPojo.setCustom5("");
     if(busCostpartPojo.getCustom6()==null) busCostpartPojo.setCustom6("");
     if(busCostpartPojo.getCustom7()==null) busCostpartPojo.setCustom7("");
     if(busCostpartPojo.getCustom8()==null) busCostpartPojo.setCustom8("");
     if(busCostpartPojo.getCustom9()==null) busCostpartPojo.setCustom9("");
     if(busCostpartPojo.getCustom10()==null) busCostpartPojo.setCustom10("");
     if(busCostpartPojo.getDeptid()==null) busCostpartPojo.setDeptid("");
     if(busCostpartPojo.getTenantid()==null) busCostpartPojo.setTenantid("");
     if(busCostpartPojo.getTenantname()==null) busCostpartPojo.setTenantname("");
     if(busCostpartPojo.getRevision()==null) busCostpartPojo.setRevision(0);
        BusCostpartEntity busCostpartEntity = new BusCostpartEntity(); 
        BeanUtils.copyProperties(busCostpartPojo,busCostpartEntity);
        
          busCostpartEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busCostpartEntity.setRevision(1);  //乐观锁
          this.busCostpartMapper.insert(busCostpartEntity);
        return this.getEntity(busCostpartEntity.getId(),busCostpartEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busCostpartPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusCostpartPojo update(BusCostpartPojo busCostpartPojo) {
        BusCostpartEntity busCostpartEntity = new BusCostpartEntity(); 
        BeanUtils.copyProperties(busCostpartPojo,busCostpartEntity);
        this.busCostpartMapper.update(busCostpartEntity);
        return this.getEntity(busCostpartEntity.getId(),busCostpartEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busCostpartMapper.delete(key,tid) ;
    }
    
                                                                                                                                                                                             
}
