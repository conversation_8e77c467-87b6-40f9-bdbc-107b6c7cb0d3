package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.std.sale.domain.BusDeliplanEntity;
import inks.service.std.sale.domain.BusDeliplanitemEntity;
import inks.service.std.sale.domain.pojo.BusDeliplanPojo;
import inks.service.std.sale.domain.pojo.BusDeliplanitemPojo;
import inks.service.std.sale.domain.pojo.BusDeliplanitemdetailPojo;
import inks.service.std.sale.mapper.BusDeliplanMapper;
import inks.service.std.sale.mapper.BusDeliplanitemMapper;
import inks.service.std.sale.service.BusDeliplanService;
import inks.service.std.sale.service.BusDeliplanitemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 发货计划(BusDeliplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:04
 */
@Service("busDeliplanService")
public class BusDeliplanServiceImpl implements BusDeliplanService {
    @Resource
    private BusDeliplanMapper busDeliplanMapper;

    @Resource
    private BusDeliplanitemMapper busDeliplanitemMapper;


    @Resource
    private BusDeliplanitemService busDeliplanitemService;

    @Override
    public BusDeliplanPojo getEntity(String key, String tid) {
        return this.busDeliplanMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<BusDeliplanitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeliplanitemdetailPojo> lst = busDeliplanMapper.getPageList(queryParam);
            PageInfo<BusDeliplanitemdetailPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<Map<String, Object>> getPageListByMach(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeliplanitemdetailPojo> lst = busDeliplanMapper.getPageList(queryParam);
            // 单据Item拆开SPU. 带属性List转为Map
            List<Map<String, Object>> mapLst = attrListToMaps(lst);
            for (Map<String, Object> map : mapLst) {
                // 格式化时间itemplandate
                Date itemplandate = (Date) map.get("itemplandate");
                if (itemplandate != null) {
                    map.put("itemplandate", DateUtils.dateTime(itemplandate));
                }

                // 如果machitemid不为空，则查询machitemid对应的工序
                String machitemid = (String) map.get("machitemid");
                if (StringUtils.isNotBlank(machitemid)) {
                    String wkWpNameByMachitemid = busDeliplanMapper.getWkWpNameByMachitemid(machitemid, queryParam.getTenantid());
                    map.put("wkwpname", wkWpNameByMachitemid);
                }
            }

            return new PageInfo<>(mapLst);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public BusDeliplanPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusDeliplanPojo busDeliplanPojo = this.busDeliplanMapper.getEntity(key, tid);
            //读取子表
            busDeliplanPojo.setItem(busDeliplanitemMapper.getList(busDeliplanPojo.getId(), tid));
            return busDeliplanPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<BusDeliplanPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeliplanPojo> lst = busDeliplanMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (BusDeliplanPojo item : lst) {
                item.setItem(busDeliplanitemMapper.getList(item.getId(), tid));
            }
            PageInfo<BusDeliplanPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<BusDeliplanPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeliplanPojo> lst = busDeliplanMapper.getPageTh(queryParam);
            PageInfo<BusDeliplanPojo> pageInfo = new PageInfo<BusDeliplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public BusDeliplanPojo insert(BusDeliplanPojo busDeliplanPojo) {
        String tid = busDeliplanPojo.getTenantid();
        //初始化NULL字段
        cleanNull(busDeliplanPojo);
        busDeliplanPojo.setItemcount(busDeliplanPojo.getItem().isEmpty() ? 0 : busDeliplanPojo.getItem().size());
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusDeliplanEntity busDeliplanEntity = new BusDeliplanEntity();
        BeanUtils.copyProperties(busDeliplanPojo, busDeliplanEntity);

        //设置id和新建日期
        busDeliplanEntity.setId(id);
        busDeliplanEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busDeliplanMapper.insert(busDeliplanEntity);
        //Item子表处理
        List<BusDeliplanitemPojo> lst = busDeliplanPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (BusDeliplanitemPojo item : lst) {
                //初始化item的NULL
                BusDeliplanitemPojo itemPojo = this.busDeliplanitemService.clearNull(item);
                BusDeliplanitemEntity busDeliplanitemEntity = new BusDeliplanitemEntity();
                BeanUtils.copyProperties(itemPojo, busDeliplanitemEntity);
                //设置id和Pid
                busDeliplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busDeliplanitemEntity.setPid(id);
                busDeliplanitemEntity.setTenantid(tid);
                busDeliplanitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busDeliplanitemMapper.insert(busDeliplanitemEntity);
            }

        }
        //返回Bill实例
        return this.getBillEntity(busDeliplanEntity.getId(), tid);
    }


    @Override
    @Transactional
    public BusDeliplanPojo update(BusDeliplanPojo busDeliplanPojo) {
        String tid = busDeliplanPojo.getTenantid();
        busDeliplanPojo.setItemcount(busDeliplanPojo.getItem().isEmpty() ? 0 : busDeliplanPojo.getItem().size());
        //主表更改
        BusDeliplanEntity busDeliplanEntity = new BusDeliplanEntity();
        BeanUtils.copyProperties(busDeliplanPojo, busDeliplanEntity);
        this.busDeliplanMapper.update(busDeliplanEntity);
        if (busDeliplanPojo.getItem() != null) {
            //Item子表处理
            List<BusDeliplanitemPojo> lst = busDeliplanPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busDeliplanMapper.getDelItemIds(busDeliplanPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String delId : lstDelIds) {
                    this.busDeliplanitemMapper.delete(delId, tid);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (BusDeliplanitemPojo item : lst) {
                    BusDeliplanitemEntity busDeliplanitemEntity = new BusDeliplanitemEntity();
                    if ("".equals(item.getId()) || item.getId() == null) {
                        //初始化item的NULL
                        BusDeliplanitemPojo itemPojo = this.busDeliplanitemService.clearNull(item);
                        BeanUtils.copyProperties(itemPojo, busDeliplanitemEntity);
                        //设置id和Pid
                        busDeliplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busDeliplanitemEntity.setPid(busDeliplanEntity.getId());  // 主表 id
                        busDeliplanitemEntity.setTenantid(tid);   // 租户id
                        busDeliplanitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busDeliplanitemMapper.insert(busDeliplanitemEntity);
                    } else {
                        BeanUtils.copyProperties(item, busDeliplanitemEntity);
                        busDeliplanitemEntity.setTenantid(tid);
                        this.busDeliplanitemMapper.update(busDeliplanitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busDeliplanEntity.getId(), tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusDeliplanPojo busDeliplanPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusDeliplanitemPojo> lst = busDeliplanPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusDeliplanitemPojo item : lst) {
                this.busDeliplanitemMapper.delete(item.getId(), tid);
            }
        }
        return this.busDeliplanMapper.delete(key, tid);
    }


    @Override
    @Transactional
    public BusDeliplanPojo approval(BusDeliplanPojo busDeliplanPojo) {
        String tid = busDeliplanPojo.getTenantid();
        //主表更改
        BusDeliplanEntity busDeliplanEntity = new BusDeliplanEntity();
        BeanUtils.copyProperties(busDeliplanPojo, busDeliplanEntity);
        this.busDeliplanMapper.approval(busDeliplanEntity);
        //返回Bill实例
        return this.getBillEntity(busDeliplanEntity.getId(), tid);
    }

    @Override
    @Transactional
    public BusDeliplanPojo disannul(List<BusDeliplanitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BusDeliplanitemPojo Pojo = lst.get(i);
            BusDeliplanitemPojo dbPojo = this.busDeliplanitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    BusDeliplanitemEntity entity = new BusDeliplanitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setTenantid(loginUser.getTenantid());
                    this.busDeliplanitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.busDeliplanMapper.updateDisannulCount(Pid, tid);
            //主表更改
            BusDeliplanEntity busDeliplanEntity = new BusDeliplanEntity();
            busDeliplanEntity.setId(Pid);
            busDeliplanEntity.setLister(loginUser.getRealname());
            busDeliplanEntity.setListerid(loginUser.getUserid());
            busDeliplanEntity.setModifydate(new Date());
            busDeliplanEntity.setTenantid(loginUser.getTenantid());
            this.busDeliplanMapper.update(busDeliplanEntity);
            //返回Bill实例
            return this.getBillEntity(busDeliplanEntity.getId(), busDeliplanEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDeliplanPojo closed(List<BusDeliplanitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BusDeliplanitemPojo Pojo = lst.get(i);
            BusDeliplanitemPojo dbPojo = this.busDeliplanitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (dbPojo.getClosed() != type) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BusDeliplanitemEntity entity = new BusDeliplanitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.busDeliplanitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.busDeliplanMapper.updateFinishCount(Pid, tid);
            //主表更改
            BusDeliplanEntity busDeliplanEntity = new BusDeliplanEntity();
            busDeliplanEntity.setId(Pid);
            busDeliplanEntity.setLister(loginUser.getRealname());
            busDeliplanEntity.setListerid(loginUser.getUserid());
            busDeliplanEntity.setModifydate(new Date());
            busDeliplanEntity.setTenantid(loginUser.getTenantid());
            this.busDeliplanMapper.update(busDeliplanEntity);
            //返回Bill实例
            return this.getBillEntity(busDeliplanEntity.getId(), busDeliplanEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    private static void cleanNull(BusDeliplanPojo busDeliplanPojo) {
        if (busDeliplanPojo.getRefno() == null) busDeliplanPojo.setRefno("");
        if (busDeliplanPojo.getBilltype() == null) busDeliplanPojo.setBilltype("");
        if (busDeliplanPojo.getBilltitle() == null) busDeliplanPojo.setBilltitle("");
        if (busDeliplanPojo.getBilldate() == null) busDeliplanPojo.setBilldate(new Date());
        if (busDeliplanPojo.getGroupid() == null) busDeliplanPojo.setGroupid("");
        if (busDeliplanPojo.getTelephone() == null) busDeliplanPojo.setTelephone("");
        if (busDeliplanPojo.getLinkman() == null) busDeliplanPojo.setLinkman("");
        if (busDeliplanPojo.getDeliadd() == null) busDeliplanPojo.setDeliadd("");
        if (busDeliplanPojo.getTaxrate() == null) busDeliplanPojo.setTaxrate(0);
        if (busDeliplanPojo.getTransport() == null) busDeliplanPojo.setTransport("");
        if (busDeliplanPojo.getSalesman() == null) busDeliplanPojo.setSalesman("");
        if (busDeliplanPojo.getSalesmanid() == null) busDeliplanPojo.setSalesmanid("");
        if (busDeliplanPojo.getOperator() == null) busDeliplanPojo.setOperator("");
        if (busDeliplanPojo.getOperatorid() == null) busDeliplanPojo.setOperatorid("");
        if (busDeliplanPojo.getSummary() == null) busDeliplanPojo.setSummary("");
        if (busDeliplanPojo.getCreateby() == null) busDeliplanPojo.setCreateby("");
        if (busDeliplanPojo.getCreatebyid() == null) busDeliplanPojo.setCreatebyid("");
        if (busDeliplanPojo.getCreatedate() == null) busDeliplanPojo.setCreatedate(new Date());
        if (busDeliplanPojo.getLister() == null) busDeliplanPojo.setLister("");
        if (busDeliplanPojo.getListerid() == null) busDeliplanPojo.setListerid("");
        if (busDeliplanPojo.getModifydate() == null) busDeliplanPojo.setModifydate(new Date());
        if (busDeliplanPojo.getAssessor() == null) busDeliplanPojo.setAssessor("");
        if (busDeliplanPojo.getAssessorid() == null) busDeliplanPojo.setAssessorid("");
        if (busDeliplanPojo.getAssessdate() == null) busDeliplanPojo.setAssessdate(new Date());
        if (busDeliplanPojo.getDisannulcount() == null) busDeliplanPojo.setDisannulcount(0);
        if (busDeliplanPojo.getBillstatecode() == null) busDeliplanPojo.setBillstatecode("");
        if (busDeliplanPojo.getBillstatedate() == null) busDeliplanPojo.setBillstatedate(new Date());
        if (busDeliplanPojo.getBilltaxamount() == null) busDeliplanPojo.setBilltaxamount(0D);
        if (busDeliplanPojo.getBilltaxtotal() == null) busDeliplanPojo.setBilltaxtotal(0D);
        if (busDeliplanPojo.getBillamount() == null) busDeliplanPojo.setBillamount(0D);
        if (busDeliplanPojo.getBillreceived() == null) busDeliplanPojo.setBillreceived(0D);
        if (busDeliplanPojo.getItemcount() == null) busDeliplanPojo.setItemcount(0);
        if (busDeliplanPojo.getPickcount() == null) busDeliplanPojo.setPickcount(0);
        if (busDeliplanPojo.getFinishcount() == null) busDeliplanPojo.setFinishcount(0);
        if (busDeliplanPojo.getPrintcount() == null) busDeliplanPojo.setPrintcount(0);
        if (busDeliplanPojo.getOaflowmark() == null) busDeliplanPojo.setOaflowmark(0);
        if (busDeliplanPojo.getBillplandate() == null) busDeliplanPojo.setBillplandate(new Date());
        if (busDeliplanPojo.getCustom1() == null) busDeliplanPojo.setCustom1("");
        if (busDeliplanPojo.getCustom2() == null) busDeliplanPojo.setCustom2("");
        if (busDeliplanPojo.getCustom3() == null) busDeliplanPojo.setCustom3("");
        if (busDeliplanPojo.getCustom4() == null) busDeliplanPojo.setCustom4("");
        if (busDeliplanPojo.getCustom5() == null) busDeliplanPojo.setCustom5("");
        if (busDeliplanPojo.getCustom6() == null) busDeliplanPojo.setCustom6("");
        if (busDeliplanPojo.getCustom7() == null) busDeliplanPojo.setCustom7("");
        if (busDeliplanPojo.getCustom8() == null) busDeliplanPojo.setCustom8("");
        if (busDeliplanPojo.getCustom9() == null) busDeliplanPojo.setCustom9("");
        if (busDeliplanPojo.getCustom10() == null) busDeliplanPojo.setCustom10("");
        if (busDeliplanPojo.getTenantid() == null) busDeliplanPojo.setTenantid("");
        if (busDeliplanPojo.getTenantname() == null) busDeliplanPojo.setTenantname("");
        if (busDeliplanPojo.getRevision() == null) busDeliplanPojo.setRevision(0);
    }

}
