package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusReceiptEntity;
import inks.service.std.sale.domain.pojo.BusReceiptPojo;
import inks.service.std.sale.domain.pojo.BusReceiptitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收款单据(BusReceipt)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:34:47
 */
@Mapper
public interface BusReceiptMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceiptPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusReceiptitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusReceiptPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busReceiptEntity 实例对象
     * @return 影响行数
     */
    int insert(BusReceiptEntity busReceiptEntity);


    /**
     * 修改数据
     *
     * @param busReceiptEntity 实例对象
     * @return 影响行数
     */
    int update(BusReceiptEntity busReceiptEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busReceiptPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusReceiptPojo busReceiptPojo);

    /**
     * 查询 被删除的Item
     *
     * @param busReceiptPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelCashIds(BusReceiptPojo busReceiptPojo);

    /**
     * 修改数据
     *
     * @param busReceiptEntity 实例对象
     * @return 影响行数
     */
    int approval(BusReceiptEntity busReceiptEntity);


    /**
     * 刷新发票完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateBusInvoFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);
    int updateBusInvoItemAvgAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);

    int updateBusMachingItemAvgAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);
    int updateBusMachingFirstLastAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);
    /**
     * 刷新送货收款额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeliFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新出纳账户额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateCashAmount(@Param("key") String key, @Param("amount") Double amount, @Param("tid") String tid);

    /**
     * get出纳账户余额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getCashAmount(@Param("key") String key, @Param("tid") String tid);

    int updateOrgReturn(@Param("orgUid") String orguid, @Param("redUid") String refno, @Param("tid") String tenantid);
}

