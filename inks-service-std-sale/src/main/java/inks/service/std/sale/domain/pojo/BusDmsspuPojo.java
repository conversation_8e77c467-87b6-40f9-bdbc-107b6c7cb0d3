package inks.service.std.sale.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * Dms商品属性Key(BusDmsspu)实体类
 *
 * <AUTHOR>
 * @since 2023-12-27 19:12:23
 */
public class BusDmsspuPojo implements Serializable {
    private static final long serialVersionUID = 446700909321291898L;
         // 属性KeyId
         @Excel(name = "属性KeyId") 
    private String id;
         // 通用分组
         @Excel(name = "通用分组") 
    private String attrgroupid;
         // 属性Key
         @Excel(name = "属性Key") 
    private String attrkey;
         // 属性名称
         @Excel(name = "属性名称") 
    private String attrname;
         // 数值类型
         @Excel(name = "数值类型") 
    private String valuetype;
         // 默认值
         @Excel(name = "默认值") 
    private String defvalue;
         // 可择值
         @Excel(name = "可择值") 
    private String valuejson;
         // 列表显示
         @Excel(name = "列表显示") 
    private Integer listshow;
         // 有效标识
         @Excel(name = "有效标识") 
    private Integer enabledmark;
         // 存储SKU
         @Excel(name = "存储SKU") 
    private Integer skumark;
         // 是否数字
         @Excel(name = "是否数字") 
    private Integer numericmark;
         // 是否必填
         @Excel(name = "是否必填") 
    private Integer requiredmark;
         // 行号
         @Excel(name = "行号") 
    private Integer rownum;
         // 备注
         @Excel(name = "备注") 
    private String remark;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 自定义1
         @Excel(name = "自定义1") 
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2") 
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3") 
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4") 
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5") 
    private String custom5;
         // 自定义6
         @Excel(name = "自定义6") 
    private String custom6;
         // 自定义7
         @Excel(name = "自定义7") 
    private String custom7;
         // 自定义8
         @Excel(name = "自定义8") 
    private String custom8;
         // 自定义9
         @Excel(name = "自定义9") 
    private String custom9;
         // 自定义10
         @Excel(name = "自定义10") 
    private String custom10;
         // 租户id
         @Excel(name = "租户id") 
    private String tenantid;
         // 租户名称
         @Excel(name = "租户名称") 
    private String tenantname;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;

     // 属性KeyId
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // 通用分组
       public String getAttrgroupid() {
        return attrgroupid;
    }
    
    public void setAttrgroupid(String attrgroupid) {
        this.attrgroupid = attrgroupid;
    }
        
     // 属性Key
       public String getAttrkey() {
        return attrkey;
    }
    
    public void setAttrkey(String attrkey) {
        this.attrkey = attrkey;
    }
        
     // 属性名称
       public String getAttrname() {
        return attrname;
    }
    
    public void setAttrname(String attrname) {
        this.attrname = attrname;
    }
        
     // 数值类型
       public String getValuetype() {
        return valuetype;
    }
    
    public void setValuetype(String valuetype) {
        this.valuetype = valuetype;
    }
        
     // 默认值
       public String getDefvalue() {
        return defvalue;
    }
    
    public void setDefvalue(String defvalue) {
        this.defvalue = defvalue;
    }
        
     // 可择值
       public String getValuejson() {
        return valuejson;
    }
    
    public void setValuejson(String valuejson) {
        this.valuejson = valuejson;
    }
        
     // 列表显示
       public Integer getListshow() {
        return listshow;
    }
    
    public void setListshow(Integer listshow) {
        this.listshow = listshow;
    }
        
     // 有效标识
       public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
     // 存储SKU
       public Integer getSkumark() {
        return skumark;
    }
    
    public void setSkumark(Integer skumark) {
        this.skumark = skumark;
    }
        
     // 是否数字
       public Integer getNumericmark() {
        return numericmark;
    }
    
    public void setNumericmark(Integer numericmark) {
        this.numericmark = numericmark;
    }
        
     // 是否必填
       public Integer getRequiredmark() {
        return requiredmark;
    }
    
    public void setRequiredmark(Integer requiredmark) {
        this.requiredmark = requiredmark;
    }
        
     // 行号
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 自定义1
       public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
     // 自定义2
       public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
     // 自定义3
       public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
     // 自定义4
       public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
     // 自定义5
       public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
     // 自定义6
       public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
     // 自定义7
       public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
     // 自定义8
       public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
     // 自定义9
       public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
     // 自定义10
       public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 租户名称
       public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

