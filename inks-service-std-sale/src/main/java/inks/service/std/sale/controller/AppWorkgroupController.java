package inks.service.std.sale.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import inks.service.std.sale.service.AppWorkgroupService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 往来单位(App_Workgroup)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 08:27:11
 */
public class AppWorkgroupController {
    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    @Resource
    private UtilsFeignService utilsFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取往来单位详细信息", notes = "获取往来单位详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.List")
    public R<AppWorkgroupPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param province 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取省份或者地级市", notes = "获取省份或者地级市", produces = "application/json")
    @RequestMapping(value = "/getProvOrCity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.List")
    public R<List<String>> getProvOrCity(String province) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.getProvOrCity(province, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Workgroup.List")
    public R<PageInfo<AppWorkgroupPojo>> getPageList(@RequestBody String json, @RequestParam(required = false) Integer dept) {//dept=1才开启过滤部门，默认不过滤
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.appWorkgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //D01M01B系列，加处get接口，getList不分页全数返回，用GroupUid排序 abc123这样排序
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.List")
    public R<List<AppWorkgroupPojo>> getList(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 获取请求的路径,/分割 https://dev.inksyun.com:31080/store/D04M01B1/saveInit
            String[] pathParts = request.getRequestURI().split("/");
            String moduleCode = pathParts[pathParts.length - 2]; // 获取倒数第二个路径参数：如"D04M01B1"
            String groupType = null;
            switch (moduleCode) {
                case "D01M01B1":
                    groupType = "客户";
                    break;
                case "D01M01B2":
                    groupType = "供应商";
                    break;
                case "D01M01B3":
                    groupType = "生产车间";
                    break;
                case "D01M01B4":
                    groupType = "外协厂商";
                    break;
                case "D01M01B5":
                    groupType = "其他部门";
                    break;
                case "D01M01B6":
                    groupType = "潜在客户";
                    break;
                default:
            }
            return R.ok(this.appWorkgroupService.getList(groupType, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //D01M01B系列，加处get接口，getList不分页全数返回，用GroupUid排序 abc123这样排序
    @ApiOperation(value = "按条件分页查询 无权限返回id,groupuid,abbreviate", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getListAbbr", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getListAbbr(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 获取请求的路径,/分割 https://dev.inksyun.com:31080/store/D04M01B1/saveInit
            String[] pathParts = request.getRequestURI().split("/");
            String moduleCode = pathParts[pathParts.length - 2]; // 获取倒数第二个路径参数：如"D04M01B1"
            String groupType = null;
            switch (moduleCode) {
                case "D01M01B1":
                    groupType = "客户";
                    break;
                case "D01M01B2":
                    groupType = "供应商";
                    break;
                case "D01M01B3":
                    groupType = "生产车间";
                    break;
                case "D01M01B4":
                    groupType = "外协厂商";
                    break;
                case "D01M01B5":
                    groupType = "其他部门";
                    break;
                case "D01M01B6":
                    groupType = "潜在客户";
                    break;
                default:
            }
            return R.ok(this.appWorkgroupService.getListAbbr(groupType, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增往来单位", notes = "新增往来单位", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Workgroup.Add")
    public R<AppWorkgroupPojo> create(@RequestBody String json) {
        try {
            AppWorkgroupPojo appWorkgroupPojo = JSONArray.parseObject(json, AppWorkgroupPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            appWorkgroupPojo.setCreateby(loginUser.getRealname());   // 创建者
            appWorkgroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            appWorkgroupPojo.setCreatedate(new Date());   // 创建时间
            appWorkgroupPojo.setLister(loginUser.getRealname());   // 制表
            appWorkgroupPojo.setListerid(loginUser.getUserid());    // 制表id  
            appWorkgroupPojo.setModifydate(new Date());   //修改时间
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.appWorkgroupService.insert(appWorkgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改往来单位", notes = "修改往来单位", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasAnyPermi = {"App_Workgroup.Edit", "App_Wg_Customer.Edit", "App_Wg_Supplier.Edit"})
    public R<AppWorkgroupPojo> update(@RequestBody String json) {
        try {
            AppWorkgroupPojo appWorkgroupPojo = JSONArray.parseObject(json, AppWorkgroupPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            appWorkgroupPojo.setLister(loginUser.getRealname());   // 制表
            appWorkgroupPojo.setListerid(loginUser.getUserid());    // 制表id  
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            appWorkgroupPojo.setModifydate(new Date());   //修改时间

            //重名检查
            AppWorkgroupPojo appWorkgroupPojo1 = this.appWorkgroupService.getEntityByUid(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "编码重复");
            }
            appWorkgroupPojo1 = this.appWorkgroupService.getEntityByName(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "名称重复");
            }

            return R.ok(this.appWorkgroupService.update(appWorkgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    @OperLog(title = "删除往来单位")
    public R<AppWorkgroupPojo> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            //检查引用
            List<String> lstcite = this.appWorkgroupService.getCiteBillName(key, tid);
            if (!lstcite.isEmpty()) {
                return R.fail(500, "禁止删除,被以下单据引用:" + lstcite);
            }
            // 未删除前先查询
            AppWorkgroupPojo workgroupDB = appWorkgroupService.getEntity(key, tid);
            this.appWorkgroupService.delete(key, tid);
            return R.ok(workgroupDB, workgroupDB.getGroupname() + "删除成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        AppWorkgroupPojo appWorkgroupPojo = this.appWorkgroupService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(appWorkgroupPojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 删除数据
     *
     * @param type 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "get新编码", notes = "get新编码", produces = "application/json")
    @RequestMapping(value = "/getNextCode", method = RequestMethod.GET)
    public R<String> getNextCode(String type) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 读取最大数
            AppWorkgroupPojo appWorkgroupPojo = new AppWorkgroupPojo();
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());
            if (type.equals("1")) {
                appWorkgroupPojo.setGrouptype("客户");
            }
            if (type.equals("2")) {
                appWorkgroupPojo.setGrouptype("供应商");
            }
            if (type.equals("3")) {
                appWorkgroupPojo.setGrouptype("生产车间");
            }
            if (type.equals("4")) {
                appWorkgroupPojo.setGrouptype("外协厂商");
            }
            if (type.equals("5")) {
                appWorkgroupPojo.setGrouptype("其他部门");
            }
            if (type.equals("6")) {
                appWorkgroupPojo.setGrouptype("潜在客户");
            }
            //生成最大
            String nextCode = this.appWorkgroupService.getNextCode(appWorkgroupPojo);
            return R.ok(nextCode);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /*
     * esay poi导入导出 时间2021-12-13 song
     * */
    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        List<AppWorkgroupPojo> list = new ArrayList<>();
        //创建表格
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("往来单位(导入时删除本行)", ""),
                AppWorkgroupPojo.class, list);
        try {
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "往来单位模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<AppWorkgroupPojo>> importExecl(MultipartFile file) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<AppWorkgroupPojo> list = POIUtil.importExcel(file.getInputStream(), AppWorkgroupPojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量云打印报表(List<AppWorkgroupPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    //    @PreAuthorize(hasPermi = "App_Workgroup.Print")
    public R<String> printWebPageTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and App_Workgroup.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<AppWorkgroupPojo> lstTh = this.appWorkgroupService.getPageList(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstTh.size() > 0) {
                map.put("groupname", lstTh.get(0).getGroupname());
                map.put("abbreviate", lstTh.get(0).getAbbreviate());
                map.put("groupuid", lstTh.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "wip批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

