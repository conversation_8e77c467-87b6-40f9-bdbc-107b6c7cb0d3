package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCostmodelitemPojo;

import java.util.List;
/**
 * 模型项目(BusCostmodelitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-07 13:30:44
 */
public interface BusCostmodelitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostmodelitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusCostmodelitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusCostmodelitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busCostmodelitemPojo 实例对象
     * @return 实例对象
     */
    BusCostmodelitemPojo insert(BusCostmodelitemPojo busCostmodelitemPojo);

    /**
     * 修改数据
     *
     * @param busCostmodelitempojo 实例对象
     * @return 实例对象
     */
    BusCostmodelitemPojo update(BusCostmodelitemPojo busCostmodelitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busCostmodelitempojo 实例对象
     * @return 实例对象
     */
    BusCostmodelitemPojo clearNull(BusCostmodelitemPojo busCostmodelitempojo);
}
