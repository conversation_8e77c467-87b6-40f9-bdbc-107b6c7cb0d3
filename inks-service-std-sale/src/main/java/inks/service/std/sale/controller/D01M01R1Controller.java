package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import inks.service.std.sale.mapper.AppWorkgroupMapper;
import inks.service.std.sale.service.AppWorkgroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 客户信息(App_Wg_Customer)表控制层
 * Customer 客户 Supplier 供应商 Branch 其他部门  Workshop 生产车间  Factory 外协厂商 Prospects潜在客户
 *
 * <AUTHOR>
 * @since 2021-11-11 09:10:03
 */
@RestController
@RequestMapping("D01M01R1")
@Api(tags = "D01M01R1:往来单位:数据报表")
public class D01M01R1Controller extends AppWorkgroupController {
    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;
    @Resource
    private AppWorkgroupMapper appWorkgroupMapper;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询全单位", notes = "按条件分页查询全单位", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<AppWorkgroupPojo>> getPageList(@RequestBody String json, @RequestParam(required = false) Integer dept) {//dept=1才开启过滤部门，默认不过滤
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.appWorkgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Supplier.List")
    public R<PageInfo<AppWorkgroupPojo>> getOnlinePageList(@RequestBody String json, String grouptype) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and App_Workgroup.deletemark=0 and App_Workgroup.enabledmark=1 ";
            if (StringUtils.isNotBlank(grouptype)) {
                qpfilter += " and App_Workgroup.GroupType in ('" + grouptype.replace(",", "','") + "') ";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.appWorkgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "销售订单BusMachining删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteBusMachiningByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteBusMachiningByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            // 级联删主子表
            return R.ok(this.appWorkgroupMapper.deleteBusMachiningByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "核价单BusOrderCost删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteBusOrderCostByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteBusOrderCostByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            // 级联删主子表
            return R.ok(this.appWorkgroupMapper.deleteBusOrderCostByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "送货单Bus_Deliery删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteBusDelieryByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteBusDelieryByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            return R.ok(this.appWorkgroupMapper.deleteBusDelieryByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购计划Buy_PlanItem删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteBuyPlanItemByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteBuyPlanItemByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            return R.ok(this.appWorkgroupMapper.deleteBuyPlanItemByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购合同Buy_Order删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteBuyOrderByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteBuyOrderByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            return R.ok(this.appWorkgroupMapper.deleteBuyOrderByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购收货Buy_Finishing删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteBuyFinishingByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteBuyFinishingByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            return R.ok(this.appWorkgroupMapper.deleteBuyFinishingByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "生产加工单Wk_Worksheet删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteWkWorksheetByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteWkWorksheetByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            return R.ok(this.appWorkgroupMapper.deleteWkWorksheetByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "出入库单Mat_Access删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteMatAccessByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteMatAccessByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            return R.ok(this.appWorkgroupMapper.deleteMatAccessByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "委制工单Wk_Subcontract删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/deleteWkSubcontractByGroupId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Workgroup.Delete")
    public R<Integer> deleteWkSubcontractByGroupId(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String tid = loginUser.getTenantid();
            return R.ok(this.appWorkgroupMapper.deleteWkSubcontractByGroupId(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
