package inks.service.std.sale.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusCostpartgroupEntity;
import inks.service.std.sale.domain.pojo.BusCostpartgroupPojo;
import inks.service.std.sale.mapper.BusCostpartgroupMapper;
import inks.service.std.sale.service.BusCostpartgroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 组件分组(BusCostpartgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-07 13:29:47
 */
@Service("busCostpartgroupService")
public class BusCostpartgroupServiceImpl implements BusCostpartgroupService {
    @Resource
    private BusCostpartgroupMapper busCostpartgroupMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusCostpartgroupPojo getEntity(String key, String tid) {
        return this.busCostpartgroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusCostpartgroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCostpartgroupPojo> lst = busCostpartgroupMapper.getPageList(queryParam);
            PageInfo<BusCostpartgroupPojo> pageInfo = new PageInfo<BusCostpartgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busCostpartgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusCostpartgroupPojo insert(BusCostpartgroupPojo busCostpartgroupPojo) {
        //初始化NULL字段
        if (busCostpartgroupPojo.getGrpkey() == null) busCostpartgroupPojo.setGrpkey("");
        if (busCostpartgroupPojo.getGrpname() == null) busCostpartgroupPojo.setGrpname("");
        if (busCostpartgroupPojo.getRownum() == null) busCostpartgroupPojo.setRownum(0);
        if (busCostpartgroupPojo.getRemark() == null) busCostpartgroupPojo.setRemark("");
        if (busCostpartgroupPojo.getCreateby() == null) busCostpartgroupPojo.setCreateby("");
        if (busCostpartgroupPojo.getCreatebyid() == null) busCostpartgroupPojo.setCreatebyid("");
        if (busCostpartgroupPojo.getCreatedate() == null) busCostpartgroupPojo.setCreatedate(new Date());
        if (busCostpartgroupPojo.getLister() == null) busCostpartgroupPojo.setLister("");
        if (busCostpartgroupPojo.getListerid() == null) busCostpartgroupPojo.setListerid("");
        if (busCostpartgroupPojo.getModifydate() == null) busCostpartgroupPojo.setModifydate(new Date());
        if (busCostpartgroupPojo.getCustom1() == null) busCostpartgroupPojo.setCustom1("");
        if (busCostpartgroupPojo.getCustom2() == null) busCostpartgroupPojo.setCustom2("");
        if (busCostpartgroupPojo.getCustom3() == null) busCostpartgroupPojo.setCustom3("");
        if (busCostpartgroupPojo.getCustom4() == null) busCostpartgroupPojo.setCustom4("");
        if (busCostpartgroupPojo.getCustom5() == null) busCostpartgroupPojo.setCustom5("");
        if (busCostpartgroupPojo.getCustom6() == null) busCostpartgroupPojo.setCustom6("");
        if (busCostpartgroupPojo.getCustom7() == null) busCostpartgroupPojo.setCustom7("");
        if (busCostpartgroupPojo.getCustom8() == null) busCostpartgroupPojo.setCustom8("");
        if (busCostpartgroupPojo.getCustom9() == null) busCostpartgroupPojo.setCustom9("");
        if (busCostpartgroupPojo.getCustom10() == null) busCostpartgroupPojo.setCustom10("");
        if (busCostpartgroupPojo.getDeptid() == null) busCostpartgroupPojo.setDeptid("");
        if (busCostpartgroupPojo.getTenantid() == null) busCostpartgroupPojo.setTenantid("");
        if (busCostpartgroupPojo.getTenantname() == null) busCostpartgroupPojo.setTenantname("");
        if (busCostpartgroupPojo.getRevision() == null) busCostpartgroupPojo.setRevision(0);
        BusCostpartgroupEntity busCostpartgroupEntity = new BusCostpartgroupEntity();
        BeanUtils.copyProperties(busCostpartgroupPojo, busCostpartgroupEntity);

        busCostpartgroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busCostpartgroupEntity.setRevision(1);  //乐观锁
        this.busCostpartgroupMapper.insert(busCostpartgroupEntity);
        return this.getEntity(busCostpartgroupEntity.getId(), busCostpartgroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busCostpartgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusCostpartgroupPojo update(BusCostpartgroupPojo busCostpartgroupPojo) {
        BusCostpartgroupEntity busCostpartgroupEntity = new BusCostpartgroupEntity();
        BeanUtils.copyProperties(busCostpartgroupPojo, busCostpartgroupEntity);
        this.busCostpartgroupMapper.update(busCostpartgroupEntity);
        return this.getEntity(busCostpartgroupEntity.getId(), busCostpartgroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busCostpartgroupMapper.delete(key, tid);
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public List<BusCostpartgroupPojo> getList(String tid) {
        return this.busCostpartgroupMapper.getList(tid);
    }

    // 拆分groupjson
    @Override
    public void splitGroupjson(List<Map<String, Object>> lst, String tid) {
        List<BusCostpartgroupPojo> lstgroup = this.busCostpartgroupMapper.getList(tid);
        if (lstgroup.size() > 0) {
            for (Map<String, Object> map : lst) {
                if (map.get("costgroupjson") != null && map.get("costgroupjson").toString().length() > 0) {
                    List<Map<String, Object>> listObjectSec = JSONArray.parseObject(map.get("costgroupjson").toString(), List.class);
                    for (Map<String, Object> mapList : listObjectSec) {
                        if (mapList.get("key") != null && mapList.get("value") != null)
                            map.put(mapList.get("key").toString(), mapList.get("value").toString());
                    }
                }
            }
        }
    }

}
