package inks.service.std.sale.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.service.std.sale.domain.pojo.BusDelieryPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo;
import inks.service.std.sale.mapper.D01M06R1Mapper;
import inks.service.std.sale.service.D01M06R1Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 销售单(BusDeliery)报表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-13 10:19:46
 */
@Service("d01m06r1Service")
public class D01M06R1ServiceImpl implements D01M06R1Service {

    @Resource
    private D01M06R1Mapper d01M06R1Mapper;

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    public PageInfo<BusDelieryitemdetailPojo> getSumPageListByGroup(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryitemdetailPojo> lst = this.d01M06R1Mapper.getSumPageListByGroup(queryParam);
            PageInfo<BusDelieryitemdetailPojo> pageInfo = new PageInfo<BusDelieryitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    public PageInfo<BusDelieryitemdetailPojo> getSumPageListByGoods(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryitemdetailPojo> lst = this.d01M06R1Mapper.getSumPageListByGoods(queryParam);
            PageInfo<BusDelieryitemdetailPojo> pageInfo = new PageInfo<BusDelieryitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

     /*
      *
      * <AUTHOR>
      * @description 获取销售单客户金额排名
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    public List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam) {
        try {
            return this.d01M06R1Mapper.getSumAmtByGroupMax(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

//    (按省份/地级市)获取送货单客户金额排名
    @Override
    public List<Map<String, Object>> getSumAmtByGroupProvince(QueryParam queryParam, String province) {
        try {
            return this.d01M06R1Mapper.getSumAmtByGroupProvince(queryParam,province);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSumAmtByGroupProvinceMach(QueryParam queryParam, String province) {
        try {
            return this.d01M06R1Mapper.getSumAmtByGroupProvinceMach(queryParam,province);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSumAmtGroupByCountry(QueryParam queryParam) {
        try {
            return this.d01M06R1Mapper.getSumAmtGroupByCountry(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSumAmtByGroupProvinceCollection(QueryParam queryParam, String province) {
        try {
            return this.d01M06R1Mapper.getSumAmtByGroupProvinceCollection(queryParam,province);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
      *
      * <AUTHOR>
      * @description 获取销售单客户金额排名
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    public List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam,String province,String city) {
        try {
            return this.d01M06R1Mapper.getSumAmtByGoodsMax(queryParam,province,city);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图年
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getSumAmtByYear(QueryParam queryParam) {
        try {
            //将 SearchPojo对象转换成json对象
            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            List<String> list = new ArrayList<>();
            //从SearchPojo 对象中取出时间
            list.add(resultStr.getString("StartDate")+"-01");
            list.add(resultStr.getString("StartDate")+"-02");
            list.add(resultStr.getString("StartDate")+"-03");
            list.add(resultStr.getString("StartDate")+"-04");
            list.add(resultStr.getString("StartDate")+"-05");
            list.add(resultStr.getString("StartDate")+"-06");
            list.add(resultStr.getString("StartDate")+"-07");
            list.add(resultStr.getString("StartDate")+"-08");
            list.add(resultStr.getString("StartDate")+"-09");
            list.add(resultStr.getString("StartDate")+"-10");
            list.add(resultStr.getString("StartDate")+"-11");
            list.add(resultStr.getString("StartDate")+"-12");
            //统计书
            Double sum = 0.0;
            List<ChartPojo> chartPojoList = d01M06R1Mapper.getSumAmtByYear(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            //循环月list
            for(int i=0;i< list.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        //判断如果月等于数据库返回月那么值就等于数据库返回值 否则为0
                        if(chartPojoList.get(j).getName().equals(list.get(i))){
                            //因为是趋势图 所以采用累加的形式
                            sum+=chartPojoList.get(j).getValue();
                            chartPojo.setValue(sum);
                            break;
                        }
                        else{
                            sum+=0.0;
                            chartPojo.setValue(sum);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图月
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getSumAmtByMonth(QueryParam queryParam) throws ParseException {
        try {
            //将object对象转行成json对象
            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            System.out.println(resultStr.getString("StartDate"));
            List<String> dateList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(resultStr.getString("StartDate")));
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
            String nowDate = dateSdf.format(new Date());
            // 到下个月不在累计
            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(resultStr.getString("StartDate").split("-")[1])) {
                // 至本年月日,不在计算
                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
                    dateList.add(dateSdf.format(calendar.getTime()));
                    calendar.add(Calendar.DATE, 1);
                    break;
                }
                dateList.add(dateSdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
            List<ChartPojo> chartPojoList = d01M06R1Mapper.getSumAmtByMonth(queryParam);
            Double sum = 0.0;
            List<ChartPojo> pojoList = new ArrayList<>();
            for(int i=0;i< dateList.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(dateList.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        if(chartPojoList.get(j).getName().equals(dateList.get(i))){
                            sum+=chartPojoList.get(j).getValue();
                            chartPojo.setValue(sum);
                            break;
                        }
                        else{
                            sum+=0.0;
                            chartPojo.setValue(sum);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图周
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getSumAmtByDay(QueryParam queryParam) {
        try {
            List<String> list = new ArrayList<>();
            for(int i=7-1;i>=0;i--){
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - i);
                Date today = calendar.getTime();
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                list.add(format.format(today));
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            queryParam.getDateRange().setStartDate(simpleDateFormat.parse(list.get(0)));
            queryParam.getDateRange().setEndDate(simpleDateFormat.parse(list.get(list.size()-1)));
            List<ChartPojo> chartPojoList = d01M06R1Mapper.getSumAmtByDay(queryParam);
            Double sum = 0.0;
            List<ChartPojo> pojoList = new ArrayList<>();
            for(int i=0;i< list.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        if(chartPojoList.get(j).getName().equals(list.get(i))){
                            sum+=chartPojoList.get(j).getValue();
                            chartPojo.setValue(sum);
                            break;
                        }
                        else{
                            sum+=0.0;
                            chartPojo.setValue(sum);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 本月销售额
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public ChartPojo getTagSumAmtQtyByMonth(QueryParam queryParam) {
        try {
            return d01M06R1Mapper.getTagSumAmtQtyByMonth(queryParam);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 销售饼状图年
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getSumAmtByYearMax(QueryParam queryParam) {
        try {
            //将 SearchPojo对象转换成json对象
            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            List<String> list = new ArrayList<>();
            //从SearchPojo 对象中取出时间
            list.add(resultStr.getString("StartDate")+"-01");
            list.add(resultStr.getString("StartDate")+"-02");
            list.add(resultStr.getString("StartDate")+"-03");
            list.add(resultStr.getString("StartDate")+"-04");
            list.add(resultStr.getString("StartDate")+"-05");
            list.add(resultStr.getString("StartDate")+"-06");
            list.add(resultStr.getString("StartDate")+"-07");
            list.add(resultStr.getString("StartDate")+"-08");
            list.add(resultStr.getString("StartDate")+"-09");
            list.add(resultStr.getString("StartDate")+"-10");
            list.add(resultStr.getString("StartDate")+"-11");
            list.add(resultStr.getString("StartDate")+"-12");
            List<ChartPojo> chartPojoList = d01M06R1Mapper.getSumAmtByYear(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            //循环月list
            for(int i=0;i< list.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        //判断如果月等于数据库返回月那么值就等于数据库返回值 否则为0
                        if(chartPojoList.get(j).getName().equals(list.get(i))){
                            //因为是趋势图 所以采用累加的形式
                            chartPojo.setValue(chartPojoList.get(j).getValue());
                            break;
                        }
                        else{
                            chartPojo.setValue(0.0);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 销售饼状图月
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getSumAmtByMonthMax(QueryParam queryParam) {
        try {
            //将object对象转行成json对象
            //JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getDateRange().toString());
            List<String> dateList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(DateUtils.dateTime( queryParam.getDateRange().getStartDate())));
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
            String nowDate = dateSdf.format(new Date());
            // 到下个月不在累计
            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(DateUtils.dateTime( queryParam.getDateRange().getStartDate()).split("-")[1])) {
                // 至本年月日,不在计算
                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
                    dateList.add(dateSdf.format(calendar.getTime()));
                    calendar.add(Calendar.DATE, 1);
                    break;
                }
                dateList.add(dateSdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
            List<ChartPojo> chartPojoList = d01M06R1Mapper.getSumAmtByMonth(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            for(int i=0;i< dateList.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(dateList.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        if(chartPojoList.get(j).getName().equals(dateList.get(i))){
                            chartPojo.setValue(chartPojoList.get(j).getValue());
                            break;
                        }
                        else{
                            chartPojo.setValue(0.0);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 销售饼状图周
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getSumAmtByDayMax(QueryParam queryParam) {
        try {
           // JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            List<String> list = new ArrayList<>();
            for(int i=7-1;i>=0;i--){
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - i);
                Date today = calendar.getTime();
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                list.add(format.format(today));
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            queryParam.getDateRange().setStartDate(simpleDateFormat.parse(list.get(0)));
            queryParam.getDateRange().setEndDate(simpleDateFormat.parse(list.get(list.size()-1)));
            List<ChartPojo> chartPojoList = d01M06R1Mapper.getSumAmtByDay(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            for(int i=0;i< list.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        if(chartPojoList.get(j).getName().equals(list.get(i))){
                            chartPojo.setValue(chartPojoList.get(j).getValue());
                            break;
                        }
                        else{
                            chartPojo.setValue(0.0);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryitemdetailPojo> lst =  d01M06R1Mapper.getPageList(queryParam);
            PageInfo<BusDelieryitemdetailPojo> pageInfo = new PageInfo<BusDelieryitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryPojo> lst = d01M06R1Mapper.getPageTh(queryParam);
            PageInfo<BusDelieryPojo> pageInfo = new PageInfo<BusDelieryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


}
