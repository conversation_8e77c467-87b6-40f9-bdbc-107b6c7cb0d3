package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusBudgetitemEntity;
import inks.service.std.sale.domain.pojo.BusBudgetitemPojo;
import inks.service.std.sale.mapper.BusBudgetitemMapper;
import inks.service.std.sale.service.BusBudgetitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 预算项目(BusBudgetitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-02 09:27:23
 */
@Service("busBudgetitemService")
public class BusBudgetitemServiceImpl implements BusBudgetitemService {
    @Resource
    private BusBudgetitemMapper busBudgetitemMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusBudgetitemPojo getEntity(String key,String tid) {
        return this.busBudgetitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusBudgetitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusBudgetitemPojo> lst = busBudgetitemMapper.getPageList(queryParam);
            PageInfo<BusBudgetitemPojo> pageInfo = new PageInfo<BusBudgetitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusBudgetitemPojo> getList(String Pid,String tid) { 
        try {
            List<BusBudgetitemPojo> lst = busBudgetitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busBudgetitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusBudgetitemPojo insert(BusBudgetitemPojo busBudgetitemPojo) {
        //初始化item的NULL
        BusBudgetitemPojo itempojo =this.clearNull(busBudgetitemPojo);
        BusBudgetitemEntity busBudgetitemEntity = new BusBudgetitemEntity(); 
        BeanUtils.copyProperties(itempojo,busBudgetitemEntity);
          //生成雪花id
          busBudgetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busBudgetitemEntity.setRevision(1);  //乐观锁      
          this.busBudgetitemMapper.insert(busBudgetitemEntity);
        return this.getEntity(busBudgetitemEntity.getId(),busBudgetitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busBudgetitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusBudgetitemPojo update(BusBudgetitemPojo busBudgetitemPojo) {
        BusBudgetitemEntity busBudgetitemEntity = new BusBudgetitemEntity(); 
        BeanUtils.copyProperties(busBudgetitemPojo,busBudgetitemEntity);
        this.busBudgetitemMapper.update(busBudgetitemEntity);
        return this.getEntity(busBudgetitemEntity.getId(),busBudgetitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busBudgetitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busBudgetitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusBudgetitemPojo clearNull(BusBudgetitemPojo busBudgetitemPojo){
     //初始化NULL字段
     if(busBudgetitemPojo.getPid()==null) busBudgetitemPojo.setPid("");
     if(busBudgetitemPojo.getGoodsid()==null) busBudgetitemPojo.setGoodsid("");
     if(busBudgetitemPojo.getItemcode()==null) busBudgetitemPojo.setItemcode("");
     if(busBudgetitemPojo.getItemname()==null) busBudgetitemPojo.setItemname("");
     if(busBudgetitemPojo.getItemspec()==null) busBudgetitemPojo.setItemspec("");
     if(busBudgetitemPojo.getItemunit()==null) busBudgetitemPojo.setItemunit("");
     if(busBudgetitemPojo.getQuantity()==null) busBudgetitemPojo.setQuantity(0D);
     if(busBudgetitemPojo.getStdprice()==null) busBudgetitemPojo.setStdprice(0D);
     if(busBudgetitemPojo.getStdamount()==null) busBudgetitemPojo.setStdamount(0D);
     if(busBudgetitemPojo.getRebate()==null) busBudgetitemPojo.setRebate(0D);
     if(busBudgetitemPojo.getTaxprice()==null) busBudgetitemPojo.setTaxprice(0D);
     if(busBudgetitemPojo.getTaxamount()==null) busBudgetitemPojo.setTaxamount(0D);
     if(busBudgetitemPojo.getItemtaxrate()==null) busBudgetitemPojo.setItemtaxrate(0);
     if(busBudgetitemPojo.getTaxtotal()==null) busBudgetitemPojo.setTaxtotal(0D);
     if(busBudgetitemPojo.getPrice()==null) busBudgetitemPojo.setPrice(0D);
     if(busBudgetitemPojo.getAmount()==null) busBudgetitemPojo.setAmount(0D);
     if(busBudgetitemPojo.getAttributejson()==null) busBudgetitemPojo.setAttributejson("");
     if(busBudgetitemPojo.getItemorgdate()==null) busBudgetitemPojo.setItemorgdate(new Date());
     if(busBudgetitemPojo.getItemplandate()==null) busBudgetitemPojo.setItemplandate(new Date());
     if(busBudgetitemPojo.getStoqty()==null) busBudgetitemPojo.setStoqty(0D);
     if(busBudgetitemPojo.getWkqty()==null) busBudgetitemPojo.setWkqty(0D);
     if(busBudgetitemPojo.getRownum()==null) busBudgetitemPojo.setRownum(0);
     if(busBudgetitemPojo.getVirtualitem()==null) busBudgetitemPojo.setVirtualitem(0);
     if(busBudgetitemPojo.getMaxqty()==null) busBudgetitemPojo.setMaxqty(0D);
     if(busBudgetitemPojo.getRemark()==null) busBudgetitemPojo.setRemark("");
     if(busBudgetitemPojo.getMachuid()==null) busBudgetitemPojo.setMachuid("");
     if(busBudgetitemPojo.getMachitemid()==null) busBudgetitemPojo.setMachitemid("");
     if(busBudgetitemPojo.getMatcost()==null) busBudgetitemPojo.setMatcost(0D);
     if(busBudgetitemPojo.getLaborcost()==null) busBudgetitemPojo.setLaborcost(0D);
     if(busBudgetitemPojo.getDirectcost()==null) busBudgetitemPojo.setDirectcost(0D);
     if(busBudgetitemPojo.getIndirectcost()==null) busBudgetitemPojo.setIndirectcost(0D);
     if(busBudgetitemPojo.getMatitemjson()==null) busBudgetitemPojo.setMatitemjson("");
     if(busBudgetitemPojo.getLaboritemjson()==null) busBudgetitemPojo.setLaboritemjson("");
     if(busBudgetitemPojo.getDirectitemjson()==null) busBudgetitemPojo.setDirectitemjson("");
     if(busBudgetitemPojo.getIndirectitemjson()==null) busBudgetitemPojo.setIndirectitemjson("");
     if(busBudgetitemPojo.getMatamt()==null) busBudgetitemPojo.setMatamt(0D);
     if(busBudgetitemPojo.getLaboramt()==null) busBudgetitemPojo.setLaboramt(0D);
     if(busBudgetitemPojo.getDirectamt()==null) busBudgetitemPojo.setDirectamt(0D);
     if(busBudgetitemPojo.getIndirectamt()==null) busBudgetitemPojo.setIndirectamt(0D);
     if(busBudgetitemPojo.getSourcetype()==null) busBudgetitemPojo.setSourcetype(0);
     if(busBudgetitemPojo.getAttacount()==null) busBudgetitemPojo.setAttacount(0);
     if(busBudgetitemPojo.getDisannulmark()==null) busBudgetitemPojo.setDisannulmark(0);
     if(busBudgetitemPojo.getDisannullisterid()==null) busBudgetitemPojo.setDisannullisterid("");
     if(busBudgetitemPojo.getDisannullister()==null) busBudgetitemPojo.setDisannullister("");
     if(busBudgetitemPojo.getDisannuldate()==null) busBudgetitemPojo.setDisannuldate(new Date());
     if(busBudgetitemPojo.getCustom1()==null) busBudgetitemPojo.setCustom1("");
     if(busBudgetitemPojo.getCustom2()==null) busBudgetitemPojo.setCustom2("");
     if(busBudgetitemPojo.getCustom3()==null) busBudgetitemPojo.setCustom3("");
     if(busBudgetitemPojo.getCustom4()==null) busBudgetitemPojo.setCustom4("");
     if(busBudgetitemPojo.getCustom5()==null) busBudgetitemPojo.setCustom5("");
     if(busBudgetitemPojo.getCustom6()==null) busBudgetitemPojo.setCustom6("");
     if(busBudgetitemPojo.getCustom7()==null) busBudgetitemPojo.setCustom7("");
     if(busBudgetitemPojo.getCustom8()==null) busBudgetitemPojo.setCustom8("");
     if(busBudgetitemPojo.getCustom9()==null) busBudgetitemPojo.setCustom9("");
     if(busBudgetitemPojo.getCustom10()==null) busBudgetitemPojo.setCustom10("");
     if(busBudgetitemPojo.getTenantid()==null) busBudgetitemPojo.setTenantid("");
     if(busBudgetitemPojo.getRevision()==null) busBudgetitemPojo.setRevision(0);
     return busBudgetitemPojo;
     }

    @Override
    public BusBudgetitemPojo getEntityByMachitemid(String machitemid, String tid) {
        return this.busBudgetitemMapper.getEntityByMachitemid(machitemid,tid);
    }
}
