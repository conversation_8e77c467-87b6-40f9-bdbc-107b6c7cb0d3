package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusInvoiceitemPojo;

import java.util.List;
/**
 * 发票项目(BusInvoiceitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 10:00:36
 */
public interface BusInvoiceitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusInvoiceitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusInvoiceitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusInvoiceitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busInvoiceitemPojo 实例对象
     * @return 实例对象
     */
    BusInvoiceitemPojo insert(BusInvoiceitemPojo busInvoiceitemPojo);

    /**
     * 修改数据
     *
     * @param busInvoiceitempojo 实例对象
     * @return 实例对象
     */
    BusInvoiceitemPojo update(BusInvoiceitemPojo busInvoiceitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busInvoiceitempojo 实例对象
     * @return 实例对象
     */
    BusInvoiceitemPojo clearNull(BusInvoiceitemPojo busInvoiceitempojo);
}
