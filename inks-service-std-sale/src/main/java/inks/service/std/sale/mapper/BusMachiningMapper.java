package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusMachiningEntity;
import inks.service.std.sale.domain.pojo.BusMachiningPojo;
import inks.service.std.sale.domain.pojo.BusMachiningitemPojo;
import inks.service.std.sale.domain.pojo.BusMachiningitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 销售订单(BusMachining)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-13 13:56:19
 */
@Mapper
public interface BusMachiningMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusMachiningPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusMachiningitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusMachiningPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busMachiningEntity 实例对象
     * @return 影响行数
     */
    int insert(BusMachiningEntity busMachiningEntity);


    /**
     * 修改数据
     *
     * @param busMachiningEntity 实例对象
     * @return 影响行数
     */
    int update(BusMachiningEntity busMachiningEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busMachiningPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusMachiningPojo busMachiningPojo);

    /**
     * 修改数据
     *
     * @param busMachiningEntity 实例对象
     * @return 影响行数
     */
    int approval(BusMachiningEntity busMachiningEntity);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);


    /**
     * 修改完工记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param busMachiningitemPojo 实例对象
     * @return 影响行数
     */
    int updateMachDeliPrice(BusMachiningitemPojo busMachiningitemPojo);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateOrderCostItemMachMark(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateOrderCostFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 所有Item
     *
     * @param ids 筛选条件
     * @return 查询结果
     */
    List<BusMachiningitemPojo> getItemListByIds(@Param("ids")String ids,@Param("pid")String pid,@Param("tid")String tid);


    //根据料号获取货品信息
    String getGoodsidByGoodsUid(@Param("goodsuid") String goodsuid, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateGoodsRequRemQty(@Param("key") String key, @Param("goodsuid") String goodsuid, @Param("tid") String tid);


    int updateMachDeliBillAmt(BusMachiningitemPojo busMachiningitemPojo);

    int updateQuotItemMachMark(@Param("key")String quotitemid, @Param("tid")String tenantid);

    int updateQuotFinishCount(@Param("key")String quotitemid, @Param("tid")String tenantid);

    int updatePrintcount(BusMachiningPojo billPrintPojo);

    int updateOaflowmark(BusMachiningPojo busMachiningPojo);

    Map<String, Object> getLastAmtByGroupId(@Param("groupid") String groupid, @Param("tid") String tenantid);

    Double getOnlineMachTaxAmountByGroupId(@Param("groupid") String groupid, @Param("tid") String tenantid);

    int updateExponent(@Param("id") String id, @Param("exponent") Integer exponent, @Param("tid") String tenantid);

    List<Map<String ,Object>> checkHistoryBillType(List<String> goodsids, String tenantid);
}

