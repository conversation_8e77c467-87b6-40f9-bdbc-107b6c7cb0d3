package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDeliplanitemPojo;
import inks.service.std.sale.domain.BusDeliplanitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 发货计划明细(BusDeliplanitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:18
 */
 @Mapper
public interface BusDeliplanitemMapper {

    BusDeliplanitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusDeliplanitemPojo> getPageList(QueryParam queryParam);

    List<BusDeliplanitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(BusDeliplanitemEntity busDeliplanitemEntity);

    int update(BusDeliplanitemEntity busDeliplanitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

