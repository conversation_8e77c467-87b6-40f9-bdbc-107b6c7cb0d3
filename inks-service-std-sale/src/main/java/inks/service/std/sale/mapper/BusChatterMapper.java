package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusChatterEntity;
import inks.service.std.sale.domain.pojo.BusChatterPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售客服(BusChatter)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-01 10:29:51
 */
@Mapper
public interface BusChatterMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusChatterPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusChatterPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param busChatterEntity 实例对象
     * @return 影响行数
     */
    int insert(BusChatterEntity busChatterEntity);

    
    /**
     * 修改数据
     *
     * @param busChatterEntity 实例对象
     * @return 影响行数
     */
    int update(BusChatterEntity busChatterEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int isChatter(@Param("userid") String userid, @Param("tenantid") String tenantid);
}

