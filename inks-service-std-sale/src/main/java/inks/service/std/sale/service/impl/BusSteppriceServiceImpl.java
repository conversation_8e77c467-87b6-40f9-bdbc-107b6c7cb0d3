package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusSteppriceEntity;
import inks.service.std.sale.domain.BusSteppriceitemEntity;
import inks.service.std.sale.domain.BusSteppriceobjEntity;
import inks.service.std.sale.domain.pojo.BusSteppricePojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemPojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemdetailPojo;
import inks.service.std.sale.domain.pojo.BusSteppriceobjPojo;
import inks.service.std.sale.mapper.BusSteppriceMapper;
import inks.service.std.sale.mapper.BusSteppriceitemMapper;
import inks.service.std.sale.mapper.BusSteppriceobjMapper;
import inks.service.std.sale.service.BusSteppriceService;
import inks.service.std.sale.service.BusSteppriceitemService;
import inks.service.std.sale.service.BusSteppriceobjService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 阶梯单价(BusStepprice)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-02 08:47:12
 */
@Service("busSteppriceService")
public class BusSteppriceServiceImpl implements BusSteppriceService {
    @Resource
    private BusSteppriceMapper busSteppriceMapper;

    @Resource
    private BusSteppriceitemMapper busSteppriceitemMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private BusSteppriceitemService busSteppriceitemService;

    @Resource
    private BusSteppriceobjMapper busSteppriceobjMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private BusSteppriceobjService busSteppriceobjService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusSteppricePojo getEntity(String key, String tid) {
        return this.busSteppriceMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusSteppriceitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusSteppriceitemdetailPojo> lst = busSteppriceMapper.getPageList(queryParam);
            PageInfo<BusSteppriceitemdetailPojo> pageInfo = new PageInfo<BusSteppriceitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusSteppricePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusSteppricePojo busSteppricePojo = this.busSteppriceMapper.getEntity(key, tid);
            //读取子表
            busSteppricePojo.setItem(busSteppriceitemMapper.getList(busSteppricePojo.getId(), busSteppricePojo.getTenantid()));
            //读取Obj
            busSteppricePojo.setObj(busSteppriceobjMapper.getList(busSteppricePojo.getId(), busSteppricePojo.getTenantid()));
            return busSteppricePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusSteppricePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusSteppricePojo> lst = busSteppriceMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busSteppriceitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setObj(busSteppriceobjMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusSteppricePojo> pageInfo = new PageInfo<BusSteppricePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusSteppricePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusSteppricePojo> lst = busSteppriceMapper.getPageTh(queryParam);
            PageInfo<BusSteppricePojo> pageInfo = new PageInfo<BusSteppricePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busSteppricePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusSteppricePojo insert(BusSteppricePojo busSteppricePojo) {
//初始化NULL字段
        if (busSteppricePojo.getRefno() == null) busSteppricePojo.setRefno("");
        if (busSteppricePojo.getBilltype() == null) busSteppricePojo.setBilltype("");
        if (busSteppricePojo.getBilltitle() == null) busSteppricePojo.setBilltitle("");
        if (busSteppricePojo.getBilldate() == null) busSteppricePojo.setBilldate(new Date());
        if (busSteppricePojo.getGroupid() == null) busSteppricePojo.setGroupid("");
        if (busSteppricePojo.getGrouplevel() == null) busSteppricePojo.setGrouplevel("");
        if (busSteppricePojo.getMachtype() == null) busSteppricePojo.setMachtype("");
        if (busSteppricePojo.getOperator() == null) busSteppricePojo.setOperator("");
        if (busSteppricePojo.getStartdate() == null) busSteppricePojo.setStartdate(new Date());
        if (busSteppricePojo.getEnddate() == null) busSteppricePojo.setEnddate(new Date());
        if (busSteppricePojo.getCreateby() == null) busSteppricePojo.setCreateby("");
        if (busSteppricePojo.getCreatebyid() == null) busSteppricePojo.setCreatebyid("");
        if (busSteppricePojo.getCreatedate() == null) busSteppricePojo.setCreatedate(new Date());
        if (busSteppricePojo.getLister() == null) busSteppricePojo.setLister("");
        if (busSteppricePojo.getListerid() == null) busSteppricePojo.setListerid("");
        if (busSteppricePojo.getModifydate() == null) busSteppricePojo.setModifydate(new Date());
        if (busSteppricePojo.getAssessor() == null) busSteppricePojo.setAssessor("");
        if (busSteppricePojo.getAssessorid() == null) busSteppricePojo.setAssessorid("");
        if (busSteppricePojo.getAssessdate() == null) busSteppricePojo.setAssessdate(new Date());
        if (busSteppricePojo.getSummary() == null) busSteppricePojo.setSummary("");
        if (busSteppricePojo.getCustom1() == null) busSteppricePojo.setCustom1("");
        if (busSteppricePojo.getCustom2() == null) busSteppricePojo.setCustom2("");
        if (busSteppricePojo.getCustom3() == null) busSteppricePojo.setCustom3("");
        if (busSteppricePojo.getCustom4() == null) busSteppricePojo.setCustom4("");
        if (busSteppricePojo.getCustom5() == null) busSteppricePojo.setCustom5("");
        if (busSteppricePojo.getCustom6() == null) busSteppricePojo.setCustom6("");
        if (busSteppricePojo.getCustom7() == null) busSteppricePojo.setCustom7("");
        if (busSteppricePojo.getCustom8() == null) busSteppricePojo.setCustom8("");
        if (busSteppricePojo.getCustom9() == null) busSteppricePojo.setCustom9("");
        if (busSteppricePojo.getCustom10() == null) busSteppricePojo.setCustom10("");
        if (busSteppricePojo.getTenantid() == null) busSteppricePojo.setTenantid("");
        if (busSteppricePojo.getTenantname() == null) busSteppricePojo.setTenantname("");
        if (busSteppricePojo.getRevision() == null) busSteppricePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusSteppriceEntity busSteppriceEntity = new BusSteppriceEntity();
        BeanUtils.copyProperties(busSteppricePojo, busSteppriceEntity);
        //设置id和新建日期
        busSteppriceEntity.setId(id);
        busSteppriceEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busSteppriceMapper.insert(busSteppriceEntity);
        //Item子表处理
        List<BusSteppriceitemPojo> lst = busSteppricePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (BusSteppriceitemPojo busSteppriceitemPojo : lst) {
                //初始化item的NULL
                BusSteppriceitemPojo itemPojo = this.busSteppriceitemService.clearNull(busSteppriceitemPojo);
                BusSteppriceitemEntity busSteppriceitemEntity = new BusSteppriceitemEntity();
                BeanUtils.copyProperties(itemPojo, busSteppriceitemEntity);
                //设置id和Pid
                busSteppriceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busSteppriceitemEntity.setPid(id);
                busSteppriceitemEntity.setTenantid(busSteppricePojo.getTenantid());
                busSteppriceitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busSteppriceitemMapper.insert(busSteppriceitemEntity);
            }
        }
        //Obj子表处理
        List<BusSteppriceobjPojo> lstObj = busSteppricePojo.getObj();
        if (lstObj != null) {
            //循环每个item子表
            for (BusSteppriceobjPojo busSteppriceobjPojo : lstObj) {
                //初始化item的NULL
                BusSteppriceobjPojo objPojo = this.busSteppriceobjService.clearNull(busSteppriceobjPojo);
                BusSteppriceobjEntity busSteppriceobjEntity = new BusSteppriceobjEntity();
                BeanUtils.copyProperties(objPojo, busSteppriceobjEntity);
                //设置id和Pid
                busSteppriceobjEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busSteppriceobjEntity.setPid(id);
                busSteppriceobjEntity.setTenantid(busSteppricePojo.getTenantid());
                busSteppriceobjEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busSteppriceobjMapper.insert(busSteppriceobjEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(busSteppriceEntity.getId(), busSteppriceEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busSteppricePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusSteppricePojo update(BusSteppricePojo busSteppricePojo) {
        //主表更改
        BusSteppriceEntity busSteppriceEntity = new BusSteppriceEntity();
        BeanUtils.copyProperties(busSteppricePojo, busSteppriceEntity);
        this.busSteppriceMapper.update(busSteppriceEntity);
        if (busSteppricePojo.getItem() != null) {
            //Item子表处理
            List<BusSteppriceitemPojo> lst = busSteppricePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busSteppriceMapper.getDelItemIds(busSteppricePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.busSteppriceitemMapper.delete(lstDelId, busSteppriceEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (BusSteppriceitemPojo busSteppriceitemPojo : lst) {
                    BusSteppriceitemEntity busSteppriceitemEntity = new BusSteppriceitemEntity();
                    if ("".equals(busSteppriceitemPojo.getId()) || busSteppriceitemPojo.getId() == null) {
                        //初始化item的NULL
                        BusSteppriceitemPojo itemPojo = this.busSteppriceitemService.clearNull(busSteppriceitemPojo);
                        BeanUtils.copyProperties(itemPojo, busSteppriceitemEntity);
                        //设置id和Pid
                        busSteppriceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busSteppriceitemEntity.setPid(busSteppriceEntity.getId());  // 主表 id
                        busSteppriceitemEntity.setTenantid(busSteppricePojo.getTenantid());   // 租户id
                        busSteppriceitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busSteppriceitemMapper.insert(busSteppriceitemEntity);
                    } else {
                        BeanUtils.copyProperties(busSteppriceitemPojo, busSteppriceitemEntity);
                        busSteppriceitemEntity.setTenantid(busSteppricePojo.getTenantid());
                        this.busSteppriceitemMapper.update(busSteppriceitemEntity);
                    }
                }
            }
        }

        if (busSteppricePojo.getObj() != null) {
            //obj子表处理
            List<BusSteppriceobjPojo> lst = busSteppricePojo.getObj();
            //获取被删除的obj
            List<String> lstDelIds = busSteppriceMapper.getDelObjIds(busSteppricePojo);
            if (lstDelIds != null) {
                //循环每个删除obj子表
                for (String lstDelId : lstDelIds) {
                    this.busSteppriceobjMapper.delete(lstDelId, busSteppriceEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (BusSteppriceobjPojo busSteppriceobjPojo : lst) {
                    BusSteppriceobjEntity busSteppriceobjEntity = new BusSteppriceobjEntity();
                    if (busSteppriceobjPojo.getId() == null || "".equals(busSteppriceobjPojo.getId())) {
                        //初始化obj的NULL
                        BusSteppriceobjPojo objPojo = this.busSteppriceobjService.clearNull(busSteppriceobjPojo);
                        BeanUtils.copyProperties(objPojo, busSteppriceobjEntity);
                        //设置id和Pid
                        busSteppriceobjEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busSteppriceobjEntity.setPid(busSteppriceEntity.getId());  // 主表 id
                        busSteppriceobjEntity.setTenantid(busSteppricePojo.getTenantid());   // 租户id
                        busSteppriceobjEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busSteppriceobjMapper.insert(busSteppriceobjEntity);
                    } else {
                        BeanUtils.copyProperties(busSteppriceobjPojo, busSteppriceobjEntity);
                        busSteppriceobjEntity.setTenantid(busSteppricePojo.getTenantid());  // 租户id
                        this.busSteppriceobjMapper.update(busSteppriceobjEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busSteppriceEntity.getId(), busSteppriceEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        BusSteppricePojo busSteppricePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusSteppriceitemPojo> lst = busSteppricePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusSteppriceitemPojo busSteppriceitemPojo : lst) {
                this.busSteppriceitemMapper.delete(busSteppriceitemPojo.getId(), tid);
            }
        }
        //Obj子表处理
        List<BusSteppriceobjPojo> lstObj = busSteppricePojo.getObj();
        if (lstObj != null) {
            //循环每个删除Obj子表
            for (BusSteppriceobjPojo busSteppriceobjPojo : lstObj) {
                this.busSteppriceobjMapper.delete(busSteppriceobjPojo.getId(), tid);
            }
        }
         this.busSteppriceMapper.delete(key, tid);
        return busSteppricePojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param busSteppricePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusSteppricePojo approval(BusSteppricePojo busSteppricePojo) {
        //主表更改
        BusSteppriceEntity busSteppriceEntity = new BusSteppriceEntity();
        BeanUtils.copyProperties(busSteppricePojo, busSteppriceEntity);
        this.busSteppriceMapper.approval(busSteppriceEntity);
        //返回Bill实例
        return this.getBillEntity(busSteppriceEntity.getId(), busSteppriceEntity.getTenantid());
    }

    /**
     * 根据客户查询售价
     *
     * @return 查询结果
     */
    @Override
    public  BusSteppriceitemPojo getStepPriceByGroupid(String goodsid,String groupid,Double qty, String tid){
        return this.busSteppriceMapper.getStepPriceByGroupid(goodsid,groupid,qty,tid);
    }
    /**
     * 根据客户查询售价
     *
     * @return 查询结果
     */
    @Override
    public  BusSteppriceitemPojo getStepPriceByGroupLevel(String goodsid,String grouplevel,Double qty, String tid){
        return this.busSteppriceMapper.getStepPriceByGroupLevel(goodsid,grouplevel,qty,tid);
    }
}
