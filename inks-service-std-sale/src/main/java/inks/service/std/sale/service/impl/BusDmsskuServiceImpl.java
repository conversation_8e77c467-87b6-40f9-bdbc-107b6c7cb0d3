package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusDmsskuEntity;
import inks.service.std.sale.domain.pojo.BusDmsskuPojo;
import inks.service.std.sale.mapper.BusDmsskuMapper;
import inks.service.std.sale.service.BusDmsskuService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * Dms商品Sku(BusDmssku)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-05 11:10:13
 */
@Service("busDmsskuService")
public class BusDmsskuServiceImpl implements BusDmsskuService {
    @Resource
    private BusDmsskuMapper busDmsskuMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDmsskuPojo getEntity(String key, String tid) {
        return this.busDmsskuMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDmsskuPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDmsskuPojo> lst = busDmsskuMapper.getPageList(queryParam);
            PageInfo<BusDmsskuPojo> pageInfo = new PageInfo<BusDmsskuPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param busDmsskuPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDmsskuPojo insert(BusDmsskuPojo busDmsskuPojo) {
    //初始化NULL字段
     if(busDmsskuPojo.getSkucode()==null) busDmsskuPojo.setSkucode("");
     if(busDmsskuPojo.getSkunum()==null) busDmsskuPojo.setSkunum(0);
     if(busDmsskuPojo.getGoodsid()==null) busDmsskuPojo.setGoodsid("");
     if(busDmsskuPojo.getOmsgoodsid()==null) busDmsskuPojo.setOmsgoodsid("");
     if(busDmsskuPojo.getItemcode()==null) busDmsskuPojo.setItemcode("");
     if(busDmsskuPojo.getItemname()==null) busDmsskuPojo.setItemname("");
     if(busDmsskuPojo.getAttributejson()==null) busDmsskuPojo.setAttributejson("");
     if(busDmsskuPojo.getBarcode()==null) busDmsskuPojo.setBarcode("");
     if(busDmsskuPojo.getSafestock()==null) busDmsskuPojo.setSafestock(0D);
     if(busDmsskuPojo.getInprice()==null) busDmsskuPojo.setInprice(0D);
     if(busDmsskuPojo.getOutprice()==null) busDmsskuPojo.setOutprice(0D);
     if(busDmsskuPojo.getIvquantity()==null) busDmsskuPojo.setIvquantity(0D);
     if(busDmsskuPojo.getIvamount()==null) busDmsskuPojo.setIvamount(0D);
     if(busDmsskuPojo.getAgeprice()==null) busDmsskuPojo.setAgeprice(0D);
     if(busDmsskuPojo.getSkuphoto()==null) busDmsskuPojo.setSkuphoto("");
     if(busDmsskuPojo.getImgs()==null) busDmsskuPojo.setImgs("");
     if(busDmsskuPojo.getExponent()==null) busDmsskuPojo.setExponent(0);
     if(busDmsskuPojo.getRemark()==null) busDmsskuPojo.setRemark("");
     if(busDmsskuPojo.getRownum()==null) busDmsskuPojo.setRownum(0);
     if(busDmsskuPojo.getCreateby()==null) busDmsskuPojo.setCreateby("");
     if(busDmsskuPojo.getCreatebyid()==null) busDmsskuPojo.setCreatebyid("");
     if(busDmsskuPojo.getCreatedate()==null) busDmsskuPojo.setCreatedate(new Date());
     if(busDmsskuPojo.getLister()==null) busDmsskuPojo.setLister("");
     if(busDmsskuPojo.getListerid()==null) busDmsskuPojo.setListerid("");
     if(busDmsskuPojo.getModifydate()==null) busDmsskuPojo.setModifydate(new Date());
     if(busDmsskuPojo.getCustom1()==null) busDmsskuPojo.setCustom1("");
     if(busDmsskuPojo.getCustom2()==null) busDmsskuPojo.setCustom2("");
     if(busDmsskuPojo.getCustom3()==null) busDmsskuPojo.setCustom3("");
     if(busDmsskuPojo.getCustom4()==null) busDmsskuPojo.setCustom4("");
     if(busDmsskuPojo.getCustom5()==null) busDmsskuPojo.setCustom5("");
     if(busDmsskuPojo.getCustom6()==null) busDmsskuPojo.setCustom6("");
     if(busDmsskuPojo.getCustom7()==null) busDmsskuPojo.setCustom7("");
     if(busDmsskuPojo.getCustom8()==null) busDmsskuPojo.setCustom8("");
     if(busDmsskuPojo.getCustom9()==null) busDmsskuPojo.setCustom9("");
     if(busDmsskuPojo.getCustom10()==null) busDmsskuPojo.setCustom10("");
     if(busDmsskuPojo.getTenantid()==null) busDmsskuPojo.setTenantid("");
     if(busDmsskuPojo.getTenantname()==null) busDmsskuPojo.setTenantname("");
     if(busDmsskuPojo.getRevision()==null) busDmsskuPojo.setRevision(0);
        BusDmsskuEntity busDmsskuEntity = new BusDmsskuEntity(); 
        BeanUtils.copyProperties(busDmsskuPojo,busDmsskuEntity);
          //生成雪花id
          busDmsskuEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busDmsskuEntity.setRevision(1);  //乐观锁
          this.busDmsskuMapper.insert(busDmsskuEntity);
        return this.getEntity(busDmsskuEntity.getId(),busDmsskuEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busDmsskuPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDmsskuPojo update(BusDmsskuPojo busDmsskuPojo) {
        BusDmsskuEntity busDmsskuEntity = new BusDmsskuEntity(); 
        BeanUtils.copyProperties(busDmsskuPojo,busDmsskuEntity);
        this.busDmsskuMapper.update(busDmsskuEntity);
        return this.getEntity(busDmsskuEntity.getId(),busDmsskuEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busDmsskuMapper.delete(key,tid) ;
    }
    

}
