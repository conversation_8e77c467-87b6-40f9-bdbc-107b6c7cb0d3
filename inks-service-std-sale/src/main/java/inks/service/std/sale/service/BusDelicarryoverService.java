package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDelicarryoverPojo;
import com.github.pagehelper.PageInfo;

/**
 * 货品账单:订单>发货(Bus_DeliCarryover)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-10 12:50:16
 */
public interface BusDelicarryoverService {

    BusDelicarryoverPojo getEntity(String key,String tid);

    PageInfo<BusDelicarryoverPojo> getPageList(QueryParam queryParam);

    BusDelicarryoverPojo insert(BusDelicarryoverPojo busDelicarryoverPojo);

    BusDelicarryoverPojo update(BusDelicarryoverPojo busDelicarryoverpojo);

    int delete(String key,String tid);
}
