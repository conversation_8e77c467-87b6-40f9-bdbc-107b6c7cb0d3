package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusDmsskuEntity;
import inks.service.std.sale.domain.pojo.BusDmsskuPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Dms商品Sku(BusDmssku)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-29 13:08:57
 */
@Mapper
public interface BusDmsskuMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDmsskuPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDmsskuPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param busDmsskuEntity 实例对象
     * @return 影响行数
     */
    int insert(BusDmsskuEntity busDmsskuEntity);

    
    /**
     * 修改数据
     *
     * @param busDmsskuEntity 实例对象
     * @return 影响行数
     */
    int update(BusDmsskuEntity busDmsskuEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                                                                         }

