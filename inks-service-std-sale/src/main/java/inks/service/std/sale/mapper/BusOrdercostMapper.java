package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusOrdercostEntity;
import inks.service.std.sale.domain.pojo.BusOrdercostPojo;
import inks.service.std.sale.domain.pojo.BusOrdercostitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单成本(BusOrdercost)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-30 08:20:50
 */
@Mapper
public interface BusOrdercostMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusOrdercostPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusOrdercostitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusOrdercostPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busOrdercostEntity 实例对象
     * @return 影响行数
     */
    int insert(BusOrdercostEntity busOrdercostEntity);

    
    /**
     * 修改数据
     *
     * @param busOrdercostEntity 实例对象
     * @return 影响行数
     */
    int update(BusOrdercostEntity busOrdercostEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param busOrdercostPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(BusOrdercostPojo busOrdercostPojo);
                                                                                                                                                                              /**
     * 修改数据
     *
     * @param busOrdercostEntity 实例对象
     * @return 影响行数
     */
    int approval(BusOrdercostEntity busOrdercostEntity);

    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);

    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    // 查询Item是否被引用

    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);
}

