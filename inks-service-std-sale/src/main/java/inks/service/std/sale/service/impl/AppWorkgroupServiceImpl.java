package inks.service.std.sale.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.BillCodeUtil;
import inks.common.core.utils.DateUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.InksConfig;
import inks.service.std.sale.constant.MyConstant;
import inks.service.std.sale.domain.AppWorkgroupEntity;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.mapper.AppWorkgroupMapper;
import inks.service.std.sale.mapper.BusAccountMapper;
import inks.service.std.sale.mapper.BusAccountrecMapper;
import inks.service.std.sale.mapper.BusInvoiceMapper;
import inks.service.std.sale.service.AppWorkgroupService;
import inks.service.std.sale.utils.SqlUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 往来单位(AppWorkgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-14 08:26:37
 */
@Service("appWorkgroupService")
public class AppWorkgroupServiceImpl implements AppWorkgroupService {
    @Resource
    private AppWorkgroupMapper appWorkgroupMapper;

    @Resource
    private BusAccountMapper busAccountMapper;

    @Resource
    private BusInvoiceMapper busInvoiceMapper;
    /**
     * 服务对象
     */
    @Resource
    private BusAccountrecMapper busAccountrecMapper;

    @Resource
    private RedisService redisService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public AppWorkgroupPojo getEntity(String key, String tid) {
        return this.appWorkgroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<AppWorkgroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<AppWorkgroupPojo> lst = appWorkgroupMapper.getPageList(queryParam);
            PageInfo<AppWorkgroupPojo> pageInfo = new PageInfo<AppWorkgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param appWorkgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public AppWorkgroupPojo insert(AppWorkgroupPojo appWorkgroupPojo) {
        //初始化NULL字段
        if (appWorkgroupPojo.getWggroupid() == null) appWorkgroupPojo.setWggroupid("");
        if (appWorkgroupPojo.getGroupuid() == null) appWorkgroupPojo.setGroupuid("");
        if (appWorkgroupPojo.getGroupname() == null) appWorkgroupPojo.setGroupname("");
        if (appWorkgroupPojo.getAbbreviate() == null) appWorkgroupPojo.setAbbreviate("");
        if (appWorkgroupPojo.getGroupclass() == null) appWorkgroupPojo.setGroupclass("");
        if (appWorkgroupPojo.getLinkman() == null) appWorkgroupPojo.setLinkman("");
        if (appWorkgroupPojo.getTelephone() == null) appWorkgroupPojo.setTelephone("");
        if (appWorkgroupPojo.getGroupfax() == null) appWorkgroupPojo.setGroupfax("");
        if (appWorkgroupPojo.getGroupadd() == null) appWorkgroupPojo.setGroupadd("");
        if (appWorkgroupPojo.getRemark() == null) appWorkgroupPojo.setRemark("");
        if (appWorkgroupPojo.getInvaliddate() == null) appWorkgroupPojo.setInvaliddate(new Date());
        if (appWorkgroupPojo.getGrouptype() == null) appWorkgroupPojo.setGrouptype("");
        if (appWorkgroupPojo.getCreditduint() == null) appWorkgroupPojo.setCreditduint("");
        if (appWorkgroupPojo.getCreditdquantity() == null) appWorkgroupPojo.setCreditdquantity(0);
        if (appWorkgroupPojo.getCreditcuint() == null) appWorkgroupPojo.setCreditcuint("");
        if (appWorkgroupPojo.getCreditcquantity() == null) appWorkgroupPojo.setCreditcquantity(0);
        if (appWorkgroupPojo.getRownum() == null) appWorkgroupPojo.setRownum(0);
        if (appWorkgroupPojo.getCreateby() == null) appWorkgroupPojo.setCreateby("");
        if (appWorkgroupPojo.getCreatebyid() == null) appWorkgroupPojo.setCreatebyid("");
        if (appWorkgroupPojo.getCreatedate() == null) appWorkgroupPojo.setCreatedate(new Date());
        if (appWorkgroupPojo.getLister() == null) appWorkgroupPojo.setLister("");
        if (appWorkgroupPojo.getListerid() == null) appWorkgroupPojo.setListerid("");
        if (appWorkgroupPojo.getModifydate() == null) appWorkgroupPojo.setModifydate(new Date());
        if (appWorkgroupPojo.getMobile() == null) appWorkgroupPojo.setMobile("");
        if (appWorkgroupPojo.getLinkmans() == null) appWorkgroupPojo.setLinkmans("");
        if (appWorkgroupPojo.getTelephones() == null) appWorkgroupPojo.setTelephones("");
        if (appWorkgroupPojo.getMobiles() == null) appWorkgroupPojo.setMobiles("");
        if (appWorkgroupPojo.getCountry() == null) appWorkgroupPojo.setCountry("");
        if (appWorkgroupPojo.getProvince() == null) appWorkgroupPojo.setProvince("");
        if (appWorkgroupPojo.getGroupzip() == null) appWorkgroupPojo.setGroupzip("");
        if (appWorkgroupPojo.getDeliveradd() == null) appWorkgroupPojo.setDeliveradd("");
        if (appWorkgroupPojo.getInvoiceadd() == null) appWorkgroupPojo.setInvoiceadd("");
        if (appWorkgroupPojo.getSeller() == null) appWorkgroupPojo.setSeller("");
        if (appWorkgroupPojo.getGrouplabel() == null) appWorkgroupPojo.setGrouplabel("");
        if (appWorkgroupPojo.getGrouplevel() == null) appWorkgroupPojo.setGrouplevel("");
        if (appWorkgroupPojo.getGroupstate() == null) appWorkgroupPojo.setGroupstate("");
        if (appWorkgroupPojo.getSource() == null) appWorkgroupPojo.setSource("");
        if (appWorkgroupPojo.getCredit() == null) appWorkgroupPojo.setCredit("");
        if (appWorkgroupPojo.getPaymentmethod() == null) appWorkgroupPojo.setPaymentmethod("");
        if (appWorkgroupPojo.getCreditcode() == null) appWorkgroupPojo.setCreditcode("");
        if (appWorkgroupPojo.getDepositbank() == null) appWorkgroupPojo.setDepositbank("");
        if (appWorkgroupPojo.getBankaccount() == null) appWorkgroupPojo.setBankaccount("");
        if (appWorkgroupPojo.getEnabledmark() == null) appWorkgroupPojo.setEnabledmark(0);
        if (appWorkgroupPojo.getDeletemark() == null) appWorkgroupPojo.setDeletemark(0);
        if (appWorkgroupPojo.getDeletelister() == null) appWorkgroupPojo.setDeletelister("");
        if (appWorkgroupPojo.getDeletelisterid() == null) appWorkgroupPojo.setDeletelisterid("");
        if (appWorkgroupPojo.getDeletedate() == null) appWorkgroupPojo.setDeletedate(new Date());
        if (appWorkgroupPojo.getFmaccoid() == null) appWorkgroupPojo.setFmaccoid("");
        if (appWorkgroupPojo.getForeaccoid() == null) appWorkgroupPojo.setForeaccoid("");
        if (appWorkgroupPojo.getBusmachremamt() == null) appWorkgroupPojo.setBusmachremamt(0D);
        if (appWorkgroupPojo.getBusdeliremamt() == null) appWorkgroupPojo.setBusdeliremamt(0D);
        if (appWorkgroupPojo.getBusinvoremamt() == null) appWorkgroupPojo.setBusinvoremamt(0D);
        if (appWorkgroupPojo.getBusaccocloseamt() == null) appWorkgroupPojo.setBusaccocloseamt(0D);
        if (appWorkgroupPojo.getBusacconowamt() == null) appWorkgroupPojo.setBusacconowamt(0D);
        if (appWorkgroupPojo.getBuyorderremamt() == null) appWorkgroupPojo.setBuyorderremamt(0D);
        if (appWorkgroupPojo.getBuyfiniremamt() == null) appWorkgroupPojo.setBuyfiniremamt(0D);
        if (appWorkgroupPojo.getBuyinvoremamt() == null) appWorkgroupPojo.setBuyinvoremamt(0D);
        if (appWorkgroupPojo.getBuyaccocloseamt() == null) appWorkgroupPojo.setBuyaccocloseamt(0D);
        if (appWorkgroupPojo.getBuyacconowamt() == null) appWorkgroupPojo.setBuyacconowamt(0D);
        if (appWorkgroupPojo.getCity() == null) appWorkgroupPojo.setCity("");
        if (appWorkgroupPojo.getCounty() == null) appWorkgroupPojo.setCounty("");
        if (appWorkgroupPojo.getStreet() == null) appWorkgroupPojo.setStreet("");
        if(appWorkgroupPojo.getLocaladd()==null) appWorkgroupPojo.setLocaladd("");
        if(appWorkgroupPojo.getDeptid()==null) appWorkgroupPojo.setDeptid("");
        if(appWorkgroupPojo.getTaxrate()==null) appWorkgroupPojo.setTaxrate(0);
        if (appWorkgroupPojo.getCustom1() == null) appWorkgroupPojo.setCustom1("");
        if (appWorkgroupPojo.getCustom2() == null) appWorkgroupPojo.setCustom2("");
        if (appWorkgroupPojo.getCustom3() == null) appWorkgroupPojo.setCustom3("");
        if (appWorkgroupPojo.getCustom4() == null) appWorkgroupPojo.setCustom4("");
        if (appWorkgroupPojo.getCustom5() == null) appWorkgroupPojo.setCustom5("");
        if (appWorkgroupPojo.getCustom6() == null) appWorkgroupPojo.setCustom6("");
        if (appWorkgroupPojo.getCustom7() == null) appWorkgroupPojo.setCustom7("");
        if (appWorkgroupPojo.getCustom8() == null) appWorkgroupPojo.setCustom8("");
        if (appWorkgroupPojo.getCustom9() == null) appWorkgroupPojo.setCustom9("");
        if (appWorkgroupPojo.getCustom10() == null) appWorkgroupPojo.setCustom10("");
        if (appWorkgroupPojo.getTenantid() == null) appWorkgroupPojo.setTenantid("");
        if (appWorkgroupPojo.getRevision() == null) appWorkgroupPojo.setRevision(0);
        AppWorkgroupEntity appWorkgroupEntity = new AppWorkgroupEntity();
        BeanUtils.copyProperties(appWorkgroupPojo, appWorkgroupEntity);
        appWorkgroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        appWorkgroupEntity.setRevision(1);  //乐观锁
        this.appWorkgroupMapper.insert(appWorkgroupEntity);
        return this.getEntity(appWorkgroupEntity.getId(), appWorkgroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param appWorkgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public AppWorkgroupPojo update(AppWorkgroupPojo appWorkgroupPojo) {
        AppWorkgroupEntity appWorkgroupEntity = new AppWorkgroupEntity();
        BeanUtils.copyProperties(appWorkgroupPojo, appWorkgroupEntity);
        this.appWorkgroupMapper.update(appWorkgroupEntity);
        return this.getEntity(appWorkgroupEntity.getId(), appWorkgroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.appWorkgroupMapper.delete(key, tid);
    }

    // 查询往来单是否被引用
    @Override
    public List<String> getCiteBillName(String key, String tid) {
        return this.appWorkgroupMapper.getCiteBillName(key, tid);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    @Override
    public AppWorkgroupPojo getEntityByUid(AppWorkgroupPojo appWorkgroupPojo) {

        return this.appWorkgroupMapper.getEntityByUid(appWorkgroupPojo);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    @Override
    public AppWorkgroupPojo getEntityByName(AppWorkgroupPojo appWorkgroupPojo) {
        return this.appWorkgroupMapper.getEntityByName(appWorkgroupPojo);
    }


    /**
     * 通过type，Get下一个Code
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    @Override
    public String getNextCode(AppWorkgroupPojo appWorkgroupPojo) {

        String NextCode = "";
        String MaxStr = this.appWorkgroupMapper.getMaxCode(appWorkgroupPojo);
        if (MaxStr != null) {
            NextCode = BillCodeUtil.SnLatter(MaxStr, 1);
        }

        return NextCode;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键 App_Workgroup.id 客户id
     * @return 实例对象
     */
    @Override
    public AppWorkgroupPojo getCustomerGeneral(String key, String tid) {
        AppWorkgroupPojo wgPojo = this.appWorkgroupMapper.getEntity(key, tid);
        if (wgPojo == null) {
            throw new RuntimeException("未找到对应客户信息");
        }

        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01 00:00:00");
        Date dtEnd = new Date();

        // 查询当前客户之前的销售账单
        BusAccountPojo busAccountPojo = this.busAccountMapper.getMaxEntityByGroup(key, tid);

        if (busAccountPojo == null) {
            wgPojo.setAccountamount(0D);
            if (DateUtils.getTimestamp(wgPojo.getCreatedate()) < DateUtils.getTimestamp(dtStart)) {
                throw new RuntimeException("请先初始化客户账单");
            }
        } else {
            dtStart = busAccountPojo.getEnddate(); //  lstAcc.get(0).getEnddate();
            // 写入 对账单金额
            wgPojo.setAccountamount(busAccountPojo.getBillcloseamount());
        }

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + wgPojo.getId() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
//        dtStart = DateUtils.parseDate("2024-02-28 00:00:00");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1000);
        queryParam.setTenantid(tid);
        List<BusAccountitemPojo> lstitem = this.busAccountMapper.pullItemList(queryParam);
        // 改为BigDec
        BigDecimal decinAmt = BigDecimal.valueOf(0);
        BigDecimal decoutAmt = BigDecimal.valueOf(0);
        if (lstitem.isEmpty()) {
            wgPojo.setFreeamount(0D);
        } else {
            // RowNum 重排
            for (int i = 0; i < lstitem.size(); i++) {
                lstitem.get(i).setRownum(i);
                BigDecimal iteminAmt = BigDecimal.valueOf(lstitem.get(i).getInamount());
                BigDecimal itemoutAmt = BigDecimal.valueOf(lstitem.get(i).getOutamount());
                decinAmt = decinAmt.add(iteminAmt);
                decoutAmt = decoutAmt.add(itemoutAmt);

            }
            wgPojo.setFreeinamount(decinAmt.doubleValue());
            wgPojo.setFreeoutamount(decoutAmt.doubleValue());
            wgPojo.setFreeamount(decinAmt.subtract(decoutAmt).doubleValue());
        }
        BigDecimal accountAmt = BigDecimal.valueOf(wgPojo.getAccountamount());
        wgPojo.setTotalamount(accountAmt.add(decinAmt).subtract(decoutAmt).doubleValue());

        // 逾期发票数
        queryParam = new QueryParam();
        strFilter = " and Groupid='" + wgPojo.getId() + "'";
        strFilter += " and Bus_Invoice.DisannulMark=0 and Bus_Invoice.Closed=0 and Bus_Invoice.Receipted<Bus_Invoice.TaxAmount";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1000);
        queryParam.setTenantid(tid);
        List<BusInvoicePojo> lstInvo = this.busInvoiceMapper.getPageTh(queryParam);
        wgPojo.setTotalinvoice(lstInvo.size());
        wgPojo.setOverdueinvoice(0);
        if (!lstInvo.isEmpty()) {
            for (BusInvoicePojo invoPojo : lstInvo) {
                if (DateUtils.getTimestamp(invoPojo.getAimdate()) < DateUtils.getTimestamp(new Date())) {
                    wgPojo.setOverdueinvoice(wgPojo.getOverdueinvoice() + 1);
                }
            }
        }

        // 加入发票管理
        queryParam = new QueryParam();
        strFilter = " and id='" + wgPojo.getId() + "'";
        queryParam.setOrderBy("CreateDate");
        queryParam.setOrderType(0);
        queryParam.setFilterstr(strFilter);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1);
        queryParam.setTenantid(tid);
        List<AppWorkgroupPojo> lst = appWorkgroupMapper.getPageListByRece(queryParam);
        if (!lst.isEmpty()) {

            wgPojo.setInvoremamount(lst.get(0).getInvoremamount());
            wgPojo.setSalefreeamount(lst.get(0).getSalefreeamount());
            wgPojo.setDeporemamount(lst.get(0).getDeporemamount());
            BigDecimal InvoremAmt = BigDecimal.valueOf(lst.get(0).getInvoremamount());
            BigDecimal SalefreeAmt = BigDecimal.valueOf(lst.get(0).getSalefreeamount());
            BigDecimal DeporemAmt = BigDecimal.valueOf(lst.get(0).getDeporemamount());
            wgPojo.setSaletotalamount(InvoremAmt.add(SalefreeAmt).subtract(DeporemAmt).doubleValue());
        }
        return wgPojo;
    }


    @Override
    public AppWorkgroupPojo getSumCustomerGeneralBySalesman(String seller, String tid) {
        // 通过业务员名字查到所有的客户id
        List<String> groupIds = this.appWorkgroupMapper.getGroupIdsBySeller(seller, tid);
        AppWorkgroupPojo sumGroupPojo = new AppWorkgroupPojo();
        for (String groupId : groupIds) {
            AppWorkgroupPojo wgPojo = getCustomerGeneral(groupId, tid);
            sumGroupPojo.setSaletotalamount(handleNull(sumGroupPojo.getSaletotalamount()) + handleNull(wgPojo.getSaletotalamount()));
            sumGroupPojo.setInvoremamount(handleNull(sumGroupPojo.getInvoremamount()) + handleNull(wgPojo.getInvoremamount()));
            sumGroupPojo.setSalefreeamount(handleNull(sumGroupPojo.getSalefreeamount()) + handleNull(wgPojo.getSalefreeamount()));
            sumGroupPojo.setDeporemamount(handleNull(sumGroupPojo.getDeporemamount()) + handleNull(wgPojo.getDeporemamount()));
            sumGroupPojo.setOverdueinvoice(handleNull(sumGroupPojo.getOverdueinvoice()) + handleNull(wgPojo.getOverdueinvoice()));
            sumGroupPojo.setTotalinvoice(handleNull(sumGroupPojo.getTotalinvoice()) + handleNull(wgPojo.getTotalinvoice()));
            sumGroupPojo.setTotalamount(handleNull(sumGroupPojo.getTotalamount()) + handleNull(wgPojo.getTotalamount()));
            sumGroupPojo.setFreeinamount(handleNull(sumGroupPojo.getFreeinamount()) + handleNull(wgPojo.getFreeinamount()));
            sumGroupPojo.setFreeoutamount(handleNull(sumGroupPojo.getFreeoutamount()) + handleNull(wgPojo.getFreeoutamount()));
            sumGroupPojo.setFreeamount(handleNull(sumGroupPojo.getFreeamount()) + handleNull(wgPojo.getFreeamount()));
            sumGroupPojo.setAccountamount(handleNull(sumGroupPojo.getAccountamount()) + handleNull(wgPojo.getAccountamount()));
        }
        return sumGroupPojo;
    }

    public static int handleNull(Integer value) {
        return value != null ? value : 0;
    }

    public static double handleNull(Double value) {
        return value != null ? value : 0.0;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<AppWorkgroupPojo> getPageListBySale(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<AppWorkgroupPojo> lst = appWorkgroupMapper.getPageListBySale(queryParam);
            PageInfo<AppWorkgroupPojo> pageInfo = new PageInfo<AppWorkgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<AppWorkgroupPojo> getPageListByRece(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<AppWorkgroupPojo> lst = appWorkgroupMapper.getPageListByRece(queryParam);
            PageInfo<AppWorkgroupPojo> pageInfo = new PageInfo<AppWorkgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //刷新销售订单结余额
    @Override
    public int updateWorkgroupBusMachRemAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBusMachRemAmt(key, tid);
    }

    //刷新销售发货结余额
    @Override
    public int updateWorkgroupBusDeliRemAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBusDeliRemAmt(key, tid);
    }

    //刷新销售发票结余额
    @Override
    public int updateWorkgroupBusInvoRemAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBusInvoRemAmt(key, tid);
    }

    //刷新销售结转期末额
    @Override
    public int updateWorkgroupBusAccoCloseAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBusAccoCloseAmt(key, tid);
    }

    //刷新销售结转本期额
    @Override
    public int updateWorkgroupBusAccoNowAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBusAccoNowAmt(key, tid);
    }

    //刷新采购订单结余额
    @Override
    public int updateWorkgroupBuyOrderRemAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBuyOrderRemAmt(key, tid);
    }

    //刷新采购收货结余额
    @Override
    public int updateWorkgroupBuyFiniRemAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBuyFiniRemAmt(key, tid);
    }

    //刷新采购发票结余额
    @Override
    public int updateWorkgroupBuyInvoRemAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBuyInvoRemAmt(key, tid);

    }


    //刷新采购结转期末额
    @Override
    public int updateWorkgroupBuyAccoCloseAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBuyAccoCloseAmt(key, tid);
    }

    //刷新采购结转本期额
    @Override
    public int updateWorkgroupBuyAccoNowAmt(String key, String tid) {
        return this.appWorkgroupMapper.updateWorkgroupBuyAccoNowAmt(key, tid);
    }

    private final String PAGELISTBYSALE_CODE = "pagelistbysale_code:";

    @Override
    @Async
    public void getPageListBySaleStart(String uuid, LoginUser loginUser, QueryParam queryParam) {

        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(PAGELISTBYSALE_CODE, uuid, missionMsg);
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(loginUser.getTenantid());
            Date startDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy", new Date()) + "-01-01");
            Date endDate = new Date();
            if (busAccountrecPojo != null) {
                startDate = DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1);
            }
            DateRange dateRange = new DateRange("", startDate, endDate);
            queryParam.setDateRange(dateRange);
            queryParam.setTenantid(loginUser.getTenantid());
            PageInfo<AppWorkgroupPojo> pageListBySale = this.getPageListBySale(queryParam);
            String cachekey = MyConstant.RECEIPT_PAGES + uuid;
            Map<String, Object> pageListBySaleMap = inks.common.core.utils.bean.BeanUtils.beanToMap(pageListBySale);
            this.redisService.setCacheObject(cachekey, JSON.toJSONString(pageListBySaleMap), 600L, TimeUnit.SECONDS);
            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(PAGELISTBYSALE_CODE, uuid, missionMsg);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public Map<String, Object> getPageListBySaleState(String key) {
        return this.redisService.getCacheMapValue(PAGELISTBYSALE_CODE, key);
    }

    @Override
    public List<String> getProvOrCity(String province, String tid) {
        return this.appWorkgroupMapper.getProvOrCity(province, tid);
    }

    @Override
    public List<AppWorkgroupPojo> getList(String groupType, String tid) {
        return this.appWorkgroupMapper.getList(groupType, tid);
    }

    @Override
    public List<Map<String,Object>> getListAbbr(String groupType, String tid) {
        return this.appWorkgroupMapper.getListAbbr(groupType, tid);
    }

    @Override
    public int changeDeptid(String key, String deptid, String tid) {
        return this.appWorkgroupMapper.changeDeptid(key, deptid, tid);
    }
}
