package inks.service.std.sale.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.sale.domain.pojo.BusDelicarryoverPojo;
import inks.service.std.sale.domain.BusDelicarryoverEntity;
import inks.service.std.sale.mapper.BusDelicarryoverMapper;
import inks.service.std.sale.service.BusDelicarryoverService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 货品账单:订单>发货(BusDelicarryover)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-10 12:50:16
 */
@Service("busDelicarryoverService")
public class BusDelicarryoverServiceImpl implements BusDelicarryoverService {
    @Resource
    private BusDelicarryoverMapper busDelicarryoverMapper;

    @Override
    public BusDelicarryoverPojo getEntity(String key, String tid) {
        return this.busDelicarryoverMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<BusDelicarryoverPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelicarryoverPojo> lst = busDelicarryoverMapper.getPageList(queryParam);
            PageInfo<BusDelicarryoverPojo> pageInfo = new PageInfo<BusDelicarryoverPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public BusDelicarryoverPojo insert(BusDelicarryoverPojo busDelicarryoverPojo) {
        //初始化NULL字段
        cleanNull(busDelicarryoverPojo);
        BusDelicarryoverEntity busDelicarryoverEntity = new BusDelicarryoverEntity(); 
        BeanUtils.copyProperties(busDelicarryoverPojo,busDelicarryoverEntity);
          //生成雪花id
          busDelicarryoverEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busDelicarryoverEntity.setRevision(1);  //乐观锁
          this.busDelicarryoverMapper.insert(busDelicarryoverEntity);
        return this.getEntity(busDelicarryoverEntity.getId(),busDelicarryoverEntity.getTenantid());
    }


    @Override
    public BusDelicarryoverPojo update(BusDelicarryoverPojo busDelicarryoverPojo) {
        BusDelicarryoverEntity busDelicarryoverEntity = new BusDelicarryoverEntity(); 
        BeanUtils.copyProperties(busDelicarryoverPojo,busDelicarryoverEntity);
        this.busDelicarryoverMapper.update(busDelicarryoverEntity);
        return this.getEntity(busDelicarryoverEntity.getId(),busDelicarryoverEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.busDelicarryoverMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(BusDelicarryoverPojo busDelicarryoverPojo) {
        if(busDelicarryoverPojo.getRecid()==null) busDelicarryoverPojo.setRecid("");
        if(busDelicarryoverPojo.getGroupid()==null) busDelicarryoverPojo.setGroupid("");
        if(busDelicarryoverPojo.getGroupname()==null) busDelicarryoverPojo.setGroupname("");
        if(busDelicarryoverPojo.getGroupuid()==null) busDelicarryoverPojo.setGroupuid("");
        if(busDelicarryoverPojo.getAbbreviate()==null) busDelicarryoverPojo.setAbbreviate("");
        if(busDelicarryoverPojo.getGoodsid()==null) busDelicarryoverPojo.setGoodsid("");
        if(busDelicarryoverPojo.getItemcode()==null) busDelicarryoverPojo.setItemcode("");
        if(busDelicarryoverPojo.getItemname()==null) busDelicarryoverPojo.setItemname("");
        if(busDelicarryoverPojo.getItemspec()==null) busDelicarryoverPojo.setItemspec("");
        if(busDelicarryoverPojo.getItemunit()==null) busDelicarryoverPojo.setItemunit("");
        if(busDelicarryoverPojo.getPcsx()==null) busDelicarryoverPojo.setPcsx(0D);
        if(busDelicarryoverPojo.getPcsy()==null) busDelicarryoverPojo.setPcsy(0D);
        if(busDelicarryoverPojo.getSetx()==null) busDelicarryoverPojo.setSetx(0D);
        if(busDelicarryoverPojo.getSety()==null) busDelicarryoverPojo.setSety(0D);
        if(busDelicarryoverPojo.getSet2pcs()==null) busDelicarryoverPojo.setSet2pcs(0D);
        if(busDelicarryoverPojo.getPnlx()==null) busDelicarryoverPojo.setPnlx(0D);
        if(busDelicarryoverPojo.getPnly()==null) busDelicarryoverPojo.setPnly(0D);
        if(busDelicarryoverPojo.getPnl2pcs()==null) busDelicarryoverPojo.setPnl2pcs(0D);
        if(busDelicarryoverPojo.getOpenqty()==null) busDelicarryoverPojo.setOpenqty(0D);
        if(busDelicarryoverPojo.getOpenamount()==null) busDelicarryoverPojo.setOpenamount(0D);
        if(busDelicarryoverPojo.getInqty()==null) busDelicarryoverPojo.setInqty(0D);
        if(busDelicarryoverPojo.getInamount()==null) busDelicarryoverPojo.setInamount(0D);
        if(busDelicarryoverPojo.getOutqty()==null) busDelicarryoverPojo.setOutqty(0D);
        if(busDelicarryoverPojo.getOutamount()==null) busDelicarryoverPojo.setOutamount(0D);
        if(busDelicarryoverPojo.getCloseqty()==null) busDelicarryoverPojo.setCloseqty(0D);
        if(busDelicarryoverPojo.getCloseamount()==null) busDelicarryoverPojo.setCloseamount(0D);
        if(busDelicarryoverPojo.getSkuid()==null) busDelicarryoverPojo.setSkuid("");
        if(busDelicarryoverPojo.getAttributejson()==null) busDelicarryoverPojo.setAttributejson("");
        if(busDelicarryoverPojo.getClosemark()==null) busDelicarryoverPojo.setClosemark(0);
        if(busDelicarryoverPojo.getRownum()==null) busDelicarryoverPojo.setRownum(0);
        if(busDelicarryoverPojo.getCreateby()==null) busDelicarryoverPojo.setCreateby("");
        if(busDelicarryoverPojo.getCreatebyid()==null) busDelicarryoverPojo.setCreatebyid("");
        if(busDelicarryoverPojo.getCreatedate()==null) busDelicarryoverPojo.setCreatedate(new Date());
        if(busDelicarryoverPojo.getLister()==null) busDelicarryoverPojo.setLister("");
        if(busDelicarryoverPojo.getListerid()==null) busDelicarryoverPojo.setListerid("");
        if(busDelicarryoverPojo.getModifydate()==null) busDelicarryoverPojo.setModifydate(new Date());
        if(busDelicarryoverPojo.getCustom1()==null) busDelicarryoverPojo.setCustom1("");
        if(busDelicarryoverPojo.getCustom2()==null) busDelicarryoverPojo.setCustom2("");
        if(busDelicarryoverPojo.getCustom3()==null) busDelicarryoverPojo.setCustom3("");
        if(busDelicarryoverPojo.getCustom4()==null) busDelicarryoverPojo.setCustom4("");
        if(busDelicarryoverPojo.getCustom5()==null) busDelicarryoverPojo.setCustom5("");
        if(busDelicarryoverPojo.getCustom6()==null) busDelicarryoverPojo.setCustom6("");
        if(busDelicarryoverPojo.getCustom7()==null) busDelicarryoverPojo.setCustom7("");
        if(busDelicarryoverPojo.getCustom8()==null) busDelicarryoverPojo.setCustom8("");
        if(busDelicarryoverPojo.getCustom9()==null) busDelicarryoverPojo.setCustom9("");
        if(busDelicarryoverPojo.getCustom10()==null) busDelicarryoverPojo.setCustom10("");
        if(busDelicarryoverPojo.getTenantid()==null) busDelicarryoverPojo.setTenantid("");
        if(busDelicarryoverPojo.getRevision()==null) busDelicarryoverPojo.setRevision(0);
   }

}
