package inks.service.std.sale.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 客服分组DMS用户子表(BusChatgroupdms)Pojo
 *
 * <AUTHOR>
 * @since 2024-01-08 15:49:07
 */
public class BusChatgroupdmsPojo implements Serializable {
    private static final long serialVersionUID = -87416701156559220L;
         // id
       @Excel(name = "id")    
  private String id;
      @Excel(name = "")    
  private String pid;
         // Dmsid
       @Excel(name = "Dmsid")    
  private String dmsid;
         // DmsName
       @Excel(name = "DmsName")    
  private String dmsname;
         // 排列序号
       @Excel(name = "排列序号")    
  private Integer rownum;
         // 备注
       @Excel(name = "备注")    
  private String remark;
         // 创建者
       @Excel(name = "创建者")    
  private String createby;
         // 创建者id
       @Excel(name = "创建者id")    
  private String createbyid;
         // 新建日期
       @Excel(name = "新建日期")    
  private Date createdate;
         // 制表
       @Excel(name = "制表")    
  private String lister;
         // 制表id
       @Excel(name = "制表id")    
  private String listerid;
         // 修改日期
       @Excel(name = "修改日期")    
  private Date modifydate;
         // 自定义1
       @Excel(name = "自定义1")    
  private String custom1;
         // 自定义2
       @Excel(name = "自定义2")    
  private String custom2;
         // 自定义3
       @Excel(name = "自定义3")    
  private String custom3;
         // 自定义4
       @Excel(name = "自定义4")    
  private String custom4;
         // 租户id
       @Excel(name = "租户id")    
  private String tenantid;
         // 租户名称
       @Excel(name = "租户名称")    
  private String tenantname;
         // 乐观锁
       @Excel(name = "乐观锁")    
  private Integer revision;

     // id
   
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
  
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
     // Dmsid
   
    public String getDmsid() {
        return dmsid;
    }

    public void setDmsid(String dmsid) {
        this.dmsid = dmsid;
    }
     // DmsName
   
    public String getDmsname() {
        return dmsname;
    }

    public void setDmsname(String dmsname) {
        this.dmsname = dmsname;
    }
     // 排列序号
   
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
     // 备注
   
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
     // 创建者
   
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
     // 创建者id
   
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
     // 新建日期
   
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
     // 制表
   
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
     // 制表id
   
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
     // 修改日期
   
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
     // 自定义1
   
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
     // 自定义2
   
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
     // 自定义3
   
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
     // 自定义4
   
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
     // 租户id
   
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
     // 租户名称
   
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
     // 乐观锁
   
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

