package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCarryoverinvoPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 发货>发票(BusCarryoverinvo)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-27 08:50:35
 */
public interface BusCarryoverinvoService {

    BusCarryoverinvoPojo getEntity(String key,String tid);

    PageInfo<BusCarryoverinvoPojo> getPageList(QueryParam queryParam);

    List<BusCarryoverinvoPojo> getList(String Pid,String tid);  

    BusCarryoverinvoPojo insert(BusCarryoverinvoPojo busCarryoverinvoPojo);

    BusCarryoverinvoPojo update(BusCarryoverinvoPojo busCarryoverinvopojo);

    int delete(String key,String tid);

    BusCarryoverinvoPojo clearNull(BusCarryoverinvoPojo busCarryoverinvopojo);
}
