package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusCostmodelEntity;
import inks.service.std.sale.domain.BusCostmodelitemEntity;
import inks.service.std.sale.domain.pojo.BusCostmodelPojo;
import inks.service.std.sale.domain.pojo.BusCostmodelitemPojo;
import inks.service.std.sale.domain.pojo.BusCostmodelitemdetailPojo;
import inks.service.std.sale.mapper.BusCostmodelMapper;
import inks.service.std.sale.mapper.BusCostmodelitemMapper;
import inks.service.std.sale.service.BusCostmodelService;
import inks.service.std.sale.service.BusCostmodelitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 预算模型(BusCostmodel)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-07 13:30:13
 */
@Service("busCostmodelService")
public class BusCostmodelServiceImpl implements BusCostmodelService {
    @Resource
    private BusCostmodelMapper busCostmodelMapper;

    @Resource
    private BusCostmodelitemMapper busCostmodelitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusCostmodelitemService busCostmodelitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusCostmodelPojo getEntity(String key, String tid) {
        return this.busCostmodelMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusCostmodelitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCostmodelitemdetailPojo> lst = busCostmodelMapper.getPageList(queryParam);
            PageInfo<BusCostmodelitemdetailPojo> pageInfo = new PageInfo<BusCostmodelitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusCostmodelPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusCostmodelPojo busCostmodelPojo = this.busCostmodelMapper.getEntity(key, tid);
            //读取子表
            busCostmodelPojo.setItem(busCostmodelitemMapper.getList(busCostmodelPojo.getId(), busCostmodelPojo.getTenantid()));
            return busCostmodelPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusCostmodelPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCostmodelPojo> lst = busCostmodelMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busCostmodelitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusCostmodelPojo> pageInfo = new PageInfo<BusCostmodelPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusCostmodelPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCostmodelPojo> lst = busCostmodelMapper.getPageTh(queryParam);
            PageInfo<BusCostmodelPojo> pageInfo = new PageInfo<BusCostmodelPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busCostmodelPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusCostmodelPojo insert(BusCostmodelPojo busCostmodelPojo) {
//初始化NULL字段
        if (busCostmodelPojo.getModelcode() == null) busCostmodelPojo.setModelcode("");
        if (busCostmodelPojo.getModelname() == null) busCostmodelPojo.setModelname("");
        if (busCostmodelPojo.getEnabledmark() == null) busCostmodelPojo.setEnabledmark(0);
        if (busCostmodelPojo.getSummary() == null) busCostmodelPojo.setSummary("");
        if (busCostmodelPojo.getCreateby() == null) busCostmodelPojo.setCreateby("");
        if (busCostmodelPojo.getCreatebyid() == null) busCostmodelPojo.setCreatebyid("");
        if (busCostmodelPojo.getCreatedate() == null) busCostmodelPojo.setCreatedate(new Date());
        if (busCostmodelPojo.getLister() == null) busCostmodelPojo.setLister("");
        if (busCostmodelPojo.getListerid() == null) busCostmodelPojo.setListerid("");
        if (busCostmodelPojo.getModifydate() == null) busCostmodelPojo.setModifydate(new Date());
        if (busCostmodelPojo.getCustom1() == null) busCostmodelPojo.setCustom1("");
        if (busCostmodelPojo.getCustom2() == null) busCostmodelPojo.setCustom2("");
        if (busCostmodelPojo.getCustom3() == null) busCostmodelPojo.setCustom3("");
        if (busCostmodelPojo.getCustom4() == null) busCostmodelPojo.setCustom4("");
        if (busCostmodelPojo.getCustom5() == null) busCostmodelPojo.setCustom5("");
        if (busCostmodelPojo.getCustom6() == null) busCostmodelPojo.setCustom6("");
        if (busCostmodelPojo.getCustom7() == null) busCostmodelPojo.setCustom7("");
        if (busCostmodelPojo.getCustom8() == null) busCostmodelPojo.setCustom8("");
        if (busCostmodelPojo.getCustom9() == null) busCostmodelPojo.setCustom9("");
        if (busCostmodelPojo.getCustom10() == null) busCostmodelPojo.setCustom10("");
        if (busCostmodelPojo.getDeptid() == null) busCostmodelPojo.setDeptid("");
        if (busCostmodelPojo.getTenantid() == null) busCostmodelPojo.setTenantid("");
        if (busCostmodelPojo.getTenantname() == null) busCostmodelPojo.setTenantname("");
        if (busCostmodelPojo.getRevision() == null) busCostmodelPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusCostmodelEntity busCostmodelEntity = new BusCostmodelEntity();
        BeanUtils.copyProperties(busCostmodelPojo, busCostmodelEntity);
        //设置id和新建日期
        busCostmodelEntity.setId(id);
        busCostmodelEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busCostmodelMapper.insert(busCostmodelEntity);
        //Item子表处理
        List<BusCostmodelitemPojo> lst = busCostmodelPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BusCostmodelitemPojo itemPojo = this.busCostmodelitemService.clearNull(lst.get(i));
                BusCostmodelitemEntity busCostmodelitemEntity = new BusCostmodelitemEntity();
                BeanUtils.copyProperties(itemPojo, busCostmodelitemEntity);
                //设置id和Pid
                busCostmodelitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busCostmodelitemEntity.setPid(id);
                busCostmodelitemEntity.setTenantid(busCostmodelPojo.getTenantid());
                busCostmodelitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busCostmodelitemMapper.insert(busCostmodelitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(busCostmodelEntity.getId(), busCostmodelEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busCostmodelPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusCostmodelPojo update(BusCostmodelPojo busCostmodelPojo) {
        //主表更改
        BusCostmodelEntity busCostmodelEntity = new BusCostmodelEntity();
        BeanUtils.copyProperties(busCostmodelPojo, busCostmodelEntity);
        this.busCostmodelMapper.update(busCostmodelEntity);
        if (busCostmodelPojo.getItem() != null) {
            //Item子表处理
            List<BusCostmodelitemPojo> lst = busCostmodelPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busCostmodelMapper.getDelItemIds(busCostmodelPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.busCostmodelitemMapper.delete(lstDelIds.get(i), busCostmodelEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BusCostmodelitemEntity busCostmodelitemEntity = new BusCostmodelitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        BusCostmodelitemPojo itemPojo = this.busCostmodelitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, busCostmodelitemEntity);
                        //设置id和Pid
                        busCostmodelitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busCostmodelitemEntity.setPid(busCostmodelEntity.getId());  // 主表 id
                        busCostmodelitemEntity.setTenantid(busCostmodelPojo.getTenantid());   // 租户id
                        busCostmodelitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busCostmodelitemMapper.insert(busCostmodelitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), busCostmodelitemEntity);
                        busCostmodelitemEntity.setTenantid(busCostmodelPojo.getTenantid());
                        this.busCostmodelitemMapper.update(busCostmodelitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busCostmodelEntity.getId(), busCostmodelEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusCostmodelPojo busCostmodelPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusCostmodelitemPojo> lst = busCostmodelPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.busCostmodelitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.busCostmodelMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public BusCostmodelPojo getDefBillEntity(String tid) {
        try {
            //读取主表
            BusCostmodelPojo busCostmodelPojo = this.busCostmodelMapper.getDefEntity(tid);
            if (busCostmodelPojo != null) {
                //读取子表
                busCostmodelPojo.setItem(busCostmodelitemMapper.getList(busCostmodelPojo.getId(), busCostmodelPojo.getTenantid()));

            }
            return busCostmodelPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
