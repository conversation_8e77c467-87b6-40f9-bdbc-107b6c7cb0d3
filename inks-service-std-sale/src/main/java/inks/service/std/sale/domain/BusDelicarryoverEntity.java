package inks.service.std.sale.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 货品账单:订单>发货(BusDelicarryover)实体类
 *
 * <AUTHOR>
 * @since 2024-12-10 17:05:53
 */
public class BusDelicarryoverEntity implements Serializable {
    private static final long serialVersionUID = 211444222845978495L;
     // id
    private String id;
     // Bus_AccountRec.id
    private String recid;
     // 客户id
    private String groupid;
     // 客户名
    private String groupname;
     // 客户Uid
    private String groupuid;
     // 缩写
    private String abbreviate;
     // 商品ID
    private String goodsid;
     // 产品编码
    private String itemcode;
     // 产品名称
    private String itemname;
     // 产品规格
    private String itemspec;
     // 产品单位
    private String itemunit;
     // PcsX
    private Double pcsx;
     // PcsY
    private Double pcsy;
     // SetX
    private Double setx;
     // SetY
    private Double sety;
     // Set2Pcs
    private Double set2pcs;
     // PnlX
    private Double pnlx;
     // PnlY
    private Double pnly;
     // Pnl2Pcs
    private Double pnl2pcs;
     // 期初数量
    private Double openqty;
     // 期初金额
    private Double openamount;
     // 入账数量
    private Double inqty;
     // 入账金额
    private Double inamount;
     // 出账数量
    private Double outqty;
     // 出账金额
    private Double outamount;
     // 期末数量
    private Double closeqty;
     // 期末金额
    private Double closeamount;
     // Skuid(备用)
    private String skuid;
     // 属性Josn
    private String attributejson;
     // 不结转
    private Integer closemark;
     // 行号
    private Integer rownum;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// Bus_AccountRec.id
    public String getRecid() {
        return recid;
    }
    
    public void setRecid(String recid) {
        this.recid = recid;
    }
        
// 客户id
    public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
        
// 客户名
    public String getGroupname() {
        return groupname;
    }
    
    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }
        
// 客户Uid
    public String getGroupuid() {
        return groupuid;
    }
    
    public void setGroupuid(String groupuid) {
        this.groupuid = groupuid;
    }
        
// 缩写
    public String getAbbreviate() {
        return abbreviate;
    }
    
    public void setAbbreviate(String abbreviate) {
        this.abbreviate = abbreviate;
    }
        
// 商品ID
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
// 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
// 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
// 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
// 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
// PcsX
    public Double getPcsx() {
        return pcsx;
    }
    
    public void setPcsx(Double pcsx) {
        this.pcsx = pcsx;
    }
        
// PcsY
    public Double getPcsy() {
        return pcsy;
    }
    
    public void setPcsy(Double pcsy) {
        this.pcsy = pcsy;
    }
        
// SetX
    public Double getSetx() {
        return setx;
    }
    
    public void setSetx(Double setx) {
        this.setx = setx;
    }
        
// SetY
    public Double getSety() {
        return sety;
    }
    
    public void setSety(Double sety) {
        this.sety = sety;
    }
        
// Set2Pcs
    public Double getSet2pcs() {
        return set2pcs;
    }
    
    public void setSet2pcs(Double set2pcs) {
        this.set2pcs = set2pcs;
    }
        
// PnlX
    public Double getPnlx() {
        return pnlx;
    }
    
    public void setPnlx(Double pnlx) {
        this.pnlx = pnlx;
    }
        
// PnlY
    public Double getPnly() {
        return pnly;
    }
    
    public void setPnly(Double pnly) {
        this.pnly = pnly;
    }
        
// Pnl2Pcs
    public Double getPnl2pcs() {
        return pnl2pcs;
    }
    
    public void setPnl2pcs(Double pnl2pcs) {
        this.pnl2pcs = pnl2pcs;
    }
        
// 期初数量
    public Double getOpenqty() {
        return openqty;
    }
    
    public void setOpenqty(Double openqty) {
        this.openqty = openqty;
    }
        
// 期初金额
    public Double getOpenamount() {
        return openamount;
    }
    
    public void setOpenamount(Double openamount) {
        this.openamount = openamount;
    }
        
// 入账数量
    public Double getInqty() {
        return inqty;
    }
    
    public void setInqty(Double inqty) {
        this.inqty = inqty;
    }
        
// 入账金额
    public Double getInamount() {
        return inamount;
    }
    
    public void setInamount(Double inamount) {
        this.inamount = inamount;
    }
        
// 出账数量
    public Double getOutqty() {
        return outqty;
    }
    
    public void setOutqty(Double outqty) {
        this.outqty = outqty;
    }
        
// 出账金额
    public Double getOutamount() {
        return outamount;
    }
    
    public void setOutamount(Double outamount) {
        this.outamount = outamount;
    }
        
// 期末数量
    public Double getCloseqty() {
        return closeqty;
    }
    
    public void setCloseqty(Double closeqty) {
        this.closeqty = closeqty;
    }
        
// 期末金额
    public Double getCloseamount() {
        return closeamount;
    }
    
    public void setCloseamount(Double closeamount) {
        this.closeamount = closeamount;
    }
        
// Skuid(备用)
    public String getSkuid() {
        return skuid;
    }
    
    public void setSkuid(String skuid) {
        this.skuid = skuid;
    }
        
// 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
// 不结转
    public Integer getClosemark() {
        return closemark;
    }
    
    public void setClosemark(Integer closemark) {
        this.closemark = closemark;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

