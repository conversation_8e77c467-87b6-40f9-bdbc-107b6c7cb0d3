package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.service.AppWorkgroupService;
import inks.service.std.sale.service.BusAccountService;
import inks.service.std.sale.service.BusAccountitemService;
import inks.service.std.sale.service.BusAccountrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 销售账单(Bus_Account)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-03 14:28:09
 */
@RestController
@RequestMapping("D01M12B1")
@Api(tags = "D01M12B1:销售账单")
public class D01M12B1Controller extends BusAccountController {
    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;
    /**
     * 服务对象
     */
    @Resource
    private BusAccountService busAccountService;

    /**
     * 服务对象Item
     */
    @Resource
    private BusAccountitemService busAccountitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 服务对象
     */
    @Resource
    private BusAccountrecService busAccountrecService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private UtilsFeignService utilsFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取指定客户销售账单详细信息", notes = "获取指定客户销售账单详细信息,key=Groupid", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByNew", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<BusAccountPojo> getBillEntityByNew(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusAccountPojo busAccountPojo = new BusAccountPojo();
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            AppWorkgroupPojo wgPojo = this.appWorkgroupService.getEntity(key, loginUser.getTenantid());
            busAccountPojo.setTenantid(loginUser.getTenantid());
            busAccountPojo.setGroupid(key);
            busAccountPojo.setGroupname(wgPojo.getGroupname());
            busAccountPojo.setGroupuid(wgPojo.getGroupuid());
            if (busAccountrecPojo == null) {
                busAccountPojo.setStartdate(DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", new Date())));
            } else {
                busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            }
            busAccountPojo.setEnddate(new Date());
            List<BusAccountitemPojo> lst = this.busAccountService.pullItemList(busAccountPojo);
            busAccountPojo.setItem(lst);
            return R.ok(busAccountPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = "拉取销售账单item", notes = "根据主表信息拉取销售账单item,传账单主表", produces = "application/json")
    @RequestMapping(value = "/pullItemList", method = RequestMethod.POST)
    public R<List<BusAccountitemPojo>> pullItemList(@RequestBody String json) {
        try {
            BusAccountPojo busAccountPojo = JSONArray.parseObject(json, BusAccountPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busAccountService.pullItemList(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<PageInfo<BusAccountPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Account.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Account.groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按年月分页查询", notes = "按年月分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageThByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<PageInfo<BusAccountPojo>> getPageThByMonth(@RequestBody String json, Integer year, Integer month) {
        try {
            if (year == null || month == null) return R.fail("请传year和month参数");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Account.RowNum");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Bus_Account.carryyear=" + year + " and Bus_Account.carrymonth=" + month);
            return R.ok(this.busAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInitPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<PageInfo<BusAccountPojo>> getInitPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Account.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Bus_Account.BillType='期初建账'");
            return R.ok(this.busAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = "初期化全部销售账单", notes = "根据主表信息，初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/batchInit", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<Integer> batchInit(@RequestBody String json) {
        try {
            BusAccountPojo busAccountPojo = JSONArray.parseObject(json, BusAccountPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            R r = systemFeignService.getBillCode("D01M12B1", loginUser.getToken());
            if (r.getCode() == 200)
                busAccountPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            busAccountPojo.setBilltype("期初建账");
            busAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountPojo.setCreatedate(new Date());   // 创建时间
            busAccountPojo.setLister(loginUser.getRealname());   // 制表
            busAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountPojo.setModifydate(new Date());   //修改时间
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busAccountService.batchInit(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取指定客户结转记录最新记录", notes = "获取指定客户结转记录最新记录, key为 Groupid", produces = "application/json")
    @RequestMapping(value = "/getMaxEntityByGroup", method = RequestMethod.GET)
    public R<BusAccountPojo> getMaxEntityByGroup(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busAccountService.getMaxEntityByGroup(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "获取指定客户结转记录最新记录", notes = "获取指定客户结转记录最新记录, key为 Groupid", produces = "application/json")
    @RequestMapping(value = "/getMaxBillEntityByGroup", method = RequestMethod.GET)
    public R<BusAccountPojo> getMaxBillEntityByGroup(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busAccountService.getMaxBillEntityByGroup(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = "跨期拉取销售对账item", notes = "跨期拉取销售对账item,包含已对账和未对账", produces = "application/json")
    @RequestMapping(value = "/getSaleAccountList", method = RequestMethod.POST)
    public R<List<BusAccountitemPojo>> getSaleAccountList(@RequestBody String json, String groupid) {
        try {
            List<BusAccountitemPojo> lstRt = new ArrayList<>();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            Date _endDate = queryParam.getDateRange().getEndDate();
            // 当前结算到哪一时间
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (busAccountrecPojo != null) {
                queryParam.getDateRange().setEndDate(busAccountrecPojo.getEnddate());
                queryParam.setFilterstr(" and Bus_Account.Groupid='" + groupid + "'");
                List<BusAccountitemPojo> lstAccoitem = this.busAccountService.getMultItemList(queryParam);
                if (lstAccoitem != null) {
                    for (BusAccountitemPojo item : lstAccoitem) {
                        BusAccountitemPojo rtitem = new BusAccountitemPojo();
                        BeanUtils.copyProperties(item, rtitem);
                        // if (i>0) rtitem.setOpenamount(0D);  //去掉中间的期初；
                        lstRt.add(rtitem);
                    }
                }
            }

            BusAccountPojo busAccountPojo = new BusAccountPojo();
            busAccountPojo.setTenantid(loginUser.getTenantid());
            busAccountPojo.setGroupid(groupid);
            busAccountPojo.setStartdate(new Date());
            if (busAccountrecPojo != null) {
                busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            }
            busAccountPojo.setEnddate(_endDate);
            List<BusAccountitemPojo> lstpullItem = this.busAccountService.pullItemList(busAccountPojo);

            if (!lstRt.isEmpty()) {
                if (lstpullItem != null) {
                    // lstRt.get(lstRt.size()-1).setCloseamount(0D);  //去掉中间的期末；
                    for (BusAccountitemPojo item : lstpullItem) {
                        BusAccountitemPojo rtitem = new BusAccountitemPojo();
                        BeanUtils.copyProperties(item, rtitem);
                        // if (i==0) rtitem.setOpenamount(0D); //去掉中间的期初；
                        lstRt.add(rtitem);
                    }
                }
                return R.ok(lstRt);
            } else {
                return R.ok(lstpullItem);
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取客户实时应收款报表", notes = "获取客户实时应付款报表", produces = "application/json")
    @RequestMapping(value = "/getNowPageList", method = RequestMethod.POST)
    public R<PageInfo<BusAccountPojo>> getNowPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("GroupUid");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            Date startDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy", new Date()) + "-01-01");
            Date endDate = new Date();
            if (busAccountrecPojo != null) {
                startDate = DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1);
            }
            DateRange dateRange = new DateRange("", startDate, endDate);
            queryParam.setDateRange(dateRange);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busAccountService.getNowPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印列表", notes = "打印列表", produces = "application/json")
    @RequestMapping(value = "/printList", method = RequestMethod.POST)
    public void printList(@RequestBody String json, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取列表信息
        BusAccountPojo busAccountPojo = JSONObject.parseObject(json, BusAccountPojo.class);
        //表头转MAP(空)
        Map<String, Object> map = new HashMap<>();
        map.put("groupname", busAccountPojo.getGroupname());
        map.put("startdate", busAccountPojo.getStartdate());
        map.put("enddate", busAccountPojo.getEnddate());
        //lst转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(busAccountPojo.getItem());
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "分页云打印销售账单明细报表", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebList", method = RequestMethod.POST)
    public R<String> printWebList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            //获取列表信息
            BusAccountPojo busAccountPojo = JSONObject.parseObject(json, BusAccountPojo.class);
            //表头转MAP(空)
            Map<String, Object> map = new HashMap<>();
            map.put("groupname", busAccountPojo.getGroupname());
            map.put("startdate", busAccountPojo.getStartdate());
            map.put("enddate", busAccountPojo.getEnddate());

            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen ********
            List<Map<String, Object>> lst = attrListToMaps(busAccountPojo.getItem());

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");

            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "对账明细");   // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "分页云打印销售账单明细报表", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());


            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Account.BillDate");

            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Account.Groupid='" + groupid + "'";
            }

            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusAccountitemdetailPojo> lstitem = this.busAccountService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen ********
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");

            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "对账明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));   // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
