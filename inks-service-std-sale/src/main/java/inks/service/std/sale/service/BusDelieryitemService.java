package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDelieryitemPojo;

import java.util.List;
/**
 * 发货明细(BusDelieryitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 10:00:03
 */
public interface BusDelieryitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDelieryitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDelieryitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusDelieryitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busDelieryitemPojo 实例对象
     * @return 实例对象
     */
    BusDelieryitemPojo insert(BusDelieryitemPojo busDelieryitemPojo);

    /**
     * 修改数据
     *
     * @param busDelieryitempojo 实例对象
     * @return 实例对象
     */
    BusDelieryitemPojo update(BusDelieryitemPojo busDelieryitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busDelieryitempojo 实例对象
     * @return 实例对象
     */
    BusDelieryitemPojo clearNull(BusDelieryitemPojo busDelieryitempojo);
}
