//package inks.service.std.sale.service.impl;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.rabbitmq.client.Channel;
//import inks.service.std.sale.service.AppWorkgroupService;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.rabbit.annotation.Queue;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//
///**
// * <AUTHOR>
// * @date 2023年02月23日 14:33
// */
////用于接收MQ消息
//@Service
//public class AppWorkgroupServiceMq {
//    @Resource
//    private AppWorkgroupService appWorkgroupService;
//    int i = 0;
//
//    //MQ消息监听 队列名：updateWorkgroupAmt 用于更新客户余额
//    @RabbitListener(queuesToDeclare = @Queue(value = "updateWorkgroupAmt"))
//    public void process(String msg, Channel channel, Message message) throws IOException {
//        try {
//        JSONObject jsonObject = JSONArray.parseObject(msg);
//        //key为客户id
//        String key = jsonObject.getString("key");
//        String tid = jsonObject.getString("tid");
//        //更新类型
//        String type = jsonObject.getString("type");
//        if (StringUtils.isNotBlank(type)) {
//            switch (type) {
//                //刷新销售订单结余额
//                case "BusMachRemAmt":
//                    this.appWorkgroupService.updateWorkgroupBusMachRemAmt(key,tid);
//                    break;
//                //刷新销售发货结余额
//                case "BusDeliRemAmt":
//                    this.appWorkgroupService.updateWorkgroupBusDeliRemAmt(key,tid);
//                    break;
//                //刷新销售发票结余额
//                case "BusInvoRemAmt":
//                    this.appWorkgroupService.updateWorkgroupBusInvoRemAmt(key,tid);
//                    break;
//                //刷新销售结转期末额
//                case "BusAccoCloseAmt":
//                    this.appWorkgroupService.updateWorkgroupBusAccoCloseAmt(key,tid);
//                    break;
//                //刷新销售结转本期额
//                case "BusAccoNowAmt":
//                    this.appWorkgroupService.updateWorkgroupBusAccoNowAmt(key,tid);
//                    break;
//                //刷新采购订单结余额
//                case "BuyOrderRemAmt":
//                    this.appWorkgroupService.updateWorkgroupBuyOrderRemAmt(key,tid);
//                    break;
//                //刷新采购收货结余额
//                case "BuyFiniRemAmt":
//                    this.appWorkgroupService.updateWorkgroupBuyFiniRemAmt(key,tid);
//                    break;
//                //刷新采购发票结余额
//                case "BuyInvoRemAmt":
//                    this.appWorkgroupService.updateWorkgroupBuyInvoRemAmt(key,tid);
//                    break;
//                //刷新采购结转期末额
//                case "BuyAccoCloseAmt":
//                    this.appWorkgroupService.updateWorkgroupBuyAccoCloseAmt(key,tid);
//                    break;
//                //刷新采购结转本期额
//                case "BuyAccoNowAmt":
//                    this.appWorkgroupService.updateWorkgroupBuyAccoNowAmt(key,tid);
//                    break;
//                default:
//                    System.out.println("非法type");
//                    break;
//            }
//        }
//    } catch (Exception e) {
//        System.out.println( "\u001B[31m============catch (Exception e)错误生产者消息为" + msg + "\u001B[0m");
//        e.printStackTrace();
//    }
//        try {
//            i++;
//            LocalDateTime now = LocalDateTime.now();
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
//            String formattedNow = now.format(formatter);
//            // 输出黄色的文本
//            System.out.println("\u001B[33m============channel.basicAck消费第"+i+"条消息:"+msg+"\u001B[0m");
//            System.out.println("\u001B[36m============当前时间：" + formattedNow +"\u001B[0m");
//            //告诉MQ服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
//            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//
//
//
//
//}
