package inks.service.std.sale.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 预算项目(BusBudgetitem)Entity
 *
 * <AUTHOR>
 * @since 2023-08-02 09:27:23
 */
public class BusBudgetitemEntity implements Serializable {
    private static final long serialVersionUID = 767082381484024850L;
          // ID
         private String id;
          // 制令单号
         private String pid;
          // 商品ID
         private String goodsid;
          // 产品编码
         private String itemcode;
          // 产品名称
         private String itemname;
          // 产品规格
         private String itemspec;
          // 产品单位
         private String itemunit;
          // 数量
         private Double quantity;
          // 标准销价
         private Double stdprice;
          // 标准金额
         private Double stdamount;
          // 折扣
         private Double rebate;
          // 含税单价
         private Double taxprice;
          // 含税金额
         private Double taxamount;
          // 税率(备用)
         private Integer itemtaxrate;
          // 税额
         private Double taxtotal;
          // 未税单价
         private Double price;
          // 未税金额
         private Double amount;
          // 属性Josn
         private String attributejson;
          // 原始交期
         private Date itemorgdate;
          // 评审交期
         private Date itemplandate;
          // 库存发货
         private Double stoqty;
          // 生产需求
         private Double wkqty;
          // 行号
         private Integer rownum;
          // 虚拟货品
         private Integer virtualitem;
          // 最大允收
         private Double maxqty;
          // 备注
         private String remark;
          // 销售单号
         private String machuid;
          // 销售子项id
         private String machitemid;
          // 材料预算
         private Double matcost;
          // 人工预算
         private Double laborcost;
          // 直接费用预算
         private Double directcost;
          // 间接费用预算
         private Double indirectcost;
          // 材料Item
         private String matitemjson;
          // 人工Item
         private String laboritemjson;
          // 直接Item
         private String directitemjson;
          // 间接Item
         private String indirectitemjson;
          // 材料费用
         private Double matamt;
          // 人工费用
         private Double laboramt;
          // 直接费用
         private Double directamt;
          // 间接费用
         private Double indirectamt;
          // 来源:0=其他1=销售订单
         private Integer sourcetype;
          // 附件记数
         private Integer attacount;
          // 作废
         private Integer disannulmark;
          // 作废经办id
         private String disannullisterid;
          // 作废经办
         private String disannullister;
          // 作废日期
         private Date disannuldate;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 自定义5
         private String custom5;
          // 自定义6
         private String custom6;
          // 自定义7
         private String custom7;
          // 自定义8
         private String custom8;
          // 自定义9
         private String custom9;
          // 自定义10
         private String custom10;
          // 租户id
         private String tenantid;
          // 乐观锁
         private Integer revision;

    // ID
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // 制令单号
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 商品ID
      public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
    // 产品编码
      public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
    // 产品名称
      public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
    // 产品规格
      public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
    // 产品单位
      public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
    // 数量
      public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
    // 标准销价
      public Double getStdprice() {
        return stdprice;
    }
    
    public void setStdprice(Double stdprice) {
        this.stdprice = stdprice;
    }
        
    // 标准金额
      public Double getStdamount() {
        return stdamount;
    }
    
    public void setStdamount(Double stdamount) {
        this.stdamount = stdamount;
    }
        
    // 折扣
      public Double getRebate() {
        return rebate;
    }
    
    public void setRebate(Double rebate) {
        this.rebate = rebate;
    }
        
    // 含税单价
      public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
    // 含税金额
      public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
    // 税率(备用)
      public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
    // 税额
      public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
    // 未税单价
      public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
    // 未税金额
      public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
    // 属性Josn
      public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
    // 原始交期
      public Date getItemorgdate() {
        return itemorgdate;
    }
    
    public void setItemorgdate(Date itemorgdate) {
        this.itemorgdate = itemorgdate;
    }
        
    // 评审交期
      public Date getItemplandate() {
        return itemplandate;
    }
    
    public void setItemplandate(Date itemplandate) {
        this.itemplandate = itemplandate;
    }
        
    // 库存发货
      public Double getStoqty() {
        return stoqty;
    }
    
    public void setStoqty(Double stoqty) {
        this.stoqty = stoqty;
    }
        
    // 生产需求
      public Double getWkqty() {
        return wkqty;
    }
    
    public void setWkqty(Double wkqty) {
        this.wkqty = wkqty;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 虚拟货品
      public Integer getVirtualitem() {
        return virtualitem;
    }
    
    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }
        
    // 最大允收
      public Double getMaxqty() {
        return maxqty;
    }
    
    public void setMaxqty(Double maxqty) {
        this.maxqty = maxqty;
    }
        
    // 备注
      public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    // 销售单号
      public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
    // 销售子项id
      public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
    // 材料预算
      public Double getMatcost() {
        return matcost;
    }
    
    public void setMatcost(Double matcost) {
        this.matcost = matcost;
    }
        
    // 人工预算
      public Double getLaborcost() {
        return laborcost;
    }
    
    public void setLaborcost(Double laborcost) {
        this.laborcost = laborcost;
    }
        
    // 直接费用预算
      public Double getDirectcost() {
        return directcost;
    }
    
    public void setDirectcost(Double directcost) {
        this.directcost = directcost;
    }
        
    // 间接费用预算
      public Double getIndirectcost() {
        return indirectcost;
    }
    
    public void setIndirectcost(Double indirectcost) {
        this.indirectcost = indirectcost;
    }
        
    // 材料Item
      public String getMatitemjson() {
        return matitemjson;
    }
    
    public void setMatitemjson(String matitemjson) {
        this.matitemjson = matitemjson;
    }
        
    // 人工Item
      public String getLaboritemjson() {
        return laboritemjson;
    }
    
    public void setLaboritemjson(String laboritemjson) {
        this.laboritemjson = laboritemjson;
    }
        
    // 直接Item
      public String getDirectitemjson() {
        return directitemjson;
    }
    
    public void setDirectitemjson(String directitemjson) {
        this.directitemjson = directitemjson;
    }
        
    // 间接Item
      public String getIndirectitemjson() {
        return indirectitemjson;
    }
    
    public void setIndirectitemjson(String indirectitemjson) {
        this.indirectitemjson = indirectitemjson;
    }
        
    // 材料费用
      public Double getMatamt() {
        return matamt;
    }
    
    public void setMatamt(Double matamt) {
        this.matamt = matamt;
    }
        
    // 人工费用
      public Double getLaboramt() {
        return laboramt;
    }
    
    public void setLaboramt(Double laboramt) {
        this.laboramt = laboramt;
    }
        
    // 直接费用
      public Double getDirectamt() {
        return directamt;
    }
    
    public void setDirectamt(Double directamt) {
        this.directamt = directamt;
    }
        
    // 间接费用
      public Double getIndirectamt() {
        return indirectamt;
    }
    
    public void setIndirectamt(Double indirectamt) {
        this.indirectamt = indirectamt;
    }
        
    // 来源:0=其他1=销售订单
      public Integer getSourcetype() {
        return sourcetype;
    }
    
    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
        
    // 附件记数
      public Integer getAttacount() {
        return attacount;
    }
    
    public void setAttacount(Integer attacount) {
        this.attacount = attacount;
    }
        
    // 作废
      public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
    // 作废经办id
      public String getDisannullisterid() {
        return disannullisterid;
    }
    
    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }
        
    // 作废经办
      public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
    // 作废日期
      public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
    // 自定义1
      public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
    // 自定义2
      public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
    // 自定义3
      public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
    // 自定义4
      public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
    // 自定义5
      public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
    // 自定义6
      public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
    // 自定义7
      public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
    // 自定义8
      public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
    // 自定义9
      public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
    // 自定义10
      public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
    // 租户id
      public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

