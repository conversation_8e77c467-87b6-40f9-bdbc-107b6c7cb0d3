package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusMachiningitemPojo;

import java.util.List;
/**
 * 订单项目(BusMachiningitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-22 19:31:12
 */
public interface BusMachiningitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusMachiningitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusMachiningitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusMachiningitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busMachiningitemPojo 实例对象
     * @return 实例对象
     */
    BusMachiningitemPojo insert(BusMachiningitemPojo busMachiningitemPojo);

    /**
     * 修改数据
     *
     * @param busMachiningitempojo 实例对象
     * @return 实例对象
     */
    BusMachiningitemPojo update(BusMachiningitemPojo busMachiningitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busMachiningitempojo 实例对象
     * @return 实例对象
     */
    BusMachiningitemPojo clearNull(BusMachiningitemPojo busMachiningitempojo);


}
