package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusDepositPojo;
import inks.service.std.sale.domain.pojo.BusDepositcashPojo;
import inks.service.std.sale.domain.pojo.BusDepositcashdetailPojo;
import inks.service.std.sale.domain.pojo.BusDeposititemPojo;
import inks.service.std.sale.service.BusDepositService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.function.Consumer;

/**
 * 预收款(Bus_Deposit)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:31:24
 */
@RestController
@RequestMapping("D01M08B1DEP")
@Api(tags = "D01M08B1DEP:预收款")
public class D01M08B1DEPController extends BusDepositController {
    /**
     * 服务对象
     */
    @Resource
    private BusDepositService busDepositService;
    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deposit.List")
    public R<PageInfo<BusDepositPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Bus_Deposit.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Deposit.OutAmount<Bus_Deposit.BillAmount";
            if (groupid != null) {
                qpfilter += " and Bus_Deposit.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDepositService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDocPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deposit.List")
    public R<PageInfo<BusDepositPojo>> getOnlineDocPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Bus_Deposit.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Deposit.FmDocMark != 1";
            if (groupid != null) {
                qpfilter += " and Bus_Deposit.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDepositService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getCashPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deposit.List")
    public R<PageInfo<BusDepositcashdetailPojo>> getCashPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Bus_Deposit.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busDepositService.getCashPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //D01/M08B1DEP加入一键移转预收,
    //目的：客户退货后不补货，订单预收金额溢出，将多余的金额转到其他预收，销售订单关闭；
    //接口名： taransferMachDepo(machid,amt,groupid,出纳账户)
    //事件：生成两张单据，一张是销售预收的负数，一张为其他预收正数；
    @ApiOperation(value = " 一键移转预收,目的：客户退货后不补货，订单预收金额溢出，将多余的金额转到其他预收，销售订单关闭；事件：生成两张单据，一张是销售预收的负数，一张为其他预收正数；", notes = "", produces = "application/json")
    @RequestMapping(value = "/taransferMachDepo", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deposit.Add")
    @Transactional
    public R<BusDepositPojo> taransferMachDepo(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        try {
            JSONObject obj = JSONObject.parseObject(json);
            // 本次预收金额
            double amount = obj.getDoubleValue("amount");
            String groupid = obj.getString("groupid");
            // 出纳账号
            String cashaccid = obj.getString("cashaccid");
            String cashaccname = obj.getString("cashaccname");
            // 销售订单
            String machbillcode = obj.getString("machbillcode");
            String machbillid = obj.getString("machbillid");
            double billtaxamount = obj.getDoubleValue("billtaxamount");

            Date now = new Date();
            String realname = loginUser.getRealname();
            String userid = loginUser.getUserid();
            String title = "【" + machbillcode + "】销售订单转入";

            // 公共字段封装
            Consumer<BusDepositPojo> commonSetter = d -> {
                d.setGroupid(groupid);
                d.setCreateby(realname);
                d.setCreatebyid(userid);
                d.setCreatedate(now);
                d.setLister(realname);
                d.setListerid(userid);
                d.setModifydate(now);
                d.setTenantid(tid);
                d.setBilltitle(title);
            };

            // ---------- 销售预收（负数） ----------
            BusDepositPojo dep1 = new BusDepositPojo();
            // 生成单据编码RefNoUtils
            String moduleCode = "D01M08B1DEP";
            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Deposit", null, tid);
            dep1.setRefno(refno);
            dep1.setBilltype("销售预收");
            dep1.setOutamount(-amount);
            commonSetter.accept(dep1);// 公共字段赋值给dep1

            BusDepositcashPojo cash1 = new BusDepositcashPojo();
            cash1.setCashaccid(cashaccid);
            cash1.setCashaccname(cashaccname);
            cash1.setAmount(-amount);
            dep1.setCash(Collections.singletonList(cash1));

            BusDeposititemPojo item1 = new BusDeposititemPojo();
            item1.setMachbillid(machbillid);
            item1.setMachbillcode(machbillcode);
            item1.setBilltaxamount(billtaxamount);
            item1.setAmount(-amount);
            dep1.setItem(Collections.singletonList(item1));

            busDepositService.insert(dep1, tid);

            // ---------- 其他预收（正数） ----------
            BusDepositPojo dep2 = new BusDepositPojo();
            String refno2 = RefNoUtils.generateRefNo(moduleCode, "Bus_Deposit", null, tid);
            dep2.setRefno(refno2);
            dep2.setBilltype("其他预收");
            dep2.setOutamount(amount);
            commonSetter.accept(dep2);

            BusDepositcashPojo cash2 = new BusDepositcashPojo();
            cash2.setCashaccid(cashaccid);
            cash2.setCashaccname(cashaccname);
            cash2.setAmount(amount);
            dep2.setCash(Collections.singletonList(cash2));

            busDepositService.insert(dep2, tid);

            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
