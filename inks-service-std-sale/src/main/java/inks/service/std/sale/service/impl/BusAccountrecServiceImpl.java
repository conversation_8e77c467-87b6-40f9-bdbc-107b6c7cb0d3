package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusAccountrecEntity;
import inks.service.std.sale.domain.pojo.BusAccountrecPojo;
import inks.service.std.sale.mapper.BusAccountMapper;
import inks.service.std.sale.mapper.BusAccountrecMapper;
import inks.service.std.sale.mapper.BusCarryoverMapper;
import inks.service.std.sale.service.BusAccountrecService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 结转记录(BusAccountrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-25 08:28:53
 */
@Service("busAccountrecService")
public class BusAccountrecServiceImpl implements BusAccountrecService {
    @Resource
    private BusAccountrecMapper busAccountrecMapper;

    @Resource
    private BusAccountMapper busAccountMapper;

    @Resource
    private BusCarryoverMapper busCarryoverMapper;
    private final static Logger logger = LoggerFactory.getLogger(BusAccountrecServiceImpl.class);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountrecPojo getEntity(String key, String tid) {
        return this.busAccountrecMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountrecPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountrecPojo> lst = busAccountrecMapper.getPageList(queryParam);
            PageInfo<BusAccountrecPojo> pageInfo = new PageInfo<BusAccountrecPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busAccountrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountrecPojo insert(BusAccountrecPojo busAccountrecPojo) {
        //初始化NULL字段
        if (busAccountrecPojo.getCarryyear() == null) busAccountrecPojo.setCarryyear(0);
        if (busAccountrecPojo.getCarrymonth() == null) busAccountrecPojo.setCarrymonth(0);
        if (busAccountrecPojo.getStartdate() == null) busAccountrecPojo.setStartdate(new Date());
        if (busAccountrecPojo.getEnddate() == null) busAccountrecPojo.setEnddate(new Date());
        if (busAccountrecPojo.getOperator() == null) busAccountrecPojo.setOperator("");
        if (busAccountrecPojo.getOperatorid() == null) busAccountrecPojo.setOperatorid("");
        if (busAccountrecPojo.getRownum() == null) busAccountrecPojo.setRownum(0);
        if (busAccountrecPojo.getRemark() == null) busAccountrecPojo.setRemark("");
        if (busAccountrecPojo.getCreateby() == null) busAccountrecPojo.setCreateby("");
        if (busAccountrecPojo.getCreatebyid() == null) busAccountrecPojo.setCreatebyid("");
        if (busAccountrecPojo.getCreatedate() == null) busAccountrecPojo.setCreatedate(new Date());
        if (busAccountrecPojo.getLister() == null) busAccountrecPojo.setLister("");
        if (busAccountrecPojo.getListerid() == null) busAccountrecPojo.setListerid("");
        if (busAccountrecPojo.getModifydate() == null) busAccountrecPojo.setModifydate(new Date());
        if (busAccountrecPojo.getBillopenamount() == null) busAccountrecPojo.setBillopenamount(0D);
        if (busAccountrecPojo.getBillinamount() == null) busAccountrecPojo.setBillinamount(0D);
        if (busAccountrecPojo.getBilloutamount() == null) busAccountrecPojo.setBilloutamount(0D);
        if (busAccountrecPojo.getBillcloseamount() == null) busAccountrecPojo.setBillcloseamount(0D);
        if (busAccountrecPojo.getInvoopenamount() == null) busAccountrecPojo.setInvoopenamount(0D);
        if (busAccountrecPojo.getInvoinamount() == null) busAccountrecPojo.setInvoinamount(0D);
        if (busAccountrecPojo.getInvooutamount() == null) busAccountrecPojo.setInvooutamount(0D);
        if (busAccountrecPojo.getInvocloseamount() == null) busAccountrecPojo.setInvocloseamount(0D);
        if (busAccountrecPojo.getArapopenamount() == null) busAccountrecPojo.setArapopenamount(0D);
        if (busAccountrecPojo.getArapinamount() == null) busAccountrecPojo.setArapinamount(0D);
        if (busAccountrecPojo.getArapoutamount() == null) busAccountrecPojo.setArapoutamount(0D);
        if (busAccountrecPojo.getArapcloseamount() == null) busAccountrecPojo.setArapcloseamount(0D);
        if (busAccountrecPojo.getDeliopenamount() == null) busAccountrecPojo.setDeliopenamount(0D);
        if (busAccountrecPojo.getDeliinamount() == null) busAccountrecPojo.setDeliinamount(0D);
        if (busAccountrecPojo.getDelioutamount() == null) busAccountrecPojo.setDelioutamount(0D);
        if (busAccountrecPojo.getDelicloseamount() == null) busAccountrecPojo.setDelicloseamount(0D);
        if (busAccountrecPojo.getPrintcount() == null) busAccountrecPojo.setPrintcount(0);
        if (busAccountrecPojo.getCustom1() == null) busAccountrecPojo.setCustom1("");
        if (busAccountrecPojo.getCustom2() == null) busAccountrecPojo.setCustom2("");
        if (busAccountrecPojo.getCustom3() == null) busAccountrecPojo.setCustom3("");
        if (busAccountrecPojo.getCustom4() == null) busAccountrecPojo.setCustom4("");
        if (busAccountrecPojo.getCustom5() == null) busAccountrecPojo.setCustom5("");
        if (busAccountrecPojo.getCustom6() == null) busAccountrecPojo.setCustom6("");
        if (busAccountrecPojo.getCustom7() == null) busAccountrecPojo.setCustom7("");
        if (busAccountrecPojo.getCustom8() == null) busAccountrecPojo.setCustom8("");
        if (busAccountrecPojo.getCustom9() == null) busAccountrecPojo.setCustom9("");
        if (busAccountrecPojo.getCustom10() == null) busAccountrecPojo.setCustom10("");
        if (busAccountrecPojo.getTenantid() == null) busAccountrecPojo.setTenantid("");
        if (busAccountrecPojo.getTenantname() == null) busAccountrecPojo.setTenantname("");
        if (busAccountrecPojo.getRevision() == null) busAccountrecPojo.setRevision(0);
        BusAccountrecEntity busAccountrecEntity = new BusAccountrecEntity();
        BeanUtils.copyProperties(busAccountrecPojo, busAccountrecEntity);

        busAccountrecEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busAccountrecEntity.setRevision(1);  //乐观锁
        this.busAccountrecMapper.insert(busAccountrecEntity);
        return this.getEntity(busAccountrecEntity.getId(), busAccountrecEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busAccountrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountrecPojo update(BusAccountrecPojo busAccountrecPojo) {
        BusAccountrecEntity busAccountrecEntity = new BusAccountrecEntity();
        BeanUtils.copyProperties(busAccountrecPojo, busAccountrecEntity);
        this.busAccountrecMapper.update(busAccountrecEntity);
        return this.getEntity(busAccountrecEntity.getId(), busAccountrecEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */


    @Override
    @Transactional
    public int delete(String key, String tid) {//key 是 BusAccountRec.id
        BusAccountrecPojo delPojo = this.busAccountrecMapper.getEntity(key, tid);

        int batchSize = 50; // 每次删除的数量
        int maxIterations = 1000; // 最大迭代次数，防止死循环
        int iterationCount = 0;

        int totalDeleted = 0;

        // 主表数据删除
        logger.info("开始删除 Bus_Account 数据，年份: {}, 月份: {}, 租户ID: {}", delPojo.getCarryyear(), delPojo.getCarrymonth(), tid);
        boolean hasMore = true;
        while (hasMore && iterationCount < maxIterations) {
            iterationCount++;
            List<String> accountIds = busAccountMapper.getAccountIdsForDeletion(delPojo.getCarryyear(), delPojo.getCarrymonth(), tid, batchSize);
            logger.info("本次要删除的 accountIds: {}", accountIds);

            if (CollectionUtils.isEmpty(accountIds)) {
                hasMore = false;
                break;
            }

            // 执行拆分后的删除操作
            int i1 = busAccountMapper.deleteBusAccountItemByMonth(accountIds, tid);
            int i2 = busAccountMapper.deleteBusAccountArapByMonth(accountIds, tid);
            int i3 = busAccountMapper.deleteBusAccountDeliByMonth(accountIds, tid);
            int i4 = busAccountMapper.deleteBusAccountInvoByMonth(accountIds, tid);
            int i5 = busAccountMapper.deleteBusAccountByMonth(accountIds, tid);

            totalDeleted += i1 + i2 + i3 + i4 + i5;

            logger.info("第 {} 次迭代删除了 Bus_Account 及相关表的数据，总共删除了 {} 行", iterationCount, totalDeleted);
        }

        if (iterationCount >= maxIterations) {
            throw new RuntimeException("删除 Bus_Account 数据时超过最大迭代次数，可能存在死循环。");
        }

        // 删除货品结转数据
        logger.info("开始删除货品结转数据，Recid: {}", key);


        // 执行拆分后的删除操作
        int j1 = busCarryoverMapper.deleteInvoCarryoverByRecid(key, tid);
        int j2 = busCarryoverMapper.deleteDeliCarryoverByRecid(key, tid);

        totalDeleted += j1 + j2;

        logger.info(" 一次性删除了Bus_InvoCarryover的数据，总共删除了 {} 行", j1);
        logger.info(" 一次性删除了Bus_DeliCarryover的数据，总共删除了 {} 行", j2);


        // 删除主记录
        int result = this.busAccountrecMapper.delete(key, tid);
        logger.info("最终删除完成。总共删除了 {} 行数据，主记录删除状态: {}", totalDeleted, result > 0 ? "成功" : "失败");

        return result;
    }


    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public BusAccountrecPojo getEntityByMax(String tid) {
        return this.busAccountrecMapper.getEntityByMax(tid);
    }

}
