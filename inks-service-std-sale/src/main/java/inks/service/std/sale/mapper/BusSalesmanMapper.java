package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusSalesmanEntity;
import inks.service.std.sale.domain.pojo.BusSalesmanPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务员信息表(BusSalesman)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-01 09:38:26
 */
@Mapper
public interface BusSalesmanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusSalesmanPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusSalesmanPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param busSalesmanEntity 实例对象
     * @return 影响行数
     */
    int insert(BusSalesmanEntity busSalesmanEntity);

    
    /**
     * 修改数据
     *
     * @param busSalesmanEntity 实例对象
     * @return 影响行数
     */
    int update(BusSalesmanEntity busSalesmanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

