package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDeliplanitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 发货计划明细(BusDeliplanitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:19
 */
public interface BusDeliplanitemService {

    BusDeliplanitemPojo getEntity(String key,String tid);

    PageInfo<BusDeliplanitemPojo> getPageList(QueryParam queryParam);

    List<BusDeliplanitemPojo> getList(String Pid,String tid);  

    BusDeliplanitemPojo insert(BusDeliplanitemPojo busDeliplanitemPojo);

    BusDeliplanitemPojo update(BusDeliplanitemPojo busDeliplanitempojo);

    int delete(String key,String tid);

    BusDeliplanitemPojo clearNull(BusDeliplanitemPojo busDeliplanitempojo);
}
