package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusChatterPojo;

/**
 * 销售客服(BusChatter)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-01 10:29:52
 */
public interface BusChatterService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusChatterPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusChatterPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busChatterPojo 实例对象
     * @return 实例对象
     */
    BusChatterPojo insert(BusChatterPojo busChatterPojo);

    /**
     * 修改数据
     *
     * @param busChatterpojo 实例对象
     * @return 实例对象
     */
    BusChatterPojo update(BusChatterPojo busChatterpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                                                                                      }
