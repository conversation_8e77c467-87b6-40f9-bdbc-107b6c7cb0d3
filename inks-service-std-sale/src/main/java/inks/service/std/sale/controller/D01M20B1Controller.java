package inks.service.std.sale.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.mapper.BusChatterMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 销售客服(Bus_Chatter)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-01 10:04:32
 */
@RestController
@RequestMapping("D01M20B1")
@Api(tags = "D01M20B1:销售客服")
public class D01M20B1Controller extends BusChatterController {
    @Resource
    private BusChatterMapper busChatterMapper;
    @Resource
    private TokenService tokenService;

    //是否是客服
    @ApiOperation(value = " 是否是销售客服", notes = "是否是销售客服", produces = "application/json")
    @RequestMapping(value = "/isChatter", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Chatter.List")
    public R<Boolean> isChatter() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            int chatter = this.busChatterMapper.isChatter(loginUser.getUserid(), loginUser.getTenantid());
            return R.ok(chatter > 0);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
