package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusCostpartgroupEntity;
import inks.service.std.sale.domain.pojo.BusCostpartgroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组件分组(BusCostpartgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-07 13:29:47
 */
@Mapper
public interface BusCostpartgroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostpartgroupPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusCostpartgroupPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param busCostpartgroupEntity 实例对象
     * @return 影响行数
     */
    int insert(BusCostpartgroupEntity busCostpartgroupEntity);


    /**
     * 修改数据
     *
     * @param busCostpartgroupEntity 实例对象
     * @return 影响行数
     */
    int update(BusCostpartgroupEntity busCostpartgroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<BusCostpartgroupPojo> getList(@Param("tid") String tid);

}

