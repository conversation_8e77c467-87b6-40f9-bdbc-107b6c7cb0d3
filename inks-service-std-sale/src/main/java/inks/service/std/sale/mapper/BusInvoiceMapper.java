package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusInvoiceEntity;
import inks.service.std.sale.domain.pojo.BusInvoicePojo;
import inks.service.std.sale.domain.pojo.BusInvoiceitemPojo;
import inks.service.std.sale.domain.pojo.BusInvoiceitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 销售开票(BusInvoice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:30:28
 */
@Mapper
public interface BusInvoiceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusInvoicePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusInvoiceitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusInvoicePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busInvoiceEntity 实例对象
     * @return 影响行数
     */
    int insert(BusInvoiceEntity busInvoiceEntity);


    /**
     * 修改数据
     *
     * @param busInvoiceEntity 实例对象
     * @return 影响行数
     */
    int update(BusInvoiceEntity busInvoiceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busInvoicePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusInvoicePojo busInvoicePojo);

    /**
     * 修改数据
     *
     * @param busInvoiceEntity 实例对象
     * @return 影响行数
     */
    int approval(BusInvoiceEntity busInvoiceEntity);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeliInvoFinish(@Param("key") String key, @Param("tid") String tid);

    int updateMachItemInvoQty(@Param("key") String machitemid, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeliInvoCount(@Param("key") String key,  @Param("tid") String tid);

    int updateMachInvoCountInvoAmt(@Param("key") String machitemid, @Param("refno") String machuid, @Param("tid") String tid);
    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeduInvoFinish(@Param("key") String key,  @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeduInvoCount(@Param("key") String key, @Param("tid") String tid);

    // 查询Item是否被引用
    List<String> getCiteBillName(@Param("key") String key, @Param("tid") String tid);

    // 拉取某个客户所有待对账项目，送货+扣款
    List<BusInvoiceitemPojo> pullItem(@Param("startdate") Date startdate, @Param("enddate") Date enddate, @Param("groupid") String groupid, @Param("tid") String tid);


    void updateMachItemAvgInvoAmt(@Param("machid") String machid, @Param("tid") String tid);

    String getMachId(@Param("machitemid") String machitemid, @Param("tid") String tid);

    void updateDeliInvoFinishBatch(List<String> deliItemIds, String tid);

    void updateDeliInvoCountBatch(List<String> deliItemIds, String tid);

    void updateDeduInvoFinishBatch(List<String> deliItemIdInDedus, String tid);

    void updateDeduInvoCountBatch(List<String> deliItemIdInDedus, String tid);

    List<String> getMachIds(List<String> machitemids, String tid);

    void updateMachItemInvoQtyBatch(List<String> machitemids, String tid);

    void updateMachInvoCountInvoAmtBatch(List<String> machIds, String tid);

    void updateMachItemAvgInvoAmtBatch(List<String> machIds, String tid);

    //void updateMachInvoCountInvoAmt222(String machId, String tid);

}

