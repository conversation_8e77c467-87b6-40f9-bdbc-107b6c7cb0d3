package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusIntendedEntity;
import inks.service.std.sale.domain.BusIntendeditemEntity;
import inks.service.std.sale.domain.pojo.BusIntendedPojo;
import inks.service.std.sale.domain.pojo.BusIntendeditemPojo;
import inks.service.std.sale.domain.pojo.BusIntendeditemdetailPojo;
import inks.service.std.sale.mapper.BusIntendedMapper;
import inks.service.std.sale.mapper.BusIntendeditemMapper;
import inks.service.std.sale.service.BusIntendedService;
import inks.service.std.sale.service.BusIntendeditemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 意向订单(BusIntended)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 09:12:04
 */
@Service("busIntendedService")
public class BusIntendedServiceImpl implements BusIntendedService {
    @Resource
    private BusIntendedMapper busIntendedMapper;
    
    @Resource
    private BusIntendeditemMapper busIntendeditemMapper;
    
     /**
     * 服务对象Item
     */
    @Resource
    private BusIntendeditemService busIntendeditemService;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusIntendedPojo getEntity(String key, String tid) {
        return this.busIntendedMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusIntendeditemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusIntendeditemdetailPojo> lst = busIntendedMapper.getPageList(queryParam);
            PageInfo<BusIntendeditemdetailPojo> pageInfo = new PageInfo<BusIntendeditemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
     /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusIntendedPojo getBillEntity(String key, String tid) {
       try {
        //读取主表
        BusIntendedPojo busIntendedPojo = this.busIntendedMapper.getEntity(key,tid);
        //读取子表
        busIntendedPojo.setItem(busIntendeditemMapper.getList(busIntendedPojo.getId(),busIntendedPojo.getTenantid()));
        return busIntendedPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusIntendedPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusIntendedPojo> lst = busIntendedMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for (BusIntendedPojo busIntendedPojo : lst) {
                busIntendedPojo.setItem(busIntendeditemMapper.getList(busIntendedPojo.getId(), busIntendedPojo.getTenantid()));
            }
            PageInfo<BusIntendedPojo> pageInfo = new PageInfo<BusIntendedPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
       /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusIntendedPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusIntendedPojo> lst = busIntendedMapper.getPageTh(queryParam);
            PageInfo<BusIntendedPojo> pageInfo = new PageInfo<BusIntendedPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param busIntendedPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusIntendedPojo insert(BusIntendedPojo busIntendedPojo) {
        //初始化NULL字段
        cleanNull(busIntendedPojo);
         //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusIntendedEntity busIntendedEntity = new BusIntendedEntity(); 
        BeanUtils.copyProperties(busIntendedPojo,busIntendedEntity);
      
        //设置id和新建日期
        busIntendedEntity.setId(id);
        busIntendedEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busIntendedMapper.insert(busIntendedEntity);
        //Item子表处理
        List<BusIntendeditemPojo> lst = busIntendedPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for (BusIntendeditemPojo busIntendeditemPojo : lst) {
                //初始化item的NULL
                BusIntendeditemPojo itemPojo = this.busIntendeditemService.clearNull(busIntendeditemPojo);
                BusIntendeditemEntity busIntendeditemEntity = new BusIntendeditemEntity();
                BeanUtils.copyProperties(itemPojo, busIntendeditemEntity);
                //设置id和Pid
                busIntendeditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busIntendeditemEntity.setPid(id);
                busIntendeditemEntity.setTenantid(busIntendedPojo.getTenantid());
                busIntendeditemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busIntendeditemMapper.insert(busIntendeditemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(busIntendedEntity.getId(),busIntendedEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busIntendedPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusIntendedPojo update(BusIntendedPojo busIntendedPojo) {
        //主表更改
        BusIntendedEntity busIntendedEntity = new BusIntendedEntity(); 
        BeanUtils.copyProperties(busIntendedPojo,busIntendedEntity);
        this.busIntendedMapper.update(busIntendedEntity);
        if (busIntendedPojo.getItem() != null) {
        //Item子表处理
        List<BusIntendeditemPojo> lst = busIntendedPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =busIntendedMapper.getDelItemIds(busIntendedPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String lstDelId : lstDelIds) {
                this.busIntendeditemMapper.delete(lstDelId, busIntendedEntity.getTenantid());
            }
        }
        if (lst != null){
            //循环每个item子表
            for (BusIntendeditemPojo busIntendeditemPojo : lst) {
                BusIntendeditemEntity busIntendeditemEntity = new BusIntendeditemEntity();
                if ("".equals(busIntendeditemPojo.getId()) || busIntendeditemPojo.getId() == null) {
                    //初始化item的NULL
                    BusIntendeditemPojo itemPojo = this.busIntendeditemService.clearNull(busIntendeditemPojo);
                    BeanUtils.copyProperties(itemPojo, busIntendeditemEntity);
                    //设置id和Pid
                    busIntendeditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busIntendeditemEntity.setPid(busIntendedEntity.getId());  // 主表 id
                    busIntendeditemEntity.setTenantid(busIntendedPojo.getTenantid());   // 租户id
                    busIntendeditemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busIntendeditemMapper.insert(busIntendeditemEntity);
                } else {
                    BeanUtils.copyProperties(busIntendeditemPojo, busIntendeditemEntity);
                    busIntendeditemEntity.setTenantid(busIntendedPojo.getTenantid());
                    this.busIntendeditemMapper.update(busIntendeditemEntity);
                }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(busIntendedEntity.getId(),busIntendedEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
       BusIntendedPojo busIntendedPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<BusIntendeditemPojo> lst = busIntendedPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for (BusIntendeditemPojo busIntendeditemPojo : lst) {
                this.busIntendeditemMapper.delete(busIntendeditemPojo.getId(), tid);
            }
        }        
         this.busIntendedMapper.delete(key,tid) ;
        return busIntendedPojo.getRefno();
    }
    

    
                                                                                                                                                                               /**
     * 审核数据
     *
     * @param busIntendedPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusIntendedPojo approval(BusIntendedPojo busIntendedPojo) {
        //主表更改
        BusIntendedEntity busIntendedEntity = new BusIntendedEntity();
        BeanUtils.copyProperties(busIntendedPojo,busIntendedEntity);
        this.busIntendedMapper.approval(busIntendedEntity);
        //返回Bill实例
        return this.getBillEntity(busIntendedEntity.getId(),busIntendedEntity.getTenantid());
    }

    private static void cleanNull(BusIntendedPojo busIntendedPojo) {
        if(busIntendedPojo.getRefno()==null) busIntendedPojo.setRefno("");
        if(busIntendedPojo.getBilltype()==null) busIntendedPojo.setBilltype("");
        if(busIntendedPojo.getBilltitle()==null) busIntendedPojo.setBilltitle("");
        if(busIntendedPojo.getBilldate()==null) busIntendedPojo.setBilldate(new Date());
        if(busIntendedPojo.getProbability()==null) busIntendedPojo.setProbability("");
        if(busIntendedPojo.getGroupid()==null) busIntendedPojo.setGroupid("");
        if(busIntendedPojo.getTradercode()==null) busIntendedPojo.setTradercode("");
        if(busIntendedPojo.getTradername()==null) busIntendedPojo.setTradername("");
        if(busIntendedPojo.getCustaddress()==null) busIntendedPojo.setCustaddress("");
        if(busIntendedPojo.getLinkman()==null) busIntendedPojo.setLinkman("");
        if(busIntendedPojo.getTel()==null) busIntendedPojo.setTel("");
        if(busIntendedPojo.getFax()==null) busIntendedPojo.setFax("");
        if(busIntendedPojo.getPeriods()==null) busIntendedPojo.setPeriods("");
        if(busIntendedPojo.getValiditydate()==null) busIntendedPojo.setValiditydate("");
        if(busIntendedPojo.getExpiredate()==null) busIntendedPojo.setExpiredate(new Date());
        if(busIntendedPojo.getValiditydesc()==null) busIntendedPojo.setValiditydesc("");
        if(busIntendedPojo.getCurrency()==null) busIntendedPojo.setCurrency("");
        if(busIntendedPojo.getDelivery()==null) busIntendedPojo.setDelivery("");
        if(busIntendedPojo.getPayment()==null) busIntendedPojo.setPayment("");
        if(busIntendedPojo.getSeller()==null) busIntendedPojo.setSeller("");
        if(busIntendedPojo.getSummary()==null) busIntendedPojo.setSummary("");
        if(busIntendedPojo.getBillclause()==null) busIntendedPojo.setBillclause("");
        if(busIntendedPojo.getBilltaxamount()==null) busIntendedPojo.setBilltaxamount(0D);
        if(busIntendedPojo.getBillamount()==null) busIntendedPojo.setBillamount(0D);
        if(busIntendedPojo.getBilltaxtotal()==null) busIntendedPojo.setBilltaxtotal(0D);
        if(busIntendedPojo.getCreateby()==null) busIntendedPojo.setCreateby("");
        if(busIntendedPojo.getCreatebyid()==null) busIntendedPojo.setCreatebyid("");
        if(busIntendedPojo.getCreatedate()==null) busIntendedPojo.setCreatedate(new Date());
        if(busIntendedPojo.getLister()==null) busIntendedPojo.setLister("");
        if(busIntendedPojo.getListerid()==null) busIntendedPojo.setListerid("");
        if(busIntendedPojo.getModifydate()==null) busIntendedPojo.setModifydate(new Date());
        if(busIntendedPojo.getSubmitterid()==null) busIntendedPojo.setSubmitterid("");
        if(busIntendedPojo.getSubmitter()==null) busIntendedPojo.setSubmitter("");
        if(busIntendedPojo.getSubmitdate()==null) busIntendedPojo.setSubmitdate(new Date());
        if(busIntendedPojo.getAssessor()==null) busIntendedPojo.setAssessor("");
        if(busIntendedPojo.getAssessorid()==null) busIntendedPojo.setAssessorid("");
        if(busIntendedPojo.getAssessdate()==null) busIntendedPojo.setAssessdate(new Date());
        if(busIntendedPojo.getItemcount()==null) busIntendedPojo.setItemcount(0);
        if(busIntendedPojo.getFinishcount()==null) busIntendedPojo.setFinishcount(0);
        if(busIntendedPojo.getDisannulcount()==null) busIntendedPojo.setDisannulcount(0);
        if(busIntendedPojo.getPrintcount()==null) busIntendedPojo.setPrintcount(0);
        if(busIntendedPojo.getCustom1()==null) busIntendedPojo.setCustom1("");
        if(busIntendedPojo.getCustom2()==null) busIntendedPojo.setCustom2("");
        if(busIntendedPojo.getCustom3()==null) busIntendedPojo.setCustom3("");
        if(busIntendedPojo.getCustom4()==null) busIntendedPojo.setCustom4("");
        if(busIntendedPojo.getCustom5()==null) busIntendedPojo.setCustom5("");
        if(busIntendedPojo.getCustom6()==null) busIntendedPojo.setCustom6("");
        if(busIntendedPojo.getCustom7()==null) busIntendedPojo.setCustom7("");
        if(busIntendedPojo.getCustom8()==null) busIntendedPojo.setCustom8("");
        if(busIntendedPojo.getCustom9()==null) busIntendedPojo.setCustom9("");
        if(busIntendedPojo.getCustom10()==null) busIntendedPojo.setCustom10("");
        if(busIntendedPojo.getDeptid()==null) busIntendedPojo.setDeptid("");
        if(busIntendedPojo.getTenantid()==null) busIntendedPojo.setTenantid("");
        if(busIntendedPojo.getTenantname()==null) busIntendedPojo.setTenantname("");
        if(busIntendedPojo.getRevision()==null) busIntendedPojo.setRevision(0);
   }

}
