package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDmsspuPojo;

/**
 * Dms商品属性Key(BusDmsspu)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-27 19:12:25
 */
public interface BusDmsspuService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDmsspuPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDmsspuPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDmsspuPojo 实例对象
     * @return 实例对象
     */
    BusDmsspuPojo insert(BusDmsspuPojo busDmsspuPojo);

    /**
     * 修改数据
     *
     * @param busDmsspupojo 实例对象
     * @return 实例对象
     */
    BusDmsspuPojo update(BusDmsspuPojo busDmsspupojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                                                                                                     }
