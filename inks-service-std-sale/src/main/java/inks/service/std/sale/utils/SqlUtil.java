package inks.service.std.sale.utils;

import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.LoginUser;
import inks.common.security.config.InksConfigThreadLocal;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SqlUtil {


    /**
     * 部门权限过滤，拼接指定字段的 IN 子句
     *
     * @param qpfilter   原SQL过滤条件
     * @param loginUser  当前登录用户对象
     * @param columnName 需要过滤的表字段名（如 Bus_Machining.Deptid）
     * @return 拼接好部门权限后的过滤条件
     */
    public static String filterDeptid(String qpfilter, LoginUser loginUser, String columnName) {
        // 部门权限过滤：当不是超级管理员，且配置了销售订单部门权限过滤参数时，进行部门权限过滤
        if (!Objects.equals(loginUser.getIsadmin(), 1) && "true".equals(InksConfigThreadLocal.getConfig("module.sale.deptpartition"))) {
            // 收集当前部门 ID 和所有子部门 ID
            Set<String> deptIds = Stream.concat(
                    Optional.ofNullable(loginUser.getTenantinfo().getDeptid())
                            .filter(StringUtils::isNotBlank)
                            .map(Stream::of)
                            .orElseGet(Stream::empty),
                    Optional.ofNullable(loginUser.getTenantinfo().getLstdept())
                            .orElseGet(Collections::emptyList)
                            .stream()
                            .map(DeptinfoPojo::getDeptid)
                            .filter(StringUtils::isNotBlank)
            ).collect(Collectors.toSet());
            // 如果非空，则拼 IN 子句
            if (!deptIds.isEmpty()) {
                String inClause = deptIds.stream()
                        .map(id -> "'" + id + "'")
                        .collect(Collectors.joining(","));
                qpfilter += " and " + columnName + " in (" + inClause + ")";
            }
        }
        return qpfilter;
    }

}
