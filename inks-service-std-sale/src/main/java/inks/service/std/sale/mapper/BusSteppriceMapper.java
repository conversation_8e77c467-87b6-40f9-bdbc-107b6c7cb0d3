package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusSteppriceEntity;
import inks.service.std.sale.domain.pojo.BusSteppricePojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemPojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶梯单价(BusStepprice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-02 08:47:11
 */
@Mapper
public interface BusSteppriceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusSteppricePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusSteppriceitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusSteppricePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busSteppriceEntity 实例对象
     * @return 影响行数
     */
    int insert(BusSteppriceEntity busSteppriceEntity);


    /**
     * 修改数据
     *
     * @param busSteppriceEntity 实例对象
     * @return 影响行数
     */
    int update(BusSteppriceEntity busSteppriceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busSteppricePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusSteppricePojo busSteppricePojo);

    /**
     * 查询 被删除的Item
     *
     * @param busSteppricePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelObjIds(BusSteppricePojo busSteppricePojo);

    /**
     * 修改数据
     *
     * @param busSteppriceEntity 实例对象
     * @return 影响行数
     */
    int approval(BusSteppriceEntity busSteppriceEntity);

    /**
     * 根据客户查询售价
     *
     * @return 查询结果
     */
    BusSteppriceitemPojo getStepPriceByGroupid(@Param("goodsid") String goodsid, @Param("groupid") String groupid, @Param("qty") Double qty, @Param("tid") String tid);

    /**
     * 根据客户等级查询售价
     *
     * @return 查询结果
     */
    BusSteppriceitemPojo getStepPriceByGroupLevel(@Param("goodsid") String goodsid, @Param("grouplevel") String grouplevel, @Param("qty") Double qty, @Param("tid") String tid);
}

