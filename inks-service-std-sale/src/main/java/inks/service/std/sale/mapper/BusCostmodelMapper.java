package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusCostmodelEntity;
import inks.service.std.sale.domain.pojo.BusCostmodelPojo;
import inks.service.std.sale.domain.pojo.BusCostmodelitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预算模型(BusCostmodel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-07 13:30:12
 */
@Mapper
public interface BusCostmodelMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostmodelPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusCostmodelitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusCostmodelPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busCostmodelEntity 实例对象
     * @return 影响行数
     */
    int insert(BusCostmodelEntity busCostmodelEntity);


    /**
     * 修改数据
     *
     * @param busCostmodelEntity 实例对象
     * @return 影响行数
     */
    int update(BusCostmodelEntity busCostmodelEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busCostmodelPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusCostmodelPojo busCostmodelPojo);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    BusCostmodelPojo getDefEntity( @Param("tid") String tid);
}

