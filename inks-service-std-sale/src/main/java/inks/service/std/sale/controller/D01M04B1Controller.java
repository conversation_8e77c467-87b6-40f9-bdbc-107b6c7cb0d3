package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusQuotationPojo;
import inks.service.std.sale.domain.pojo.BusQuotationitemdetailPojo;
import inks.service.std.sale.service.BusQuotationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 报价管理(Bus_Quotation)表控制层
 *
 * <AUTHOR>
 * @since 2022-01-10 14:43:35
 */
@RestController
@RequestMapping("D01M04B1")
@Api(tags = "D01M04B1:标准报价")
public class D01M04B1Controller extends BusQuotationController {
    /**
     * 服务对象
     */
    @Resource
    private BusQuotationService busQuotationService;

    @Resource
    private TokenService tokenService;


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Quotation.List")
    public R<PageInfo<BusQuotationitemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Quotation.CreateDate");

            String qpfilter = "";
//            if (groupid != null) {
//                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
//            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busQuotationService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "OnlinePageTh按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Quotation.List")
    public R<PageInfo<BusQuotationPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Quotation.CreateDate");

            String qpfilter = "";
            qpfilter += "and Bus_Quotation.FinishCount<Bus_Quotation.ItemCount";
            if (StringUtils.isNotBlank(groupid)) {
                qpfilter += " and Bus_OrderCost.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.busQuotationService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "OnlinePageList按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Quotation.List")
    public R<PageInfo<BusQuotationitemdetailPojo>> getOnlinePageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Quotation.CreateDate");

            String qpfilter = "";
            qpfilter += "and Bus_QuotationItem.MachMark<>'1'";
            qpfilter += " and Bus_QuotationItem.DisannulMark=0 and Bus_QuotationItem.Closed=0 ";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busQuotationService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
