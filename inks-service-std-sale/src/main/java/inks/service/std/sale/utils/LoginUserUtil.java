package inks.service.std.sale.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import inks.api.feign.AuthFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.utils.SecurityUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.service.std.sale.mapper.D01MBIR1Mapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.apache.commons.lang3.StringUtils.*;

@Component
public class LoginUserUtil {

    @Autowired
    private TokenService tokenService;
    @Autowired
    private AuthFeignService authFeignService;

    @Autowired
    private D01MBIR1Mapper d01MBIR1Mapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 通过授权码获取LoginUser用户信息
    public LoginUser determineLoginUser(String auth, String dev) {
        // 首先判断是否有请求头中的authcode,如果有,则直接走getLoginUser()方法
        String authCode = SecurityUtils.getAuthCode();
        if (isNotBlank(authCode)) {
            return getLoginUser();
        }

        if (StringUtils.isBlank(auth)) {
            // 处理之前Token权限流
            return tokenService.getLoginUser(ServletUtils.getRequest());
        } else {
            if (StringUtils.isBlank(dev)) {
                dev = "a";
            }
            String cacheKey = "auth_dev:" + auth + "_" + dev;
            String cachedLoginUser = redisTemplate.opsForValue().get(cacheKey);
            if (cachedLoginUser == null) {
                HashMap<String, String> authMap = d01MBIR1Mapper.getAuthByCode(auth);
                if (authMap == null) {
                    throw new RuntimeException("授权码错误");
                }
                LoginUser loginUserNew = new LoginUser();
                loginUserNew.setUsername(authMap.get("UserName"));
                loginUserNew.setPassword(authMap.get("UserPassword"));
                loginUserNew.setTenantid(authMap.get("Tenantid"));

                redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(loginUserNew), 7200, TimeUnit.SECONDS);
                return loginUserNew;
            } else {
                return JSONArray.parseObject(cachedLoginUser, LoginUser.class);
            }
        }
    }




    // 有请求头authcode 有Redis:authcode-->token-->loginuser
    //                无Redis:authcode-->auth服务-->loginByAuthCode
    // 无,走请求头token
    public LoginUser getLoginUser() {
        // 请求头authcode中获取授权码
        String authCode = SecurityUtils.getAuthCode();
        // 如果没传授权码，走之前的token流程
        if (authCode == null) {
            return tokenService.getLoginUser(ServletUtils.getRequest());
        }
        // 如果传了授权码,先从Redis中获取用户信息 authcode:token键值对、token:loginuser键值对0
        // authcode-->token-->loginuser
        LoginUser loginUser = tokenService.getLoginUserByAuthCode(authCode);
        if (loginUser != null) {
            return loginUser;
        }
        // Redis中没有: 通过auth服务再次尝试获取
        Map<String, Object> data = authFeignService.loginByAuthCode(authCode).getData();
        String loginUserJson = JSON.toJSONString(data.get("loginuser"));
        try {
            // 尝试将获取到的JSON字符串解析为LoginUser对象
            return JSON.parseObject(loginUserJson, LoginUser.class);
        } catch (Exception e) {
            // 如果解析过程中出现异常，打印堆栈信息并返回null
            e.printStackTrace();
            return null;
        }
    }

}
