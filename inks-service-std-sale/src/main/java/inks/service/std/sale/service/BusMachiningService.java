package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.ReportsPojo;
import inks.service.std.sale.domain.pojo.BusMachiningPojo;
import inks.service.std.sale.domain.pojo.BusMachiningitemPojo;
import inks.service.std.sale.domain.pojo.BusMachiningitemdetailPojo;

import java.util.List;
import java.util.Map;

/**
 * 销售订单(BusMachining)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-13 13:56:19
 */
public interface BusMachiningService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusMachiningPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusMachiningitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusMachiningPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusMachiningPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusMachiningPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busMachiningPojo 实例对象
     * @return 实例对象
     */
    BusMachiningPojo insert(BusMachiningPojo busMachiningPojo);

    /**
     * 修改数据
     *
     * @param busMachiningpojo 实例对象
     * @return 实例对象
     */
    BusMachiningPojo update(BusMachiningPojo busMachiningpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param busMachiningPojo 实例对象
     * @return 实例对象
     */
    BusMachiningPojo approval(BusMachiningPojo busMachiningPojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BusMachiningPojo disannul(List<BusMachiningitemPojo> lst,Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BusMachiningPojo closed(List<BusMachiningitemPojo> lst,Integer type, LoginUser loginUser);


    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    /**
     * 查询 所有Item
     *
     * @param ids 筛选条件
     * @return 查询结果
     */
    List<BusMachiningitemPojo> getItemListByIds(String ids,String pid,String tid);

    // 查询货品是否被引用
    //  List<String> getCiteBillName(String key, String tid);

    // 开始批量打印
    void printBatchBillStart(List<String> ids, String uuid, String printapproved, ReportsPojo reportsPojo , LoginUser loginUser);

    /**
     * 获取批量打印状态
     * @param key
     * @return
     */
    Map<String, Object> getPrintBatchBillState(String key);

    int updatePrintcount(BusMachiningPojo billPrintPojo);

    Map<String, Object> getLastAmtByGroupId(String groupid, String tenantid);

    List<Map<String ,Object>> checkHistoryBillType(List<String> goodsids, String tenantid);

    BusMachiningPojo deleteCoupon(String key, String couponid, String tenantid);
}
