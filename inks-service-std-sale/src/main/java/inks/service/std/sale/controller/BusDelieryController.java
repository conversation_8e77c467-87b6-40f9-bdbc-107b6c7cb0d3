package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.enums.BusinessType;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.*;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.FieldFilter;
import inks.common.security.annotation.InksConfig;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import inks.service.std.sale.domain.pojo.BusDelieryPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo;
import inks.service.std.sale.service.AppWorkgroupService;
import inks.service.std.sale.service.BusDelieryService;
import inks.service.std.sale.service.BusDelieryitemService;
import inks.service.std.sale.utils.PrintColor;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;
import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 发出商品(BusDeliery)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-25 15:16:29
 */
public class BusDelieryController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BusDelieryController.class);
    /**
     * 服务对象
     */
    @Resource
    private BusDelieryService busDelieryService;
    /**
     * 服务对象Item
     */
    @Resource
    private BusDelieryitemService busDelieryitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private UtilsFeignService utilsFeignService;
    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取发出商品详细信息", notes = "获取发出商品详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<BusDelieryPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busDelieryService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    @InksConfig({"system.bill.amountfilter"})
    @FieldFilter(permission = "Bus_Deliery.Amount")
    public R<PageInfo<BusDelieryitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取发出商品详细信息", notes = "获取发出商品详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    @InksConfig({"system.bill.amountfilter"})
    @FieldFilter(permission = "Bus_Deliery.Amount")
    public R<BusDelieryPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busDelieryService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    @InksConfig({"system.bill.amountfilter"})
    @FieldFilter(permission = "Bus_Deliery.Amount")
    public R<PageInfo<BusDelieryPojo>> getBillList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    @InksConfig({"system.bill.amountfilter"})
    @FieldFilter(permission = "Bus_Deliery.Amount")
    public R<PageInfo<BusDelieryPojo>> getPageTh(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = " 新增发出商品", notes = "新增发出商品", produces = "application/json")
//    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Deliery.Add")
//    @InksConfig
//    public R<BusDelieryPojo> create(@RequestBody String json, HttpServletRequest request) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        String tid = loginUser.getTenantid();
//        try {
//            BusDelieryPojo busDelieryPojo = JSONArray.parseObject(json, BusDelieryPojo.class);
//            // item转json,校验各项金额相差不能大于1
//            String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(busDelieryPojo.getItem()));
//            //PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
//            if (lessThanOne != null) return R.fail(lessThanOne);
//
//
//            String[] pathParts = request.getRequestURI().split("/");
//            String moduleCode = pathParts[pathParts.length - 2];
//// 生成单据编码RefNoUtils
//            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Deliery", null, tid);
//            busDelieryPojo.setRefno(refno);
//            busDelieryPojo.setCreateby(loginUser.getRealName());   // 创建者
//            busDelieryPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
//            busDelieryPojo.setCreatedate(new Date());   // 创建时间
//            busDelieryPojo.setLister(loginUser.getRealname());   // 制表
//            busDelieryPojo.setListerid(loginUser.getUserid());    // 制表id
//            busDelieryPojo.setModifydate(new Date());   //修改时间
//            busDelieryPojo.setTenantid(tid);   //租户id
//            RefNoUtils.saveRedisRefNo(refno, moduleCode, tid);// 保存单据编码RefNoUtils
//            return R.ok(this.busDelieryService.insert(busDelieryPojo));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改发出商品", notes = "修改发出商品", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Edit")
    @InksConfig
    public R<BusDelieryPojo> update(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        BusDelieryPojo busDelieryPojo = JSONArray.parseObject(json, BusDelieryPojo.class);
        // item转json,校验各项金额相差不能大于1
        String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(busDelieryPojo.getItem()));
        //PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
        if (lessThanOne != null) return R.fail(lessThanOne);

        busDelieryPojo.setLister(loginUser.getRealname());   // 制表
        busDelieryPojo.setListerid(loginUser.getUserid());    // 制表id
        busDelieryPojo.setModifydate(new Date());   //修改时间
        busDelieryPojo.setAssessor(""); //审核员
        busDelieryPojo.setAssessorid(""); //审核员
        busDelieryPojo.setAssessdate(new Date()); //审核时间
        busDelieryPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.busDelieryService.update(busDelieryPojo, warn));
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除发出商品", notes = "删除发出商品", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.Delete")
    @OperLog(title = "删除发出商品")
    @InksConfig
    public R<BusDelieryPojo> delete(String key, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 如果是D01M06B1，额外需要检查引用
            // 获取请求的路径,/分割 https://dev.inksyun.com:31080/store/D04M01B1/saveInit
            String[] pathParts = request.getRequestURI().split("/");
            String moduleCode = pathParts[pathParts.length - 2]; // 获取倒数第二个路径参数：如"D04M01B1"
            if (Objects.equals(moduleCode, "D01M06B1")) {
                //检查引用
                List<BusDelieryitemPojo> lst = this.busDelieryitemService.getList(key, loginUser.getTenantid());
                for (BusDelieryitemPojo item : lst) {
                    List<String> lstcite = this.busDelieryService.getItemCiteBillName(item.getId(), item.getPid(), loginUser.getTenantid());
                    if (!lstcite.isEmpty()) {
                        return R.fail(500, "禁止删除,被以下单据引用:" + lstcite);
                    }
                }
            }
            BusDelieryPojo delPojo = this.busDelieryService.getEntity(key, loginUser.getTenantid());
            this.busDelieryService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());// 删除单据编码RefNoUtils
            return R.ok(delPojo, delPojo.getRefno() + "删除完成");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增发出商品Item", notes = "新增发出商品Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Add")
    public R<BusDelieryitemPojo> createItem(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusDelieryitemPojo busDelieryitemPojo = JSONArray.parseObject(json, BusDelieryitemPojo.class);

            busDelieryitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busDelieryitemService.insert(busDelieryitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除发出商品Item", notes = "删除发出商品Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.Delete")

    public R<Integer> deleteItem(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busDelieryitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核发出商品", notes = "审核发出商品", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.Approval")
    public R<BusDelieryPojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusDelieryPojo busDelieryPojo = this.busDelieryService.getEntity(key, loginUser.getTenantid());
            // 查询系统参数systemFeign
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            Map<String, String> tencfg;
            if (r.getCode() == 200) {
                tencfg = r.getData();
            } else {
                throw new BaseBusinessException("获得系统参数出错" + r.getMsg());
            }
            String OkMsg = "";
            if (busDelieryPojo.getAssessor().isEmpty()) {
                AppWorkgroupPojo wgPojo = this.appWorkgroupService.getCustomerGeneral(busDelieryPojo.getGroupid(), loginUser.getTenantid());
                if (Objects.equals(tencfg.get("module.sale.delichkcreditlimit"), "reject")) {
                    if (wgPojo != null && wgPojo.getTotalamount() > wgPojo.getCreditcquantity()) {
                        // 检查用户是否有"Bus_Deliery.PassAdmin"权限
                        if (loginUser.getPermissions().contains("Bus_Deliery.PassAdmin")) {
                            OkMsg = wgPojo.getAbbreviate() + " 累积金额" + wgPojo.getTotalamount() + "超过信用额度,特批放行";
                        } else {
                            return R.fail(wgPojo.getAbbreviate() + " 累积金额" + wgPojo.getTotalamount() + "超过信用额度,审核失败");
                        }
                    }
                }
                if (Objects.equals(tencfg.get("module.sale.delichkoverinvo"), "reject")) {
                    if (wgPojo != null && wgPojo.getOverdueinvoice() > 0) {
                        // 检查用户是否有"Bus_Deliery.PassAdmin"权限
                        if (loginUser.getPermissions().contains("Bus_Deliery.PassAdmin")) {
                            OkMsg += wgPojo.getAbbreviate() + " " + wgPojo.getOverdueinvoice() + "张 逾期发票,特批放行";
                        } else {
                            return R.fail(wgPojo.getAbbreviate() + " " + wgPojo.getOverdueinvoice() + "张 逾期发票,审核失败");
                        }
                    }
                }
                busDelieryPojo.setAssessor(loginUser.getRealname()); //审核员
                busDelieryPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busDelieryPojo.setAssessor(""); //审核员
                busDelieryPojo.setAssessorid(""); //审核员
            }
            busDelieryPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busDelieryService.approval(busDelieryPojo), OkMsg);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "特权审核发出商品", notes = "特权审核发出商品", produces = "application/json")
    @RequestMapping(value = "/approvalByPassAdmin", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.PassAdmin")
    public R<BusDelieryPojo> approvalByPassAdmin(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusDelieryPojo busDelieryPojo = this.busDelieryService.getEntity(key, loginUser.getTenantid());
            if (busDelieryPojo.getAssessor().equals("")) {
                busDelieryPojo.setAssessor(loginUser.getRealname()); //审核员
                busDelieryPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busDelieryPojo.setAssessor(""); //审核员
                busDelieryPojo.setAssessorid(""); //审核员
            }
            busDelieryPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busDelieryService.approval(busDelieryPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "作废发出商品", notes = "作废发出商品,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Edit")
    @InksConfig
    public R<BusDelieryPojo> disannul(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BusDelieryitemPojo> lst = JSONArray.parseArray(json, BusDelieryitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busDelieryService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "中止发出商品", notes = "作废发出商品,?type=1中止，0为反中止", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Edit")
    @OperLog(title = "中止发出商品", businessType = BusinessType.UPDATE)
    public R<BusDelieryPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BusDelieryitemPojo> lst = JSONArray.parseArray(json, BusDelieryitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busDelieryService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusDelieryPojo busDelieryPojo = this.busDelieryService.getBillEntity(key, loginUser.getTenantid());
        // 检查是否审核后方可打印单据,改为 feign Eric 20230203
        R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
        if (r.getCode() == 200) {
            Map<String, String> tencfg = r.getData();
            String printapproved = tencfg.get("system.bill.printapproved");
            if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {
                throw new BaseBusinessException("请先审核单据");
            }
        } else {
            throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
        }
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busDelieryPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busDelieryPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDelieryitemPojo busDelieryitemPojo = new BusDelieryitemPojo();
                    busDelieryPojo.getItem().add(busDelieryitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(busDelieryPojo.getItem());

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

        // 刷入打印Num++
//        BusDelieryPojo billPrintPojo = new BusDelieryPojo();
//        billPrintPojo.setId(busDelieryPojo.getId());
//        billPrintPojo.setPrintcount(busDelieryPojo.getPrintcount()+1);
//        billPrintPojo.setTenantid(busDelieryPojo.getTenantid());
//        this.busDelieryService.update(billPrintPojo);

        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),cmd=1为预览", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BusDelieryPojo busDelieryPojo = this.busDelieryService.getEntity(key, loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busDelieryPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BusDelieryitemPojo> lstitem = this.busDelieryitemService.getList(key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrcostListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "送货单据" + busDelieryPojo.getRefno());
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 刷入打印Num++
//            BusDelieryPojo billPrintPojo = new BusDelieryPojo();
//            billPrintPojo.setId(busDelieryPojo.getId());
//            billPrintPojo.setPrintcount(busDelieryPojo.getPrintcount()+1);
//            billPrintPojo.setTenantid(busDelieryPojo.getTenantid());
//            this.busDelieryService.update(billPrintPojo);
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印2份相同报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),cmd=1为预览", produces = "application/json")
    @RequestMapping(value = "/printWebBillMulti", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printWebBillMulti(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BusDelieryPojo busDelieryPojo = this.busDelieryService.getEntity(key, loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busDelieryPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            QueryParam queryParam = new QueryParam();
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Bus_DelieryItem.Pid='" + key + "'");
            queryParam.setOrderBy("Bus_DelieryItem.RowNum");
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            List<BusDelieryitemdetailPojo> lstitem = this.busDelieryService.getPageList(queryParam).getList();
            List<BusDelieryitemdetailPojo> lstCopy = new ArrayList<>();
            for (BusDelieryitemdetailPojo map2 : lstitem) {
                BusDelieryitemdetailPojo newPojo = new BusDelieryitemdetailPojo();
                BeanUtils.copyProperties(map2, newPojo);
                lstCopy.add(newPojo);
            }
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrcostListToMaps(lstitem);
            List<Map<String, Object>> lstMap = attrcostListToMaps(lstCopy);

            // 给第一个 lst 中的每个 map 添加字段 ToWho=1，PageNo=1
            lst.forEach(m -> {
                m.put("ToWho", 1);
                m.put("PageNo", 1);
            });
            // 给第二个 lstCopy 中的每个 map 添加字段 ToWho=2，PageNo=1
            lstMap.forEach(m -> {
                m.put("ToWho", 2);
                m.put("PageNo", 1);
            });

            // 合并两个 lst
            lst.addAll(lstMap);


            PrintColor.red("lst:" + lst);
            PrintColor.red("lst.size():" + lst.size());


            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            // 创建一个空的Map
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "送货单据" + busDelieryPojo.getRefno());
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());

            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),{ids:xxx}", produces = "application/json")
    @RequestMapping(value = "/printWebItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printWebItem(String key, String ptid, String sn, Integer cmd, @RequestBody String json, Integer redis) {
        try {
            Map<String, Object> mapids = JSONArray.parseObject(json, Map.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //获取单据信息
            BusDelieryPojo busDelieryPojo = this.busDelieryService.getEntity(key, loginUser.getTenantid());
//            //=========获取单据表头信息========
//            BusMachiningitemPojo busMachiningitemPojo = this.busMachiningitemService.getEntity(key, loginUser.getTenantid());
//            //=========获取单据表头信息========
//            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(busMachiningitemPojo.getPid(), loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData(); // redisService.getCacheObject("tenant_config:" + loginUser.getTenantid());
                String printapproved = tencfg.get("system.bill.printapproved");
                if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busDelieryPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BusDelieryitemPojo> lstitem = this.busDelieryService.getItemListByIds(mapids.get("ids").toString(), key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "销售订单" + busDelieryPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,{ids:xxx}", produces = "application/json")
    @RequestMapping(value = "/printItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printItem(String key, String ptid, @RequestBody String json) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Map<String, Object> mapids = JSONArray.parseObject(json, Map.class);
        //获取单据信息
        BusDelieryPojo busDelieryPojo = this.busDelieryService.getEntity(key, loginUser.getTenantid());
        busDelieryPojo.setItem(this.busDelieryService.getItemListByIds(mapids.get("ids").toString(), key, loginUser.getTenantid()));
        // 检查是否审核后方可打印单据,改为 feign Eric 20230203
        R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
        if (r.getCode() == 200) {
            Map<String, String> tencfg = r.getData(); // redisService.getCacheObject("tenant_config:" + loginUser.getTenantid());
            String printapproved = tencfg.get("system.bill.printapproved");
            if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {
                throw new BaseBusinessException("请先审核单据");
            }
        } else {
            throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
        }
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busDelieryPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busDelieryPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDelieryitemPojo busDelieryitemPojo = new BusDelieryitemPojo();
                    busDelieryPojo.getItem().add(busDelieryitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(busDelieryPojo.getItem());

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表 json={key,key},ptid打印模版,sn远程打印SN(可选),cmd=1为预览", produces = "application/json")
    @RequestMapping(value = "/printBatchWebBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";

            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData(); // redisService.getCacheObject("tenant_config:" + loginUser.getTenantid());
                printapproved = tencfg.get("system.bill.printapproved");
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            for (String key : lstkeys) {
                //=========获取单据表头信息========
                BusDelieryPojo busDelieryPojo = this.busDelieryService.getEntity(key, loginUser.getTenantid());
                if (busDelieryPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {
                    throw new BaseBusinessException("请先审核单据");
                }
                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(busDelieryPojo);
                // 获取单据表头.加入公司信息
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                List<BusDelieryitemPojo> lstitem = this.busDelieryitemService.getList(key, loginUser.getTenantid());
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrListToMaps(lstitem);

                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
                ptRefNoMain += busDelieryPojo.getRefno() + ";";
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            mapPrint.put("msg", "送货单据" + ptRefNoMain);
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 刷入打印Num++
//            BusDelieryPojo billPrintPojo = new BusDelieryPojo();
//            billPrintPojo.setId(busDelieryPojo.getId());
//            billPrintPojo.setPrintcount(busDelieryPojo.getPrintcount()+1);
//            billPrintPojo.setTenantid(busDelieryPojo.getTenantid());
//            this.busDelieryService.update(billPrintPojo);
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "批量打印单据", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public void printBatchBill(@RequestBody String json, String ptid) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            String content;
            if (reportsPojo != null) {
                content = reportsPojo.getRptdata();
            } else {
                throw new BaseBusinessException("未找到报表");
            }

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }

            //数据填充
            JasperPrint printAll = new JasperPrint();
            for (int a = 0; a < lstkeys.size(); a++) {
                String key = lstkeys.get(a);
                //获取单据信息
                BusDelieryPojo busDelieryPojo = this.busDelieryService.getBillEntity(key, loginUser.getTenantid());
                if (busDelieryPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {

                    throw new BaseBusinessException("请先审核单据");
                }
                //表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(busDelieryPojo);
                // 加入公司信息
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                // 判定是否需要追行
                if (reportsPojo.getPagerow() > 0) {
                    int index = 0;
                    // 取行余数
                    index = busDelieryPojo.getItem().size() % reportsPojo.getPagerow();
                    if (index > 0) {
                        // 补全空白行
                        for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                            BusDelieryitemPojo busDelieryitemPojo = new BusDelieryitemPojo();
                            busDelieryPojo.getItem().add(busDelieryitemPojo);
                        }
                    }
                }
                // 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(busDelieryPojo.getItem());
                //item转数据源
                JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
                //报表生成
                InputStream stream = new ByteArrayInputStream(content.getBytes());
                //编译报表
                JasperReport jasperReport = JasperCompileManager.compileReport(stream);
                //数据填充
                JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                if (a == 0) {
                    printAll = print;
                } else {
                    List<JRPrintPage> pages = print.getPages();
                    for (JRPrintPage page : pages) {
                        printAll.addPage(page);
                    }
                }
            }
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "批量打印单据", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBillStart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printBatchBillStart(@RequestBody String json, String ptid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                throw new BaseBusinessException("未找到报表");
            }

            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");

            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            // 单据id集
            List<String> ids = JSONArray.parseArray(json, String.class);
            // id
            String uuid = UUID.randomUUID().toString();
            // 开始异步
            this.busDelieryService.printBatchBillStart(ids, uuid, printapproved, reportsPojo, loginUser);
            return R.ok(uuid);
        } catch (Exception ex) {
            return R.fail(ex.getMessage());
        }

    }

    @ApiOperation(value = "获取批量打印状态", notes = "获取批量打印状态，key=?", produces = "application/json")
    @RequestMapping(value = "/getPrintBatchBillState", method = RequestMethod.GET)
    public R<Map<String, Object>> getPrintBatchBillState(@RequestParam String key) {
        Map<String, Object> PrintState = this.busDelieryService.getPrintBatchBillState(key);
        return R.ok(PrintState);
    }


    @ApiOperation(value = "显示批量打印单据PDF", notes = "显示批量打印单据PDF,key=?", produces = "application/json")
    @RequestMapping(value = "/getPrintBatchBillResult", method = RequestMethod.GET)
    public void getPrintBatchBillResult(@RequestParam String key) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            // Redis的byte[]
            String cachekey = "report_pages:" + key;
            byte[] base64file = this.redisService.getCacheObject(cachekey);
            // Byte[]转打印报表
            Object objPrintAll = StreamUtils.toObject(base64file);
            JasperPrint printAll = (JasperPrint) objPrintAll;
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type) {
        try {
            if (type == null) type = "wxe";  // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
            //获取token
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //创建VM数据对象
            VelocityContext context = new VelocityContext();
            //从redis中获取模板对象
            // Object obj = redisService.getCacheObject(verifyKey);
            ApprrecPojo apprrecPojo = redisService.getCacheObject(verifyKey);
            ApprovePojo approvePojo = new ApprovePojo();
            //获得第三方账号
            R rja = systemFeignService.getJustauthByUserid(loginUser.getUserid(), type, loginUser.getTenantid());
            JustauthPojo justauthPojo = new JustauthPojo();
            if (rja.getCode() == 200) {
                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
            } else {
                return R.fail("获得第三方账号出错" + rja.getMsg());
            }
            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
            approvePojo.setUserid(justauthPojo.getAuthuuid());
            approvePojo.setModelcode(apprrecPojo.getTemplateid());
            approvePojo.setObject(this.busDelieryService.getBillEntity(key, loginUser.getTenantid()));
            context.put("approvePojo", approvePojo);
            String str = apprrecPojo.getDatatemp();
            // 初始化并取得Velocity引擎
            VelocityEngine ve = new VelocityEngine();
            ve.init();
            // 转换输出
            StringWriter writer = new StringWriter();
            ve.evaluate(context, writer, "", str); // 关键方法
            //写回String
            str = writer.toString();
            //新建审批记录
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            apprrecPojo.setDatatemp(str);
            apprrecPojo.setApprname("订单审批");
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key);    // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(new Date());
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(new Date());
            apprrecPojo.setTenantid(loginUser.getTenantid());
            //将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            redisService.setCacheObject(CachKey, apprrecPojo, (long) (60 * 12), TimeUnit.MINUTES);
            if ("wxe".equals(type)) {
                R r = this.utilsFeignService.wxeapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r);
                }
            } else {
                R r = this.utilsFeignService.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r);
                }
            }
            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R<BusDelieryPojo> justapprovel(String key, String type) {
        try {
            System.out.println("审核通过,写入审核信息");
            //1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = redisService.getCacheObject(verifyKey);
            //2. 获得单据数据
            BusDelieryPojo busDelieryPojo = this.busDelieryService.getEntity(apprrecPojo.getBillid(), apprrecPojo.getTenantid());
            //3. 写入审核批
            //获得第三方账号
            if (type == null) type = "wxe";
            R rja = systemFeignService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, apprrecPojo.getTenantid());
            JustauthPojo justauthPojo = new JustauthPojo();
            if (rja.getCode() == 200) {
                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
            } else {
                System.out.println("写入审核:获得第三方账号出错：" + rja.getMsg());
                return R.fail("获得第三方账号出错" + rja.getMsg());
            }
            busDelieryPojo.setAssessorid(justauthPojo.getUserid());
            busDelieryPojo.setAssessor(justauthPojo.getRealname()); //审核员
            busDelieryPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busDelieryService.approval(busDelieryPojo));
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印BusDeliery明细报表(分页PageList)", notes = "打印明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid, HttpServletRequest request) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Deliery.BillDate");

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
        }
        // 获取请求的路径,/分割
        String[] pathParts = request.getRequestURI().split("/");
        String moduleCode = pathParts[pathParts.length - 2];  // 获取倒数第二个路径参数：如"D04M01B1"
        // 使用参数进行相应的逻辑操作
        if ("D01M06B1".equals(moduleCode)) {
            qpfilter += " and Bus_Deliery.BillType IN ('发出商品','订单退货')";
        } else if ("D01M06B2".equals(moduleCode)) {
            qpfilter += " and Bus_Deliery.BillType IN ('其他发货','其他退货')";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusDelieryitemdetailPojo> lst = this.busDelieryService.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDelieryitemdetailPojo itemdetailPojo = new BusDelieryitemdetailPojo();
                    lst.add(itemdetailPojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "打印BusDeliery单据报表(分页PageTh)", notes = "打印PageTh报表", produces = "application/json")
    @RequestMapping(value = "/printPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public void printPageTh(@RequestBody String json, String groupid, String ptid, HttpServletRequest request) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Deliery.BillDate");

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
        }
        // 获取请求的路径,/分割
        String[] pathParts = request.getRequestURI().split("/");
        String moduleCode = pathParts[pathParts.length - 2];  // 获取倒数第二个路径参数：如"D04M01B1"
        // 使用参数进行相应的逻辑操作
        if ("D01M06B1".equals(moduleCode)) {
            qpfilter += " and Bus_Deliery.BillType IN ('发出商品','订单退货')";
        } else if ("D01M06B2".equals(moduleCode)) {
            qpfilter += " and Bus_Deliery.BillType IN ('其他发货','其他退货')";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusDelieryPojo> lst = this.busDelieryService.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDelieryPojo pojo = new BusDelieryPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印BusDeliery明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, Integer online, HttpServletRequest request) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 获取请求的路径,/分割
            String[] pathParts = request.getRequestURI().split("/");
            String moduleCode = pathParts[pathParts.length - 2];  // 获取倒数第二个路径参数：如"D04M01B1"
            // 使用参数进行相应的逻辑操作
            if ("D01M06B1".equals(moduleCode)) {
                qpfilter += " and Bus_Deliery.BillType IN ('发出商品','订单退货')";
            } else if ("D01M06B2".equals(moduleCode)) {
                qpfilter += " and Bus_Deliery.BillType IN ('其他发货','其他退货')";
            }
            if (online != null && online == 1) {
                qpfilter += " and Bus_DelieryItem.FinishQty<Bus_DelieryItem.Quantity+Bus_DelieryItem.FreeQty";
                qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.FinishClosed=0 ";  // 未关闭、未注销
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusDelieryitemdetailPojo> lstitem = this.busDelieryService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BusDeliery明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "批量云打印报表(List<BusDelieryPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageTh", method = RequestMethod.POST)
    //    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printWebPageTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, Integer online, HttpServletRequest request) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 获取请求的路径,/分割
            String[] pathParts = request.getRequestURI().split("/");
            String moduleCode = pathParts[pathParts.length - 2];  // 获取倒数第二个路径参数：如"D04M01B1"
            // 使用参数进行相应的逻辑操作
            if ("D01M06B1".equals(moduleCode)) {
                qpfilter += " and Bus_Deliery.BillType IN ('发出商品','订单退货')";
            } else if ("D01M06B2".equals(moduleCode)) {
                qpfilter += " and Bus_Deliery.BillType IN ('其他发货','其他退货')";
            }
            if (online != null && online == 1) {
                qpfilter += " and Bus_Deliery.FinishCount<Bus_Deliery.ItemCount";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusDelieryPojo> lstTh = this.busDelieryService.getPageTh(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstTh.size() > 0) {
                map.put("groupname", lstTh.get(0).getGroupname());
                map.put("abbreviate", lstTh.get(0).getAbbreviate());
                map.put("groupuid", lstTh.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "wip批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

