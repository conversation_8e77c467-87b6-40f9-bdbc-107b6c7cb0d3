package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusAccountPojo;
import inks.service.std.sale.domain.pojo.BusAccountrecPojo;
import inks.service.std.sale.domain.pojo.BusInvocarryoverPojo;
import inks.service.std.sale.service.BusAccountService;
import inks.service.std.sale.service.BusAccountrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 结转记录(Bus_AccountRec)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-25 08:28:53
 */
@RestController
@RequestMapping("D01M14B1")
@Api(tags = "D01M14B1:结账记录")
public class D01M14B1Controller extends BusAccountrecController {
    /**
     * 服务对象
     */
    @Resource
    private BusAccountrecService busAccountrecService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 服务对象
     */
    @Resource
    private BusAccountService busAccountService;

    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = " 新增结转记录", notes = "新增结转记录", produces = "application/json")
    @RequestMapping(value = "/open", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_AccountRec.Add")
    public R<BusAccountrecPojo> open(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));


            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (busAccountrecPojo != null) {
                return R.fail("已有开账记录,禁止重复开账");
            }
            busAccountrecPojo = new BusAccountrecPojo();
            busAccountrecPojo.setCarryyear(year);
            busAccountrecPojo.setCarrymonth(month);
            busAccountrecPojo.setRownum(Integer.parseInt(strRowNum));
            busAccountrecPojo.setStartdate(DateUtils.parseDate(year + "-" + month + "-1 00:00:00"));
            busAccountrecPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(busAccountrecPojo.getStartdate(), 1), -1));
            busAccountrecPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountrecPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountrecPojo.setCreatedate(new Date());   // 创建时间
            busAccountrecPojo.setLister(loginUser.getRealname());   // 制表
            busAccountrecPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountrecPojo.setModifydate(new Date());   //修改时间
            busAccountrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            busAccountrecPojo.setTenantname(loginUser.getTenantinfo().getTenantname());

            if (DateUtils.getTimestamp(busAccountrecPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.busAccountrecService.insert(busAccountrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结转记录最新记录", notes = "获取结转记录最新记录", produces = "application/json")
    @RequestMapping(value = "/getEntityByMax", method = RequestMethod.GET)
    public R<BusAccountrecPojo> getEntityByMax() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busAccountrecService.getEntityByMax(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = "初期化全部销售账单", notes = "初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/batchCreate", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<Integer> batchCreate(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (busAccountrecPojo == null) {
                R.fail("请先开账");
            }
            BusAccountPojo busAccountPojo = new BusAccountPojo();
            busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            busAccountPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(busAccountPojo.getStartdate(), 1), -1));
            //生成单据编码
            R r = systemFeignService.getBillCode("D01M12B1", loginUser.getToken());
            if (r.getCode() == 200)
                busAccountPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            busAccountPojo.setBilltype("销售账单");
            busAccountPojo.setCarryyear(year);
            busAccountPojo.setCarrymonth(month);
            busAccountPojo.setRownum(Integer.parseInt(strRowNum));
            busAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountPojo.setCreatedate(new Date());   // 创建时间
            busAccountPojo.setLister(loginUser.getRealname());   // 制表
            busAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountPojo.setModifydate(new Date());   //修改时间
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            busAccountPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(busAccountPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.busAccountService.batchCreate(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除结转记录", notes = "删除结转记录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_AccountRec.Delete")
    @OperLog(title = "删除结转记录")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (key.equals(busAccountrecPojo.getId())) {
                return R.ok(this.busAccountrecService.delete(key, loginUser.getTenantid()));
            } else {
                return R.fail("只可以对最后一期返结账");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //---------------------------做异步任务-------------------------
    @ApiOperation(value = "Start初期化全部销售账单", notes = "初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/batchCreateStart", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<String> batchCreateStart(Integer year, Integer month) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (busAccountrecPojo == null) {
                R.fail("请先开账");
            }
            BusAccountPojo busAccountPojo = new BusAccountPojo();
            busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            busAccountPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(busAccountPojo.getStartdate(), 1), -1));
            //生成单据编码
            R r = systemFeignService.getBillCode("D01M12B1", loginUser.getToken());
            if (r.getCode() == 200)
                busAccountPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            busAccountPojo.setBilltype("销售账单");
            busAccountPojo.setCarryyear(year);
            busAccountPojo.setCarrymonth(month);
            busAccountPojo.setRownum(Integer.parseInt(strRowNum));
            busAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountPojo.setCreatedate(new Date());   // 创建时间
            busAccountPojo.setLister(loginUser.getRealname());   // 制表
            busAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountPojo.setModifydate(new Date());   //修改时间
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            busAccountPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(busAccountPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            // -----开始异步  批量生产账单---
            // uuid作为Redis的hkey
            String uuid = UUID.randomUUID().toString();
            // 读取指定系统参数"module.sale.goodscarryover"：是否生成货品账单
            String configValue = systemFeignService.getConfigValue("module.sale.goodscarryover", loginUser.getTenantid(), loginUser.getToken()).getData();
            boolean isCreateGoodsCarryover = Boolean.parseBoolean(configValue);
            this.busAccountService.batchCreateStart(busAccountPojo, uuid, isCreateGoodsCarryover);
            return R.ok(uuid);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "State初期化全部销售账单", notes = "取客户应收款列表By销售单", produces = "application/json")
    @RequestMapping(value = "/batchCreateState", method = RequestMethod.GET)
    public R<Map<String, Object>> batchCreateState(@RequestParam String key) {
        Map<String, Object> state = this.busAccountService.batchCreateState(key);
        return R.ok(state);
    }

    @ApiOperation(value = "更新多个货品的货品账单", notes = "初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/updateInvoCarryByids", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<List<BusInvocarryoverPojo>> updateInvoCarryByids(@RequestBody String json, Integer year, Integer month) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<String> invocarryids = JSON.parseObject(json, new TypeReference<List<String>>() {
            });
            return R.ok(busAccountService.updateInvoCarryByids(invocarryids, year, month, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
