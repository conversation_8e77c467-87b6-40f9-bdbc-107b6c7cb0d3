package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.BillCodeUtil;
import inks.service.std.sale.domain.BusDmsgoodsEntity;
import inks.service.std.sale.domain.pojo.BusDmsgoodsPojo;
import inks.service.std.sale.mapper.BusDmsgoodsMapper;
import inks.service.std.sale.service.BusDmsgoodsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * DMS商品(BusDmsgoods)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-23 16:01:24
 */
@Service("busDmsgoodsService")
public class BusDmsgoodsServiceImpl implements BusDmsgoodsService {
    @Resource
    private BusDmsgoodsMapper busDmsgoodsMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDmsgoodsPojo getEntity(String key, String tid) {
        return this.busDmsgoodsMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDmsgoodsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDmsgoodsPojo> lst = busDmsgoodsMapper.getPageList(queryParam);
            PageInfo<BusDmsgoodsPojo> pageInfo = new PageInfo<BusDmsgoodsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busDmsgoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDmsgoodsPojo insert(BusDmsgoodsPojo busDmsgoodsPojo) {
        String tid = busDmsgoodsPojo.getTenantid();
        // 校验货品编码是否重复
        int i = busDmsgoodsMapper.checkGoodsUid(busDmsgoodsPojo.getGoodsuid(), null, tid);
        if (i > 0) {
            throw new BaseBusinessException("货品编码重复");
        }
        //初始化NULL字段
        if (busDmsgoodsPojo.getGoodsuid() == null) busDmsgoodsPojo.setGoodsuid("");
        if (busDmsgoodsPojo.getGoodsname() == null) busDmsgoodsPojo.setGoodsname("");
        if (busDmsgoodsPojo.getGoodsspec() == null) busDmsgoodsPojo.setGoodsspec("");
        if (busDmsgoodsPojo.getGoodsunit() == null) busDmsgoodsPojo.setGoodsunit("");
        if (busDmsgoodsPojo.getOriprice() == null) busDmsgoodsPojo.setOriprice(0D);
        if (busDmsgoodsPojo.getPrice() == null) busDmsgoodsPojo.setPrice(0D);
        if (busDmsgoodsPojo.getStructure() == null) busDmsgoodsPojo.setStructure("");
        if (busDmsgoodsPojo.getMaterial() == null) busDmsgoodsPojo.setMaterial("");
        if (busDmsgoodsPojo.getExponent() == null) busDmsgoodsPojo.setExponent(0);
        if (busDmsgoodsPojo.getProdcycle() == null) busDmsgoodsPojo.setProdcycle("");
        if (busDmsgoodsPojo.getProcess() == null) busDmsgoodsPojo.setProcess("");
        if (busDmsgoodsPojo.getBrief() == null) busDmsgoodsPojo.setBrief("");
        if (busDmsgoodsPojo.getContent() == null) busDmsgoodsPojo.setContent("");
        if (busDmsgoodsPojo.getColor() == null) busDmsgoodsPojo.setColor("");
        if (busDmsgoodsPojo.getLevel() == null) busDmsgoodsPojo.setLevel("");
        if (busDmsgoodsPojo.getAttributejson() == null) busDmsgoodsPojo.setAttributejson("[]");
        if (busDmsgoodsPojo.getAttributestr() == null) busDmsgoodsPojo.setAttributestr("");
        if (busDmsgoodsPojo.getPic() == null) busDmsgoodsPojo.setPic("");
        if (busDmsgoodsPojo.getImgs() == null) busDmsgoodsPojo.setImgs("");
        if (busDmsgoodsPojo.getStatus() == null) busDmsgoodsPojo.setStatus(0);
        if (busDmsgoodsPojo.getCategoryid() == null) busDmsgoodsPojo.setCategoryid("");
        if (busDmsgoodsPojo.getSoldnum() == null) busDmsgoodsPojo.setSoldnum(0);
        if (busDmsgoodsPojo.getTotalstocks() == null) busDmsgoodsPojo.setTotalstocks(0);
        if (busDmsgoodsPojo.getDeliverymode() == null) busDmsgoodsPojo.setDeliverymode("");
        if (busDmsgoodsPojo.getPutawaydate() == null) busDmsgoodsPojo.setPutawaydate(new Date());
        if (busDmsgoodsPojo.getMainmark() == null) busDmsgoodsPojo.setMainmark(0);
        if (busDmsgoodsPojo.getRownum() == null) busDmsgoodsPojo.setRownum(0);
        if (busDmsgoodsPojo.getRemark() == null) busDmsgoodsPojo.setRemark("");
        if (busDmsgoodsPojo.getCreateby() == null) busDmsgoodsPojo.setCreateby("");
        if (busDmsgoodsPojo.getCreatebyid() == null) busDmsgoodsPojo.setCreatebyid("");
        if (busDmsgoodsPojo.getCreatedate() == null) busDmsgoodsPojo.setCreatedate(new Date());
        if (busDmsgoodsPojo.getLister() == null) busDmsgoodsPojo.setLister("");
        if (busDmsgoodsPojo.getListerid() == null) busDmsgoodsPojo.setListerid("");
        if (busDmsgoodsPojo.getModifydate() == null) busDmsgoodsPojo.setModifydate(new Date());
        if (busDmsgoodsPojo.getCustom1() == null) busDmsgoodsPojo.setCustom1("");
        if (busDmsgoodsPojo.getCustom2() == null) busDmsgoodsPojo.setCustom2("");
        if (busDmsgoodsPojo.getCustom3() == null) busDmsgoodsPojo.setCustom3("");
        if (busDmsgoodsPojo.getCustom4() == null) busDmsgoodsPojo.setCustom4("");
        if (busDmsgoodsPojo.getCustom5() == null) busDmsgoodsPojo.setCustom5("");
        if (busDmsgoodsPojo.getCustom6() == null) busDmsgoodsPojo.setCustom6("");
        if (busDmsgoodsPojo.getCustom7() == null) busDmsgoodsPojo.setCustom7("");
        if (busDmsgoodsPojo.getCustom8() == null) busDmsgoodsPojo.setCustom8("");
        if (busDmsgoodsPojo.getCustom9() == null) busDmsgoodsPojo.setCustom9("");
        if (busDmsgoodsPojo.getCustom10() == null) busDmsgoodsPojo.setCustom10("");
        if (tid == null) busDmsgoodsPojo.setTenantid("");
        if (busDmsgoodsPojo.getTenantname() == null) busDmsgoodsPojo.setTenantname("");
        if (busDmsgoodsPojo.getRevision() == null) busDmsgoodsPojo.setRevision(0);
        BusDmsgoodsEntity busDmsgoodsEntity = new BusDmsgoodsEntity();
        BeanUtils.copyProperties(busDmsgoodsPojo, busDmsgoodsEntity);
        //生成雪花id
        busDmsgoodsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busDmsgoodsEntity.setRevision(1);  //乐观锁
        this.busDmsgoodsMapper.insert(busDmsgoodsEntity);
        return this.getEntity(busDmsgoodsEntity.getId(), busDmsgoodsEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busDmsgoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDmsgoodsPojo update(BusDmsgoodsPojo busDmsgoodsPojo) {
        String tid = busDmsgoodsPojo.getTenantid();
        // 校验货品编码是否重复
        int i = busDmsgoodsMapper.checkGoodsUid(busDmsgoodsPojo.getGoodsuid(), busDmsgoodsPojo.getId(), tid);
        if (i > 0) {
            throw new BaseBusinessException("货品编码重复");
        }
        BusDmsgoodsEntity busDmsgoodsEntity = new BusDmsgoodsEntity();
        BeanUtils.copyProperties(busDmsgoodsPojo, busDmsgoodsEntity);
        this.busDmsgoodsMapper.update(busDmsgoodsEntity);
        return this.getEntity(busDmsgoodsEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public String delete(String key, String tid) {
        BusDmsgoodsPojo del = busDmsgoodsMapper.getEntity(key, tid);
         this.busDmsgoodsMapper.delete(key, tid);
        return del.getGoodsuid();
    }


    public String getNextCode(String tenantid) {
        String NextCode = "";
        String MaxStr = this.busDmsgoodsMapper.getMaxCode(tenantid);
        if (MaxStr != null) {
            NextCode = BillCodeUtil.SnLatter(MaxStr, 1);
        }

        return NextCode;
    }
}
