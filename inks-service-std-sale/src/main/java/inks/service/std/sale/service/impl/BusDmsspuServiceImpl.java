package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.sale.domain.BusDmsspuEntity;
import inks.service.std.sale.domain.pojo.BusDmsspuPojo;
import inks.service.std.sale.mapper.BusDmsspuMapper;
import inks.service.std.sale.service.BusDmsspuService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * Dms商品属性Key(BusDmsspu)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-27 19:12:25
 */
@Service("busDmsspuService")
public class BusDmsspuServiceImpl implements BusDmsspuService {
    @Resource
    private BusDmsspuMapper busDmsspuMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDmsspuPojo getEntity(String key, String tid) {
        return this.busDmsspuMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDmsspuPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDmsspuPojo> lst = busDmsspuMapper.getPageList(queryParam);
            PageInfo<BusDmsspuPojo> pageInfo = new PageInfo<BusDmsspuPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param busDmsspuPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDmsspuPojo insert(BusDmsspuPojo busDmsspuPojo) {
    //初始化NULL字段
     if(busDmsspuPojo.getAttrgroupid()==null) busDmsspuPojo.setAttrgroupid("");
     if(busDmsspuPojo.getAttrkey()==null) busDmsspuPojo.setAttrkey("");
     if(busDmsspuPojo.getAttrname()==null) busDmsspuPojo.setAttrname("");
     if(busDmsspuPojo.getValuetype()==null) busDmsspuPojo.setValuetype("");
     if(busDmsspuPojo.getDefvalue()==null) busDmsspuPojo.setDefvalue("");
     if(busDmsspuPojo.getValuejson()==null) busDmsspuPojo.setValuejson("");
     if(busDmsspuPojo.getListshow()==null) busDmsspuPojo.setListshow(0);
     if(busDmsspuPojo.getEnabledmark()==null) busDmsspuPojo.setEnabledmark(0);
     if(busDmsspuPojo.getSkumark()==null) busDmsspuPojo.setSkumark(0);
     if(busDmsspuPojo.getNumericmark()==null) busDmsspuPojo.setNumericmark(0);
     if(busDmsspuPojo.getRequiredmark()==null) busDmsspuPojo.setRequiredmark(0);
     if(busDmsspuPojo.getRownum()==null) busDmsspuPojo.setRownum(0);
     if(busDmsspuPojo.getRemark()==null) busDmsspuPojo.setRemark("");
     if(busDmsspuPojo.getCreateby()==null) busDmsspuPojo.setCreateby("");
     if(busDmsspuPojo.getCreatebyid()==null) busDmsspuPojo.setCreatebyid("");
     if(busDmsspuPojo.getCreatedate()==null) busDmsspuPojo.setCreatedate(new Date());
     if(busDmsspuPojo.getLister()==null) busDmsspuPojo.setLister("");
     if(busDmsspuPojo.getListerid()==null) busDmsspuPojo.setListerid("");
     if(busDmsspuPojo.getModifydate()==null) busDmsspuPojo.setModifydate(new Date());
     if(busDmsspuPojo.getCustom1()==null) busDmsspuPojo.setCustom1("");
     if(busDmsspuPojo.getCustom2()==null) busDmsspuPojo.setCustom2("");
     if(busDmsspuPojo.getCustom3()==null) busDmsspuPojo.setCustom3("");
     if(busDmsspuPojo.getCustom4()==null) busDmsspuPojo.setCustom4("");
     if(busDmsspuPojo.getCustom5()==null) busDmsspuPojo.setCustom5("");
     if(busDmsspuPojo.getCustom6()==null) busDmsspuPojo.setCustom6("");
     if(busDmsspuPojo.getCustom7()==null) busDmsspuPojo.setCustom7("");
     if(busDmsspuPojo.getCustom8()==null) busDmsspuPojo.setCustom8("");
     if(busDmsspuPojo.getCustom9()==null) busDmsspuPojo.setCustom9("");
     if(busDmsspuPojo.getCustom10()==null) busDmsspuPojo.setCustom10("");
     if(busDmsspuPojo.getTenantid()==null) busDmsspuPojo.setTenantid("");
     if(busDmsspuPojo.getTenantname()==null) busDmsspuPojo.setTenantname("");
     if(busDmsspuPojo.getRevision()==null) busDmsspuPojo.setRevision(0);
        BusDmsspuEntity busDmsspuEntity = new BusDmsspuEntity(); 
        BeanUtils.copyProperties(busDmsspuPojo,busDmsspuEntity);
        
          busDmsspuEntity.setId(UUID.randomUUID().toString());
          busDmsspuEntity.setRevision(1);  //乐观锁
          this.busDmsspuMapper.insert(busDmsspuEntity);
        return this.getEntity(busDmsspuEntity.getId(),busDmsspuEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busDmsspuPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDmsspuPojo update(BusDmsspuPojo busDmsspuPojo) {
        BusDmsspuEntity busDmsspuEntity = new BusDmsspuEntity(); 
        BeanUtils.copyProperties(busDmsspuPojo,busDmsspuEntity);
        this.busDmsspuMapper.update(busDmsspuEntity);
        return this.getEntity(busDmsspuEntity.getId(),busDmsspuEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busDmsspuMapper.delete(key,tid) ;
    }
    
                                                                                                                                                                         
}
