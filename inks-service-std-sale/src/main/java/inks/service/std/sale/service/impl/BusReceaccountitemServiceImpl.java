package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusReceaccountitemEntity;
import inks.service.std.sale.domain.pojo.BusReceaccountitemPojo;
import inks.service.std.sale.mapper.BusReceaccountitemMapper;
import inks.service.std.sale.service.BusReceaccountitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 应收账单Item(BusReceaccountitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-23 13:32:16
 */
@Service("busReceaccountitemService")
public class BusReceaccountitemServiceImpl implements BusReceaccountitemService {
    @Resource
    private BusReceaccountitemMapper busReceaccountitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusReceaccountitemPojo getEntity(String key,String tid) {
        return this.busReceaccountitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceaccountitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceaccountitemPojo> lst = busReceaccountitemMapper.getPageList(queryParam);
            PageInfo<BusReceaccountitemPojo> pageInfo = new PageInfo<BusReceaccountitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusReceaccountitemPojo> getList(String Pid,String tid) { 
        try {
            List<BusReceaccountitemPojo> lst = busReceaccountitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busReceaccountitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusReceaccountitemPojo insert(BusReceaccountitemPojo busReceaccountitemPojo) {
        //初始化item的NULL
        BusReceaccountitemPojo itempojo =this.clearNull(busReceaccountitemPojo);
        BusReceaccountitemEntity busReceaccountitemEntity = new BusReceaccountitemEntity(); 
        BeanUtils.copyProperties(itempojo,busReceaccountitemEntity);
        
          busReceaccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busReceaccountitemEntity.setRevision(1);  //乐观锁      
          this.busReceaccountitemMapper.insert(busReceaccountitemEntity);
        return this.getEntity(busReceaccountitemEntity.getId(),busReceaccountitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busReceaccountitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusReceaccountitemPojo update(BusReceaccountitemPojo busReceaccountitemPojo) {
        BusReceaccountitemEntity busReceaccountitemEntity = new BusReceaccountitemEntity(); 
        BeanUtils.copyProperties(busReceaccountitemPojo,busReceaccountitemEntity);
        this.busReceaccountitemMapper.update(busReceaccountitemEntity);
        return this.getEntity(busReceaccountitemEntity.getId(),busReceaccountitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busReceaccountitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busReceaccountitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusReceaccountitemPojo clearNull(BusReceaccountitemPojo busReceaccountitemPojo){
     //初始化NULL字段
     if(busReceaccountitemPojo.getPid()==null) busReceaccountitemPojo.setPid("");
     if(busReceaccountitemPojo.getDirection()==null) busReceaccountitemPojo.setDirection("");
     if(busReceaccountitemPojo.getBilltype()==null) busReceaccountitemPojo.setBilltype("");
     if(busReceaccountitemPojo.getBilldate()==null) busReceaccountitemPojo.setBilldate(new Date());
     if(busReceaccountitemPojo.getBilltitle()==null) busReceaccountitemPojo.setBilltitle("");
     if(busReceaccountitemPojo.getBilluid()==null) busReceaccountitemPojo.setBilluid("");
     if(busReceaccountitemPojo.getBillid()==null) busReceaccountitemPojo.setBillid("");
     if(busReceaccountitemPojo.getOpenamount()==null) busReceaccountitemPojo.setOpenamount(0D);
     if(busReceaccountitemPojo.getInamount()==null) busReceaccountitemPojo.setInamount(0D);
     if(busReceaccountitemPojo.getOutamount()==null) busReceaccountitemPojo.setOutamount(0D);
     if(busReceaccountitemPojo.getCloseamount()==null) busReceaccountitemPojo.setCloseamount(0D);
     if(busReceaccountitemPojo.getRownum()==null) busReceaccountitemPojo.setRownum(0);
     if(busReceaccountitemPojo.getRemark()==null) busReceaccountitemPojo.setRemark("");
     if(busReceaccountitemPojo.getCustom1()==null) busReceaccountitemPojo.setCustom1("");
     if(busReceaccountitemPojo.getCustom2()==null) busReceaccountitemPojo.setCustom2("");
     if(busReceaccountitemPojo.getCustom3()==null) busReceaccountitemPojo.setCustom3("");
     if(busReceaccountitemPojo.getCustom4()==null) busReceaccountitemPojo.setCustom4("");
     if(busReceaccountitemPojo.getCustom5()==null) busReceaccountitemPojo.setCustom5("");
     if(busReceaccountitemPojo.getCustom6()==null) busReceaccountitemPojo.setCustom6("");
     if(busReceaccountitemPojo.getCustom7()==null) busReceaccountitemPojo.setCustom7("");
     if(busReceaccountitemPojo.getCustom8()==null) busReceaccountitemPojo.setCustom8("");
     if(busReceaccountitemPojo.getCustom9()==null) busReceaccountitemPojo.setCustom9("");
     if(busReceaccountitemPojo.getCustom10()==null) busReceaccountitemPojo.setCustom10("");
     if(busReceaccountitemPojo.getTenantid()==null) busReceaccountitemPojo.setTenantid("");
     if(busReceaccountitemPojo.getRevision()==null) busReceaccountitemPojo.setRevision(0);
     return busReceaccountitemPojo;
     }
}
