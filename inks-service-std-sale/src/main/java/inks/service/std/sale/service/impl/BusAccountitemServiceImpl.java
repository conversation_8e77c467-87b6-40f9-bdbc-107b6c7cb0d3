package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusAccountitemEntity;
import inks.service.std.sale.domain.pojo.BusAccountitemPojo;
import inks.service.std.sale.mapper.BusAccountitemMapper;
import inks.service.std.sale.service.BusAccountitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 账单明细(BusAccountitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-01 12:51:42
 */
@Service("busAccountitemService")
public class BusAccountitemServiceImpl implements BusAccountitemService {
    @Resource
    private BusAccountitemMapper busAccountitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountitemPojo getEntity(String key,String tid) {
        return this.busAccountitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountitemPojo> lst = busAccountitemMapper.getPageList(queryParam);
            PageInfo<BusAccountitemPojo> pageInfo = new PageInfo<BusAccountitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusAccountitemPojo> getList(String Pid,String tid) { 
        try {
            List<BusAccountitemPojo> lst = busAccountitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busAccountitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountitemPojo insert(BusAccountitemPojo busAccountitemPojo) {
        //初始化item的NULL
        BusAccountitemPojo itempojo =this.clearNull(busAccountitemPojo);
        BusAccountitemEntity busAccountitemEntity = new BusAccountitemEntity(); 
        BeanUtils.copyProperties(itempojo,busAccountitemEntity);
        
          busAccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busAccountitemEntity.setRevision(1);  //乐观锁      
          this.busAccountitemMapper.insert(busAccountitemEntity);
        return this.getEntity(busAccountitemEntity.getId(),busAccountitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busAccountitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountitemPojo update(BusAccountitemPojo busAccountitemPojo) {
        BusAccountitemEntity busAccountitemEntity = new BusAccountitemEntity(); 
        BeanUtils.copyProperties(busAccountitemPojo,busAccountitemEntity);
        this.busAccountitemMapper.update(busAccountitemEntity);
        return this.getEntity(busAccountitemEntity.getId(),busAccountitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busAccountitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busAccountitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusAccountitemPojo clearNull(BusAccountitemPojo busAccountitemPojo){
     //初始化NULL字段
     if(busAccountitemPojo.getPid()==null) busAccountitemPojo.setPid("");
     if(busAccountitemPojo.getDirection()==null) busAccountitemPojo.setDirection("");
     if(busAccountitemPojo.getModulecode()==null) busAccountitemPojo.setModulecode("");
     if(busAccountitemPojo.getBilltype()==null) busAccountitemPojo.setBilltype("");
     if(busAccountitemPojo.getBilldate()==null) busAccountitemPojo.setBilldate(new Date());
     if(busAccountitemPojo.getBilltitle()==null) busAccountitemPojo.setBilltitle("");
     if(busAccountitemPojo.getBilluid()==null) busAccountitemPojo.setBilluid("");
     if(busAccountitemPojo.getBillid()==null) busAccountitemPojo.setBillid("");
     if(busAccountitemPojo.getOpenamount()==null) busAccountitemPojo.setOpenamount(0D);
     if(busAccountitemPojo.getInamount()==null) busAccountitemPojo.setInamount(0D);
     if(busAccountitemPojo.getOutamount()==null) busAccountitemPojo.setOutamount(0D);
     if(busAccountitemPojo.getCloseamount()==null) busAccountitemPojo.setCloseamount(0D);
     if(busAccountitemPojo.getRownum()==null) busAccountitemPojo.setRownum(0);
     if(busAccountitemPojo.getRemark()==null) busAccountitemPojo.setRemark("");
     if(busAccountitemPojo.getCustom1()==null) busAccountitemPojo.setCustom1("");
     if(busAccountitemPojo.getCustom2()==null) busAccountitemPojo.setCustom2("");
     if(busAccountitemPojo.getCustom3()==null) busAccountitemPojo.setCustom3("");
     if(busAccountitemPojo.getCustom4()==null) busAccountitemPojo.setCustom4("");
     if(busAccountitemPojo.getCustom5()==null) busAccountitemPojo.setCustom5("");
     if(busAccountitemPojo.getCustom6()==null) busAccountitemPojo.setCustom6("");
     if(busAccountitemPojo.getCustom7()==null) busAccountitemPojo.setCustom7("");
     if(busAccountitemPojo.getCustom8()==null) busAccountitemPojo.setCustom8("");
     if(busAccountitemPojo.getCustom9()==null) busAccountitemPojo.setCustom9("");
     if(busAccountitemPojo.getCustom10()==null) busAccountitemPojo.setCustom10("");
     if(busAccountitemPojo.getTenantid()==null) busAccountitemPojo.setTenantid("");
     if(busAccountitemPojo.getRevision()==null) busAccountitemPojo.setRevision(0);
     return busAccountitemPojo;
     }
}
