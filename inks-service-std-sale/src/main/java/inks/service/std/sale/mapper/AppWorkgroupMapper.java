package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.AppWorkgroupEntity;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 往来单位(AppWorkgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 08:26:35
 */
@Mapper
public interface AppWorkgroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    AppWorkgroupPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<AppWorkgroupPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param appWorkgroupEntity 实例对象
     * @return 影响行数
     */
    int insert(AppWorkgroupEntity appWorkgroupEntity);

    
    /**
     * 修改数据
     *
     * @param appWorkgroupEntity 实例对象
     * @return 影响行数
     */
    int update(AppWorkgroupEntity appWorkgroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    // 查询往来单位是否被引用
    List<String>  getCiteBillName(@Param("key") String key,@Param("tid") String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    AppWorkgroupPojo getEntityByUid(AppWorkgroupPojo appWorkgroupPojo);


    /**
     * 通过ID查询单条数据
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    AppWorkgroupPojo getEntityByName(AppWorkgroupPojo appWorkgroupPojo);


    /**
     * 通过ID查询单条数据
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    String getMaxCode(AppWorkgroupPojo appWorkgroupPojo);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<AppWorkgroupPojo> getPageListBySale(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<AppWorkgroupPojo> getPageListByRece(QueryParam queryParam);
    //刷新销售订单结余额
    int updateWorkgroupBusMachRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新销售发货结余额
    int updateWorkgroupBusDeliRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新销售发票结余额
    int updateWorkgroupBusInvoRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新销售结转期末额
    int updateWorkgroupBusAccoCloseAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新销售结转本期额
    int updateWorkgroupBusAccoNowAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购订单结余额
    int updateWorkgroupBuyOrderRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购收货结余额
    int updateWorkgroupBuyFiniRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购发票结余额
    int updateWorkgroupBuyInvoRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购结转期末额
    int updateWorkgroupBuyAccoCloseAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购结转本期额
    int updateWorkgroupBuyAccoNowAmt(@Param("key")String key,@Param("tid")String tid);

    List<String> getProvOrCity(@Param("province")String province, @Param("tid")String tid);

    Integer deleteBusMachiningByGroupId(@Param("groupid") String key, @Param("tid") String tid);

    Integer deleteBusDelieryByGroupId(@Param("groupid") String key, @Param("tid") String tid);

    Integer deleteBuyPlanItemByGroupId(@Param("groupid") String key, @Param("tid") String tid);

    Integer deleteBuyOrderByGroupId(@Param("groupid") String key, @Param("tid") String tid);

    Integer deleteBuyFinishingByGroupId(@Param("groupid") String key, @Param("tid") String tid);

    Integer deleteWkWorksheetByGroupId(@Param("groupid") String key, @Param("tid") String tid);

    Integer deleteMatAccessByGroupId(@Param("groupid") String key, @Param("tid") String tid);

    Integer deleteWkSubcontractByGroupId(@Param("groupid") String key, @Param("tid") String tid);


    Integer deleteBusOrderCostByGroupId(@Param("groupid") String key, @Param("tid") String tid);

    List<String> getGroupIdsBySeller(@Param("seller") String seller, @Param("tid") String tid);

    List<AppWorkgroupPojo> getList(String groupType, String tid);

    List<Map<String,Object>> getListAbbr(String groupType, String tid);

    int changeDeptid(String key, String deptid,String tid);
}

