package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDelicarryoverPojo;
import inks.service.std.sale.domain.BusDelicarryoverEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 货品账单:订单>发货(Bus_DeliCarryover)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-10 12:50:16
 */
@Mapper
public interface BusDelicarryoverMapper {

    BusDelicarryoverPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusDelicarryoverPojo> getPageList(QueryParam queryParam);

    int insert(BusDelicarryoverEntity busDelicarryoverEntity);

    int update(BusDelicarryoverEntity busDelicarryoverEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

