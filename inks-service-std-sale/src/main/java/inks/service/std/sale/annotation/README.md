# RefNo AOP 注解使用说明

## 概述

RefNo AOP 注解系统提供了自动化的单据编码生成和清理功能，通过AOP切面技术，简化了Controller中的重复代码。

## 注解说明

### @RefNoGeneration - 单据编码生成注解

用于标记需要自动生成单据编码的方法，通常用于create/insert操作。

#### 属性说明

- `billType`: 单据类型，如 "Bus_Receipt", "Bus_Deliery" 等（必填）
- `field`: 编码字段名，默认为 "refno"
- `customPrefix`: 自定义前缀，可选
- `saveToRedis`: 是否保存到Redis缓存，默认为true
- `paramIndex`: 参数索引，指定业务对象在方法参数中的位置，默认为0
- `paramType`: 参数类型，支持JSON_STRING和POJO两种类型

#### 使用示例

```java
@RefNoGeneration(billType = "Bus_Receipt", field = "refno", paramType = RefNoGeneration.ParamType.JSON_STRING)
public R<BusReceiptPojo> create(@RequestBody String json) {
    // 编码会自动生成并设置到解析后的对象中
    BusReceiptPojo pojo = JSONArray.parseObject(json, BusReceiptPojo.class);
    // 业务逻辑...
    return R.ok(service.insert(pojo));
}
```

### @RefNoCleanup - 单据编码清理注解

用于标记需要清理Redis中单据编码缓存的方法，通常用于delete操作。

#### 属性说明

- `afterSuccess`: 是否在方法执行成功后清理，默认为true
- `ignoreException`: 是否忽略清理异常，默认为true

#### 使用示例

```java
@RefNoCleanup(afterSuccess = true, ignoreException = true)
public R<Integer> delete(String key) {
    // 业务删除逻辑
    String refno = service.delete(key, tenantId);
    // Redis缓存会自动清理
    return R.ok(1, "删除成功");
}
```

## 工作原理

### 编码生成流程

1. AOP切面拦截标记了@RefNoGeneration的方法
2. 自动获取登录用户信息和模块代码
3. 调用RefNoUtils.generateRefNo()生成编码
4. 通过反射将编码设置到业务对象中
5. 保存编码到Redis缓存（如果启用）

### 编码清理流程

1. AOP切面拦截标记了@RefNoCleanup的方法
2. 根据配置在方法执行前或后进行清理
3. 自动获取模块代码和租户信息
4. 调用RefNoUtils.deleteRedisRefNo()清理缓存

### 模块代码提取

AOP切面会自动从Controller中提取模块代码，支持以下方式：

1. 从Controller类的`moduleCode`字段获取
2. 从Controller类名中提取（如D01M06B1Controller -> D01M06B1）

## 迁移指南

### 原有代码

```java
public R<BusReceiptPojo> create(@RequestBody String json) {
    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    BusReceiptPojo pojo = JSONArray.parseObject(json, BusReceiptPojo.class);
    
    // 手动生成编码
    String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Receipt", null, loginUser.getTenantid());
    pojo.setRefno(refno);
    
    BusReceiptPojo result = service.insert(pojo);
    
    // 手动保存缓存
    RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
    
    return R.ok(result);
}
```

### 使用注解后

```java
@RefNoGeneration(billType = "Bus_Receipt", field = "refno", paramType = RefNoGeneration.ParamType.JSON_STRING)
public R<BusReceiptPojo> create(@RequestBody String json) {
    BusReceiptPojo pojo = JSONArray.parseObject(json, BusReceiptPojo.class);
    // 编码自动生成和缓存保存
    BusReceiptPojo result = service.insert(pojo);
    return R.ok(result);
}
```

## 注意事项

1. **参数类型匹配**: 确保paramType与实际参数类型匹配
2. **字段名正确**: field属性必须与业务对象的字段名一致
3. **模块代码**: 确保Controller有moduleCode字段或类名符合命名规范
4. **异常处理**: 编码生成失败会抛出RuntimeException，需要适当处理
5. **性能考虑**: AOP会增加少量性能开销，但可以忽略不计

## 配置要求

确保在配置类中启用了AOP：

```java
@Configuration
@EnableAspectJAutoProxy
public class SaleAutoConfiguration {
    // 配置内容
}
```

## 支持的参数类型

- `JSON_STRING`: JSON字符串参数，会自动解析为JSONObject
- `POJO`: 直接的业务对象参数

## 错误处理

- 编码生成失败会抛出RuntimeException
- 清理操作失败默认会被忽略（可通过ignoreException控制）
- 所有操作都有详细的日志记录
