package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemPojo;
import inks.service.std.sale.service.AppWorkgroupService;
import inks.service.std.sale.service.BusSteppriceService;
import inks.service.std.sale.service.BusSteppriceitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 阶梯单价(Bus_StepPrice)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-31 21:21:51
 */
@RestController
@RequestMapping("D01M15B1")
@Api(tags = "D01M15B1:阶梯单价")
public class D01M15B1Controller extends BusSteppriceController {

    /**
     * 服务对象
     */
    @Resource
    private BusSteppriceService busSteppriceService;

    /**
     * 服务对象Item
     */
    @Resource
    private BusSteppriceitemService busSteppriceitemService;

    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取阶梯单价", notes = "获取阶梯单价,json:{goodsid,groupid,qty}", produces = "application/json")
    @RequestMapping(value = "/getStepPrice", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_StepPrice.List")
    public R<Double> getStepPrice(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String goodsid = jsonObject.getString("goodsid");
            String groupid = jsonObject.getString("groupid");
            Double qty = jsonObject.getObject("qty", Double.class);

            AppWorkgroupPojo appWorkgroupPojo = this.appWorkgroupService.getEntity(groupid, loginUser.getTenantid());
            if (appWorkgroupPojo != null) {
                // 查询客户专用单价
                BusSteppriceitemPojo busSteppriceitemPojo = this.busSteppriceService.getStepPriceByGroupid(goodsid, groupid, qty, loginUser.getTenantid());
                if (busSteppriceitemPojo != null) {
                    return R.ok(busSteppriceitemPojo.getPrice());
                } else {
                    // 查询客户等级单价
                    busSteppriceitemPojo = this.busSteppriceService.getStepPriceByGroupLevel(goodsid, appWorkgroupPojo.getGrouplevel(), qty, loginUser.getTenantid());
                    if (busSteppriceitemPojo != null) {
                        return R.ok(busSteppriceitemPojo.getPrice());
                    } else {
                        return R.ok(null);
                    }
                }
            } else {
                return R.fail("未找到客户信息");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
