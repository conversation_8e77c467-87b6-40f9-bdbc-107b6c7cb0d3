package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusSteppricePojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemPojo;
import inks.service.std.sale.domain.pojo.BusSteppriceitemdetailPojo;
import inks.service.std.sale.service.BusSteppriceService;
import inks.service.std.sale.service.BusSteppriceitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 阶梯单价(BusStepprice)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-02 08:47:05
 */

public class BusSteppriceController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BusSteppriceController.class);
    private final String moduleCode = "D01M15B1";
    /**
     * 服务对象
     */
    @Resource
    private BusSteppriceService busSteppriceService;
    /**
     * 服务对象Item
     */
    @Resource
    private BusSteppriceitemService busSteppriceitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private UtilsFeignService utilsFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取阶梯单价详细信息", notes = "获取阶梯单价详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_StepPrice.List")
    public R<BusSteppricePojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busSteppriceService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_StepPrice.List")
    public R<PageInfo<BusSteppriceitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_StepPrice.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busSteppriceService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取阶梯单价详细信息", notes = "获取阶梯单价详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_StepPrice.List")
    public R<BusSteppricePojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busSteppriceService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_StepPrice.List")
    public R<PageInfo<BusSteppricePojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_StepPrice.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busSteppriceService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_StepPrice.List")
    public R<PageInfo<BusSteppricePojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_StepPrice.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busSteppriceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增阶梯单价", notes = "新增阶梯单价", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_StepPrice.Add")
    public R<BusSteppricePojo> create(@RequestBody String json) {
        try {
            BusSteppricePojo busSteppricePojo = JSONArray.parseObject(json, BusSteppricePojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_StepPrice", null, loginUser.getTenantid());
            busSteppricePojo.setRefno(refno);
            busSteppricePojo.setCreateby(loginUser.getRealName());   // 创建者
            busSteppricePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busSteppricePojo.setCreatedate(new Date());   // 创建时间
            busSteppricePojo.setLister(loginUser.getRealname());   // 制表
            busSteppricePojo.setListerid(loginUser.getUserid());    // 制表id            
            busSteppricePojo.setModifydate(new Date());   //修改时间
            busSteppricePojo.setTenantid(loginUser.getTenantid());   //租户id
            BusSteppricePojo insert = this.busSteppriceService.insert(busSteppricePojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());// 保存单据编码RefNoUtils
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改阶梯单价", notes = "修改阶梯单价", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_StepPrice.Edit")
    public R<BusSteppricePojo> update(@RequestBody String json) {
        try {
            BusSteppricePojo busSteppricePojo = JSONArray.parseObject(json, BusSteppricePojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busSteppricePojo.setLister(loginUser.getRealname());   // 制表
            busSteppricePojo.setListerid(loginUser.getUserid());    // 制表id   
            busSteppricePojo.setModifydate(new Date());   //修改时间
            busSteppricePojo.setAssessor(""); //审核员
            busSteppricePojo.setAssessorid(""); //审核员
            busSteppricePojo.setAssessdate(new Date()); //审核时间
            busSteppricePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busSteppriceService.update(busSteppricePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除阶梯单价", notes = "删除阶梯单价", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_StepPrice.Delete")
    @OperLog(title = "删除阶梯单价")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String refno = this.busSteppriceService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增阶梯单价Item", notes = "新增阶梯单价Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_StepPrice.Add")
    public R<BusSteppriceitemPojo> createItem(@RequestBody String json) {
        try {
            BusSteppriceitemPojo busSteppriceitemPojo = JSONArray.parseObject(json, BusSteppriceitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busSteppriceitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busSteppriceitemService.insert(busSteppriceitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除阶梯单价Item", notes = "删除阶梯单价Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_StepPrice.Delete")
    public R<Integer> deleteItem(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busSteppriceitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核阶梯单价", notes = "审核阶梯单价", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_StepPrice.Approval")
    public R<BusSteppricePojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusSteppricePojo busSteppricePojo = this.busSteppriceService.getEntity(key, loginUser.getTenantid());
            if (busSteppricePojo.getAssessor().equals("")) {
                busSteppricePojo.setAssessor(loginUser.getRealname()); //审核员
                busSteppricePojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busSteppricePojo.setAssessor(""); //审核员
                busSteppricePojo.setAssessorid(""); //审核员
            }
            busSteppricePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busSteppriceService.approval(busSteppricePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type) {
        try {
            if (type == null) type = "wxe";  // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
            //获取token
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //创建VM数据对象
            VelocityContext context = new VelocityContext();
            //从redis中获取模板对象
            // Object obj = redisService.getCacheObject(verifyKey);
            ApprrecPojo apprrecPojo = redisService.getCacheObject(verifyKey);
            ApprovePojo approvePojo = new ApprovePojo();
            //获得第三方账号
            R rja = systemFeignService.getJustauthByUserid(loginUser.getUserid(), type, loginUser.getTenantid());
            JustauthPojo justauthPojo = new JustauthPojo();
            if (rja.getCode() == 200) {
                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
            } else {
                return R.fail("获得第三方账号出错" + rja.getMsg());
            }
            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
            approvePojo.setUserid(justauthPojo.getAuthuuid());
            approvePojo.setModelcode(apprrecPojo.getTemplateid());
            approvePojo.setObject(this.busSteppriceService.getBillEntity(key, loginUser.getTenantid()));
            context.put("approvePojo", approvePojo);
            String str = apprrecPojo.getDatatemp();
            // 初始化并取得Velocity引擎
            VelocityEngine ve = new VelocityEngine();
            ve.init();
            // 转换输出
            StringWriter writer = new StringWriter();
            ve.evaluate(context, writer, "", str); // 关键方法
            //写回String
            str = writer.toString();
            //新建审批记录
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            apprrecPojo.setDatatemp(str);
            apprrecPojo.setApprname("订单审批");
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key);    // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(new Date());
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(new Date());
            apprrecPojo.setTenantid(loginUser.getTenantid());
            //将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            redisService.setCacheObject(CachKey, apprrecPojo, (long) (60 * 12), TimeUnit.MINUTES);
            if ("wxe".equals(type)) {
                R r = this.utilsFeignService.wxeapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r);
                }
            } else {
                R r = this.utilsFeignService.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r);
                }
            }
            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R<BusSteppricePojo> justapprovel(String key, String type) {
        try {
            System.out.println("审核通过,写入审核信息");
            //1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = redisService.getCacheObject(verifyKey);
            //2. 获得单据数据
            BusSteppricePojo busSteppricePojo = this.busSteppriceService.getEntity(apprrecPojo.getBillid(), apprrecPojo.getTenantid());
            //3. 写入审核批
            //获得第三方账号
            if (type == null) type = "wxe";
            R rja = systemFeignService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, apprrecPojo.getTenantid());
            JustauthPojo justauthPojo = new JustauthPojo();
            if (rja.getCode() == 200) {
                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
            } else {
                System.out.println("写入审核:获得第三方账号出错：" + rja.getMsg());
                return R.fail("获得第三方账号出错" + rja.getMsg());
            }
            busSteppricePojo.setAssessorid(justauthPojo.getUserid());
            busSteppricePojo.setAssessor(justauthPojo.getRealname()); //审核员
            busSteppricePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busSteppriceService.approval(busSteppricePojo));
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_StepPrice.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusSteppricePojo busSteppricePojo = this.busSteppriceService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busSteppricePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busSteppricePojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusSteppriceitemPojo busSteppriceitemPojo = new BusSteppriceitemPojo();
                    busSteppricePojo.getItem().add(busSteppriceitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(busSteppricePojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

