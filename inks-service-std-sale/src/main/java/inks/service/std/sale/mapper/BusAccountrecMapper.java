package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusAccountrecEntity;
import inks.service.std.sale.domain.pojo.BusAccountrecPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 结转记录(BusAccountrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-25 08:28:53
 */
@Mapper
public interface BusAccountrecMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountrecPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusAccountrecPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param busAccountrecEntity 实例对象
     * @return 影响行数
     */
    int insert(BusAccountrecEntity busAccountrecEntity);


    /**
     * 修改数据
     *
     * @param busAccountrecEntity 实例对象
     * @return 影响行数
     */
    int update(BusAccountrecEntity busAccountrecEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    BusAccountrecPojo getEntityByMax(@Param("tid") String tid);


    BusAccountrecPojo getEntityByYearMonth(Integer carryYear, Integer carryMonth, String tid);
}

