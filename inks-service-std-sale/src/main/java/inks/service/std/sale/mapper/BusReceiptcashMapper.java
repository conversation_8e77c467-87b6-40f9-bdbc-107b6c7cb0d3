package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusReceiptcashEntity;
import inks.service.std.sale.domain.pojo.BusReceiptcashPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 现金项目(BusReceiptcash)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-20 13:13:44
 */
 @Mapper
public interface BusReceiptcashMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceiptcashPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusReceiptcashPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusReceiptcashPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busReceiptcashEntity 实例对象
     * @return 影响行数
     */
    int insert(BusReceiptcashEntity busReceiptcashEntity);

    
    /**
     * 修改数据
     *
     * @param busReceiptcashEntity 实例对象
     * @return 影响行数
     */
    int update(BusReceiptcashEntity busReceiptcashEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

