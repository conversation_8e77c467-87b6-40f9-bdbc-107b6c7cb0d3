package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusDeliplanPojo;
import inks.service.std.sale.domain.pojo.BusDeliplanitemPojo;
import inks.service.std.sale.domain.pojo.BusDeliplanitemdetailPojo;
import inks.service.std.sale.service.BusDeliplanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 发货计划(Bus_DeliPlan)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:04
 */
@RestController
@RequestMapping("D01M07B1")
@Api(tags = "D01M07B1:发货计划")
public class D01M07B1Controller extends BusDeliplanController {

    @Resource
    private BusDeliplanService busDeliplanService;

    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.List")
    public R<PageInfo<BusDeliplanPojo>> getOnlinePageTh(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliPlan.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "and Bus_DeliPlan.FinishCount+Bus_DeliPlan.DisannulCount<Bus_DeliPlan.ItemCount";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDeliplanService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "【包括前3天、今天、明天和后天】按条件分页查询发货计划单的待发货明细", notes = "按条件分页查询待发货明细 ？groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDeliPage3Day", method = RequestMethod.POST)
    public R<PageInfo<Map<String, Object>>> getOnlineDeliPage3Day(@RequestBody(required = false) String json, String groupid, @RequestParam(required = false) Integer day) {
        LoginUser loginUser = tokenService.getLoginUser();
        try {
            QueryParam queryParam;
            if (json == null) {
                queryParam = new QueryParam();
            } else {
                queryParam = JSONArray.parseObject(json, QueryParam.class);
            }
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliPlan.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_DeliPlanItem.FinishQty<Bus_DeliPlanItem.Quantity";
            qpfilter += " and Bus_DeliPlanItem.DisannulMark=0 and Bus_DeliPlanItem.Closed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_DeliPlan.Assessorid<>''";  // 已审核

            if (day != null) {
                // 当前时间 至 当前时间加上day天之间
                qpfilter += " and DATE(Bus_Machining.BillPlanDate) between CURDATE() and DATE_ADD(CURDATE(), INTERVAL " + day + " DAY)";
            } else {
                //qpfilter += " and DATE(Bus_DeliPlan.BillPlanDate) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 2 DAY)";// 包括今天、明天和后天
                // 包括前3天、今天、明天和后天
                qpfilter += " and DATE(Bus_DeliPlan.BillDate) BETWEEN DATE_ADD(CURDATE(), INTERVAL -3 DAY) AND DATE_ADD(CURDATE(), INTERVAL 2 DAY)";
            }
            if (groupid != null) {
                qpfilter += " and Bus_DeliPlan.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            PageInfo<BusDeliplanitemdetailPojo> onlinePageListPageInfo = this.busDeliplanService.getPageList(queryParam);
            // ============对象转Map,并拆解spu,塞回PageInfo============
            List<BusDeliplanitemdetailPojo> list = onlinePageListPageInfo.getList();
            // 单据Item. 带属性List转为Map
            List<Map<String, Object>> lst = attrListToMaps(list);
            // 创建新的 PageInfo 对象
            PageInfo<Map<String, Object>> newPageInfo = new PageInfo<>();
            // 复制原 PageInfo 的属性到新的 PageInfo 对象
            BeanUtils.copyProperties(onlinePageListPageInfo, newPageInfo);
            // 设置转换后的列表为新 PageInfo 的内容
            newPageInfo.setList(lst);
            return R.ok(newPageInfo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.List")
    public R<PageInfo<BusDeliplanitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliPlan.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_DeliPlanItem.FinishQty<Bus_DeliPlanItem.Quantity";
            qpfilter += " and Bus_DeliPlanItem.DisannulMark=0 and Bus_DeliPlanItem.Closed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Bus_DeliPlan.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDeliplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "getOnlinePageList基础上，接口加入AttributeJson字段展开，itemplandate日期格式化；根据MachItemid查订单明细wkwpname字段，", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageListByMach", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.List")
    public R<PageInfo<Map<String, Object>>> getOnlinePageListByMach(@RequestBody String json, String groupid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliPlan.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "  and Bus_DeliPlanItem.FinishQty<Bus_DeliPlanItem.Quantity";
            qpfilter += " and Bus_DeliPlanItem.DisannulMark=0 and Bus_DeliPlanItem.Closed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Bus_DeliPlan.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDeliplanService.getPageListByMach(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "作废发货计划", notes = "作废发货计划,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Edit")
    public R<BusDeliplanPojo> disannul(@RequestBody String json, Integer type) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (type == null) type = 1;
            List<BusDeliplanitemPojo> lst = JSONArray.parseArray(json, BusDeliplanitemPojo.class);

            //  BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(lst.get(0).getPid(), loginUser.getTenantid());

            return R.ok(this.busDeliplanService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "中止发货计划", notes = "中止发货计划,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Edit")
    public R<BusDeliplanPojo> closed(@RequestBody String json, Integer type) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (type == null) type = 1;
            List<BusDeliplanitemPojo> lst = JSONArray.parseArray(json, BusDeliplanitemPojo.class);

            return R.ok(this.busDeliplanService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
