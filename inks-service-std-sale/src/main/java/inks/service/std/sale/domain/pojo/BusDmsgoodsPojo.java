package inks.service.std.sale.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * DMS商品(BusDmsgoods)实体类
 *
 * <AUTHOR>
 * @since 2024-01-17 14:30:52
 */
public class BusDmsgoodsPojo implements Serializable {
    private static final long serialVersionUID = 592684431906314342L;
     // 产品ID
    @Excel(name = "产品ID") 
    private String id;
     // 货品编码
    @Excel(name = "货品编码") 
    private String goodsuid;
     // 商品名称
    @Excel(name = "商品名称") 
    private String goodsname;
     // 规格
    @Excel(name = "规格") 
    private String goodsspec;
     // 货品单位
    @Excel(name = "货品单位") 
    private String goodsunit;
     // 原价
    @Excel(name = "原价") 
    private Double oriprice;
     // 现价
    @Excel(name = "现价") 
    private Double price;
     // 结构
    @Excel(name = "结构") 
    private String structure;
     // 材料
    @Excel(name = "材料") 
    private String material;
     // 指数
    @Excel(name = "指数") 
    private Integer exponent;
     // 生产周期
    @Excel(name = "生产周期") 
    private String prodcycle;
     // 工艺
    @Excel(name = "工艺") 
    private String process;
     // 简要描述,卖点等
    @Excel(name = "简要描述,卖点等") 
    private String brief;
     // 详细描述
    @Excel(name = "详细描述") 
    private String content;
     // 颜色
    @Excel(name = "颜色") 
    private String color;
     // 等级
    @Excel(name = "等级") 
    private String level;
     // 属性Josn
    @Excel(name = "属性Josn") 
    private String attributejson;
     // SPU文本
    @Excel(name = "SPU文本") 
    private String attributestr;
     // 商品主图
    @Excel(name = "商品主图") 
    private String pic;
     // 商品图片，以,分割
    @Excel(name = "商品图片，以,分割") 
    private String imgs;
     // 默认是1，表示正常状态, -1表示删除, 0下架
    @Excel(name = "默认是1，表示正常状态, -1表示删除, 0下架") 
    private Integer status;
     // 商品分类
    @Excel(name = "商品分类") 
    private String categoryid;
     // 销量
    @Excel(name = "销量") 
    private Integer soldnum;
     // 总库存
    @Excel(name = "总库存") 
    private Integer totalstocks;
     // 配送方式
    @Excel(name = "配送方式") 
    private String deliverymode;
     // 上架时间
    @Excel(name = "上架时间") 
    private Date putawaydate;
     // 是否主产品
    @Excel(name = "是否主产品") 
    private Integer mainmark;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;


   // 产品ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }


    // 货品编码
    public String getGoodsuid() {
        return goodsuid;
    }
    
    public void setGoodsuid(String goodsuid) {
        this.goodsuid = goodsuid;
    }
        
   // 商品名称
    public String getGoodsname() {
        return goodsname;
    }
    
    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }
        
   // 规格
    public String getGoodsspec() {
        return goodsspec;
    }
    
    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }
        
   // 货品单位
    public String getGoodsunit() {
        return goodsunit;
    }
    
    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }
        

   // 原价
    public Double getOriprice() {
        return oriprice;
    }
    
    public void setOriprice(Double oriprice) {
        this.oriprice = oriprice;
    }
        
   // 现价
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
   // 结构
    public String getStructure() {
        return structure;
    }
    
    public void setStructure(String structure) {
        this.structure = structure;
    }
        
   // 材料
    public String getMaterial() {
        return material;
    }
    
    public void setMaterial(String material) {
        this.material = material;
    }
        
   // 指数
    public Integer getExponent() {
        return exponent;
    }
    
    public void setExponent(Integer exponent) {
        this.exponent = exponent;
    }
        
   // 生产周期
    public String getProdcycle() {
        return prodcycle;
    }
    
    public void setProdcycle(String prodcycle) {
        this.prodcycle = prodcycle;
    }
        
   // 工艺
    public String getProcess() {
        return process;
    }
    
    public void setProcess(String process) {
        this.process = process;
    }
        
   // 简要描述,卖点等
    public String getBrief() {
        return brief;
    }
    
    public void setBrief(String brief) {
        this.brief = brief;
    }
        
   // 详细描述
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
        
   // 颜色
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
        
   // 等级
    public String getLevel() {
        return level;
    }
    
    public void setLevel(String level) {
        this.level = level;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // SPU文本
    public String getAttributestr() {
        return attributestr;
    }
    
    public void setAttributestr(String attributestr) {
        this.attributestr = attributestr;
    }
        
   // 商品主图
    public String getPic() {
        return pic;
    }
    
    public void setPic(String pic) {
        this.pic = pic;
    }
        
   // 商品图片，以,分割
    public String getImgs() {
        return imgs;
    }
    
    public void setImgs(String imgs) {
        this.imgs = imgs;
    }
        
   // 默认是1，表示正常状态, -1表示删除, 0下架
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
        
   // 商品分类
    public String getCategoryid() {
        return categoryid;
    }
    
    public void setCategoryid(String categoryid) {
        this.categoryid = categoryid;
    }
        
   // 销量
    public Integer getSoldnum() {
        return soldnum;
    }
    
    public void setSoldnum(Integer soldnum) {
        this.soldnum = soldnum;
    }
        
   // 总库存
    public Integer getTotalstocks() {
        return totalstocks;
    }
    
    public void setTotalstocks(Integer totalstocks) {
        this.totalstocks = totalstocks;
    }
        
   // 配送方式
    public String getDeliverymode() {
        return deliverymode;
    }
    
    public void setDeliverymode(String deliverymode) {
        this.deliverymode = deliverymode;
    }
        
   // 上架时间
    public Date getPutawaydate() {
        return putawaydate;
    }
    
    public void setPutawaydate(Date putawaydate) {
        this.putawaydate = putawaydate;
    }
        
   // 是否主产品
    public Integer getMainmark() {
        return mainmark;
    }
    
    public void setMainmark(Integer mainmark) {
        this.mainmark = mainmark;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

