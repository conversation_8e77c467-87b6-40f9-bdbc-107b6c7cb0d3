package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusAccountarapPojo;

import java.util.List;
/**
 * 发票to收款(BusAccountarap)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-16 11:31:29
 */
public interface BusAccountarapService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountarapPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusAccountarapPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusAccountarapPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busAccountarapPojo 实例对象
     * @return 实例对象
     */
    BusAccountarapPojo insert(BusAccountarapPojo busAccountarapPojo);

    /**
     * 修改数据
     *
     * @param busAccountarappojo 实例对象
     * @return 实例对象
     */
    BusAccountarapPojo update(BusAccountarapPojo busAccountarappojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busAccountarappojo 实例对象
     * @return 实例对象
     */
    BusAccountarapPojo clearNull(BusAccountarapPojo busAccountarappojo);
}
