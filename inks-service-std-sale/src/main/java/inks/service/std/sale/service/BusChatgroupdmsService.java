package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusChatgroupdmsPojo;

import java.util.List;
/**
 * 客服分组DMS用户子表(BusChatgroupdms)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-08 15:49:07
 */
public interface BusChatgroupdmsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusChatgroupdmsPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusChatgroupdmsPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusChatgroupdmsPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busChatgroupdmsPojo 实例对象
     * @return 实例对象
     */
    BusChatgroupdmsPojo insert(BusChatgroupdmsPojo busChatgroupdmsPojo);

    /**
     * 修改数据
     *
     * @param busChatgroupdmspojo 实例对象
     * @return 实例对象
     */
    BusChatgroupdmsPojo update(BusChatgroupdmsPojo busChatgroupdmspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busChatgroupdmspojo 实例对象
     * @return 实例对象
     */
    BusChatgroupdmsPojo clearNull(BusChatgroupdmsPojo busChatgroupdmspojo);
}
