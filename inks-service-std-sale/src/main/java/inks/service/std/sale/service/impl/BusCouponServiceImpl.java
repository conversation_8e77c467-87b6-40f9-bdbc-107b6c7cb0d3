package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusCouponEntity;
import inks.service.std.sale.domain.pojo.BusCouponPojo;
import inks.service.std.sale.mapper.BusCouponMapper;
import inks.service.std.sale.mapper.BusCouponusageMapper;
import inks.service.std.sale.service.BusCouponService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 优惠券表(BusCoupon)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
@Service("busCouponService")
public class BusCouponServiceImpl implements BusCouponService {
    @Resource
    private BusCouponMapper busCouponMapper;
    @Autowired
    private BusCouponusageMapper busCouponusageMapper;
    @Override
    public BusCouponPojo getEntity(String key, String tid) {
        return this.busCouponMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<BusCouponPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCouponPojo> lst = busCouponMapper.getPageList(queryParam);
            PageInfo<BusCouponPojo> pageInfo = new PageInfo<BusCouponPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public BusCouponPojo insert(BusCouponPojo busCouponPojo) {
        //初始化NULL字段
        cleanNull(busCouponPojo);
        BusCouponEntity busCouponEntity = new BusCouponEntity();
        BeanUtils.copyProperties(busCouponPojo, busCouponEntity);
        //生成雪花id
        busCouponEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busCouponEntity.setRevision(1);  //乐观锁
        // 优惠券编码用时间戳 取当前时间的秒数
        busCouponEntity.setCouponcode(String.valueOf(System.currentTimeMillis() / 1000));
        this.busCouponMapper.insert(busCouponEntity);
        return this.getEntity(busCouponEntity.getId(), busCouponEntity.getTenantid());
    }


    @Override
    public BusCouponPojo update(BusCouponPojo busCouponPojo) {
        BusCouponEntity busCouponEntity = new BusCouponEntity();
        BeanUtils.copyProperties(busCouponPojo, busCouponEntity);
        this.busCouponMapper.update(busCouponEntity);
        return this.getEntity(busCouponEntity.getId(), busCouponEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        // 已使用的优惠券禁止删除
        List<String> lstcite = busCouponusageMapper.getItemCiteBillName(key,tid);
        if (!lstcite.isEmpty()) {
            throw new RuntimeException("优惠券已被使用,禁止删除");
        }
        return this.busCouponMapper.delete(key, tid);
    }

    @Override
    @Transactional
    public BusCouponPojo approval(BusCouponPojo busCouponPojo) {
        //主表更改
        BusCouponEntity busCouponEntity = new BusCouponEntity();
        BeanUtils.copyProperties(busCouponPojo, busCouponEntity);
        this.busCouponMapper.approval(busCouponEntity);
        //返回Bill实例
        return this.getEntity(busCouponEntity.getId(), busCouponEntity.getTenantid());
    }

    @Override
    public BusCouponPojo disannul(String key, Integer type, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        BusCouponPojo dbPojo = this.busCouponMapper.getEntity(key, tid);
        if (dbPojo != null) {
            if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                BusCouponEntity entity = new BusCouponEntity();
                entity.setId(dbPojo.getId());
                entity.setDisannulmark(type);
                entity.setDisannuldate(new Date());
                entity.setDisannulby(loginUser.getRealname());
                entity.setDisannulbyid(loginUser.getUserid());
                this.busCouponMapper.update(entity);
            }
            // TODO 检查优惠券是否被使用 禁止作废
        }
        return busCouponMapper.getEntity(key, tid);
    }

    private static void cleanNull(BusCouponPojo busCouponPojo) {
        if (busCouponPojo.getGroupid() == null) busCouponPojo.setGroupid("");
        if (busCouponPojo.getCouponname() == null) busCouponPojo.setCouponname("");
        if (busCouponPojo.getCouponcode() == null) busCouponPojo.setCouponcode("");
        if (busCouponPojo.getCoupontype() == null) busCouponPojo.setCoupontype(0);
        if (busCouponPojo.getCouponamount() == null) busCouponPojo.setCouponamount(0D);
        if (busCouponPojo.getActiveamount() == null) busCouponPojo.setActiveamount(0D);
        if (busCouponPojo.getUsedamount() == null) busCouponPojo.setUsedamount(0D);
        if (busCouponPojo.getUselimitrate() == null) busCouponPojo.setUselimitrate(0D);
        if (busCouponPojo.getGoodsid() == null) busCouponPojo.setGoodsid("");
        if (busCouponPojo.getItemcode() == null) busCouponPojo.setItemcode("");
        if (busCouponPojo.getItemname() == null) busCouponPojo.setItemname("");
        if (busCouponPojo.getItemspec() == null) busCouponPojo.setItemspec("");
        if (busCouponPojo.getItemunit() == null) busCouponPojo.setItemunit("");
        if (busCouponPojo.getRemark() == null) busCouponPojo.setRemark("");
        if (busCouponPojo.getRownum() == null) busCouponPojo.setRownum(0);
        if (busCouponPojo.getDisannulmark() == null) busCouponPojo.setDisannulmark(0);
        if (busCouponPojo.getDisannulby() == null) busCouponPojo.setDisannulby("");
        if (busCouponPojo.getDisannulbyid() == null) busCouponPojo.setDisannulbyid("");
        if (busCouponPojo.getDisannuldate() == null) busCouponPojo.setDisannuldate(new Date());
        if (busCouponPojo.getAssessor() == null) busCouponPojo.setAssessor("");
        if (busCouponPojo.getAssessorid() == null) busCouponPojo.setAssessorid("");
        if (busCouponPojo.getAssessdate() == null) busCouponPojo.setAssessdate(new Date());
        if (busCouponPojo.getCreateby() == null) busCouponPojo.setCreateby("");
        if (busCouponPojo.getCreatebyid() == null) busCouponPojo.setCreatebyid("");
        if (busCouponPojo.getCreatedate() == null) busCouponPojo.setCreatedate(new Date());
        if (busCouponPojo.getLister() == null) busCouponPojo.setLister("");
        if (busCouponPojo.getListerid() == null) busCouponPojo.setListerid("");
        if (busCouponPojo.getModifydate() == null) busCouponPojo.setModifydate(new Date());
        if (busCouponPojo.getCustom1() == null) busCouponPojo.setCustom1("");
        if (busCouponPojo.getCustom2() == null) busCouponPojo.setCustom2("");
        if (busCouponPojo.getCustom3() == null) busCouponPojo.setCustom3("");
        if (busCouponPojo.getCustom4() == null) busCouponPojo.setCustom4("");
        if (busCouponPojo.getCustom5() == null) busCouponPojo.setCustom5("");
        if (busCouponPojo.getCustom6() == null) busCouponPojo.setCustom6("");
        if (busCouponPojo.getCustom7() == null) busCouponPojo.setCustom7("");
        if (busCouponPojo.getCustom8() == null) busCouponPojo.setCustom8("");
        if (busCouponPojo.getCustom9() == null) busCouponPojo.setCustom9("");
        if (busCouponPojo.getCustom10() == null) busCouponPojo.setCustom10("");
        if (busCouponPojo.getTenantid() == null) busCouponPojo.setTenantid("");
        if (busCouponPojo.getTenantname() == null) busCouponPojo.setTenantname("");
        if (busCouponPojo.getRevision() == null) busCouponPojo.setRevision(0);
    }

}
