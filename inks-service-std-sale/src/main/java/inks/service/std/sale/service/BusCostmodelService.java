package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCostmodelPojo;
import inks.service.std.sale.domain.pojo.BusCostmodelitemdetailPojo;

/**
 * 预算模型(BusCostmodel)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-07 13:30:12
 */
public interface BusCostmodelService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostmodelPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusCostmodelitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostmodelPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusCostmodelPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusCostmodelPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busCostmodelPojo 实例对象
     * @return 实例对象
     */
    BusCostmodelPojo insert(BusCostmodelPojo busCostmodelPojo);

    /**
     * 修改数据
     *
     * @param busCostmodelpojo 实例对象
     * @return 实例对象
     */
    BusCostmodelPojo update(BusCostmodelPojo busCostmodelpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    BusCostmodelPojo getDefBillEntity(String tid);

}
