package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusBudgetEntity;
import inks.service.std.sale.domain.BusBudgetitemEntity;
import inks.service.std.sale.domain.pojo.BusBudgetPojo;
import inks.service.std.sale.domain.pojo.BusBudgetitemPojo;
import inks.service.std.sale.domain.pojo.BusBudgetitemdetailPojo;
import inks.service.std.sale.mapper.BusBudgetMapper;
import inks.service.std.sale.mapper.BusBudgetitemMapper;
import inks.service.std.sale.mapper.BusMachiningitemMapper;
import inks.service.std.sale.service.BusBudgetService;
import inks.service.std.sale.service.BusBudgetitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 成本预算(BusBudget)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-02 09:26:57
 */
@Service("busBudgetService")
public class BusBudgetServiceImpl implements BusBudgetService {
    @Resource
    private BusBudgetMapper busBudgetMapper;

    @Resource
    private BusBudgetitemMapper busBudgetitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusBudgetitemService busBudgetitemService;
    @Resource
    private BusMachiningitemMapper busMachiningitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusBudgetPojo getEntity(String key, String tid) {
        return this.busBudgetMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusBudgetitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusBudgetitemdetailPojo> lst = busBudgetMapper.getPageList(queryParam);
            PageInfo<BusBudgetitemdetailPojo> pageInfo = new PageInfo<BusBudgetitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusBudgetPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusBudgetPojo busBudgetPojo = this.busBudgetMapper.getEntity(key, tid);
            //读取子表
            busBudgetPojo.setItem(busBudgetitemMapper.getList(busBudgetPojo.getId(), busBudgetPojo.getTenantid()));
            return busBudgetPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusBudgetPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusBudgetPojo> lst = busBudgetMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busBudgetitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusBudgetPojo> pageInfo = new PageInfo<BusBudgetPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusBudgetPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusBudgetPojo> lst = busBudgetMapper.getPageTh(queryParam);
            PageInfo<BusBudgetPojo> pageInfo = new PageInfo<BusBudgetPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busBudgetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusBudgetPojo insert(BusBudgetPojo busBudgetPojo) {
//初始化NULL字段
        if (busBudgetPojo.getRefno() == null) busBudgetPojo.setRefno("");
        if (busBudgetPojo.getBilltype() == null) busBudgetPojo.setBilltype("");
        if (busBudgetPojo.getBilldate() == null) busBudgetPojo.setBilldate(new Date());
        if (busBudgetPojo.getBilltitle() == null) busBudgetPojo.setBilltitle("");
        if (busBudgetPojo.getMachbillid() == null) busBudgetPojo.setMachbillid("");
        if (busBudgetPojo.getMachbillcode() == null) busBudgetPojo.setMachbillcode("");
        if (busBudgetPojo.getGroupid() == null) busBudgetPojo.setGroupid("");
        if (busBudgetPojo.getWorkshop() == null) busBudgetPojo.setWorkshop("");
        if (busBudgetPojo.getBillmatcost() == null) busBudgetPojo.setBillmatcost(0D);
        if (busBudgetPojo.getBilllaborcost() == null) busBudgetPojo.setBilllaborcost(0D);
        if (busBudgetPojo.getBilldirectcost() == null) busBudgetPojo.setBilldirectcost(0D);
        if (busBudgetPojo.getBillindirectcost() == null) busBudgetPojo.setBillindirectcost(0D);
        if (busBudgetPojo.getBillmatamt() == null) busBudgetPojo.setBillmatamt("");
        if (busBudgetPojo.getBilllaboramt() == null) busBudgetPojo.setBilllaboramt(0D);
        if (busBudgetPojo.getBilldirectamt() == null) busBudgetPojo.setBilldirectamt(0D);
        if (busBudgetPojo.getBillindirectamt() == null) busBudgetPojo.setBillindirectamt(0D);
        if (busBudgetPojo.getOperator() == null) busBudgetPojo.setOperator("");
        if (busBudgetPojo.getSummary() == null) busBudgetPojo.setSummary("");
        if (busBudgetPojo.getCreateby() == null) busBudgetPojo.setCreateby("");
        if (busBudgetPojo.getCreatebyid() == null) busBudgetPojo.setCreatebyid("");
        if (busBudgetPojo.getCreatedate() == null) busBudgetPojo.setCreatedate(new Date());
        if (busBudgetPojo.getLister() == null) busBudgetPojo.setLister("");
        if (busBudgetPojo.getListerid() == null) busBudgetPojo.setListerid("");
        if (busBudgetPojo.getModifydate() == null) busBudgetPojo.setModifydate(new Date());
        if (busBudgetPojo.getAssessor() == null) busBudgetPojo.setAssessor("");
        if (busBudgetPojo.getAssessorid() == null) busBudgetPojo.setAssessorid("");
        if (busBudgetPojo.getAssessdate() == null) busBudgetPojo.setAssessdate(new Date());
        if (busBudgetPojo.getCustom1() == null) busBudgetPojo.setCustom1("");
        if (busBudgetPojo.getCustom2() == null) busBudgetPojo.setCustom2("");
        if (busBudgetPojo.getCustom3() == null) busBudgetPojo.setCustom3("");
        if (busBudgetPojo.getCustom4() == null) busBudgetPojo.setCustom4("");
        if (busBudgetPojo.getCustom5() == null) busBudgetPojo.setCustom5("");
        if (busBudgetPojo.getCustom6() == null) busBudgetPojo.setCustom6("");
        if (busBudgetPojo.getCustom7() == null) busBudgetPojo.setCustom7("");
        if (busBudgetPojo.getCustom8() == null) busBudgetPojo.setCustom8("");
        if (busBudgetPojo.getCustom9() == null) busBudgetPojo.setCustom9("");
        if (busBudgetPojo.getCustom10() == null) busBudgetPojo.setCustom10("");
        if (busBudgetPojo.getTenantid() == null) busBudgetPojo.setTenantid("");
        if (busBudgetPojo.getTenantname() == null) busBudgetPojo.setTenantname("");
        if (busBudgetPojo.getRevision() == null) busBudgetPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusBudgetEntity busBudgetEntity = new BusBudgetEntity();
        BeanUtils.copyProperties(busBudgetPojo, busBudgetEntity);

        //设置id和新建日期
        busBudgetEntity.setId(id);
        busBudgetEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busBudgetMapper.insert(busBudgetEntity);
        //Item子表处理
        List<BusBudgetitemPojo> lst = busBudgetPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (BusBudgetitemPojo busBudgetitemPojo : lst) {
                //初始化item的NULL
                BusBudgetitemPojo itemPojo = this.busBudgetitemService.clearNull(busBudgetitemPojo);
                BusBudgetitemEntity busBudgetitemEntity = new BusBudgetitemEntity();
                BeanUtils.copyProperties(itemPojo, busBudgetitemEntity);
                //设置id和Pid
                busBudgetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busBudgetitemEntity.setPid(id);
                busBudgetitemEntity.setTenantid(busBudgetPojo.getTenantid());
                busBudgetitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busBudgetitemMapper.insert(busBudgetitemEntity);
                // 同步订单子表的人工预算
                busMachiningitemMapper.updateLaborCostAmt(busBudgetitemEntity.getMachitemid(), busBudgetitemEntity.getLaborcost(), busBudgetPojo.getTenantid());
            }
        }

        //返回Bill实例
        return this.getBillEntity(busBudgetEntity.getId(), busBudgetEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busBudgetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusBudgetPojo update(BusBudgetPojo busBudgetPojo) {
        String tid = busBudgetPojo.getTenantid();
        //主表更改
        BusBudgetEntity busBudgetEntity = new BusBudgetEntity();
        BeanUtils.copyProperties(busBudgetPojo, busBudgetEntity);
        this.busBudgetMapper.update(busBudgetEntity);
        if (busBudgetPojo.getItem() != null) {
            //Item子表处理
            List<BusBudgetitemPojo> lst = busBudgetPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busBudgetMapper.getDelItemIds(busBudgetPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    BusBudgetitemPojo delPojo = busBudgetitemMapper.getEntity(lstDelId, tid);
                    this.busBudgetitemMapper.delete(lstDelId, tid);
                    // 同步订单子表的人工预算
                    busMachiningitemMapper.updateLaborCostAmt(delPojo.getMachitemid(), 0D, tid);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (BusBudgetitemPojo busBudgetitemPojo : lst) {
                    BusBudgetitemEntity busBudgetitemEntity = new BusBudgetitemEntity();
                    if ("".equals(busBudgetitemPojo.getId()) || busBudgetitemPojo.getId() == null) {
                        //初始化item的NULL
                        BusBudgetitemPojo itemPojo = this.busBudgetitemService.clearNull(busBudgetitemPojo);
                        BeanUtils.copyProperties(itemPojo, busBudgetitemEntity);
                        //设置id和Pid
                        busBudgetitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busBudgetitemEntity.setPid(busBudgetEntity.getId());  // 主表 id
                        busBudgetitemEntity.setTenantid(tid);   // 租户id
                        busBudgetitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busBudgetitemMapper.insert(busBudgetitemEntity);
                    } else {
                        BeanUtils.copyProperties(busBudgetitemPojo, busBudgetitemEntity);
                        busBudgetitemEntity.setTenantid(tid);
                        this.busBudgetitemMapper.update(busBudgetitemEntity);
                        // 同步订单子表的人工预算
                        busMachiningitemMapper.updateLaborCostAmt(busBudgetitemEntity.getMachitemid(), busBudgetitemEntity.getLaborcost(), tid);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busBudgetEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        BusBudgetPojo busBudgetPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusBudgetitemPojo> lst = busBudgetPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusBudgetitemPojo busBudgetitemPojo : lst) {
                this.busBudgetitemMapper.delete(busBudgetitemPojo.getId(), tid);
                // 同步订单子表的人工预算
                busMachiningitemMapper.updateLaborCostAmt(busBudgetitemPojo.getMachitemid(), 0D, tid);
            }
        }
         this.busBudgetMapper.delete(key, tid);
        return busBudgetPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param busBudgetPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusBudgetPojo approval(BusBudgetPojo busBudgetPojo) {
        //主表更改
        BusBudgetEntity busBudgetEntity = new BusBudgetEntity();
        BeanUtils.copyProperties(busBudgetPojo, busBudgetEntity);
        this.busBudgetMapper.approval(busBudgetEntity);
        //返回Bill实例
        return this.getBillEntity(busBudgetEntity.getId(), busBudgetEntity.getTenantid());
    }

}
