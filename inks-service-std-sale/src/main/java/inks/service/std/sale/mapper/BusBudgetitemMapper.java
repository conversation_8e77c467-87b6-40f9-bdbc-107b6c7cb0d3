package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusBudgetitemEntity;
import inks.service.std.sale.domain.pojo.BusBudgetitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预算项目(BusBudgetitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-02 09:27:23
 */
 @Mapper
public interface BusBudgetitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusBudgetitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusBudgetitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusBudgetitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busBudgetitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusBudgetitemEntity busBudgetitemEntity);

    
    /**
     * 修改数据
     *
     * @param busBudgetitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusBudgetitemEntity busBudgetitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    BusBudgetitemPojo getEntityByMachitemid(@Param("machitemid") String machitemid, @Param("tid") String tid);
}

