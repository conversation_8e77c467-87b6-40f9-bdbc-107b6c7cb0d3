package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusCouponactiveEntity;
import inks.service.std.sale.domain.pojo.BusCouponPojo;
import inks.service.std.sale.domain.pojo.BusCouponactivePojo;
import inks.service.std.sale.mapper.BusCouponMapper;
import inks.service.std.sale.mapper.BusCouponactiveMapper;
import inks.service.std.sale.service.BusCouponactiveService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 优惠券激活记录(BusCouponactive)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
@Service("busCouponactiveService")
public class BusCouponactiveServiceImpl implements BusCouponactiveService {
    @Resource
    private BusCouponactiveMapper busCouponactiveMapper;
    @Resource
    private BusCouponMapper busCouponMapper;

    @Override
    public BusCouponactivePojo getEntity(String key, String tid) {
        return this.busCouponactiveMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<BusCouponactivePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCouponactivePojo> lst = busCouponactiveMapper.getPageList(queryParam);
            PageInfo<BusCouponactivePojo> pageInfo = new PageInfo<BusCouponactivePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public BusCouponactivePojo insert(BusCouponactivePojo busCouponactivePojo) {
        String tid = busCouponactivePojo.getTenantid();
        //初始化NULL字段
        cleanNull(busCouponactivePojo);
        BusCouponactiveEntity busCouponactiveEntity = new BusCouponactiveEntity();
        BeanUtils.copyProperties(busCouponactivePojo, busCouponactiveEntity);
        //生成雪花id
        busCouponactiveEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busCouponactiveEntity.setRevision(1);  //乐观锁

        // 校验累计激活不能超过优惠券总金额
        String couponid = busCouponactivePojo.getCouponid();
        BusCouponPojo couponPojo = busCouponMapper.getEntity(couponid, tid);
        if (couponPojo.getActiveamount() + busCouponactivePojo.getActiveamount() > couponPojo.getCouponamount()) {
            throw new BaseBusinessException("累计激活金额：" + (couponPojo.getActiveamount() + busCouponactivePojo.getActiveamount()) + " 超过优惠券总金额：" + couponPojo.getCouponamount());
        }
        this.busCouponactiveMapper.insert(busCouponactiveEntity);
        // 优惠券主表同步下累计已激活金额
        busCouponactiveMapper.syncCouponActiveAmount(couponid, tid);
        return this.getEntity(busCouponactiveEntity.getId(), busCouponactiveEntity.getTenantid());
    }


    @Override
    public BusCouponactivePojo update(BusCouponactivePojo busCouponactivePojo) {
        String tid = busCouponactivePojo.getTenantid();
        BusCouponactiveEntity busCouponactiveEntity = new BusCouponactiveEntity();
        BeanUtils.copyProperties(busCouponactivePojo, busCouponactiveEntity);
        // 校验累计激活不能超过优惠券总金额
        String couponid = busCouponactivePojo.getCouponid();
        BusCouponPojo couponPojo = busCouponMapper.getEntity(couponid, tid);
        BusCouponactivePojo orgActDB = busCouponactiveMapper.getEntity(busCouponactiveEntity.getId(), tid);
        if (orgActDB.getActiveamount() + busCouponactivePojo.getActiveamount() - orgActDB.getActiveamount() != busCouponactivePojo.getActiveamount()) {
            throw new BaseBusinessException("累计激活金额：" + (orgActDB.getActiveamount() + busCouponactivePojo.getActiveamount()) + " 超过优惠券总金额：" + couponPojo.getCouponamount());
        }
        this.busCouponactiveMapper.update(busCouponactiveEntity);
        // 优惠券主表同步下累计已激活金额
        busCouponactiveMapper.syncCouponActiveAmount(couponid, tid);
        return this.getEntity(busCouponactiveEntity.getId(), tid);
    }


    @Override
    public int delete(String key, String tid) {
        BusCouponactivePojo couponactiveDB = busCouponactiveMapper.getEntity(key, tid);
        int delete = this.busCouponactiveMapper.delete(key, tid);
        // 优惠券主表同步下累计已激活金额
        String couponid = couponactiveDB.getCouponid();
        busCouponactiveMapper.syncCouponActiveAmount(couponid, tid);
        return delete;
    }

    @Override
    @Transactional
    public BusCouponactivePojo approval(BusCouponactivePojo busCouponactivePojo) {
        //主表更改
        BusCouponactiveEntity busCouponactiveEntity = new BusCouponactiveEntity();
        BeanUtils.copyProperties(busCouponactivePojo, busCouponactiveEntity);
        this.busCouponactiveMapper.approval(busCouponactiveEntity);
        //返回Bill实例
        return this.getEntity(busCouponactiveEntity.getId(), busCouponactiveEntity.getTenantid());
    }

    private static void cleanNull(BusCouponactivePojo busCouponactivePojo) {
        if (busCouponactivePojo.getCouponid() == null) busCouponactivePojo.setCouponid("");
        if (busCouponactivePojo.getActiveamount() == null) busCouponactivePojo.setActiveamount(0D);
        if (busCouponactivePojo.getActivedesc() == null) busCouponactivePojo.setActivedesc("");
        if (busCouponactivePojo.getRemark() == null) busCouponactivePojo.setRemark("");
        if (busCouponactivePojo.getRownum() == null) busCouponactivePojo.setRownum(0);
        if (busCouponactivePojo.getAssessor() == null) busCouponactivePojo.setAssessor("");
        if (busCouponactivePojo.getAssessorid() == null) busCouponactivePojo.setAssessorid("");
        if (busCouponactivePojo.getAssessdate() == null) busCouponactivePojo.setAssessdate(new Date());
        if (busCouponactivePojo.getCreateby() == null) busCouponactivePojo.setCreateby("");
        if (busCouponactivePojo.getCreatebyid() == null) busCouponactivePojo.setCreatebyid("");
        if (busCouponactivePojo.getCreatedate() == null) busCouponactivePojo.setCreatedate(new Date());
        if (busCouponactivePojo.getLister() == null) busCouponactivePojo.setLister("");
        if (busCouponactivePojo.getListerid() == null) busCouponactivePojo.setListerid("");
        if (busCouponactivePojo.getModifydate() == null) busCouponactivePojo.setModifydate(new Date());
        if (busCouponactivePojo.getCustom1() == null) busCouponactivePojo.setCustom1("");
        if (busCouponactivePojo.getCustom2() == null) busCouponactivePojo.setCustom2("");
        if (busCouponactivePojo.getCustom3() == null) busCouponactivePojo.setCustom3("");
        if (busCouponactivePojo.getCustom4() == null) busCouponactivePojo.setCustom4("");
        if (busCouponactivePojo.getCustom5() == null) busCouponactivePojo.setCustom5("");
        if (busCouponactivePojo.getCustom6() == null) busCouponactivePojo.setCustom6("");
        if (busCouponactivePojo.getCustom7() == null) busCouponactivePojo.setCustom7("");
        if (busCouponactivePojo.getCustom8() == null) busCouponactivePojo.setCustom8("");
        if (busCouponactivePojo.getCustom9() == null) busCouponactivePojo.setCustom9("");
        if (busCouponactivePojo.getCustom10() == null) busCouponactivePojo.setCustom10("");
        if (busCouponactivePojo.getTenantid() == null) busCouponactivePojo.setTenantid("");
        if (busCouponactivePojo.getTenantname() == null) busCouponactivePojo.setTenantname("");
        if (busCouponactivePojo.getRevision() == null) busCouponactivePojo.setRevision(0);
    }

}
