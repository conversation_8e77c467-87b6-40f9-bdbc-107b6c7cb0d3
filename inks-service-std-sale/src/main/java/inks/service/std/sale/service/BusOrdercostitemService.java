package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusOrdercostitemPojo;

import java.util.List;
/**
 * 成本项目(BusOrdercostitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-30 08:18:24
 */
public interface BusOrdercostitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusOrdercostitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusOrdercostitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusOrdercostitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busOrdercostitemPojo 实例对象
     * @return 实例对象
     */
    BusOrdercostitemPojo insert(BusOrdercostitemPojo busOrdercostitemPojo);

    /**
     * 修改数据
     *
     * @param busOrdercostitempojo 实例对象
     * @return 实例对象
     */
    BusOrdercostitemPojo update(BusOrdercostitemPojo busOrdercostitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busOrdercostitempojo 实例对象
     * @return 实例对象
     */
    BusOrdercostitemPojo clearNull(BusOrdercostitemPojo busOrdercostitempojo);
}
