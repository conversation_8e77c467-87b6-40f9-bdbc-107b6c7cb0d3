package inks.service.std.sale.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCouponPojo;
import com.github.pagehelper.PageInfo;

/**
 * 优惠券表(Bus_Coupon)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
public interface BusCouponService {

    BusCouponPojo getEntity(String key,String tid);

    PageInfo<BusCouponPojo> getPageList(QueryParam queryParam);

    BusCouponPojo insert(BusCouponPojo busCouponPojo);

    BusCouponPojo update(BusCouponPojo busCouponpojo);

    int delete(String key,String tid);

     BusCouponPojo approval(BusCouponPojo busCouponPojo);

    BusCouponPojo disannul(String key, Integer type, LoginUser loginUser);
}
