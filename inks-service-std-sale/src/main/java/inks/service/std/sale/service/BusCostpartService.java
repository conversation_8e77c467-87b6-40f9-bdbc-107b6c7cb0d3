package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCostpartPojo;

/**
 * 成本组件(BusCostpart)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-07 13:29:31
 */
public interface BusCostpartService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostpartPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusCostpartPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busCostpartPojo 实例对象
     * @return 实例对象
     */
    BusCostpartPojo insert(BusCostpartPojo busCostpartPojo);

    /**
     * 修改数据
     *
     * @param busCostpartpojo 实例对象
     * @return 实例对象
     */
    BusCostpartPojo update(BusCostpartPojo busCostpartpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                                                                                                                         }
