package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusInvocarryoverPojo;
import com.github.pagehelper.PageInfo;

/**
 * 货品账单:发货>发票(Bus_InvoCarryover)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-10 11:30:19
 */
public interface BusInvocarryoverService {

    BusInvocarryoverPojo getEntity(String key,String tid);

    PageInfo<BusInvocarryoverPojo> getPageList(QueryParam queryParam);

    BusInvocarryoverPojo insert(BusInvocarryoverPojo busInvocarryoverPojo);

    BusInvocarryoverPojo update(BusInvocarryoverPojo busInvocarryoverpojo);

    int delete(String key,String tid);
}
