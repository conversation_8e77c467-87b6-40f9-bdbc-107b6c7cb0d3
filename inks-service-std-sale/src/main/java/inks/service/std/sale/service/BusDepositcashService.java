package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDepositcashPojo;

import java.util.List;
/**
 * 现金项目(BusDepositcash)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-20 13:12:41
 */
public interface BusDepositcashService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDepositcashPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDepositcashPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusDepositcashPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busDepositcashPojo 实例对象
     * @return 实例对象
     */
    BusDepositcashPojo insert(BusDepositcashPojo busDepositcashPojo);

    /**
     * 修改数据
     *
     * @param busDepositcashpojo 实例对象
     * @return 实例对象
     */
    BusDepositcashPojo update(BusDepositcashPojo busDepositcashpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busDepositcashpojo 实例对象
     * @return 实例对象
     */
    BusDepositcashPojo clearNull(BusDepositcashPojo busDepositcashpojo);
}
