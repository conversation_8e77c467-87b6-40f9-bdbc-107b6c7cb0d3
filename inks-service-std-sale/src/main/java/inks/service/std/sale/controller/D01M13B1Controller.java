package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusAccountPojo;
import inks.service.std.sale.domain.pojo.BusAccountitemPojo;
import inks.service.std.sale.service.BusAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 应收账单(Bus_ReceAccount)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-23 13:32:01
 */
@RestController
@RequestMapping("D01M13B1")
@Api(tags = "D01M13B1:应收账单")
public class D01M13B1Controller extends BusReceaccountController {

    private final String moduleCode = "D01M12B1";
    /**
     * 服务对象
     */
    @Resource
    private BusAccountService busAccountService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = "拉取应收账单item", notes = "拉取应收账单item,传账单主表", produces = "application/json")
    @RequestMapping(value = "/pullItemList", method = RequestMethod.POST)
    public R<List<BusAccountitemPojo>> pullItemList(@RequestBody String json) {
        try {
            BusAccountPojo busAccountPojo = JSONArray.parseObject(json, BusAccountPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busAccountService.pullItemList(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInitPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<PageInfo<BusAccountPojo>> getInitPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Account.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Bus_Account.BillType='期初建账'");
            return R.ok(this.busAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = "初期化全部应收账单", notes = "初期化全部应收账单", produces = "application/json")
    @RequestMapping(value = "/batchInit", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<Integer> batchInit(@RequestBody String json) {
        try {
            BusAccountPojo busAccountPojo = JSONArray.parseObject(json, BusAccountPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            R r = systemFeignService.getBillCode("D01M12B1", loginUser.getToken());
            if (r.getCode() == 200)
                busAccountPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            busAccountPojo.setBilltype("期初建账");
            busAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountPojo.setCreatedate(new Date());   // 创建时间
            busAccountPojo.setLister(loginUser.getRealname());   // 制表
            busAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountPojo.setModifydate(new Date());   //修改时间
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busAccountService.batchInit(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "初期化全部应收账单", notes = "初期化全部应收账单", produces = "application/json")
    @RequestMapping(value = "/batchCreate", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<Integer> batchCreate(@RequestBody String json) {
        try {
            BusAccountPojo busAccountPojo = JSONArray.parseObject(json, BusAccountPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Account", null, loginUser.getTenantid());
            busAccountPojo.setRefno(refno);
            busAccountPojo.setBilltype("应收账单");
            busAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountPojo.setCreatedate(new Date());   // 创建时间
            busAccountPojo.setLister(loginUser.getRealname());   // 制表
            busAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountPojo.setModifydate(new Date());   //修改时间
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            int i = this.busAccountService.batchCreate(busAccountPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());// 保存单据编码RefNoUtils
            return R.ok(i);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
