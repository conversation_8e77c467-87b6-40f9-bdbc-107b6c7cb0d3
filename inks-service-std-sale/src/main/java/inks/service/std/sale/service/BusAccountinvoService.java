package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusAccountinvoPojo;

import java.util.List;
/**
 * 销售单to发票(BusAccountinvo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-16 11:27:03
 */
public interface BusAccountinvoService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountinvoPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusAccountinvoPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusAccountinvoPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busAccountinvoPojo 实例对象
     * @return 实例对象
     */
    BusAccountinvoPojo insert(BusAccountinvoPojo busAccountinvoPojo);

    /**
     * 修改数据
     *
     * @param busAccountinvopojo 实例对象
     * @return 实例对象
     */
    BusAccountinvoPojo update(BusAccountinvoPojo busAccountinvopojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busAccountinvopojo 实例对象
     * @return 实例对象
     */
    BusAccountinvoPojo clearNull(BusAccountinvoPojo busAccountinvopojo);
}
