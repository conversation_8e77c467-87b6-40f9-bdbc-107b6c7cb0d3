package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCarryoveritemPojo;
import inks.service.std.sale.domain.BusCarryoveritemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单>发货(BusCarryoveritem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-27 08:51:10
 */
 @Mapper
public interface BusCarryoveritemMapper {

    BusCarryoveritemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusCarryoveritemPojo> getPageList(QueryParam queryParam);

    List<BusCarryoveritemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(BusCarryoveritemEntity busCarryoveritemEntity);

    int update(BusCarryoveritemEntity busCarryoveritemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

