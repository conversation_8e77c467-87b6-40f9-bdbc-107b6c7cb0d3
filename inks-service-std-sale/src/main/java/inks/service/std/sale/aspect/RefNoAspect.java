package inks.service.std.sale.aspect;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.StringUtils;
import inks.common.security.service.TokenService;
import inks.service.std.sale.annotation.RefNoCleanup;
import inks.service.std.sale.annotation.RefNoGeneration;
import inks.service.std.sale.config.RefNoProperties;
import inks.service.std.sale.utils.RefNoAopUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;



/**
 * 单据编码AOP切面
 * 自动处理单据编码的生成和清理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class RefNoAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(RefNoAspect.class);
    
    @Autowired
    private TokenService tokenService;

    @Autowired
    private RefNoProperties refNoProperties;
    
    /**
     * 单据编码生成切面
     * 在标记了@RefNoGeneration注解的方法执行前生成编码
     */
    @Before("@annotation(refNoGeneration)")
    public void generateRefNo(JoinPoint joinPoint, RefNoGeneration refNoGeneration) {
        // 检查功能是否启用
        if (!refNoProperties.isEnabled()) {
            if (refNoProperties.isVerboseLogging()) {
                logger.debug("RefNo AOP功能已禁用，跳过编码生成");
            }
            return;
        }

        try {
            // 获取登录用户信息
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (loginUser == null) {
                logger.warn("无法获取登录用户信息，跳过编码生成");
                return;
            }
            
            // 获取模块代码
            String moduleCode = extractModuleCode(joinPoint);
            if (StringUtils.isBlank(moduleCode)) {
                logger.warn("无法提取模块代码，跳过编码生成");
                return;
            }
            
            // 生成单据编码
            String customPrefix = StringUtils.isNotBlank(refNoGeneration.customPrefix()) 
                ? refNoGeneration.customPrefix() : null;
            String refno = RefNoUtils.generateRefNo(moduleCode, refNoGeneration.billType(), 
                customPrefix, loginUser.getTenantid());
            
            // 设置编码到业务对象
            setRefNoToBusinessObject(joinPoint, refNoGeneration, refno);
            
            // 保存到Redis缓存
            if (refNoGeneration.saveToRedis()) {
                RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            }
            
            if (refNoProperties.isVerboseLogging()) {
                logger.info("自动生成单据编码成功: {} -> {}", refNoGeneration.billType(), refno);
            }
            
        } catch (Exception e) {
            logger.error("单据编码生成失败", e);
            throw new RuntimeException("单据编码生成失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 单据编码清理切面 - 方法执行成功后
     */
    @AfterReturning("@annotation(refNoCleanup)")
    public void cleanupRefNoAfterSuccess(JoinPoint joinPoint, RefNoCleanup refNoCleanup) {
        if (refNoCleanup.afterSuccess()) {
            performCleanup(joinPoint, refNoCleanup);
        }
    }
    
    /**
     * 单据编码清理切面 - 方法执行前
     */
    @Before("@annotation(refNoCleanup)")
    public void cleanupRefNoBefore(JoinPoint joinPoint, RefNoCleanup refNoCleanup) {
        if (!refNoCleanup.afterSuccess()) {
            performCleanup(joinPoint, refNoCleanup);
        }
    }
    
    /**
     * 执行清理操作
     */
    private void performCleanup(JoinPoint joinPoint, RefNoCleanup refNoCleanup) {
        try {
            // 获取登录用户信息
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (loginUser == null) {
                logger.warn("无法获取登录用户信息，跳过编码清理");
                return;
            }
            
            // 获取模块代码
            String moduleCode = extractModuleCode(joinPoint);
            if (StringUtils.isBlank(moduleCode)) {
                logger.warn("无法提取模块代码，跳过编码清理");
                return;
            }
            
            // 清理Redis缓存
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            
            logger.info("自动清理单据编码缓存成功: {}", moduleCode);
            
        } catch (Exception e) {
            logger.error("单据编码清理失败", e);
            if (!refNoCleanup.ignoreException()) {
                throw new RuntimeException("单据编码清理失败: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 从切点中提取模块代码
     * 通过Controller类名或字段获取
     */
    private String extractModuleCode(JoinPoint joinPoint) {
        try {
            Object target = joinPoint.getTarget();
            return RefNoAopUtils.extractModuleCode(target);
        } catch (Exception e) {
            logger.error("提取模块代码失败", e);
            return null;
        }
    }
    
    /**
     * 设置编码到业务对象
     */
    private void setRefNoToBusinessObject(JoinPoint joinPoint, RefNoGeneration refNoGeneration, String refno) {
        try {
            Object[] args = joinPoint.getArgs();
            if (!RefNoAopUtils.isValidParamIndex(args, refNoGeneration.paramIndex())) {
                logger.warn("参数索引超出范围: {}", refNoGeneration.paramIndex());
                return;
            }

            Object param = RefNoAopUtils.getParameter(args, refNoGeneration.paramIndex());
            Object businessObject = null;

            // 根据参数类型处理
            if (refNoGeneration.paramType() == RefNoGeneration.ParamType.JSON_STRING) {
                // JSON字符串参数，需要解析
                if (param instanceof String) {
                    businessObject = JSONObject.parseObject((String) param);
                }
            } else if (refNoGeneration.paramType() == RefNoGeneration.ParamType.POJO) {
                // 直接的POJO对象
                businessObject = param;
            }

            if (businessObject == null) {
                logger.warn("无法获取业务对象");
                return;
            }

            // 设置编码字段
            RefNoAopUtils.setFieldValue(businessObject, refNoGeneration.field(), refno);

        } catch (Exception e) {
            logger.error("设置编码到业务对象失败", e);
            throw new RuntimeException("设置编码失败: " + e.getMessage(), e);
        }
    }
    

}
