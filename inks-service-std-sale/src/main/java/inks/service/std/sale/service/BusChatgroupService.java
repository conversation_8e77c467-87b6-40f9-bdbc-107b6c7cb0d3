package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusChatgroupPojo;
import inks.service.std.sale.domain.pojo.BusChatgroupitemdetailPojo;

/**
 * 客服分组(BusChatgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-08 15:45:53
 */
public interface BusChatgroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusChatgroupPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusChatgroupitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusChatgroupPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusChatgroupPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusChatgroupPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busChatgroupPojo 实例对象
     * @return 实例对象
     */
    BusChatgroupPojo insert(BusChatgroupPojo busChatgroupPojo);

    /**
     * 修改数据
     *
     * @param busChatgrouppojo 实例对象
     * @return 实例对象
     */
    BusChatgroupPojo update(BusChatgroupPojo busChatgrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

                                                                                                              }
