package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusChatgroupdmsEntity;
import inks.service.std.sale.domain.pojo.BusChatgroupdmsPojo;
import inks.service.std.sale.mapper.BusChatgroupdmsMapper;
import inks.service.std.sale.service.BusChatgroupdmsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 客服分组DMS用户子表(BusChatgroupdms)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-08 15:49:07
 */
@Service("busChatgroupdmsService")
public class BusChatgroupdmsServiceImpl implements BusChatgroupdmsService {
    @Resource
    private BusChatgroupdmsMapper busChatgroupdmsMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusChatgroupdmsPojo getEntity(String key,String tid) {
        return this.busChatgroupdmsMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusChatgroupdmsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusChatgroupdmsPojo> lst = busChatgroupdmsMapper.getPageList(queryParam);
            PageInfo<BusChatgroupdmsPojo> pageInfo = new PageInfo<BusChatgroupdmsPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusChatgroupdmsPojo> getList(String Pid,String tid) { 
        try {
            List<BusChatgroupdmsPojo> lst = busChatgroupdmsMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busChatgroupdmsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusChatgroupdmsPojo insert(BusChatgroupdmsPojo busChatgroupdmsPojo) {
        //初始化item的NULL
        BusChatgroupdmsPojo itempojo =this.clearNull(busChatgroupdmsPojo);
        BusChatgroupdmsEntity busChatgroupdmsEntity = new BusChatgroupdmsEntity(); 
        BeanUtils.copyProperties(itempojo,busChatgroupdmsEntity);
          //生成雪花id
          busChatgroupdmsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busChatgroupdmsEntity.setRevision(1);  //乐观锁      
          this.busChatgroupdmsMapper.insert(busChatgroupdmsEntity);
        return this.getEntity(busChatgroupdmsEntity.getId(),busChatgroupdmsEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busChatgroupdmsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusChatgroupdmsPojo update(BusChatgroupdmsPojo busChatgroupdmsPojo) {
        BusChatgroupdmsEntity busChatgroupdmsEntity = new BusChatgroupdmsEntity(); 
        BeanUtils.copyProperties(busChatgroupdmsPojo,busChatgroupdmsEntity);
        this.busChatgroupdmsMapper.update(busChatgroupdmsEntity);
        return this.getEntity(busChatgroupdmsEntity.getId(),busChatgroupdmsEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busChatgroupdmsMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busChatgroupdmsPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusChatgroupdmsPojo clearNull(BusChatgroupdmsPojo busChatgroupdmsPojo){
     //初始化NULL字段
     if(busChatgroupdmsPojo.getPid()==null) busChatgroupdmsPojo.setPid("");
     if(busChatgroupdmsPojo.getDmsid()==null) busChatgroupdmsPojo.setDmsid("");
     if(busChatgroupdmsPojo.getDmsname()==null) busChatgroupdmsPojo.setDmsname("");
     if(busChatgroupdmsPojo.getRownum()==null) busChatgroupdmsPojo.setRownum(0);
     if(busChatgroupdmsPojo.getRemark()==null) busChatgroupdmsPojo.setRemark("");
     if(busChatgroupdmsPojo.getCreateby()==null) busChatgroupdmsPojo.setCreateby("");
     if(busChatgroupdmsPojo.getCreatebyid()==null) busChatgroupdmsPojo.setCreatebyid("");
     if(busChatgroupdmsPojo.getCreatedate()==null) busChatgroupdmsPojo.setCreatedate(new Date());
     if(busChatgroupdmsPojo.getLister()==null) busChatgroupdmsPojo.setLister("");
     if(busChatgroupdmsPojo.getListerid()==null) busChatgroupdmsPojo.setListerid("");
     if(busChatgroupdmsPojo.getModifydate()==null) busChatgroupdmsPojo.setModifydate(new Date());
     if(busChatgroupdmsPojo.getCustom1()==null) busChatgroupdmsPojo.setCustom1("");
     if(busChatgroupdmsPojo.getCustom2()==null) busChatgroupdmsPojo.setCustom2("");
     if(busChatgroupdmsPojo.getCustom3()==null) busChatgroupdmsPojo.setCustom3("");
     if(busChatgroupdmsPojo.getCustom4()==null) busChatgroupdmsPojo.setCustom4("");
     if(busChatgroupdmsPojo.getTenantid()==null) busChatgroupdmsPojo.setTenantid("");
     if(busChatgroupdmsPojo.getTenantname()==null) busChatgroupdmsPojo.setTenantname("");
     if(busChatgroupdmsPojo.getRevision()==null) busChatgroupdmsPojo.setRevision(0);
     return busChatgroupdmsPojo;
     }
}
