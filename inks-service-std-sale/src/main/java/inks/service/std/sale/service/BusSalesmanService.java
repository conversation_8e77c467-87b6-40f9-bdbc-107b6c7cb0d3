package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusSalesmanPojo;

/**
 * 业务员信息表(BusSalesman)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-01 09:38:26
 */
public interface BusSalesmanService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusSalesmanPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusSalesmanPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busSalesmanPojo 实例对象
     * @return 实例对象
     */
    BusSalesmanPojo insert(BusSalesmanPojo busSalesmanPojo);

    /**
     * 修改数据
     *
     * @param busSalesmanpojo 实例对象
     * @return 实例对象
     */
    BusSalesmanPojo update(BusSalesmanPojo busSalesmanpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
}
