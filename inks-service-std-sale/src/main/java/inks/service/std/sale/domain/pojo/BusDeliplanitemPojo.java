package inks.service.std.sale.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

/**
 * 发货计划明细(BusDeliplanitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:18
 */
public class BusDeliplanitemPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = -23200153959772512L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 商品编码
  @Excel(name = "商品编码")    
  private String goodsid;
     // 产品编码
  @Excel(name = "产品编码")    
  private String itemcode;
     // 产品名称
  @Excel(name = "产品名称")    
  private String itemname;
     // 产品规格
  @Excel(name = "产品规格")    
  private String itemspec;
     // 产品单位
  @Excel(name = "产品单位")    
  private String itemunit;
     // 数量
  @Excel(name = "数量")    
  private Double quantity;
     // 含税单价
  @Excel(name = "含税单价")    
  private Double taxprice;
     // 含税金额
  @Excel(name = "含税金额")    
  private Double taxamount;
     // 未税单价
  @Excel(name = "未税单价")    
  private Double price;
     // 未税金额
  @Excel(name = "未税金额")    
  private Double amount;
     // 记录税率
  @Excel(name = "记录税率")    
  private Integer itemtaxrate;
     // 税额
  @Excel(name = "税额")    
  private Double taxtotal;
     // 标准销价
  @Excel(name = "标准销价")    
  private Double stdprice;
     // 标准金额
  @Excel(name = "标准金额")    
  private Double stdamount;
     // 折扣
  @Excel(name = "折扣")    
  private Integer rebate;
     // 赠品
  @Excel(name = "赠品")    
  private Double freeqty;
     // 拣货数量
  @Excel(name = "拣货数量")    
  private Double pickqty;
     // 发货数量
  @Excel(name = "发货数量")
  private Double finishqty;
     // 发货关闭
  @Excel(name = "发货关闭")    
  private Integer closed;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 引用号
  @Excel(name = "引用号")    
  private String citeuid;
     // 引用子项id
  @Excel(name = "引用子项id")    
  private String citeitemid;
     // 客户订单号
  @Excel(name = "客户订单号")    
  private String custpo;
     // 状态编码
  @Excel(name = "状态编码")    
  private String statecode;
     // 状态时间
  @Excel(name = "状态时间")    
  private Date statedate;
     // 订单类别
  @Excel(name = "订单类别")    
  private String machtype;
     // 虚拟货品
  @Excel(name = "虚拟货品")    
  private Integer virtualitem;
     // 指定库位
  @Excel(name = "指定库位")    
  private String location;
     // 指定批号
  @Excel(name = "指定批号")    
  private String batchno;
     // 销售单号
  @Excel(name = "销售单号")    
  private String machuid;
     // 销售子项id
  @Excel(name = "销售子项id")    
  private String machitemid;
     // 作废
  @Excel(name = "作废")    
  private Integer disannulmark;
     // 作废经办
  @Excel(name = "作废经办")    
  private String disannullister;
     // 作废日期
  @Excel(name = "作废日期")    
  private Date disannuldate;
     // 属性Josn
  @Excel(name = "属性Josn")    
  private String attributejson;
     // 订单下达
  @Excel(name = "订单下达")    
  private Date machdate;
     // 来源:0=其他1=销售订单2=退货单
  @Excel(name = "来源:0=其他1=销售订单2=退货单")    
  private Integer sourcetype;
     // 评审交期
  @Excel(name = "评审交期")
  private Date itemplandate;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 商品编码
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
   // 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
   // 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
   // 数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
   // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
   // 未税单价
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
   // 未税金额
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
   // 记录税率
    public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
   // 税额
    public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
   // 标准销价
    public Double getStdprice() {
        return stdprice;
    }
    
    public void setStdprice(Double stdprice) {
        this.stdprice = stdprice;
    }
        
   // 标准金额
    public Double getStdamount() {
        return stdamount;
    }
    
    public void setStdamount(Double stdamount) {
        this.stdamount = stdamount;
    }
        
   // 折扣
    public Integer getRebate() {
        return rebate;
    }
    
    public void setRebate(Integer rebate) {
        this.rebate = rebate;
    }
        
   // 赠品
    public Double getFreeqty() {
        return freeqty;
    }
    
    public void setFreeqty(Double freeqty) {
        this.freeqty = freeqty;
    }
        
   // 拣货数量
    public Double getPickqty() {
        return pickqty;
    }
    
    public void setPickqty(Double pickqty) {
        this.pickqty = pickqty;
    }
        
   // 发货数量
    public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
   // 发货关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 引用号
    public String getCiteuid() {
        return citeuid;
    }
    
    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }
        
   // 引用子项id
    public String getCiteitemid() {
        return citeitemid;
    }
    
    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }
        
   // 客户订单号
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
   // 状态编码
    public String getStatecode() {
        return statecode;
    }
    
    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
        
   // 状态时间
    public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
   // 订单类别
    public String getMachtype() {
        return machtype;
    }
    
    public void setMachtype(String machtype) {
        this.machtype = machtype;
    }
        
   // 虚拟货品
    public Integer getVirtualitem() {
        return virtualitem;
    }
    
    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }
        
   // 指定库位
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
        
   // 指定批号
    public String getBatchno() {
        return batchno;
    }
    
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }
        
   // 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
   // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
   // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
   // 作废经办
    public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
   // 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // 订单下达
    public Date getMachdate() {
        return machdate;
    }
    
    public void setMachdate(Date machdate) {
        this.machdate = machdate;
    }
        
   // 来源:0=其他1=销售订单2=退货单
    public Integer getSourcetype() {
        return sourcetype;
    }
    
    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
        
   // 评审交期
    public Date getItemplandate() {
        return itemplandate;
    }

    public void setItemplandate(Date itemplandate) {
        this.itemplandate = itemplandate;
    }

   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

