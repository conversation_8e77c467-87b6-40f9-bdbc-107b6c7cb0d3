package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusCarryoverPojo;
import inks.service.std.sale.domain.pojo.BusCarryoveritemPojo;
import inks.service.std.sale.domain.pojo.BusCarryoveritemdetailPojo;
import inks.service.std.sale.service.BusCarryoverService;
import inks.service.std.sale.service.BusCarryoveritemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 货品账单(Bus_Carryover)表控制层
 *
 * <AUTHOR>
 * @since 2024-08-27 08:50:19
 */
//@RestController
//@RequestMapping("busCarryover")
public class BusCarryoverController {

    private final static Logger logger = LoggerFactory.getLogger(BusCarryoverController.class);
    private final String moduleCode = "DxxMxxB1";
    @Resource
    private BusCarryoverService busCarryoverService;
    @Resource
    private BusCarryoveritemService busCarryoveritemService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;

    @ApiOperation(value = " 获取货品账单详细信息", notes = "获取货品账单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Carryover.List")
    public R<BusCarryoverPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busCarryoverService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Carryover.List")
    public R<PageInfo<BusCarryoveritemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Carryover.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busCarryoverService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取货品账单详细信息", notes = "获取货品账单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Carryover.List")
    public R<BusCarryoverPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.busCarryoverService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Carryover.List")
    public R<PageInfo<BusCarryoverPojo>> getBillList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Carryover.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busCarryoverService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Carryover.List")
    public R<PageInfo<BusCarryoverPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Carryover.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busCarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增货品账单", notes = "新增货品账单", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Carryover.Add")
    public R<BusCarryoverPojo> create(@RequestBody String json) {
        try {
            BusCarryoverPojo busCarryoverPojo = JSONArray.parseObject(json, BusCarryoverPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Carryover", null, loginUser.getTenantid());
            busCarryoverPojo.setRefno(refno);
            busCarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            busCarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busCarryoverPojo.setCreatedate(new Date());   // 创建时间
            busCarryoverPojo.setLister(loginUser.getRealname());   // 制表
            busCarryoverPojo.setListerid(loginUser.getUserid());    // 制表id            
            busCarryoverPojo.setModifydate(new Date());   //修改时间
            busCarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());
            return R.ok(this.busCarryoverService.insert(busCarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改货品账单", notes = "修改货品账单", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Carryover.Edit")
    public R<BusCarryoverPojo> update(@RequestBody String json) {
        try {
            BusCarryoverPojo busCarryoverPojo = JSONArray.parseObject(json, BusCarryoverPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCarryoverPojo.setLister(loginUser.getRealname());   // 制表
            busCarryoverPojo.setListerid(loginUser.getUserid());    // 制表id   
            busCarryoverPojo.setModifydate(new Date());   //修改时间
            busCarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busCarryoverService.update(busCarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除货品账单", notes = "删除货品账单", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Carryover.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            this.busCarryoverService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增货品账单Item", notes = "新增货品账单Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Carryover.Add")
    public R<BusCarryoveritemPojo> createItem(@RequestBody String json) {
        try {
            BusCarryoveritemPojo busCarryoveritemPojo = JSONArray.parseObject(json, BusCarryoveritemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCarryoveritemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busCarryoveritemService.insert(busCarryoveritemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改货品账单Item", notes = "修改货品账单Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Carryover.Edit")
    public R<BusCarryoveritemPojo> updateItem(@RequestBody String json) {
        try {
            BusCarryoveritemPojo busCarryoveritemPojo = JSONArray.parseObject(json, BusCarryoveritemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busCarryoveritemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busCarryoveritemService.update(busCarryoveritemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除货品账单Item", notes = "删除货品账单Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Carryover.Delete")
    public R<Integer> deleteItem(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busCarryoveritemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Carryover.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusCarryoverPojo busCarryoverPojo = this.busCarryoverService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busCarryoverPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busCarryoverPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusCarryoveritemPojo busCarryoveritemPojo = new BusCarryoveritemPojo();
                    busCarryoverPojo.getItem().add(busCarryoveritemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(busCarryoverPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

