package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.AmountUtils;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.InksConfig;
import inks.common.security.annotation.NoRepeatSubmit;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusDelieryPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo;
import inks.service.std.sale.service.BusDelieryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 发出商品(Bus_Deliery)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 14:07:43
 */
@RestController
@RequestMapping("D01M06B3")
@Api(tags = "D01M06B3:退货返工")
public class D01M06B3Controller extends BusDelieryController {
    /**
     * 服务对象
     */
    @Resource
    private BusDelieryService busDelieryService;


    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_Deliery.BillType IN ('退货返工','返工补发')";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_Deliery.BillType IN ('退货返工','返工补发')";
            qpfilter += " and Bus_DelieryItem.FinishQty<Bus_DelieryItem.Quantity+Bus_DelieryItem.FreeQty";
            qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.FinishClosed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Deliery.BillType IN ('退货返工','返工补发')";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Deliery.BillType IN ('退货返工','返工补发')";
            qpfilter += " and Bus_Deliery.FinishCount<Bus_Deliery.ItemCount";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待返工补发明细", notes = "按条件分页查询待返工补发明细?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineReturnPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getOnlineReturnPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_DelieryItem.ReturnQty<Bus_DelieryItem.Quantity";
            qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.ReturnClosed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Deliery.Assessorid<>''";  // 已审核
            qpfilter += " and Bus_Deliery.BillType='退货返工'";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询待返工补发明细", notes = "按条件分页查询待返工补发明细?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineReturnPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryPojo>> getOnlineReturnPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_Deliery.ReturnCount<Bus_Deliery.ItemCount";
            qpfilter += " and Bus_Deliery.Assessorid<>''";  // 已审核
            qpfilter += " and Bus_Deliery.BillType='退货返工'";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    private final String moduleCode = "D01M06B3";
    @ApiOperation(value = " 新增发出商品", notes = "新增发出商品", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Add")
    @NoRepeatSubmit
    @InksConfig
    public R<BusDelieryPojo> create(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        try {
            BusDelieryPojo busDelieryPojo = JSONArray.parseObject(json, BusDelieryPojo.class);
            // item转json,校验各项金额相差不能大于1
            String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(busDelieryPojo.getItem()));
            //PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
            if (lessThanOne != null) return R.fail(lessThanOne);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
// 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Deliery", null, tid);
            busDelieryPojo.setRefno(refno);   // 单据编码
            busDelieryPojo.setCreateby(loginUser.getRealname());   // 创建者
            busDelieryPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busDelieryPojo.setCreatedate(new Date());   // 创建时间
            busDelieryPojo.setLister(loginUser.getRealname());   // 制表
            busDelieryPojo.setListerid(loginUser.getUserid());    // 制表id
            busDelieryPojo.setModifydate(new Date());   //修改时间
            busDelieryPojo.setTenantid(tid);   //租户id
            BusDelieryPojo insert = this.busDelieryService.insert(busDelieryPojo,warn);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, tid);// 保存单据编码RefNoUtils
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

