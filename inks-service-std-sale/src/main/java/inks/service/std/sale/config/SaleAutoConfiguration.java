package inks.service.std.sale.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

//配合META-INF/spring.factories 实现自动配置：作为依赖时，省去启动类扫包配置
@Configuration
@ComponentScan("inks.service.std.sale") // 自动扫描 Component
@MapperScan("inks.service.std.sale.mapper") // 自动扫描 Mapper
public class SaleAutoConfiguration {
    // 可以在此添加其他自动配置的 Bean
}