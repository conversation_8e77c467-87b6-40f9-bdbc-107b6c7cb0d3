package inks.service.std.sale.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.StreamUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.service.std.sale.domain.BusMachiningEntity;
import inks.service.std.sale.domain.BusMachiningitemEntity;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.mapper.*;
import inks.service.std.sale.service.BusCouponusageService;
import inks.service.std.sale.service.BusMachiningService;
import inks.service.std.sale.service.BusMachiningitemService;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;
import static org.apache.commons.lang3.StringUtils.*;

/**
 * 销售订单(BusMachining)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 14:57:39
 */
@Service("busMachiningService")
public class BusMachiningServiceImpl implements BusMachiningService {
    @Resource
    private BusMachiningMapper busMachiningMapper;

    @Resource
    private BusMachiningitemMapper busMachiningitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusMachiningitemService busMachiningitemService;

    @Resource
    private BusOrdercostitemMapper busOrdercostitemMapper;
    @Resource
    private BusQuotationitemMapper busQuotationitemMapper;
    @Resource
    private BusCouponusageService busCouponusageService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;

    @Resource
    private sale_SyncMapper saleSyncMapper;

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusMachiningPojo getEntity(String key, String tid) {
        return this.busMachiningMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusMachiningitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusMachiningitemdetailPojo> lst = busMachiningMapper.getPageList(queryParam);
            PageInfo<BusMachiningitemdetailPojo> pageInfo = new PageInfo<BusMachiningitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusMachiningPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusMachiningPojo busMachiningPojo = this.busMachiningMapper.getEntity(key, tid);
            //读取子表
            busMachiningPojo.setItem(busMachiningitemMapper.getList(busMachiningPojo.getId(), busMachiningPojo.getTenantid()));
            return busMachiningPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusMachiningPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusMachiningPojo> lst = busMachiningMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (BusMachiningPojo busMachiningPojo : lst) {
                busMachiningPojo.setItem(busMachiningitemMapper.getList(busMachiningPojo.getId(), busMachiningPojo.getTenantid()));
            }
            PageInfo<BusMachiningPojo> pageInfo = new PageInfo<BusMachiningPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusMachiningPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusMachiningPojo> lst = busMachiningMapper.getPageTh(queryParam);
            PageInfo<BusMachiningPojo> pageInfo = new PageInfo<BusMachiningPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busMachiningPojo 实例对象
     * @return 实例对象
     */
    @Override
    // @Transactional
    public BusMachiningPojo insert(BusMachiningPojo busMachiningPojo) {
        //初始化NULL字段
        cleanNull(busMachiningPojo);
        String tid = busMachiningPojo.getTenantid();
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
        BeanUtils.copyProperties(busMachiningPojo, busMachiningEntity);
        //设置id和新建日期
        busMachiningEntity.setId(id);
        busMachiningEntity.setRevision(1);  //乐观锁
        //Item子表处理
        List<BusMachiningitemPojo> lst = busMachiningPojo.getItem();
        // 统计生产行数(销售订单子表生产需求WkQty>0的行数,虚拟品WkQty=0不统计)
        Integer wkItemCount = Math.toIntExact(lst.stream().filter(item -> item.getWkqty() > 0).count());
        busMachiningEntity.setWkitemcount(wkItemCount);
        // 获取公式
        Map<String, Object> tencfg = this.redisService.getCacheObject("tenant_config:" + tid);
        String attrstrExpr = null;
        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
            attrstrExpr = tencfg.get("system.bill.attributestr").toString();
        }
        for (int i = 0; i < lst.size(); i++) {
            BusMachiningitemPojo busMachiningitemPojo = lst.get(i);
            // 同步订单核价单
            if (busMachiningitemPojo.getOrdercostitemid() != null && !"".equals(busMachiningitemPojo.getOrdercostitemid())) {
                BusOrdercostitemPojo busOrdercostitemPojo = this.busOrdercostitemMapper.getEntity(busMachiningitemPojo.getOrdercostitemid(), tid);
                if (busOrdercostitemPojo != null && busOrdercostitemPojo.getMachmark() >= 1) {
                    int Rowno = i + 1;
                    throw new RuntimeException(Rowno + "行,核价单已转订单,禁止重复转单:" + busOrdercostitemPojo.getGoodsname());
                }
            }
            // 同步订单报价价单
            if (busMachiningitemPojo.getQuotitemid() != null && !"".equals(busMachiningitemPojo.getQuotitemid())) {
                BusQuotationitemPojo busQuotationitemPojo = this.busQuotationitemMapper.getEntity(busMachiningitemPojo.getQuotitemid(), tid);
                if (busQuotationitemPojo != null && busQuotationitemPojo.getMachmark() >= 1) {
                    int Rowno = i + 1;
                    throw new RuntimeException(Rowno + "行,报价单已转订单,禁止重复转单:" + busQuotationitemPojo.getGoodsname());
                }
            }
            // 计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
            if (isNotBlank(attrstrExpr) && isNotBlank(busMachiningitemPojo.getAttributejson())) {
                busMachiningitemPojo.setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(busMachiningitemPojo.getAttributejson(), attrstrExpr));
            }
        }
        // 使用金额：即整个销售订单的优惠金额，累加所有项的 Subamount
        double totalSubAmount = lst.stream()
                .mapToDouble(item -> item.getSubamount() == null ? 0.0 : item.getSubamount())
                .sum();
        busMachiningEntity.setBillsubtotal(totalSubAmount);
        transactionTemplate.execute((status) -> {
            //插入主表
            this.busMachiningMapper.insert(busMachiningEntity);
            //循环每个item子表
            for (BusMachiningitemPojo busMachiningitemPojo : lst) {
                //初始化item的NULL
                BusMachiningitemPojo itemPojo = this.busMachiningitemService.clearNull(busMachiningitemPojo);
                BusMachiningitemEntity busMachiningitemEntity = new BusMachiningitemEntity();
                BeanUtils.copyProperties(itemPojo, busMachiningitemEntity);
                //设置id和Pid
                busMachiningitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busMachiningitemEntity.setPid(id);
                busMachiningitemEntity.setTenantid(tid);
                busMachiningitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busMachiningitemMapper.insert(busMachiningitemEntity);
                // 同步订单核价单
                String ordercostitemid = busMachiningitemEntity.getOrdercostitemid();
                if (isNotBlank(ordercostitemid)) {
                    this.busMachiningMapper.updateOrderCostItemMachMark(ordercostitemid, tid);
                    this.busMachiningMapper.updateOrderCostFinishCount(ordercostitemid, tid);
                }
                // 同步订单报价单
                String quotitemid = busMachiningitemEntity.getQuotitemid();
                if (isNotBlank(quotitemid)) {
                    this.busMachiningMapper.updateQuotItemMachMark(quotitemid, tid);
                    this.busMachiningMapper.updateQuotFinishCount(quotitemid, tid);
                }
                // 同步物料数量
                String matcode = busMachiningitemPojo.getMatcode();
                if (isNotBlank(matcode)) {
                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(matcode, tid);
                    this.busMachiningMapper.updateGoodsRequRemQty(matid, matcode, tid);
                }
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidSet.forEach(goodsid -> {
                saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
            });
            // 同步货品数量 SQL替代MQ
            saleSyncMapper.updateWorkgroupBusMachRemAmt(busMachiningPojo.getGroupid(), tid);
            // 如果有优惠券：优惠券处理
            String couponid = busMachiningPojo.getCouponid();
            if (isNotBlank(couponid)) {
                //1. 优惠券使用记录：绑定到销售订单
                BusCouponusagePojo couponusagePojo = new BusCouponusagePojo();
                couponusagePojo.setCouponid(couponid);
                couponusagePojo.setModulecode("D01M03B1");
                couponusagePojo.setCiteuid(busMachiningEntity.getRefno());
                couponusagePojo.setCiteid(busMachiningEntity.getId());
                // 设置总的使用优惠金额
                couponusagePojo.setUsedamount(busMachiningEntity.getBillsubtotal());
                Date date = new Date();
                couponusagePojo.setCreateby(busMachiningEntity.getCreateby());
                couponusagePojo.setCreatebyid(busMachiningEntity.getCreatebyid());
                couponusagePojo.setCreatedate(date);
                couponusagePojo.setLister(busMachiningEntity.getCreateby());
                couponusagePojo.setListerid(busMachiningEntity.getCreatebyid());
                couponusagePojo.setModifydate(date);
                busCouponusageService.insert(couponusagePojo);
            }
            return Boolean.TRUE;
        });


        //返回Bill实例
        return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());

    }


    /**
     * 修改数据
     *
     * @param busMachiningPojo 实例对象 system.bill.attributestr
     * @return 实例对象
     */
    @Override
    //@Transactional
    public BusMachiningPojo update(BusMachiningPojo busMachiningPojo) {
        String tid = busMachiningPojo.getTenantid();
        //原始单据使用的优惠券
        BusMachiningPojo machOrg = this.busMachiningMapper.getEntity(busMachiningPojo.getId(), tid);
        String couponCodeOrg = machOrg.getBillsubcode();
        List<BusMachiningitemPojo> lst = busMachiningPojo.getItem();
        if (lst == null) throw new RuntimeException("单据内容不能为空");
        // 最终需要同步的所有GoodsidSet
        Set<String> allGoodsidSet = new HashSet<>();
        // 获取公式
        Map<String, Object> tencfg = this.redisService.getCacheObject("tenant_config:" + tid);
        String attrstrExpr = null;
        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
            attrstrExpr = tencfg.get("system.bill.attributestr").toString();
        }
        // 检查核价单
        for (int i = 0; i < lst.size(); i++) {
            BusMachiningitemPojo busMachiningitemPojo = lst.get(i);
            // 同步订单核价单
            if (isBlank(busMachiningitemPojo.getId())) {
                if (isNotBlank(busMachiningitemPojo.getOrdercostitemid())) {
                    BusOrdercostitemPojo busOrdercostitemPojo = this.busOrdercostitemMapper.getEntity(busMachiningitemPojo.getOrdercostitemid(), tid);
                    if (busOrdercostitemPojo != null && busOrdercostitemPojo.getMachmark() == 1) {
                        int Rowno = i + 1;
                        throw new RuntimeException(Rowno + "行,核价单已转订单,禁止重复转单:" + busOrdercostitemPojo.getGoodsname());
                    }
                }
            } else {
                // 读出原有记录
                BusMachiningitemPojo itemDbPojo = this.busMachiningitemMapper.getEntity(busMachiningitemPojo.getId(), tid);
                // 加入主料是否变更 20221212 EricRen
                if (itemDbPojo.getMatcode() != null && !itemDbPojo.getMatcode().equals((busMachiningitemPojo.getMatcode()))) {
                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(itemDbPojo.getMatcode(), tid);
                    this.busMachiningMapper.updateGoodsRequRemQty(matid, itemDbPojo.getMatcode(), tid);
                }
            }
            // 计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
            if (isNotBlank(attrstrExpr) && isNotBlank(busMachiningitemPojo.getAttributejson())) {
                busMachiningitemPojo.setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(busMachiningitemPojo.getAttributejson(), attrstrExpr));
            }
        }
        //主表更改
        BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
        BeanUtils.copyProperties(busMachiningPojo, busMachiningEntity);
        busMachiningEntity.setItemcount(lst.size());
        // 使用金额：即整个销售订单的优惠金额，累加所有项的 Subamount
        double totalSubAmount = lst.stream()
                .mapToDouble(item -> item.getSubamount() == null ? 0.0 : item.getSubamount())
                .sum();
        busMachiningEntity.setBillsubtotal(totalSubAmount);
        //Item子表处理
        transactionTemplate.execute((status) -> {
            // 统计生产行数(销售订单子表生产需求WkQty>0的行数);
            busMachiningEntity.setWkitemcount(Math.toIntExact(lst.stream().filter(item -> item.getWkqty() > 0).count()));
            this.busMachiningMapper.update(busMachiningEntity);
            //获取被删除的Item
            List<String> lstDelIds = busMachiningMapper.getDelItemIds(busMachiningPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    BusMachiningitemPojo busMachiningitemPojo = this.busMachiningitemMapper.getEntity(lstDelId, busMachiningEntity.getTenantid());
                    allGoodsidSet.add(busMachiningitemPojo.getGoodsid());
                    List<String> lstcite = getItemCiteBillName(lstDelId, busMachiningitemPojo.getPid(), busMachiningEntity.getTenantid());
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.busMachiningitemMapper.delete(lstDelId, busMachiningEntity.getTenantid());
                    // 同步订单核价单
                    if (busMachiningitemPojo.getOrdercostitemid() != null && !"".equals(busMachiningitemPojo.getOrdercostitemid())) {
                        this.busMachiningMapper.updateOrderCostItemMachMark(busMachiningitemPojo.getOrdercostitemid(), busMachiningitemPojo.getTenantid());
                        this.busMachiningMapper.updateOrderCostFinishCount(busMachiningitemPojo.getOrdercostitemid(), busMachiningitemPojo.getTenantid());
                    }
                    // 同步订单报价单
                    String quotitemid = busMachiningitemPojo.getQuotitemid();
                    if (isNotBlank(quotitemid)) {
                        this.busMachiningMapper.updateQuotItemMachMark(quotitemid, tid);
                        this.busMachiningMapper.updateQuotFinishCount(quotitemid, tid);
                    }
                    // 同步货品数量 (物料)
                    if (busMachiningitemPojo.getMatcode() != null && !"".equals(busMachiningitemPojo.getMatcode())) {
                        String matid = this.busMachiningMapper.getGoodsidByGoodsUid(busMachiningitemPojo.getMatcode(), tid);
                        this.busMachiningMapper.updateGoodsRequRemQty(matid, busMachiningitemPojo.getMatcode(), tid);
                    }
                }
            }

            //循环每个item子表
            for (BusMachiningitemPojo busMachiningitemPojo : lst) {
                BusMachiningitemEntity busMachiningitemEntity = new BusMachiningitemEntity();
                if ("".equals(busMachiningitemPojo.getId()) || busMachiningitemPojo.getId() == null) {
                    //初始化item的NULL
                    BusMachiningitemPojo itemPojo = this.busMachiningitemService.clearNull(busMachiningitemPojo);
                    BeanUtils.copyProperties(itemPojo, busMachiningitemEntity);
                    //设置id和Pid
                    busMachiningitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busMachiningitemEntity.setPid(busMachiningEntity.getId());  // 主表 id
                    busMachiningitemEntity.setTenantid(tid);   // 租户id
                    busMachiningitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busMachiningitemMapper.insert(busMachiningitemEntity);
                } else {
                    busMachiningitemPojo.setTenantid(tid);
                    // 读出原有记录
                    BeanUtils.copyProperties(busMachiningitemPojo, busMachiningitemEntity);
                    this.busMachiningitemMapper.update(busMachiningitemEntity);
                    // 刷新有送货单单价；Eric 20220708
                    if (tencfg != null && tencfg.get("module.sale.machsyncdeliprice") != null) {
                        String machsyncdeliprice = tencfg.get("module.sale.machsyncdeliprice").toString();
                        if (machsyncdeliprice.equals("true")) {
                            this.busMachiningMapper.updateMachDeliPrice(busMachiningitemPojo);
                            this.busMachiningMapper.updateMachDeliBillAmt(busMachiningitemPojo);
                        }
                    }
                }
                // 同步订单核价单
                String ordercostitemid = busMachiningitemEntity.getOrdercostitemid();
                if (isNotBlank(ordercostitemid)) {
                    this.busMachiningMapper.updateOrderCostItemMachMark(ordercostitemid, tid);
                    this.busMachiningMapper.updateOrderCostFinishCount(ordercostitemid, tid);
                }

                // 同步订单报价单
                String quotitemid = busMachiningitemPojo.getQuotitemid();
                if (isNotBlank(quotitemid)) {
                    this.busMachiningMapper.updateQuotItemMachMark(quotitemid, tid);
                    this.busMachiningMapper.updateQuotFinishCount(quotitemid, tid);
                }
                // 同步货品数量 (物料)
                String matcode = busMachiningitemPojo.getMatcode();
                if (isNotBlank(matcode)) {
                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(matcode, tid);
                    this.busMachiningMapper.updateGoodsRequRemQty(matid, matcode, tid);
                }
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
            allGoodsidSet.addAll(goodsidLstSet);
            // 同步货品数量 SQL替代MQ
            allGoodsidSet.forEach(goodsid -> {
                saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
            });
            // 同步货品数量 SQL替代MQ
            saleSyncMapper.updateWorkgroupBusMachRemAmt(busMachiningPojo.getGroupid(), tid);

            // TODO 如果有优惠券：优惠券处理 使用记录修改或新增
            Date date = new Date();
            String couponid = busMachiningPojo.getCouponid();
            String couponCode = busMachiningPojo.getBillsubcode();
            // 1. 删除原单据的优惠券记录（只要有就删）
            if (isNotBlank(couponCodeOrg)) {
                busCouponusageService.deleteByCouponCodeAndCiteid(couponCodeOrg, machOrg.getId(), tid);
            }

            // 2. 新增本次使用记录（如果本次有优惠券）
            if (isNotBlank(couponid)) {
                //1. 优惠券使用记录：绑定到销售订单
                BusCouponusagePojo couponusagePojo = new BusCouponusagePojo();
                couponusagePojo.setCouponid(couponid);
                couponusagePojo.setModulecode("D01M03B1");
                couponusagePojo.setCiteuid(busMachiningPojo.getRefno());
                couponusagePojo.setCiteid(busMachiningPojo.getId());
                // 设置总的使用优惠金额
                couponusagePojo.setUsedamount(busMachiningEntity.getBillsubtotal());
                couponusagePojo.setCreateby(busMachiningPojo.getCreateby());
                couponusagePojo.setCreatebyid(busMachiningPojo.getCreatebyid());
                couponusagePojo.setCreatedate(date);
                couponusagePojo.setLister(busMachiningPojo.getCreateby());
                couponusagePojo.setListerid(busMachiningPojo.getCreatebyid());
                couponusagePojo.setModifydate(date);
                busCouponusageService.insert(couponusagePojo);
            }
            return Boolean.TRUE;
        });

        //返回Bill实例
        return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        BusMachiningPojo busMachiningPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusMachiningitemPojo> lst = busMachiningPojo.getItem();
        Integer delNum;
        delNum = transactionTemplate.execute((status) -> {
            if (lst != null) {
                //循环每个删除item子表
                for (BusMachiningitemPojo busMachiningitemPojo : lst) {
                    List<String> lstcite = getItemCiteBillName(busMachiningitemPojo.getId(), busMachiningitemPojo.getPid(), tid);
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.busMachiningitemMapper.delete(busMachiningitemPojo.getId(), tid);
                    // 同步订单核价单
                    if (busMachiningitemPojo.getOrdercostitemid() != null && !"".equals(busMachiningitemPojo.getOrdercostitemid())) {
                        this.busMachiningMapper.updateOrderCostItemMachMark(busMachiningitemPojo.getOrdercostitemid(), tid);
                        this.busMachiningMapper.updateOrderCostFinishCount(busMachiningitemPojo.getOrdercostitemid(), tid);
                    }
                    // 同步订单报价单
                    if (busMachiningitemPojo.getQuotitemid() != null && !"".equals(busMachiningitemPojo.getQuotitemid())) {
                        this.busMachiningMapper.updateQuotItemMachMark(busMachiningitemPojo.getQuotitemid(), tid);
                        this.busMachiningMapper.updateQuotFinishCount(busMachiningitemPojo.getQuotitemid(), tid);
                    }
                    // 同步货品数量
                    if (busMachiningitemPojo.getMatcode() != null && !"".equals(busMachiningitemPojo.getMatcode())) {
                        String matid = this.busMachiningMapper.getGoodsidByGoodsUid(busMachiningitemPojo.getMatcode(), tid);
                        this.busMachiningMapper.updateGoodsRequRemQty(matid, busMachiningitemPojo.getMatcode(), tid);
                    }
                }
            }
            this.busMachiningMapper.delete(key, tid);

            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
            });
            // 同步货品数量 SQL替代MQ
            saleSyncMapper.updateWorkgroupBusMachRemAmt(busMachiningPojo.getGroupid(), tid);
            // 删除优惠券使用并同步优惠券主表已使用金额
            String couponid = busMachiningPojo.getCouponid();
            if (isNotBlank(couponid)) {
                busCouponusageService.deleteByCouponidAndCiteid(couponid, busMachiningPojo.getId(), tid);
            }
            return lst.size();
        });

        return delNum;
    }


    private static void cleanNull(BusMachiningPojo busMachiningPojo) {
        if (busMachiningPojo.getRefno() == null) busMachiningPojo.setRefno("");
        if (busMachiningPojo.getBilltype() == null) busMachiningPojo.setBilltype("");
        if (busMachiningPojo.getBilltitle() == null) busMachiningPojo.setBilltitle("");
        if (busMachiningPojo.getBilldate() == null) busMachiningPojo.setBilldate(new Date());
        if (busMachiningPojo.getGroupid() == null) busMachiningPojo.setGroupid("");
        if (busMachiningPojo.getCustorderid() == null) busMachiningPojo.setCustorderid("");
        if (busMachiningPojo.getLogisticsmode() == null) busMachiningPojo.setLogisticsmode("");
        if (busMachiningPojo.getLogisticsport() == null) busMachiningPojo.setLogisticsport("");
        if (busMachiningPojo.getCountry() == null) busMachiningPojo.setCountry("");
        if (busMachiningPojo.getAdvaamount() == null) busMachiningPojo.setAdvaamount(0D);
        if (busMachiningPojo.getSalesman() == null) busMachiningPojo.setSalesman("");
        if (busMachiningPojo.getSalesmanid() == null) busMachiningPojo.setSalesmanid("");
        if (busMachiningPojo.getTaxrate() == null) busMachiningPojo.setTaxrate(0);
        if (busMachiningPojo.getSummary() == null) busMachiningPojo.setSummary("");
        if (busMachiningPojo.getCreateby() == null) busMachiningPojo.setCreateby("");
        if (busMachiningPojo.getCreatebyid() == null) busMachiningPojo.setCreatebyid("");
        if (busMachiningPojo.getCreatedate() == null) busMachiningPojo.setCreatedate(new Date());
        if (busMachiningPojo.getLister() == null) busMachiningPojo.setLister("");
        if (busMachiningPojo.getListerid() == null) busMachiningPojo.setListerid("");
        if (busMachiningPojo.getModifydate() == null) busMachiningPojo.setModifydate(new Date());
        if (busMachiningPojo.getAssessor() == null) busMachiningPojo.setAssessor("");
        if (busMachiningPojo.getAssessorid() == null) busMachiningPojo.setAssessorid("");
        if (busMachiningPojo.getAssessdate() == null) busMachiningPojo.setAssessdate(new Date());
        if (busMachiningPojo.getBilltaxamount() == null) busMachiningPojo.setBilltaxamount(0D);
        if (busMachiningPojo.getBilltaxtotal() == null) busMachiningPojo.setBilltaxtotal(0D);
        if (busMachiningPojo.getBillamount() == null) busMachiningPojo.setBillamount(0D);
        if (busMachiningPojo.getBillstatecode() == null) busMachiningPojo.setBillstatecode("");
        if (busMachiningPojo.getBillstatedate() == null) busMachiningPojo.setBillstatedate(new Date());
        if (busMachiningPojo.getBillplandate() == null) busMachiningPojo.setBillplandate(new Date());
        if (busMachiningPojo.getBillwkwpid() == null) busMachiningPojo.setBillwkwpid("");
        if (busMachiningPojo.getBillwkwpcode() == null) busMachiningPojo.setBillwkwpcode("");
        if (busMachiningPojo.getBillwkwpname() == null) busMachiningPojo.setBillwkwpname("");
        if (busMachiningPojo.getGroupcode() == null) busMachiningPojo.setGroupcode("");
        if (busMachiningPojo.getDisannulcount() == null) busMachiningPojo.setDisannulcount(0);
        if (busMachiningPojo.getItemcount() == null) busMachiningPojo.setItemcount(busMachiningPojo.getItem().size());
        if (busMachiningPojo.getPickcount() == null) busMachiningPojo.setPickcount(0);
        if (busMachiningPojo.getFinishcount() == null) busMachiningPojo.setFinishcount(0);
        if (busMachiningPojo.getPrintcount() == null) busMachiningPojo.setPrintcount(0);
        if (busMachiningPojo.getWkfinishcount() == null) busMachiningPojo.setWkfinishcount(0);
        if (busMachiningPojo.getWkitemcount() == null) busMachiningPojo.setWkitemcount(0);
        if (busMachiningPojo.getWkwipcount() == null) busMachiningPojo.setWkwipcount(0);
        if (busMachiningPojo.getPayment() == null) busMachiningPojo.setPayment("");
        if (busMachiningPojo.getOaflowmark() == null) busMachiningPojo.setOaflowmark(0);
        if (busMachiningPojo.getBillcostbudgetamt() == null) busMachiningPojo.setBillcostbudgetamt(0D);
        if (busMachiningPojo.getFirstamt() == null) busMachiningPojo.setFirstamt(0D);
        if (busMachiningPojo.getInvoamt() == null) busMachiningPojo.setInvoamt(0D);
        if (busMachiningPojo.getInvocount() == null) busMachiningPojo.setInvocount(0);
        if (busMachiningPojo.getLastamt() == null) busMachiningPojo.setLastamt(0D);
//        if (busMachiningPojo.getMoneyid() == null) busMachiningPojo.setMoneyid(0);  //0可能也是一个币种
        if (busMachiningPojo.getMoneyname() == null) busMachiningPojo.setMoneyname("");
        if (busMachiningPojo.getMainplancount() == null) busMachiningPojo.setMainplancount(0);
        if (busMachiningPojo.getBillsubtotal() == null) busMachiningPojo.setBillsubtotal(0D);
        if (busMachiningPojo.getBillsubcode() == null) busMachiningPojo.setBillsubcode("");
        if (busMachiningPojo.getFinishtaxamount() == null) busMachiningPojo.setFinishtaxamount(0D);
        if (busMachiningPojo.getFinishamount() == null) busMachiningPojo.setFinishamount(0D);
        if (busMachiningPojo.getExponent() == null) busMachiningPojo.setExponent(0);
        if (busMachiningPojo.getCustom1() == null) busMachiningPojo.setCustom1("");
        if (busMachiningPojo.getCustom2() == null) busMachiningPojo.setCustom2("");
        if (busMachiningPojo.getCustom3() == null) busMachiningPojo.setCustom3("");
        if (busMachiningPojo.getCustom4() == null) busMachiningPojo.setCustom4("");
        if (busMachiningPojo.getCustom5() == null) busMachiningPojo.setCustom5("");
        if (busMachiningPojo.getCustom6() == null) busMachiningPojo.setCustom6("");
        if (busMachiningPojo.getCustom7() == null) busMachiningPojo.setCustom7("");
        if (busMachiningPojo.getCustom8() == null) busMachiningPojo.setCustom8("");
        if (busMachiningPojo.getCustom9() == null) busMachiningPojo.setCustom9("");
        if (busMachiningPojo.getCustom10() == null) busMachiningPojo.setCustom10("");
        if (busMachiningPojo.getDeptid() == null) busMachiningPojo.setDeptid("");
        if (busMachiningPojo.getTenantname() == null) busMachiningPojo.setTenantname("");
        if (busMachiningPojo.getTenantid() == null) busMachiningPojo.setTenantid("");
        if (busMachiningPojo.getRevision() == null) busMachiningPojo.setRevision(0);
    }


    /**
     * 审核数据
     *
     * @param busMachiningPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusMachiningPojo approval(BusMachiningPojo busMachiningPojo) {
        //主表更改
        BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
        BeanUtils.copyProperties(busMachiningPojo, busMachiningEntity);
        this.busMachiningMapper.approval(busMachiningEntity);
        //返回Bill实例
        return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusMachiningPojo disannul(List<BusMachiningitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BusMachiningitemPojo Pojo = lst.get(i);
            BusMachiningitemPojo dbPojo = this.busMachiningitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getWipused() == 1 || dbPojo.getWkquantity() > 0 || dbPojo.getBuyquantity() > 0 ||
                            dbPojo.getPickqty() > 0 || dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    BusMachiningitemEntity entity = new BusMachiningitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
//                    entity.setDisannuldate(new Date());
//                    entity.setDisannullister(loginUser.getRealname());
//                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.busMachiningitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }

                // 同步订单核价单
                if (dbPojo.getOrdercostitemid() != null && !"".equals(dbPojo.getOrdercostitemid())) {
                    this.busMachiningMapper.updateOrderCostItemMachMark(dbPojo.getOrdercostitemid(), tid);
                    this.busMachiningMapper.updateOrderCostFinishCount(dbPojo.getOrdercostitemid(), tid);
                }

                // 同步订单报价单
                if (dbPojo.getQuotitemid() != null && !"".equals(dbPojo.getQuotitemid())) {
                    this.busMachiningMapper.updateQuotItemMachMark(dbPojo.getQuotitemid(), tid);
                    this.busMachiningMapper.updateQuotFinishCount(dbPojo.getQuotitemid(), tid);
                }
                // 同步货品数量
                if (dbPojo.getMatcode() != null && !"".equals(dbPojo.getMatcode())) {
                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(dbPojo.getMatcode(), tid);
                    this.busMachiningMapper.updateGoodsRequRemQty(matid, dbPojo.getMatcode(), tid);
                }

            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
        });
        // 同步客户余额(来自同一个客户) SQL替代MQ
        String groupid = busMachiningitemMapper.getGroupidByItemid(lst.get(0).getId(), tid);
        saleSyncMapper.updateWorkgroupBusMachRemAmt(groupid, tid);

        if (disNum > 0) {
            this.busMachiningMapper.updateDisannulCount(Pid, tid);
            //主表更改
            BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
            busMachiningEntity.setId(Pid);
            busMachiningEntity.setLister(loginUser.getRealname());
            busMachiningEntity.setListerid(loginUser.getUserid());
            busMachiningEntity.setModifydate(new Date());
            busMachiningEntity.setTenantid(loginUser.getTenantid());
            this.busMachiningMapper.update(busMachiningEntity);
            //返回Bill实例
            return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    @OperLog(title = "中止销售订单")
    public BusMachiningPojo closed(List<BusMachiningitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BusMachiningitemPojo Pojo = lst.get(i);
            BusMachiningitemPojo dbPojo = this.busMachiningitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BusMachiningitemEntity entity = new BusMachiningitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.busMachiningitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
                // 同步订单核价单
                if (dbPojo.getOrdercostitemid() != null && !"".equals(dbPojo.getOrdercostitemid())) {
                    this.busMachiningMapper.updateOrderCostItemMachMark(dbPojo.getOrdercostitemid(), tid);
                    this.busMachiningMapper.updateOrderCostFinishCount(dbPojo.getOrdercostitemid(), tid);
                }

                // 同步订单报价单
                if (dbPojo.getQuotitemid() != null && !"".equals(dbPojo.getQuotitemid())) {
                    this.busMachiningMapper.updateQuotItemMachMark(dbPojo.getQuotitemid(), tid);
                    this.busMachiningMapper.updateQuotFinishCount(dbPojo.getQuotitemid(), tid);
                }
                // 同步货品数量
                if (dbPojo.getMatcode() != null && !"".equals(dbPojo.getMatcode())) {
                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(dbPojo.getMatcode(), tid);
                    this.busMachiningMapper.updateGoodsRequRemQty(matid, dbPojo.getMatcode(), tid);
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
        });
        // 同步客户余额(来自同一个客户) SQL替代MQ
        String groupid = busMachiningitemMapper.getGroupidByItemid(lst.get(0).getId(), tid);
        saleSyncMapper.updateWorkgroupBusMachRemAmt(groupid, tid);

        if (disNum > 0) {
            this.busMachiningMapper.updateFinishCount(Pid, tid);
            //主表更改
            BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
            busMachiningEntity.setId(Pid);
            busMachiningEntity.setLister(loginUser.getRealname());
            busMachiningEntity.setListerid(loginUser.getUserid());
            busMachiningEntity.setModifydate(new Date());
            busMachiningEntity.setTenantid(loginUser.getTenantid());
            this.busMachiningMapper.update(busMachiningEntity);
            //返回Bill实例
            return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.busMachiningMapper.getItemCiteBillName(key, pid, tid);
    }

    /**
     * 查询 所有Item
     *
     * @param ids 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusMachiningitemPojo> getItemListByIds(String ids, String pid, String tid) {

        return this.busMachiningMapper.getItemListByIds(ids, pid, tid);
    }

    private final String PRINTBATCH_STATE = "printbatch_codes:";

    @Override
    @Async
    // 开始批量打印
    public void printBatchBillStart(List<String> ids, String uuid, String printapproved, ReportsPojo reportsPojo, LoginUser loginUser) {

        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100"); //开始处理代码
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("totalCount", ids.size());
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(PRINTBATCH_STATE, uuid, missionMsg);
            //数据填充
            JasperPrint printAll = new JasperPrint();
            List<JasperPrint> lstPrint = new ArrayList<>();
            String content = reportsPojo.getRptdata();
            int successCount = 0;
            int failCount = 0;
            for (int a = 0; a < ids.size(); a++) {
                String key = ids.get(a);
                //获取单据信息
                BusMachiningPojo busMachiningPojo = this.getBillEntity(key, loginUser.getTenantid());
                if (busMachiningPojo == null) {
                    failCount++;
                } else if (printapproved != null && printapproved.equals("true") && "".equals(busMachiningPojo.getAssessor())) {
                    failCount++;
                } else {
                    //表头转MAP
                    Map<String, Object> map = inks.common.core.utils.bean.BeanUtils.beanToMap(busMachiningPojo);
                    // 加入公司信息
                    inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                    // 判定是否需要追行
                    if (reportsPojo.getPagerow() > 0) {
                        int index = 0;
                        // 取行余数
                        index = busMachiningPojo.getItem().size() % reportsPojo.getPagerow();
                        if (index > 0) {
                            // 补全空白行
                            for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                                BusMachiningitemPojo busMachiningitemPojo = new BusMachiningitemPojo();
                                busMachiningPojo.getItem().add(busMachiningitemPojo);
                            }
                        }
                    }
                    // 带属性List转为Map  EricRen 20220427
                    List<Map<String, Object>> lst = attrcostListToMaps(busMachiningPojo.getItem());
                    //item转数据源
                    JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
                    //报表生成
                    InputStream stream = new ByteArrayInputStream(content.getBytes());
                    //编译报表
                    JasperReport jasperReport = JasperCompileManager.compileReport(stream);
                    //数据填充
                    JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                    if (a == 0) {
                        printAll = print;
                    } else {
                        List<JRPrintPage> pages = print.getPages();
                        for (JRPrintPage page : pages) {
                            printAll.addPage(page);
                        }
                    }
                    lstPrint.add(print);
                }
                successCount++;
                missionMsg.put("code", "150"); //任务处理中代码
                missionMsg.put("msg", "任务处理中");
                missionMsg.put("totalCount", ids.size());
                missionMsg.put("successCount", successCount);
                this.redisService.setCacheMapValue(PRINTBATCH_STATE, uuid, missionMsg);
            }

//            //输出文件
//            String pdfPath = "D:\\test.pdf";
//            JasperExportManager.exportReportToPdfFile(printAll,pdfPath);


            byte[] base64File = StreamUtils.toByteArray(printAll);

            String cachekey = "report_pages:" + uuid;
            this.redisService.setCacheObject(cachekey, base64File, 60L, TimeUnit.MINUTES);

            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("successCount", successCount);
            missionMsg.put("failCount", failCount);
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(PRINTBATCH_STATE, uuid, missionMsg);

        } catch (Exception ex) {

        }
    }

    /**
     * 获取批量打印状态
     *
     * @param key
     * @return
     */
    @Override
    public Map<String, Object> getPrintBatchBillState(String key) {
        return this.redisService.getCacheMapValue(PRINTBATCH_STATE, key);
    }

    @Override
    public int updatePrintcount(BusMachiningPojo billPrintPojo) {
        return this.busMachiningMapper.updatePrintcount(billPrintPojo);
    }


    @Override//通过客户id查询销售订单总应收款，已收款，未发货金额
    public Map<String, Object> getLastAmtByGroupId(String groupid, String tenantid) {
        // 返回未关闭未作废订单子表的sum TaxAmount,AvgLastAmt
        Map<String, Object> map1 = this.busMachiningMapper.getLastAmtByGroupId(groupid, tenantid);
        // 返回未发货金额
        Double amt = this.busMachiningMapper.getOnlineMachTaxAmountByGroupId(groupid, tenantid);
        map1.put("onlinetaxamount", amt);

        return map1;
    }

    @Override
    public List<Map<String, Object>> checkHistoryBillType(List<String> goodsids, String tenantid) {
        return this.busMachiningMapper.checkHistoryBillType(goodsids, tenantid);
    }

    @Override
    @Transactional
    public BusMachiningPojo deleteCoupon(String key, String couponid, String tid) {
        BusMachiningPojo machDB = getBillEntity(key, tid);
        // 删除销售单优惠券(优惠券字段置空，关联优惠券使用删除)
        machDB.setBillsubcode("");
        machDB.setBillsubtotal(0D);
        // 重新计算每个商品优惠(即sub优惠变为0)
        BigDecimal billAmount = BigDecimal.ZERO;
        BigDecimal billTaxAmount = BigDecimal.ZERO;
        for (BusMachiningitemPojo machitemDB : machDB.getItem()) {
            machitemDB.setSubprice(0D);
            machitemDB.setSubamount(0D);
            BigDecimal stdPrice = BigDecimal.valueOf(machitemDB.getStdprice());
            BigDecimal stdAmount = BigDecimal.valueOf(machitemDB.getStdamount());
            int taxRate = machitemDB.getItemtaxrate(); // 税率（单位：百分比）
            // 设置价格和金额
            machitemDB.setPrice(stdPrice.doubleValue());
            machitemDB.setAmount(stdAmount.doubleValue());
            // 计算税率比例
            BigDecimal rate = BigDecimal.valueOf(taxRate).divide(BigDecimal.valueOf(100));
            // 含税单价 = 单价 × (1 + 税率)
            BigDecimal taxPrice = stdPrice.multiply(BigDecimal.ONE.add(rate));
            // 含税总额 = 金额 × (1 + 税率)
            BigDecimal taxAmount = stdAmount.multiply(BigDecimal.ONE.add(rate));
            // 税额 = 含税总额 - 原金额
            BigDecimal taxOnly = taxAmount.subtract(stdAmount);
            // 设置含税字段
            machitemDB.setTaxprice(taxPrice.doubleValue());
            machitemDB.setTaxamount(taxAmount.doubleValue());
            machitemDB.setTaxtotal(taxOnly.doubleValue());
            // 累计主表金额
            billAmount = billAmount.add(stdPrice);
            billTaxAmount = billTaxAmount.add(taxAmount);
        }
        machDB.setBillamount(billAmount.doubleValue());
        machDB.setBilltaxamount(billTaxAmount.doubleValue());
        machDB.setBilltaxtotal(billTaxAmount.subtract(billAmount).doubleValue());
        this.update(machDB);
        busCouponusageService.deleteByCouponidAndCiteid(couponid, key, tid);
        return getBillEntity(key, tid);
    }
}
