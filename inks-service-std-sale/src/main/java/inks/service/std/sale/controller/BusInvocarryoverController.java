package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusInvocarryoverPojo;
import inks.service.std.sale.service.BusInvocarryoverService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 货品账单:发货>发票(Bus_InvoCarryover)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-10 11:30:17
 */
//@RestController
//@RequestMapping("busInvocarryover")
public class BusInvocarryoverController {

    private final static Logger logger = LoggerFactory.getLogger(BusInvocarryoverController.class);
    @Resource
    private BusInvocarryoverService busInvocarryoverService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取货品账单:发货>发票详细信息", notes = "获取货品账单:发货>发票详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_InvoCarryover.List")
    public R<BusInvocarryoverPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busInvocarryoverService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_InvoCarryover.List")
    public R<PageInfo<BusInvocarryoverPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_InvoCarryover.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvocarryoverService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增货品账单:发货>发票", notes = "新增货品账单:发货>发票", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_InvoCarryover.Add")
    public R<BusInvocarryoverPojo> create(@RequestBody String json) {
        try {
            BusInvocarryoverPojo busInvocarryoverPojo = JSONArray.parseObject(json, BusInvocarryoverPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busInvocarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            busInvocarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busInvocarryoverPojo.setCreatedate(new Date());   // 创建时间
            busInvocarryoverPojo.setLister(loginUser.getRealname());   // 制表
            busInvocarryoverPojo.setListerid(loginUser.getUserid());    // 制表id  
            busInvocarryoverPojo.setModifydate(new Date());   //修改时间
            busInvocarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busInvocarryoverService.insert(busInvocarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改货品账单:发货>发票", notes = "修改货品账单:发货>发票", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_InvoCarryover.Edit")
    public R<BusInvocarryoverPojo> update(@RequestBody String json) {
        try {
            BusInvocarryoverPojo busInvocarryoverPojo = JSONArray.parseObject(json, BusInvocarryoverPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busInvocarryoverPojo.setLister(loginUser.getRealname());   // 制表
            busInvocarryoverPojo.setListerid(loginUser.getUserid());    // 制表id  
            busInvocarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            busInvocarryoverPojo.setModifydate(new Date());   //修改时间
//            busInvocarryoverPojo.setAssessor(""); // 审核员
//            busInvocarryoverPojo.setAssessorid(""); // 审核员id
//            busInvocarryoverPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busInvocarryoverService.update(busInvocarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除货品账单:发货>发票", notes = "删除货品账单:发货>发票", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_InvoCarryover.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busInvocarryoverService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_InvoCarryover.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusInvocarryoverPojo busInvocarryoverPojo = this.busInvocarryoverService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busInvocarryoverPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

