package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusReceaccountEntity;
import inks.service.std.sale.domain.BusReceaccountitemEntity;
import inks.service.std.sale.domain.pojo.BusReceaccountPojo;
import inks.service.std.sale.domain.pojo.BusReceaccountitemPojo;
import inks.service.std.sale.domain.pojo.BusReceaccountitemdetailPojo;
import inks.service.std.sale.mapper.BusReceaccountMapper;
import inks.service.std.sale.mapper.BusReceaccountitemMapper;
import inks.service.std.sale.service.BusReceaccountService;
import inks.service.std.sale.service.BusReceaccountitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 应收账单(BusReceaccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-23 13:32:01
 */
@Service("busReceaccountService")
public class BusReceaccountServiceImpl implements BusReceaccountService {
    @Resource
    private BusReceaccountMapper busReceaccountMapper;
    
    @Resource
    private BusReceaccountitemMapper busReceaccountitemMapper;
    
     /**
     * 服务对象Item
     */
    @Resource
    private BusReceaccountitemService busReceaccountitemService;
    
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusReceaccountPojo getEntity(String key, String tid) {
        return this.busReceaccountMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceaccountitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceaccountitemdetailPojo> lst = busReceaccountMapper.getPageList(queryParam);
            PageInfo<BusReceaccountitemdetailPojo> pageInfo = new PageInfo<BusReceaccountitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
     /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusReceaccountPojo getBillEntity(String key, String tid) {
       try {
        //读取主表
        BusReceaccountPojo busReceaccountPojo = this.busReceaccountMapper.getEntity(key,tid);
        //读取子表
        busReceaccountPojo.setItem(busReceaccountitemMapper.getList(busReceaccountPojo.getId(),busReceaccountPojo.getTenantid()));
        return busReceaccountPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceaccountPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceaccountPojo> lst = busReceaccountMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(busReceaccountitemMapper.getList(lst.get(i).getId(),lst.get(i).getTenantid()));
            }
            PageInfo<BusReceaccountPojo> pageInfo = new PageInfo<BusReceaccountPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
       /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceaccountPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceaccountPojo> lst = busReceaccountMapper.getPageTh(queryParam);
            PageInfo<BusReceaccountPojo> pageInfo = new PageInfo<BusReceaccountPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param busReceaccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusReceaccountPojo insert(BusReceaccountPojo busReceaccountPojo) {
//初始化NULL字段
     if(busReceaccountPojo.getRefno()==null) busReceaccountPojo.setRefno("");
     if(busReceaccountPojo.getBilltype()==null) busReceaccountPojo.setBilltype("");
     if(busReceaccountPojo.getBilldate()==null) busReceaccountPojo.setBilldate(new Date());
     if(busReceaccountPojo.getBilltitle()==null) busReceaccountPojo.setBilltitle("");
     if(busReceaccountPojo.getGroupid()==null) busReceaccountPojo.setGroupid("");
     if(busReceaccountPojo.getCarryyear()==null) busReceaccountPojo.setCarryyear(0);
     if(busReceaccountPojo.getCarrymonth()==null) busReceaccountPojo.setCarrymonth(0);
     if(busReceaccountPojo.getStartdate()==null) busReceaccountPojo.setStartdate(new Date());
     if(busReceaccountPojo.getEnddate()==null) busReceaccountPojo.setEnddate(new Date());
     if(busReceaccountPojo.getOperator()==null) busReceaccountPojo.setOperator("");
     if(busReceaccountPojo.getOperatorid()==null) busReceaccountPojo.setOperatorid("");
     if(busReceaccountPojo.getSummary()==null) busReceaccountPojo.setSummary("");
     if(busReceaccountPojo.getCreateby()==null) busReceaccountPojo.setCreateby("");
     if(busReceaccountPojo.getCreatebyid()==null) busReceaccountPojo.setCreatebyid("");
     if(busReceaccountPojo.getCreatedate()==null) busReceaccountPojo.setCreatedate(new Date());
     if(busReceaccountPojo.getLister()==null) busReceaccountPojo.setLister("");
     if(busReceaccountPojo.getListerid()==null) busReceaccountPojo.setListerid("");
     if(busReceaccountPojo.getModifydate()==null) busReceaccountPojo.setModifydate(new Date());
     if(busReceaccountPojo.getBillopenamount()==null) busReceaccountPojo.setBillopenamount(0D);
     if(busReceaccountPojo.getBillinamount()==null) busReceaccountPojo.setBillinamount(0D);
     if(busReceaccountPojo.getBilloutamount()==null) busReceaccountPojo.setBilloutamount(0D);
     if(busReceaccountPojo.getBillcloseamount()==null) busReceaccountPojo.setBillcloseamount(0D);
     if(busReceaccountPojo.getPrintcount()==null) busReceaccountPojo.setPrintcount(0);
     if(busReceaccountPojo.getCustom1()==null) busReceaccountPojo.setCustom1("");
     if(busReceaccountPojo.getCustom2()==null) busReceaccountPojo.setCustom2("");
     if(busReceaccountPojo.getCustom3()==null) busReceaccountPojo.setCustom3("");
     if(busReceaccountPojo.getCustom4()==null) busReceaccountPojo.setCustom4("");
     if(busReceaccountPojo.getCustom5()==null) busReceaccountPojo.setCustom5("");
     if(busReceaccountPojo.getCustom6()==null) busReceaccountPojo.setCustom6("");
     if(busReceaccountPojo.getCustom7()==null) busReceaccountPojo.setCustom7("");
     if(busReceaccountPojo.getCustom8()==null) busReceaccountPojo.setCustom8("");
     if(busReceaccountPojo.getCustom9()==null) busReceaccountPojo.setCustom9("");
     if(busReceaccountPojo.getCustom10()==null) busReceaccountPojo.setCustom10("");
     if(busReceaccountPojo.getTenantid()==null) busReceaccountPojo.setTenantid("");
     if(busReceaccountPojo.getTenantname()==null) busReceaccountPojo.setTenantname("");
     if(busReceaccountPojo.getRevision()==null) busReceaccountPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusReceaccountEntity busReceaccountEntity = new BusReceaccountEntity(); 
        BeanUtils.copyProperties(busReceaccountPojo,busReceaccountEntity);
        //设置id和新建日期
        busReceaccountEntity.setId(id);
        busReceaccountEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busReceaccountMapper.insert(busReceaccountEntity);
        //Item子表处理
        List<BusReceaccountitemPojo> lst = busReceaccountPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               BusReceaccountitemPojo itemPojo =this.busReceaccountitemService.clearNull(lst.get(i));
               BusReceaccountitemEntity busReceaccountitemEntity = new BusReceaccountitemEntity(); 
               BeanUtils.copyProperties(itemPojo,busReceaccountitemEntity);
               //设置id和Pid
               busReceaccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               busReceaccountitemEntity.setPid(id);
               busReceaccountitemEntity.setTenantid(busReceaccountPojo.getTenantid());
               busReceaccountitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.busReceaccountitemMapper.insert(busReceaccountitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(busReceaccountEntity.getId(),busReceaccountEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busReceaccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusReceaccountPojo update(BusReceaccountPojo busReceaccountPojo) {
        //主表更改
        BusReceaccountEntity busReceaccountEntity = new BusReceaccountEntity(); 
        BeanUtils.copyProperties(busReceaccountPojo,busReceaccountEntity);
        this.busReceaccountMapper.update(busReceaccountEntity);
        if (busReceaccountPojo.getItem() != null) {
        //Item子表处理
        List<BusReceaccountitemPojo> lst = busReceaccountPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =busReceaccountMapper.getDelItemIds(busReceaccountPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.busReceaccountitemMapper.delete(lstDelIds.get(i),busReceaccountEntity.getTenantid());
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               BusReceaccountitemEntity busReceaccountitemEntity = new BusReceaccountitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               BusReceaccountitemPojo itemPojo =this.busReceaccountitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,busReceaccountitemEntity);
               //设置id和Pid
               busReceaccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               busReceaccountitemEntity.setPid(busReceaccountEntity.getId());  // 主表 id
               busReceaccountitemEntity.setTenantid(busReceaccountPojo.getTenantid());   // 租户id
               busReceaccountitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.busReceaccountitemMapper.insert(busReceaccountitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),busReceaccountitemEntity);       
                busReceaccountitemEntity.setTenantid(busReceaccountPojo.getTenantid());        
               this.busReceaccountitemMapper.update(busReceaccountitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(busReceaccountEntity.getId(),busReceaccountEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
       BusReceaccountPojo busReceaccountPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<BusReceaccountitemPojo> lst = busReceaccountPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for (BusReceaccountitemPojo busReceaccountitemPojo : lst) {
                this.busReceaccountitemMapper.delete(busReceaccountitemPojo.getId(), tid);
            }
        }        
         this.busReceaccountMapper.delete(key,tid) ;
        return busReceaccountPojo.getRefno();
    }
    

    
                                                                                                                                                                                         
}
