package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusDelieryitemEntity;
import inks.service.std.sale.domain.pojo.BusDelieryitemPojo;
import inks.service.std.sale.mapper.BusDelieryitemMapper;
import inks.service.std.sale.service.BusDelieryitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 发货明细(BusDelieryitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-14 20:05:16
 */
@Service("busDelieryitemService")
public class BusDelieryitemServiceImpl implements BusDelieryitemService {
    @Resource
    private BusDelieryitemMapper busDelieryitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDelieryitemPojo getEntity(String key, String tid) {
        return this.busDelieryitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryitemPojo> lst = busDelieryitemMapper.getPageList(queryParam);
            PageInfo<BusDelieryitemPojo> pageInfo = new PageInfo<BusDelieryitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusDelieryitemPojo> getList(String Pid, String tid) {
        try {
            List<BusDelieryitemPojo> lst = busDelieryitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param busDelieryitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDelieryitemPojo insert(BusDelieryitemPojo busDelieryitemPojo) {
        //初始化item的NULL
        BusDelieryitemPojo itempojo = this.clearNull(busDelieryitemPojo);
        BusDelieryitemEntity busDelieryitemEntity = new BusDelieryitemEntity();
        BeanUtils.copyProperties(itempojo, busDelieryitemEntity);

        busDelieryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busDelieryitemEntity.setRevision(1);  //乐观锁
        this.busDelieryitemMapper.insert(busDelieryitemEntity);
        return this.getEntity(busDelieryitemEntity.getId(), busDelieryitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busDelieryitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDelieryitemPojo update(BusDelieryitemPojo busDelieryitemPojo) {
        BusDelieryitemEntity busDelieryitemEntity = new BusDelieryitemEntity();
        BeanUtils.copyProperties(busDelieryitemPojo, busDelieryitemEntity);
        this.busDelieryitemMapper.update(busDelieryitemEntity);
        return this.getEntity(busDelieryitemEntity.getId(), busDelieryitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busDelieryitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param busDelieryitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDelieryitemPojo clearNull(BusDelieryitemPojo busDelieryitemPojo) {
        //初始化NULL字段
        if (busDelieryitemPojo.getPid() == null) busDelieryitemPojo.setPid("");
        if (busDelieryitemPojo.getGoodsid() == null) busDelieryitemPojo.setGoodsid("");
        if (busDelieryitemPojo.getItemcode() == null) busDelieryitemPojo.setItemcode("");
        if (busDelieryitemPojo.getItemname() == null) busDelieryitemPojo.setItemname("");
        if (busDelieryitemPojo.getItemspec() == null) busDelieryitemPojo.setItemspec("");
        if (busDelieryitemPojo.getItemunit() == null) busDelieryitemPojo.setItemunit("");
        if (busDelieryitemPojo.getQuantity() == null) busDelieryitemPojo.setQuantity(0D);
        if (busDelieryitemPojo.getTaxprice() == null) busDelieryitemPojo.setTaxprice(0D);
        if (busDelieryitemPojo.getTaxamount() == null) busDelieryitemPojo.setTaxamount(0D);
        if (busDelieryitemPojo.getPrice() == null) busDelieryitemPojo.setPrice(0D);
        if (busDelieryitemPojo.getAmount() == null) busDelieryitemPojo.setAmount(0D);
        if (busDelieryitemPojo.getItemtaxrate() == null) busDelieryitemPojo.setItemtaxrate(0);
        if (busDelieryitemPojo.getTaxtotal() == null) busDelieryitemPojo.setTaxtotal(0D);
        if (busDelieryitemPojo.getStdprice() == null) busDelieryitemPojo.setStdprice(0D);
        if (busDelieryitemPojo.getStdamount() == null) busDelieryitemPojo.setStdamount(0D);
        if (busDelieryitemPojo.getRebate() == null) busDelieryitemPojo.setRebate(0D);
        if (busDelieryitemPojo.getFreeqty() == null) busDelieryitemPojo.setFreeqty(0D);
        if (busDelieryitemPojo.getPickqty() == null) busDelieryitemPojo.setPickqty(0D);
        if (busDelieryitemPojo.getFinishqty() == null) busDelieryitemPojo.setFinishqty(0D);
        if (busDelieryitemPojo.getFinishclosed() == null) busDelieryitemPojo.setFinishclosed(0);
        if (busDelieryitemPojo.getRownum() == null) busDelieryitemPojo.setRownum(0);
        if (busDelieryitemPojo.getRemark() == null) busDelieryitemPojo.setRemark("");
        if (busDelieryitemPojo.getCiteuid() == null) busDelieryitemPojo.setCiteuid("");
        if (busDelieryitemPojo.getCiteitemid() == null) busDelieryitemPojo.setCiteitemid("");
        if (busDelieryitemPojo.getCustpo() == null) busDelieryitemPojo.setCustpo("");
        if (busDelieryitemPojo.getStatecode() == null) busDelieryitemPojo.setStatecode("");
        if (busDelieryitemPojo.getStatedate() == null) busDelieryitemPojo.setStatedate(new Date());
        if (busDelieryitemPojo.getBussqty() == null) busDelieryitemPojo.setBussqty(0D);
        if (busDelieryitemPojo.getBussclosed() == null) busDelieryitemPojo.setBussclosed(0);
        if (busDelieryitemPojo.getMachtype() == null) busDelieryitemPojo.setMachtype("");
        if (busDelieryitemPojo.getInvoqty() == null) busDelieryitemPojo.setInvoqty(0D);
        if (busDelieryitemPojo.getInvoclosed() == null) busDelieryitemPojo.setInvoclosed(0);
        if (busDelieryitemPojo.getReturnqty() == null) busDelieryitemPojo.setReturnqty(0D);
        if (busDelieryitemPojo.getReturnmatqty() == null) busDelieryitemPojo.setReturnmatqty(0D);
        if (busDelieryitemPojo.getReturnclosed() == null) busDelieryitemPojo.setReturnclosed(0);
        if (busDelieryitemPojo.getSalescost() == null) busDelieryitemPojo.setSalescost(0D);
        if (busDelieryitemPojo.getVirtualitem() == null) busDelieryitemPojo.setVirtualitem(0);
        if (busDelieryitemPojo.getLocation() == null) busDelieryitemPojo.setLocation("");
        if (busDelieryitemPojo.getBatchno() == null) busDelieryitemPojo.setBatchno("");
        if (busDelieryitemPojo.getMachuid() == null) busDelieryitemPojo.setMachuid("");
        if (busDelieryitemPojo.getMachitemid() == null) busDelieryitemPojo.setMachitemid("");
        if (busDelieryitemPojo.getDisannulmark() == null) busDelieryitemPojo.setDisannulmark(0);
        if (busDelieryitemPojo.getDisannullisterid() == null) busDelieryitemPojo.setDisannullisterid("");
        if (busDelieryitemPojo.getDisannullister() == null) busDelieryitemPojo.setDisannullister("");
        if (busDelieryitemPojo.getDisannuldate() == null) busDelieryitemPojo.setDisannuldate(new Date());
        if (busDelieryitemPojo.getBfitemid() == null) busDelieryitemPojo.setBfitemid(0);
        if (busDelieryitemPojo.getAttributejson() == null) busDelieryitemPojo.setAttributejson("");
        if (busDelieryitemPojo.getAttributestr() == null) busDelieryitemPojo.setAttributestr("");
        if (busDelieryitemPojo.getCostitemjson() == null) busDelieryitemPojo.setCostitemjson("");
        if (busDelieryitemPojo.getCostgroupjson() == null) busDelieryitemPojo.setCostgroupjson("");
     if(busDelieryitemPojo.getSourcetype()==null) busDelieryitemPojo.setSourcetype(0);
     if(busDelieryitemPojo.getMachbatch()==null) busDelieryitemPojo.setMachbatch("");
     if(busDelieryitemPojo.getDeliplanuid()==null) busDelieryitemPojo.setDeliplanuid("");
     if(busDelieryitemPojo.getDeliplanitemid()==null) busDelieryitemPojo.setDeliplanitemid("");
        if (busDelieryitemPojo.getCustom1() == null) busDelieryitemPojo.setCustom1("");
        if (busDelieryitemPojo.getCustom2() == null) busDelieryitemPojo.setCustom2("");
        if (busDelieryitemPojo.getCustom3() == null) busDelieryitemPojo.setCustom3("");
        if (busDelieryitemPojo.getCustom4() == null) busDelieryitemPojo.setCustom4("");
        if (busDelieryitemPojo.getCustom5() == null) busDelieryitemPojo.setCustom5("");
        if (busDelieryitemPojo.getCustom6() == null) busDelieryitemPojo.setCustom6("");
        if (busDelieryitemPojo.getCustom7() == null) busDelieryitemPojo.setCustom7("");
        if (busDelieryitemPojo.getCustom8() == null) busDelieryitemPojo.setCustom8("");
        if (busDelieryitemPojo.getCustom9() == null) busDelieryitemPojo.setCustom9("");
        if (busDelieryitemPojo.getCustom10() == null) busDelieryitemPojo.setCustom10("");
        if (busDelieryitemPojo.getCustom11() == null) busDelieryitemPojo.setCustom11("");
        if (busDelieryitemPojo.getCustom12() == null) busDelieryitemPojo.setCustom12("");
        if (busDelieryitemPojo.getCustom13() == null) busDelieryitemPojo.setCustom13("");
        if (busDelieryitemPojo.getCustom14() == null) busDelieryitemPojo.setCustom14("");
        if (busDelieryitemPojo.getCustom15() == null) busDelieryitemPojo.setCustom15("");
        if (busDelieryitemPojo.getCustom16() == null) busDelieryitemPojo.setCustom16("");
        if (busDelieryitemPojo.getCustom17() == null) busDelieryitemPojo.setCustom17("");
        if (busDelieryitemPojo.getCustom18() == null) busDelieryitemPojo.setCustom18("");
        if (busDelieryitemPojo.getTenantid() == null) busDelieryitemPojo.setTenantid("");
        if (busDelieryitemPojo.getRevision() == null) busDelieryitemPojo.setRevision(0);
        return busDelieryitemPojo;
    }
}
