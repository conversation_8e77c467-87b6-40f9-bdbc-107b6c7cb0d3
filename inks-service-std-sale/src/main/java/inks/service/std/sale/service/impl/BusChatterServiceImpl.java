package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusChatterEntity;
import inks.service.std.sale.domain.pojo.BusChatterPojo;
import inks.service.std.sale.mapper.BusChatterMapper;
import inks.service.std.sale.service.BusChatterService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 销售客服(BusChatter)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-01 10:29:52
 */
@Service("busChatterService")
public class BusChatterServiceImpl implements BusChatterService {
    @Resource
    private BusChatterMapper busChatterMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusChatterPojo getEntity(String key, String tid) {
        return this.busChatterMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusChatterPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusChatterPojo> lst = busChatterMapper.getPageList(queryParam);
            PageInfo<BusChatterPojo> pageInfo = new PageInfo<BusChatterPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param busChatterPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusChatterPojo insert(BusChatterPojo busChatterPojo) {
    //初始化NULL字段
     if(busChatterPojo.getUserid()==null) busChatterPojo.setUserid("");
     if(busChatterPojo.getNickname()==null) busChatterPojo.setNickname("");
     if(busChatterPojo.getStatemark()==null) busChatterPojo.setStatemark(0);
     if(busChatterPojo.getAvatar()==null) busChatterPojo.setAvatar("");
     if(busChatterPojo.getSex()==null) busChatterPojo.setSex(0);
     if(busChatterPojo.getMobile()==null) busChatterPojo.setMobile("");
     if(busChatterPojo.getEmail()==null) busChatterPojo.setEmail("");
     if(busChatterPojo.getLeadermark()==null) busChatterPojo.setLeadermark(0);
     if(busChatterPojo.getRownum()==null) busChatterPojo.setRownum(0);
     if(busChatterPojo.getRemark()==null) busChatterPojo.setRemark("");
     if(busChatterPojo.getCreateby()==null) busChatterPojo.setCreateby("");
     if(busChatterPojo.getCreatebyid()==null) busChatterPojo.setCreatebyid("");
     if(busChatterPojo.getCreatedate()==null) busChatterPojo.setCreatedate(new Date());
     if(busChatterPojo.getLister()==null) busChatterPojo.setLister("");
     if(busChatterPojo.getListerid()==null) busChatterPojo.setListerid("");
     if(busChatterPojo.getModifydate()==null) busChatterPojo.setModifydate(new Date());
     if(busChatterPojo.getCustom1()==null) busChatterPojo.setCustom1("");
     if(busChatterPojo.getCustom2()==null) busChatterPojo.setCustom2("");
     if(busChatterPojo.getCustom3()==null) busChatterPojo.setCustom3("");
     if(busChatterPojo.getCustom4()==null) busChatterPojo.setCustom4("");
     if(busChatterPojo.getCustom5()==null) busChatterPojo.setCustom5("");
     if(busChatterPojo.getCustom6()==null) busChatterPojo.setCustom6("");
     if(busChatterPojo.getCustom7()==null) busChatterPojo.setCustom7("");
     if(busChatterPojo.getCustom8()==null) busChatterPojo.setCustom8("");
     if(busChatterPojo.getCustom9()==null) busChatterPojo.setCustom9("");
     if(busChatterPojo.getCustom10()==null) busChatterPojo.setCustom10("");
     if(busChatterPojo.getTenantid()==null) busChatterPojo.setTenantid("");
     if(busChatterPojo.getTenantname()==null) busChatterPojo.setTenantname("");
     if(busChatterPojo.getRevision()==null) busChatterPojo.setRevision(0);
        BusChatterEntity busChatterEntity = new BusChatterEntity(); 
        BeanUtils.copyProperties(busChatterPojo,busChatterEntity);
          //生成雪花id
          busChatterEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busChatterEntity.setRevision(1);  //乐观锁
          this.busChatterMapper.insert(busChatterEntity);
        return this.getEntity(busChatterEntity.getId(),busChatterEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busChatterPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusChatterPojo update(BusChatterPojo busChatterPojo) {
        BusChatterEntity busChatterEntity = new BusChatterEntity(); 
        BeanUtils.copyProperties(busChatterPojo,busChatterEntity);
        this.busChatterMapper.update(busChatterEntity);
        return this.getEntity(busChatterEntity.getId(),busChatterEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busChatterMapper.delete(key,tid) ;
    }
    
                                                                                                                                                          
}
