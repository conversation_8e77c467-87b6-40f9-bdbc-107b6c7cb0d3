package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusSteppriceitemEntity;
import inks.service.std.sale.domain.pojo.BusSteppriceitemPojo;
import inks.service.std.sale.mapper.BusSteppriceitemMapper;
import inks.service.std.sale.service.BusSteppriceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 阶梯项目(BusSteppriceitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-31 21:22:06
 */
@Service("busSteppriceitemService")
public class BusSteppriceitemServiceImpl implements BusSteppriceitemService {
    @Resource
    private BusSteppriceitemMapper busSteppriceitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusSteppriceitemPojo getEntity(String key,String tid) {
        return this.busSteppriceitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusSteppriceitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusSteppriceitemPojo> lst = busSteppriceitemMapper.getPageList(queryParam);
            PageInfo<BusSteppriceitemPojo> pageInfo = new PageInfo<BusSteppriceitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusSteppriceitemPojo> getList(String Pid,String tid) { 
        try {
            List<BusSteppriceitemPojo> lst = busSteppriceitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busSteppriceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusSteppriceitemPojo insert(BusSteppriceitemPojo busSteppriceitemPojo) {
        //初始化item的NULL
        BusSteppriceitemPojo itempojo =this.clearNull(busSteppriceitemPojo);
        BusSteppriceitemEntity busSteppriceitemEntity = new BusSteppriceitemEntity(); 
        BeanUtils.copyProperties(itempojo,busSteppriceitemEntity);
        
          busSteppriceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busSteppriceitemEntity.setRevision(1);  //乐观锁      
          this.busSteppriceitemMapper.insert(busSteppriceitemEntity);
        return this.getEntity(busSteppriceitemEntity.getId(),busSteppriceitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busSteppriceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusSteppriceitemPojo update(BusSteppriceitemPojo busSteppriceitemPojo) {
        BusSteppriceitemEntity busSteppriceitemEntity = new BusSteppriceitemEntity(); 
        BeanUtils.copyProperties(busSteppriceitemPojo,busSteppriceitemEntity);
        this.busSteppriceitemMapper.update(busSteppriceitemEntity);
        return this.getEntity(busSteppriceitemEntity.getId(),busSteppriceitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busSteppriceitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busSteppriceitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusSteppriceitemPojo clearNull(BusSteppriceitemPojo busSteppriceitemPojo){
     //初始化NULL字段
     if(busSteppriceitemPojo.getPid()==null) busSteppriceitemPojo.setPid("");
     if(busSteppriceitemPojo.getStartqty()==null) busSteppriceitemPojo.setStartqty(0);
     if(busSteppriceitemPojo.getEndqty()==null) busSteppriceitemPojo.setEndqty(0);
     if(busSteppriceitemPojo.getPrice()==null) busSteppriceitemPojo.setPrice(0D);
     if(busSteppriceitemPojo.getAmount()==null) busSteppriceitemPojo.setAmount(0D);
     if(busSteppriceitemPojo.getRebate()==null) busSteppriceitemPojo.setRebate(0D);
     if(busSteppriceitemPojo.getRownum()==null) busSteppriceitemPojo.setRownum(0);
     if(busSteppriceitemPojo.getRemark()==null) busSteppriceitemPojo.setRemark("");
     if(busSteppriceitemPojo.getCustom1()==null) busSteppriceitemPojo.setCustom1("");
     if(busSteppriceitemPojo.getCustom2()==null) busSteppriceitemPojo.setCustom2("");
     if(busSteppriceitemPojo.getCustom3()==null) busSteppriceitemPojo.setCustom3("");
     if(busSteppriceitemPojo.getCustom4()==null) busSteppriceitemPojo.setCustom4("");
     if(busSteppriceitemPojo.getCustom5()==null) busSteppriceitemPojo.setCustom5("");
     if(busSteppriceitemPojo.getCustom6()==null) busSteppriceitemPojo.setCustom6("");
     if(busSteppriceitemPojo.getCustom7()==null) busSteppriceitemPojo.setCustom7("");
     if(busSteppriceitemPojo.getCustom8()==null) busSteppriceitemPojo.setCustom8("");
     if(busSteppriceitemPojo.getCustom9()==null) busSteppriceitemPojo.setCustom9("");
     if(busSteppriceitemPojo.getCustom10()==null) busSteppriceitemPojo.setCustom10("");
     if(busSteppriceitemPojo.getTenantid()==null) busSteppriceitemPojo.setTenantid("");
     if(busSteppriceitemPojo.getRevision()==null) busSteppriceitemPojo.setRevision(0);
     return busSteppriceitemPojo;
     }
}
