package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;

import java.util.List;
import java.util.Map;

/**
 * 往来单位(AppWorkgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-14 08:26:37
 */
public interface AppWorkgroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    AppWorkgroupPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<AppWorkgroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param appWorkgroupPojo 实例对象
     * @return 实例对象
     */
    AppWorkgroupPojo insert(AppWorkgroupPojo appWorkgroupPojo);

    /**
     * 修改数据
     *
     * @param appWorkgrouppojo 实例对象
     * @return 实例对象
     */
    AppWorkgroupPojo update(AppWorkgroupPojo appWorkgrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    // 查询往来单是否被引用
    List<String> getCiteBillName(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    AppWorkgroupPojo getEntityByUid(AppWorkgroupPojo appWorkgroupPojo);


    /**
     * 通过ID查询单条数据
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    AppWorkgroupPojo getEntityByName(AppWorkgroupPojo appWorkgroupPojo);

    /**
     * 通过type，Get下一个Code
     *
     * @param appWorkgroupPojo
     * @return 实例对象
     */
    String getNextCode(AppWorkgroupPojo appWorkgroupPojo);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    AppWorkgroupPojo getCustomerGeneral(String key, String tid);

    AppWorkgroupPojo getSumCustomerGeneralBySalesman(String seller, String tenantid);


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<AppWorkgroupPojo> getPageListBySale(QueryParam queryParam);


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<AppWorkgroupPojo> getPageListByRece(QueryParam queryParam);

    //刷新销售订单结余额
    int updateWorkgroupBusMachRemAmt(String key, String tid);

    //刷新销售发货结余额
    int updateWorkgroupBusDeliRemAmt(String key, String tid);

    //刷新销售发票结余额
    int updateWorkgroupBusInvoRemAmt(String key, String tid);

    //刷新销售结转期末额
    int updateWorkgroupBusAccoCloseAmt(String key, String tid);

    //刷新销售结转本期额
    int updateWorkgroupBusAccoNowAmt(String key, String tid);

    //刷新采购订单结余额
    int updateWorkgroupBuyOrderRemAmt(String key, String tid);

    //刷新采购收货结余额
    int updateWorkgroupBuyFiniRemAmt(String key, String tid);

    //刷新采购发票结余额
    int updateWorkgroupBuyInvoRemAmt(String key, String tid);

    //刷新采购结转期末额
    int updateWorkgroupBuyAccoCloseAmt(String key, String tid);

    //刷新采购结转本期额
    int updateWorkgroupBuyAccoNowAmt(String key, String tid);

    void getPageListBySaleStart(String uuid, LoginUser loginUser, QueryParam queryParam);

    Map<String, Object> getPageListBySaleState(String key);

    List<String> getProvOrCity(String province, String tid);

    List<AppWorkgroupPojo> getList(String groupType, String tid);

    List<Map<String,Object>> getListAbbr(String groupType, String tid);

    int changeDeptid(String key, String deptid, String tid);
}
