package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusQuotationPojo;
import inks.service.std.sale.domain.pojo.BusQuotationitemdetailPojo;

/**
 * 报价单(BusQuotation)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-06 08:21:09
 */
public interface BusQuotationService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusQuotationPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusQuotationitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusQuotationPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusQuotationPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusQuotationPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busQuotationPojo 实例对象
     * @return 实例对象
     */
    BusQuotationPojo insert(BusQuotationPojo busQuotationPojo);

    /**
     * 修改数据
     *
     * @param busQuotationpojo 实例对象
     * @return 实例对象
     */
    BusQuotationPojo update(BusQuotationPojo busQuotationpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key,String tid);

                                                                                                                                                                               /**
     * 审核数据
     *
     * @param busQuotationPojo 实例对象
     * @return 实例对象
     */
     BusQuotationPojo approval(BusQuotationPojo busQuotationPojo);
                                                                                                         }
