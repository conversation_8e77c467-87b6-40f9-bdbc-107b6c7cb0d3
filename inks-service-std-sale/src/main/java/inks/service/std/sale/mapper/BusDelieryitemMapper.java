package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusDelieryitemEntity;
import inks.service.std.sale.domain.pojo.BusDelieryitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 发货明细(BusDelieryitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-11 10:00:02
 */
 @Mapper
public interface BusDelieryitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDelieryitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDelieryitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusDelieryitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busDelieryitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusDelieryitemEntity busDelieryitemEntity);

    
    /**
     * 修改数据
     *
     * @param busDelieryitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusDelieryitemEntity busDelieryitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<Map<String,String>> getAllByTid(String tid);

    int upateAttrStr(@Param("id") String id, @Param("attrStr") String attrStr, @Param("tid") String tid);

    String getBillType(String pid, String tid);
}

