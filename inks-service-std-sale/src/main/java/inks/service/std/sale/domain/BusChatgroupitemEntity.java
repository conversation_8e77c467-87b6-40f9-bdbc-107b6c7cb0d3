package inks.service.std.sale.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 客服分组客服子表(BusChatgroupitem)Entity
 *
 * <AUTHOR>
 * @since 2024-01-08 15:49:07
 */
public class BusChatgroupitemEntity implements Serializable {
    private static final long serialVersionUID = 873666453458252936L;
          // id
         private String id;
         private String pid;
          // 客服id
         private String chatterid;
          // 名字
         private String chattername;
          // 客服的userid
         private String userid;
          // 排列序号
         private Integer rownum;
          // 备注
         private String remark;
          // 创建者
         private String createby;
          // 创建者id
         private String createbyid;
          // 新建日期
         private Date createdate;
          // 制表
         private String lister;
          // 制表id
         private String listerid;
          // 修改日期
         private Date modifydate;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 租户id
         private String tenantid;
          // 租户名称
         private String tenantname;
          // 乐观锁
         private Integer revision;

    // id
  
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
 
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
    // 客服id
  
    public String getChatterid() {
        return chatterid;
    }

    public void setChatterid(String chatterid) {
        this.chatterid = chatterid;
    }
    // 名字
  
    public String getChattername() {
        return chattername;
    }

    public void setChattername(String chattername) {
        this.chattername = chattername;
    }
    // 客服的userid
  
    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }
    // 排列序号
  
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 备注
  
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 创建者
  
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
    // 创建者id
  
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
    // 新建日期
  
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 制表
  
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 制表id
  
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
    // 修改日期
  
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 自定义1
  
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2
  
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3
  
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4
  
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 租户id
  
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 租户名称
  
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
    // 乐观锁
  
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

