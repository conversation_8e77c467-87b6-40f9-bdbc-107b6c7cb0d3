package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusDeliplanPojo;
import inks.service.std.sale.domain.pojo.BusDeliplanitemPojo;
import inks.service.std.sale.domain.pojo.BusDeliplanitemdetailPojo;
import inks.service.std.sale.service.BusDeliplanService;
import inks.service.std.sale.service.BusDeliplanitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 发货计划(Bus_DeliPlan)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:04
 */
//@RestController
//@RequestMapping("busDeliplan")
public class BusDeliplanController {

    private final static Logger logger = LoggerFactory.getLogger(BusDeliplanController.class);
    private final String moduleCode = "DxxMxxB1";
    @Resource
    private BusDeliplanService busDeliplanService;
    @Resource
    private BusDeliplanitemService busDeliplanitemService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;

    @ApiOperation(value = " 获取发货计划详细信息", notes = "获取发货计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.List")
    public R<BusDeliplanPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busDeliplanService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.List")
    public R<PageInfo<BusDeliplanitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliPlan.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDeliplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取发货计划详细信息", notes = "获取发货计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.List")
    public R<BusDeliplanPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busDeliplanService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.List")
    public R<PageInfo<BusDeliplanPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliPlan.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDeliplanService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.List")
    public R<PageInfo<BusDeliplanPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliPlan.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDeliplanService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增发货计划", notes = "新增发货计划", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Add")
    public R<BusDeliplanPojo> create(@RequestBody String json) {
        try {
            BusDeliplanPojo busDeliplanPojo = JSONArray.parseObject(json, BusDeliplanPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_DeliPlan", null, tid);
            busDeliplanPojo.setRefno(refno);
            busDeliplanPojo.setCreateby(loginUser.getRealName());   // 创建者
            busDeliplanPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busDeliplanPojo.setCreatedate(new Date());   // 创建时间
            busDeliplanPojo.setLister(loginUser.getRealname());   // 制表
            busDeliplanPojo.setListerid(loginUser.getUserid());    // 制表id            
            busDeliplanPojo.setModifydate(new Date());   //修改时间
            busDeliplanPojo.setTenantid(tid);   //租户id
            BusDeliplanPojo insertDB = this.busDeliplanService.insert(busDeliplanPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, tid);// 保存单据编码RefNoUtils
            return R.ok(insertDB);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改发货计划", notes = "修改发货计划", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Edit")
    public R<BusDeliplanPojo> update(@RequestBody String json) {
        try {
            BusDeliplanPojo busDeliplanPojo = JSONArray.parseObject(json, BusDeliplanPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busDeliplanPojo.setLister(loginUser.getRealname());   // 制表
            busDeliplanPojo.setListerid(loginUser.getUserid());    // 制表id   
            busDeliplanPojo.setModifydate(new Date());   //修改时间
            busDeliplanPojo.setAssessor(""); //审核员
            busDeliplanPojo.setAssessorid(""); //审核员
            busDeliplanPojo.setAssessdate(new Date()); //审核时间
            busDeliplanPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busDeliplanService.update(busDeliplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除发货计划", notes = "删除发货计划", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            this.busDeliplanService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());// 删除单据编码RefNoUtils
            return R.ok(1);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增发货计划Item", notes = "新增发货计划Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Add")
    public R<BusDeliplanitemPojo> createItem(@RequestBody String json) {
        try {
            BusDeliplanitemPojo busDeliplanitemPojo = JSONArray.parseObject(json, BusDeliplanitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busDeliplanitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busDeliplanitemService.insert(busDeliplanitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改发货计划Item", notes = "修改发货计划Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Edit")
    public R<BusDeliplanitemPojo> updateItem(@RequestBody String json) {
        try {
            BusDeliplanitemPojo busDeliplanitemPojo = JSONArray.parseObject(json, BusDeliplanitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busDeliplanitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busDeliplanitemService.update(busDeliplanitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除发货计划Item", notes = "删除发货计划Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Delete")
    public R<Integer> deleteItem(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busDeliplanitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审核发货计划", notes = "审核发货计划", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Approval")
    public R<BusDeliplanPojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusDeliplanPojo busDeliplanPojo = this.busDeliplanService.getEntity(key, loginUser.getTenantid());
            if (busDeliplanPojo.getAssessor().equals("")) {
                busDeliplanPojo.setAssessor(loginUser.getRealname()); //审核员
                busDeliplanPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busDeliplanPojo.setAssessor(""); //审核员
                busDeliplanPojo.setAssessorid(""); //审核员
            }
            busDeliplanPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busDeliplanService.approval(busDeliplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliPlan.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusDeliplanPojo busDeliplanPojo = this.busDeliplanService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busDeliplanPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busDeliplanPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDeliplanitemPojo busDeliplanitemPojo = new BusDeliplanitemPojo();
                    busDeliplanPojo.getItem().add(busDeliplanitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(busDeliplanPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

