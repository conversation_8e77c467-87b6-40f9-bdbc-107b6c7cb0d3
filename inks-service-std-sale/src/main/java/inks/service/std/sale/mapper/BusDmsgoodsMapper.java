package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusDmsgoodsEntity;
import inks.service.std.sale.domain.pojo.BusDmsgoodsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DMS商品(BusDmsgoods)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-21 15:25:29
 */
@Mapper
public interface BusDmsgoodsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDmsgoodsPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDmsgoodsPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param busDmsgoodsEntity 实例对象
     * @return 影响行数
     */
    int insert(BusDmsgoodsEntity busDmsgoodsEntity);

    
    /**
     * 修改数据
     *
     * @param busDmsgoodsEntity 实例对象
     * @return 影响行数
     */
    int update(BusDmsgoodsEntity busDmsgoodsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    String getMaxCode(String tenantid);

    int checkGoodsUid(@Param("goodsuid") String goodsuid, @Param("id") String id, @Param("tid") String tid);
}

