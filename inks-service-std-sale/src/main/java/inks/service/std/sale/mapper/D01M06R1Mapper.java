package inks.service.std.sale.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDelieryPojo;
import inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 销售订单(BusMachining)报表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-13 10:19:46
 */
@Mapper
public interface D01M06R1Mapper {
    /*
     *
     * <AUTHOR>
     * @description 客户订单金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<BusDelieryitemdetailPojo> getSumPageListByGroup(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 货品金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<BusDelieryitemdetailPojo> getSumPageListByGoods(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 客户订单金额排名
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam);

//    (按省份/地级市)获取送货单客户金额排名
    List<Map<String, Object>> getSumAmtByGroupProvince(@Param("queryParam") QueryParam queryParam,@Param("province") String province);

    List<Map<String, Object>> getSumAmtByGroupProvinceMach(@Param("queryParam") QueryParam queryParam,@Param("province") String province);

    List<Map<String, Object>> getSumAmtByGroupProvinceCollection(@Param("queryParam")QueryParam queryParam, @Param("province")String province);

    List<Map<String, Object>> getSumAmtGroupByCountry(@Param("queryParam") QueryParam queryParam);


     /*
      *
      * <AUTHOR>
      * @description 货品金额排名
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByGoodsMax(@Param("queryParam")QueryParam queryParam,@Param("province")String province,@Param("city")String city);
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图年度
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图月度
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图周度
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 本月销售额
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    ChartPojo getTagSumAmtQtyByMonth(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDelieryitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDelieryPojo> getPageTh(QueryParam queryParam);

}


