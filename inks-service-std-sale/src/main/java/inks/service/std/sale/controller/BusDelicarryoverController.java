package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusDelicarryoverPojo;
import inks.service.std.sale.service.BusDelicarryoverService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 货品账单:订单>发货(Bus_DeliCarryover)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-10 12:50:16
 */
//@RestController
//@RequestMapping("busDelicarryover")
public class BusDelicarryoverController {

    private final static Logger logger = LoggerFactory.getLogger(BusDelicarryoverController.class);
    @Resource
    private BusDelicarryoverService busDelicarryoverService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取货品账单:订单>发货详细信息", notes = "获取货品账单:订单>发货详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliCarryover.List")
    public R<BusDelicarryoverPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.busDelicarryoverService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliCarryover.List")
    public R<PageInfo<BusDelicarryoverPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_DeliCarryover.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelicarryoverService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增货品账单:订单>发货", notes = "新增货品账单:订单>发货", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliCarryover.Add")
    public R<BusDelicarryoverPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusDelicarryoverPojo busDelicarryoverPojo = JSONArray.parseObject(json, BusDelicarryoverPojo.class);

            busDelicarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            busDelicarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busDelicarryoverPojo.setCreatedate(new Date());   // 创建时间
            busDelicarryoverPojo.setLister(loginUser.getRealname());   // 制表
            busDelicarryoverPojo.setListerid(loginUser.getUserid());    // 制表id  
            busDelicarryoverPojo.setModifydate(new Date());   //修改时间
            busDelicarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busDelicarryoverService.insert(busDelicarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改货品账单:订单>发货", notes = "修改货品账单:订单>发货", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_DeliCarryover.Edit")
    public R<BusDelicarryoverPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusDelicarryoverPojo busDelicarryoverPojo = JSONArray.parseObject(json, BusDelicarryoverPojo.class);

            busDelicarryoverPojo.setLister(loginUser.getRealname());   // 制表
            busDelicarryoverPojo.setListerid(loginUser.getUserid());    // 制表id  
            busDelicarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            busDelicarryoverPojo.setModifydate(new Date());   //修改时间
//            busDelicarryoverPojo.setAssessor(""); // 审核员
//            busDelicarryoverPojo.setAssessorid(""); // 审核员id
//            busDelicarryoverPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busDelicarryoverService.update(busDelicarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除货品账单:订单>发货", notes = "删除货品账单:订单>发货", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliCarryover.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busDelicarryoverService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_DeliCarryover.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusDelicarryoverPojo busDelicarryoverPojo = this.busDelicarryoverService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busDelicarryoverPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

