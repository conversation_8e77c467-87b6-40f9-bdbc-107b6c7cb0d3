package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCouponactivePojo;
import inks.service.std.sale.domain.BusCouponactiveEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 优惠券激活记录(Bus_CouponActive)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
@Mapper
public interface BusCouponactiveMapper {

    BusCouponactivePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusCouponactivePojo> getPageList(QueryParam queryParam);

    int insert(BusCouponactiveEntity busCouponactiveEntity);

    int update(BusCouponactiveEntity busCouponactiveEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    

    int approval(BusCouponactiveEntity busCouponactiveEntity);

    int syncCouponActiveAmount(String couponid, String tid);
}

