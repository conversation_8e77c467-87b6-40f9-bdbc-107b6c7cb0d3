package inks.service.std.sale.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 意向项目(BusIntendeditem)Pojo
 *
 * <AUTHOR>
 * @since 2024-01-17 14:31:37
 */
public class BusIntendeditemPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = 855184642014765450L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 商品ID
  @Excel(name = "商品ID")    
  private String goodsid;
     // 类型
  @Excel(name = "类型")    
  private String itemtype;
     // 名称
  @Excel(name = "名称")    
  private String itemname;
     // 规格
  @Excel(name = "规格")    
  private String itemspec;
     // 单位
  @Excel(name = "单位")    
  private String itemunit;
     // dms货品id
  @Excel(name = "dms货品id")    
  private String dmsgoodsid;
     // 数量
  @Excel(name = "数量")    
  private Double quantity;
     // 标准销价
  @Excel(name = "标准销价")    
  private Double stdprice;
     // 标准金额
  @Excel(name = "标准金额")    
  private Double stdamount;
     // 折扣
  @Excel(name = "折扣")    
  private Double rebate;
     // 单价
  @Excel(name = "单价")    
  private Double price;
     // 金额
  @Excel(name = "金额")    
  private Double amount;
     // 税率
  @Excel(name = "税率")    
  private Integer itemtaxrate;
     // 含税单价
  @Excel(name = "含税单价")    
  private Double taxprice;
     // 税额
  @Excel(name = "税额")    
  private Double taxtotal;
     // 含税金额
  @Excel(name = "含税金额")    
  private Double taxamount;
     // 计划交期
  @Excel(name = "计划交期")    
  private Date plandate;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 属性Josn
  @Excel(name = "属性Josn")    
  private String attributejson;
     // 成本Item
  @Excel(name = "成本Item")    
  private String costitemjson;
     // 成本分类
  @Excel(name = "成本分类")    
  private String costgroupjson;
     // 虚拟货品
  @Excel(name = "虚拟货品")    
  private Integer virtualitem;
     // 1转报价2转核价3转订单
  @Excel(name = "1转报价2转核价3转订单")    
  private Integer finishmark;
     // 作废
  @Excel(name = "作废")    
  private Integer disannulmark;
     // 作废经办id
  @Excel(name = "作废经办id")    
  private String disannullisterid;
     // 作废经办
  @Excel(name = "作废经办")    
  private String disannullister;
     // 作废日期
  @Excel(name = "作废日期")    
  private Date disannuldate;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

  // dmsgoodsid查询的dms货品信息
    private String dmsgoodsname;//dms货品名称
    private String dmsgoodsspec;//dms货品规格
    private String dmsgoodsunit;//dms货品单位
    private String dmsgoodsuid; //dms货品编码

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public String getDmsgoodsname() {
        return dmsgoodsname;
    }

    public void setDmsgoodsname(String dmsgoodsname) {
        this.dmsgoodsname = dmsgoodsname;
    }

    public String getDmsgoodsspec() {
        return dmsgoodsspec;
    }

    public void setDmsgoodsspec(String dmsgoodsspec) {
        this.dmsgoodsspec = dmsgoodsspec;
    }

    public String getDmsgoodsunit() {
        return dmsgoodsunit;
    }

    public void setDmsgoodsunit(String dmsgoodsunit) {
        this.dmsgoodsunit = dmsgoodsunit;
    }

    public String getDmsgoodsuid() {
        return dmsgoodsuid;
    }

    public void setDmsgoodsuid(String dmsgoodsuid) {
        this.dmsgoodsuid = dmsgoodsuid;
    }

    // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 商品ID
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 类型
    public String getItemtype() {
        return itemtype;
    }
    
    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }
        
   // 名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
   // 单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
   // dms货品id
    public String getDmsgoodsid() {
        return dmsgoodsid;
    }
    
    public void setDmsgoodsid(String dmsgoodsid) {
        this.dmsgoodsid = dmsgoodsid;
    }
        
   // 数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 标准销价
    public Double getStdprice() {
        return stdprice;
    }
    
    public void setStdprice(Double stdprice) {
        this.stdprice = stdprice;
    }
        
   // 标准金额
    public Double getStdamount() {
        return stdamount;
    }
    
    public void setStdamount(Double stdamount) {
        this.stdamount = stdamount;
    }
        
   // 折扣
    public Double getRebate() {
        return rebate;
    }
    
    public void setRebate(Double rebate) {
        this.rebate = rebate;
    }
        
   // 单价
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
   // 金额
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
   // 税率
    public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
   // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
   // 税额
    public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
   // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
   // 计划交期
    public Date getPlandate() {
        return plandate;
    }
    
    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // 成本Item
    public String getCostitemjson() {
        return costitemjson;
    }
    
    public void setCostitemjson(String costitemjson) {
        this.costitemjson = costitemjson;
    }
        
   // 成本分类
    public String getCostgroupjson() {
        return costgroupjson;
    }
    
    public void setCostgroupjson(String costgroupjson) {
        this.costgroupjson = costgroupjson;
    }
        
   // 虚拟货品
    public Integer getVirtualitem() {
        return virtualitem;
    }
    
    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }
        
   // 1转报价2转核价3转订单
    public Integer getFinishmark() {
        return finishmark;
    }
    
    public void setFinishmark(Integer finishmark) {
        this.finishmark = finishmark;
    }
        
   // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
   // 作废经办id
    public String getDisannullisterid() {
        return disannullisterid;
    }
    
    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }
        
   // 作废经办
    public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
   // 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

