package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusSalesmanEntity;
import inks.service.std.sale.domain.pojo.BusSalesmanPojo;
import inks.service.std.sale.mapper.BusSalesmanMapper;
import inks.service.std.sale.service.BusSalesmanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 业务员信息表(BusSalesman)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-01 09:38:26
 */
@Service("busSalesmanService")
public class BusSalesmanServiceImpl implements BusSalesmanService {
    @Resource
    private BusSalesmanMapper busSalesmanMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusSalesmanPojo getEntity(String key, String tid) {
        return this.busSalesmanMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusSalesmanPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusSalesmanPojo> lst = busSalesmanMapper.getPageList(queryParam);
            PageInfo<BusSalesmanPojo> pageInfo = new PageInfo<BusSalesmanPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param busSalesmanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusSalesmanPojo insert(BusSalesmanPojo busSalesmanPojo) {
    //初始化NULL字段
     if(busSalesmanPojo.getUserid()==null) busSalesmanPojo.setUserid("");
     if(busSalesmanPojo.getName()==null) busSalesmanPojo.setName("");
     if(busSalesmanPojo.getNickname()==null) busSalesmanPojo.setNickname("");
     if(busSalesmanPojo.getAvatar()==null) busSalesmanPojo.setAvatar("");
     if(busSalesmanPojo.getAge()==null) busSalesmanPojo.setAge(0);
     if(busSalesmanPojo.getGender()==null) busSalesmanPojo.setGender(0);
     if(busSalesmanPojo.getPhone()==null) busSalesmanPojo.setPhone("");
     if(busSalesmanPojo.getEmail()==null) busSalesmanPojo.setEmail("");
     if(busSalesmanPojo.getAddress()==null) busSalesmanPojo.setAddress("");
     if(busSalesmanPojo.getLevel()==null) busSalesmanPojo.setLevel("");
     if(busSalesmanPojo.getLeadermark()==null) busSalesmanPojo.setLeadermark(0);
     if(busSalesmanPojo.getStatus()==null) busSalesmanPojo.setStatus(0);
     if(busSalesmanPojo.getRownum()==null) busSalesmanPojo.setRownum(0);
     if(busSalesmanPojo.getRemark()==null) busSalesmanPojo.setRemark("");
     if(busSalesmanPojo.getCreateby()==null) busSalesmanPojo.setCreateby("");
     if(busSalesmanPojo.getCreatebyid()==null) busSalesmanPojo.setCreatebyid("");
     if(busSalesmanPojo.getCreatedate()==null) busSalesmanPojo.setCreatedate(new Date());
     if(busSalesmanPojo.getLister()==null) busSalesmanPojo.setLister("");
     if(busSalesmanPojo.getListerid()==null) busSalesmanPojo.setListerid("");
     if(busSalesmanPojo.getModifydate()==null) busSalesmanPojo.setModifydate(new Date());
     if(busSalesmanPojo.getCustom1()==null) busSalesmanPojo.setCustom1("");
     if(busSalesmanPojo.getCustom2()==null) busSalesmanPojo.setCustom2("");
     if(busSalesmanPojo.getCustom3()==null) busSalesmanPojo.setCustom3("");
     if(busSalesmanPojo.getCustom4()==null) busSalesmanPojo.setCustom4("");
     if(busSalesmanPojo.getCustom5()==null) busSalesmanPojo.setCustom5("");
     if(busSalesmanPojo.getCustom6()==null) busSalesmanPojo.setCustom6("");
     if(busSalesmanPojo.getCustom7()==null) busSalesmanPojo.setCustom7("");
     if(busSalesmanPojo.getCustom8()==null) busSalesmanPojo.setCustom8("");
     if(busSalesmanPojo.getCustom9()==null) busSalesmanPojo.setCustom9("");
     if(busSalesmanPojo.getCustom10()==null) busSalesmanPojo.setCustom10("");
     if(busSalesmanPojo.getTenantid()==null) busSalesmanPojo.setTenantid("");
     if(busSalesmanPojo.getTenantname()==null) busSalesmanPojo.setTenantname("");
     if(busSalesmanPojo.getRevision()==null) busSalesmanPojo.setRevision(0);
        BusSalesmanEntity busSalesmanEntity = new BusSalesmanEntity(); 
        BeanUtils.copyProperties(busSalesmanPojo,busSalesmanEntity);
          //生成雪花id
          busSalesmanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busSalesmanEntity.setRevision(1);  //乐观锁
          this.busSalesmanMapper.insert(busSalesmanEntity);
        return this.getEntity(busSalesmanEntity.getId(),busSalesmanEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busSalesmanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusSalesmanPojo update(BusSalesmanPojo busSalesmanPojo) {
        BusSalesmanEntity busSalesmanEntity = new BusSalesmanEntity(); 
        BeanUtils.copyProperties(busSalesmanPojo,busSalesmanEntity);
        this.busSalesmanMapper.update(busSalesmanEntity);
        return this.getEntity(busSalesmanEntity.getId(),busSalesmanEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busSalesmanMapper.delete(key,tid) ;
    }
    

}
