package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;

import java.util.List;
import java.util.Map;

public interface D01MBIR1Service {
    /*
     *
     * <AUTHOR>
     * @description 客户订单金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam);

    List<ChartPojo> getSumAmtByGroupMaxMach(QueryParam queryParam);

    List<Map<String, Object>> getSumAmtByGroupMachAndDeliAndReceipt(QueryParam queryParam,String groupname);

    /*
     *
     * <AUTHOR>
     * @description 货品金额排名  发货的金额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam);
    List<ChartPojo> getSumAmtByGoodsMaxMach(QueryParam queryParam);


    /*
     *
     * <AUTHOR>
     * @description 业务员订单金额占比
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtBySalesman(QueryParam queryParam);

    List<ChartPojo> getSumAmtBySalesmanMach(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam, Integer trend);

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam, Integer trend);


    /**
     * @return
     * @author: nanno
     * @description: 销售趋势图月度
     * @createTime: 2023/3/9 9:41
     * @params * @Param: null
     */
    List<ChartPojo> getSumAmtByMonthMach(QueryParam queryParam, Integer trend);


    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam, Integer trend);

    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    ChartPojo getTagSumAmtQtyByDate(QueryParam queryParam);

    ChartPojo getTagSumAmtQtyByDateMach(QueryParam queryParam);

    /**
     * @return
     * @author: nanno
     * @description: 订单完成率
     * @createTime: 2023/3/8 16:57
     * @params * @Param: null
     */
    ChartPojo getMachFinishRate(QueryParam queryParam);

    ChartPojo getDeliFinishRate(QueryParam queryParam);

    ChartPojo getReceiptFinishRate(QueryParam queryParam);

    List<ChartPojo> getGroupDistribution(String tid);

    PageInfo<ChartPojo> getSpuWeightGroupByGroup(QueryParam queryParam);

    List<ChartPojo> getSpuWeightGroupByCaiZhi(QueryParam queryParam);

    List<ChartPojo> getSpuWeightGroupByGongYi(QueryParam queryParam);

    PageInfo<Map<String, Object>> getSpuWeightGroupByGuiGe(QueryParam queryParam);


//    /*
//     *
//     * <AUTHOR>
//     * @description 本月销售额
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    ChartPojo getTagSumAmtQtyByMonth(QueryParam queryParam);
//    /*
//     *
//     * <AUTHOR>
//     * @description 根据当前月查询本月开票
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getTagSumAmtByMonth(String tid);
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 订单逾期
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    ChartPojo getPageList(String tid);
//    /*
//     *
//     * <AUTHOR>
//     * @description 热销产品
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getSumByGoodsMax(QueryParam queryParam);
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图年
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getSumAmtByYearMax(QueryParam queryParam);
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图月
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getSumAmtByMonthMax(QueryParam queryParam);
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图周
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getSumAmtByDayMax(QueryParam queryParam);
//    /*
//     *
//     * <AUTHOR>
//     * @description 查询在线订单数
//     * @date 2021/12/29
//     * @param * @param null
//     * @return
//     */
//    Integer getCountMachItemOnline(String tid);
}
