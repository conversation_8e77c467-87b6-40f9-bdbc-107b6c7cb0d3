package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusIntendedPojo;
import inks.service.std.sale.domain.pojo.BusIntendeditemPojo;
import inks.service.std.sale.domain.pojo.BusIntendeditemdetailPojo;
import inks.service.std.sale.service.BusIntendedService;
import inks.service.std.sale.service.BusIntendeditemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 意向订单(BusIntended)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-17 09:12:04
 */
public class BusIntendedController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BusIntendedController.class);
    private final String moduleCode = "D01M17B1";
    /**
     * 服务对象
     */
    @Resource
    private BusIntendedService busIntendedService;
    /**
     * 服务对象Item
     */
    @Resource
    private BusIntendeditemService busIntendeditemService;
    /**
     * 引用Redis服务
     */

    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取意向订单详细信息", notes = "获取意向订单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<BusIntendedPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busIntendedService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendeditemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取意向订单详细信息", notes = "获取意向订单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<BusIntendedPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busIntendedService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendedPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendedPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增意向订单", notes = "新增意向订单", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.Add")
    public R<BusIntendedPojo> create(@RequestBody String json) {
        try {
            BusIntendedPojo busIntendedPojo = JSONArray.parseObject(json, BusIntendedPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Intended", null, loginUser.getTenantid());
            busIntendedPojo.setRefno(refno);
            busIntendedPojo.setCreateby(loginUser.getRealName());   // 创建者
            busIntendedPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busIntendedPojo.setCreatedate(new Date());   // 创建时间
            busIntendedPojo.setLister(loginUser.getRealname());   // 制表
            busIntendedPojo.setListerid(loginUser.getUserid());    // 制表id            
            busIntendedPojo.setModifydate(new Date());   //修改时间
            busIntendedPojo.setTenantid(loginUser.getTenantid());   //租户id
            BusIntendedPojo insert = this.busIntendedService.insert(busIntendedPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());// 保存单据编码RefNoUtils
            return R.ok(insert);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改意向订单", notes = "修改意向订单", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.Edit")
    public R<BusIntendedPojo> update(@RequestBody String json) {
        try {
            BusIntendedPojo busIntendedPojo = JSONArray.parseObject(json, BusIntendedPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busIntendedPojo.setLister(loginUser.getRealname());   // 制表
            busIntendedPojo.setListerid(loginUser.getUserid());    // 制表id   
            busIntendedPojo.setModifydate(new Date());   //修改时间
            busIntendedPojo.setAssessor(""); //审核员
            busIntendedPojo.setAssessorid(""); //审核员
            busIntendedPojo.setAssessdate(new Date()); //审核时间
            busIntendedPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busIntendedService.update(busIntendedPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除意向订单", notes = "删除意向订单", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Intended.Delete")
    @OperLog(title = "删除意向订单")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String refno = this.busIntendedService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增意向订单Item", notes = "新增意向订单Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.Add")
    public R<BusIntendeditemPojo> createItem(@RequestBody String json) {
        try {
            BusIntendeditemPojo busIntendeditemPojo = JSONArray.parseObject(json, BusIntendeditemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busIntendeditemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busIntendeditemService.insert(busIntendeditemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除意向订单Item", notes = "删除意向订单Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Intended.Delete")
    public R<Integer> deleteItem(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busIntendeditemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核意向订单", notes = "审核意向订单", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Intended.Approval")
    public R<BusIntendedPojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusIntendedPojo busIntendedPojo = this.busIntendedService.getEntity(key, loginUser.getTenantid());
            if (busIntendedPojo.getAssessor().equals("")) {
                busIntendedPojo.setAssessor(loginUser.getRealname()); //审核员
                busIntendedPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busIntendedPojo.setAssessor(""); //审核员
                busIntendedPojo.setAssessorid(""); //审核员
            }
            busIntendedPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busIntendedService.approval(busIntendedPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Intended.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusIntendedPojo busIntendedPojo = this.busIntendedService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busIntendedPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busIntendedPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusIntendeditemPojo busIntendeditemPojo = new BusIntendeditemPojo();
                    busIntendedPojo.getItem().add(busIntendeditemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(busIntendedPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

