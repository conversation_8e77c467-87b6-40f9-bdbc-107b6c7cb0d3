package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusCostmodelitemEntity;
import inks.service.std.sale.domain.pojo.BusCostmodelitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模型项目(BusCostmodelitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-07 13:30:44
 */
 @Mapper
public interface BusCostmodelitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostmodelitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusCostmodelitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusCostmodelitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busCostmodelitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusCostmodelitemEntity busCostmodelitemEntity);

    
    /**
     * 修改数据
     *
     * @param busCostmodelitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusCostmodelitemEntity busCostmodelitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

