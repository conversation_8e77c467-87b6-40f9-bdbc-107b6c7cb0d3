package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusIntendeditemEntity;
import inks.service.std.sale.domain.pojo.BusIntendeditemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 意向项目(BusIntendeditem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-17 09:12:51
 */
 @Mapper
public interface BusIntendeditemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusIntendeditemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusIntendeditemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusIntendeditemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busIntendeditemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusIntendeditemEntity busIntendeditemEntity);

    
    /**
     * 修改数据
     *
     * @param busIntendeditemEntity 实例对象
     * @return 影响行数
     */
    int update(BusIntendeditemEntity busIntendeditemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

