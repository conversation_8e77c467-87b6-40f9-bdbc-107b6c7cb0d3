package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusIntendedPojo;
import inks.service.std.sale.domain.pojo.BusIntendeditemdetailPojo;
import inks.service.std.sale.service.BusIntendedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 意向订单(Bus_Intended)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-17 09:12:04
 */
@RestController
@RequestMapping("D01M17B1")
@Api(tags = "D01M17B1:意向订单")
public class D01M17B1Controller extends BusIntendedController {

    @Resource
    private BusIntendedService busIntendedService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendeditemdetailPojo>> getOnlinePageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_IntendedItem.DisannulMark=0 and Bus_IntendedItem.FinishMark=0 ";  // 未完成转单、未注销
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendedPojo>> getOnlinePageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Intended.FinishCount+Bus_Intended.DisannulCount<Bus_Intended.ItemCount";

            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //------------------------------------------------以下为Scm用户查询-----------------------------------------

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getScmOnlinePageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendeditemdetailPojo>> getScmOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_IntendedItem.DisannulMark=0 and Bus_IntendedItem.FinishMark=0 ";  // 未完成转单、未注销
            // 过滤Scm用户的Groupids
            qpfilter += " and Bus_Intended.Groupid in (" + loginUser.getGroupids() + ")";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getScmOnlinePageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendedPojo>> getScmOnlinePageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Intended.FinishCount+Bus_Intended.DisannulCount<Bus_Intended.ItemCount";
            // 过滤Scm用户的Groupids
            qpfilter += " and Bus_Intended.Groupid in (" + loginUser.getGroupids() + ")";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getScmPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendeditemdetailPojo>> getScmPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            // 过滤Scm用户的Groupids
            String qpfilter = " and Bus_Intended.Groupid in (" + loginUser.getGroupids() + ")";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getScmPageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Intended.List")
    public R<PageInfo<BusIntendedPojo>> getScmPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Intended.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            // 过滤Scm用户的Groupids
            String qpfilter = " and Bus_Intended.Groupid in (" + loginUser.getGroupids() + ")";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busIntendedService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
