package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCouponactivePojo;
import com.github.pagehelper.PageInfo;

/**
 * 优惠券激活记录(Bus_CouponActive)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
public interface BusCouponactiveService {

    BusCouponactivePojo getEntity(String key,String tid);

    PageInfo<BusCouponactivePojo> getPageList(QueryParam queryParam);

    BusCouponactivePojo insert(BusCouponactivePojo busCouponactivePojo);

    BusCouponactivePojo update(BusCouponactivePojo busCouponactivepojo);

    int delete(String key,String tid);

     BusCouponactivePojo approval(BusCouponactivePojo busCouponactivePojo);
}
