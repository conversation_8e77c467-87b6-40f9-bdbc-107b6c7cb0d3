package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCouponPojo;
import inks.service.std.sale.domain.BusCouponEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 优惠券表(Bus_Coupon)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
@Mapper
public interface BusCouponMapper {

    BusCouponPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusCouponPojo> getPageList(QueryParam queryParam);

    int insert(BusCouponEntity busCouponEntity);

    int update(BusCouponEntity busCouponEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    

    int approval(BusCouponEntity busCouponEntity);

    String getidByCode(String couponcode, String tid);
}

