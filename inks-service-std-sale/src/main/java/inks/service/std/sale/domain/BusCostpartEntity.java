package inks.service.std.sale.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 成本组件(BusCostpart)实体类
 *
 * <AUTHOR>
 * @since 2022-08-07 13:29:29
 */
public class BusCostpartEntity implements Serializable {
    private static final long serialVersionUID = -13334076322350914L;
         // id
         private String id;
         // 分组id
         private String partgroupid;
         // 类型
         private String itemtype;
         // 名称
         private String itemname;
         // 规格
         private String itemspec;
         // 单位
         private String itemunit;
         // 基础价
         private Double baseprice;
         // 折扣
         private Double rebate;
         // 二级折扣
         private Double rebatesec;
         // 单价
         private Double price;
         // 最小单价
         private Double pricemin;
         // 最大单价
         private Double pricemax;
         // 有效性
         private Integer enabledmark;
         // 允许删除
         private Integer allowdelete;
         // 允许编辑
         private Integer allowedit;
         // 行号
         private Integer rownum;
         // 备注
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 自定义6
         private String custom6;
         // 自定义7
         private String custom7;
         // 自定义8
         private String custom8;
         // 自定义9
         private String custom9;
         // 自定义10
         private String custom10;
         // 部门id
         private String deptid;
         // 租户id
         private String tenantid;
         // 租户名称
         private String tenantname;
         // 乐观锁
         private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 分组id
    public String getPartgroupid() {
        return partgroupid;
    }
    
    public void setPartgroupid(String partgroupid) {
        this.partgroupid = partgroupid;
    }
        
// 类型
    public String getItemtype() {
        return itemtype;
    }
    
    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }
        
// 名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
// 规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
// 单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
// 基础价
    public Double getBaseprice() {
        return baseprice;
    }
    
    public void setBaseprice(Double baseprice) {
        this.baseprice = baseprice;
    }
        
// 折扣
    public Double getRebate() {
        return rebate;
    }
    
    public void setRebate(Double rebate) {
        this.rebate = rebate;
    }
        
// 二级折扣
    public Double getRebatesec() {
        return rebatesec;
    }
    
    public void setRebatesec(Double rebatesec) {
        this.rebatesec = rebatesec;
    }
        
// 单价
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
// 最小单价
    public Double getPricemin() {
        return pricemin;
    }
    
    public void setPricemin(Double pricemin) {
        this.pricemin = pricemin;
    }
        
// 最大单价
    public Double getPricemax() {
        return pricemax;
    }
    
    public void setPricemax(Double pricemax) {
        this.pricemax = pricemax;
    }
        
// 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 允许删除
    public Integer getAllowdelete() {
        return allowdelete;
    }
    
    public void setAllowdelete(Integer allowdelete) {
        this.allowdelete = allowdelete;
    }
        
// 允许编辑
    public Integer getAllowedit() {
        return allowedit;
    }
    
    public void setAllowedit(Integer allowedit) {
        this.allowedit = allowedit;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 部门id
    public String getDeptid() {
        return deptid;
    }
    
    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

