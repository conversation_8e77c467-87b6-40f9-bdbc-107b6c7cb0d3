package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import inks.service.std.sale.service.AppWorkgroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 外协厂商(App_Wg_Factory)表控制层
 * Customer 客户 Supplier 供应商 Branch 其他部门 Workshop 生产车间  Factory 外协厂商 Prospects潜在客户
 *
 * <AUTHOR>
 * @since 2021-11-11 09:10:03
 */
@RestController
@RequestMapping("D01M01B4")
@Api(tags = "D01M01B4:往来单位:外协厂商")
public class D01M01B4Controller extends AppWorkgroupController {
    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    @ApiOperation(value = " 获取往来单位详细信息", notes = "获取往来单位详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Factory.List")
    public R<AppWorkgroupPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.appWorkgroupService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Factory.List")
    public R<PageInfo<AppWorkgroupPojo>> getPageList(@RequestBody String json, @RequestParam(required = false) Integer dept) {//dept=1才开启过滤部门，默认不过滤
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and GroupType='外协厂商'";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.appWorkgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增外协厂商", notes = "新增外协厂商", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Factory.Add")
    public R<AppWorkgroupPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            AppWorkgroupPojo appWorkgroupPojo = JSONArray.parseObject(json, AppWorkgroupPojo.class);


            appWorkgroupPojo.setCreateby(loginUser.getRealname());   // 创建者
            appWorkgroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            appWorkgroupPojo.setCreatedate(new Date());   // 创建时间
            appWorkgroupPojo.setLister(loginUser.getRealname());   // 制表
            appWorkgroupPojo.setListerid(loginUser.getUserid());    // 制表id
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            appWorkgroupPojo.setGrouptype("外协厂商");
            appWorkgroupPojo.setId(""); // 去掉null
            //重名检查
            AppWorkgroupPojo appWorkgroupPojo1 = this.appWorkgroupService.getEntityByUid(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "编码重复");
            }
            appWorkgroupPojo1 = this.appWorkgroupService.getEntityByName(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "名称重复");
            }
            return R.ok(this.appWorkgroupService.insert(appWorkgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改往来单位", notes = "修改往来单位", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Factory.Edit")
    public R<AppWorkgroupPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            AppWorkgroupPojo appWorkgroupPojo = JSONArray.parseObject(json, AppWorkgroupPojo.class);

            appWorkgroupPojo.setLister(loginUser.getRealname());   // 制表
            appWorkgroupPojo.setListerid(loginUser.getUserid());    // 制表id
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            appWorkgroupPojo.setModifydate(new Date());   //修改时间

            //重名检查
            AppWorkgroupPojo appWorkgroupPojo1 = this.appWorkgroupService.getEntityByUid(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "编码重复");
            }
            appWorkgroupPojo1 = this.appWorkgroupService.getEntityByName(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail(500, "名称重复");
            }

            return R.ok(this.appWorkgroupService.update(appWorkgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Factory.Delete")
    @OperLog(title = "删除外协厂商信息")
    public R<AppWorkgroupPojo> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //检查引用
            List<String> lstcite = this.appWorkgroupService.getCiteBillName(key, loginUser.getTenantid());
            if (!lstcite.isEmpty()) {
                return R.fail(500, "禁止删除,被以下单据引用:" + lstcite);
            }
            // 未删除前先查询
            AppWorkgroupPojo workgroupDB = appWorkgroupService.getEntity(key, loginUser.getTenantid());
            this.appWorkgroupService.delete(key, loginUser.getTenantid());
            return R.ok(workgroupDB, workgroupDB.getGroupname() + "删除成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

