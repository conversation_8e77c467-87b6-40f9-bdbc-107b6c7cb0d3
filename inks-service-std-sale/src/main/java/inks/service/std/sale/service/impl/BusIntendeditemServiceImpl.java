package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusIntendeditemEntity;
import inks.service.std.sale.domain.pojo.BusIntendeditemPojo;
import inks.service.std.sale.mapper.BusIntendeditemMapper;
import inks.service.std.sale.service.BusIntendeditemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 意向项目(BusIntendeditem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 09:12:51
 */
@Service("busIntendeditemService")
public class BusIntendeditemServiceImpl implements BusIntendeditemService {
    @Resource
    private BusIntendeditemMapper busIntendeditemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusIntendeditemPojo getEntity(String key, String tid) {
        return this.busIntendeditemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusIntendeditemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusIntendeditemPojo> lst = busIntendeditemMapper.getPageList(queryParam);
            PageInfo<BusIntendeditemPojo> pageInfo = new PageInfo<BusIntendeditemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusIntendeditemPojo> getList(String Pid, String tid) {
        try {
            List<BusIntendeditemPojo> lst = busIntendeditemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param busIntendeditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusIntendeditemPojo insert(BusIntendeditemPojo busIntendeditemPojo) {
        //初始化item的NULL
        BusIntendeditemPojo itempojo = this.clearNull(busIntendeditemPojo);
        BusIntendeditemEntity busIntendeditemEntity = new BusIntendeditemEntity();
        BeanUtils.copyProperties(itempojo, busIntendeditemEntity);
        //生成雪花id
        busIntendeditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busIntendeditemEntity.setRevision(1);  //乐观锁
        this.busIntendeditemMapper.insert(busIntendeditemEntity);
        return this.getEntity(busIntendeditemEntity.getId(), busIntendeditemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busIntendeditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusIntendeditemPojo update(BusIntendeditemPojo busIntendeditemPojo) {
        BusIntendeditemEntity busIntendeditemEntity = new BusIntendeditemEntity();
        BeanUtils.copyProperties(busIntendeditemPojo, busIntendeditemEntity);
        this.busIntendeditemMapper.update(busIntendeditemEntity);
        return this.getEntity(busIntendeditemEntity.getId(), busIntendeditemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busIntendeditemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param busIntendeditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusIntendeditemPojo clearNull(BusIntendeditemPojo busIntendeditemPojo) {
        //初始化NULL字段
        if (busIntendeditemPojo.getPid() == null) busIntendeditemPojo.setPid("");
        if (busIntendeditemPojo.getGoodsid() == null) busIntendeditemPojo.setGoodsid("");
        if (busIntendeditemPojo.getItemtype() == null) busIntendeditemPojo.setItemtype("");
        if (busIntendeditemPojo.getItemname() == null) busIntendeditemPojo.setItemname("");
        if (busIntendeditemPojo.getItemspec() == null) busIntendeditemPojo.setItemspec("");
        if (busIntendeditemPojo.getItemunit() == null) busIntendeditemPojo.setItemunit("");
        if (busIntendeditemPojo.getDmsgoodsid() == null) busIntendeditemPojo.setDmsgoodsid("");
        if (busIntendeditemPojo.getQuantity() == null) busIntendeditemPojo.setQuantity(0D);
        if (busIntendeditemPojo.getStdprice() == null) busIntendeditemPojo.setStdprice(0D);
        if (busIntendeditemPojo.getStdamount() == null) busIntendeditemPojo.setStdamount(0D);
        if (busIntendeditemPojo.getRebate() == null) busIntendeditemPojo.setRebate(0D);
        if (busIntendeditemPojo.getPrice() == null) busIntendeditemPojo.setPrice(0D);
        if (busIntendeditemPojo.getAmount() == null) busIntendeditemPojo.setAmount(0D);
        if (busIntendeditemPojo.getItemtaxrate() == null) busIntendeditemPojo.setItemtaxrate(0);
        if (busIntendeditemPojo.getTaxprice() == null) busIntendeditemPojo.setTaxprice(0D);
        if (busIntendeditemPojo.getTaxtotal() == null) busIntendeditemPojo.setTaxtotal(0D);
        if (busIntendeditemPojo.getTaxamount() == null) busIntendeditemPojo.setTaxamount(0D);
        if (busIntendeditemPojo.getPlandate() == null) busIntendeditemPojo.setPlandate(new Date());
        if (busIntendeditemPojo.getRemark() == null) busIntendeditemPojo.setRemark("");
        if (busIntendeditemPojo.getRownum() == null) busIntendeditemPojo.setRownum(0);
        if (StringUtils.isBlank(busIntendeditemPojo.getAttributejson())) busIntendeditemPojo.setAttributejson("{}");
        if (isBlank(busIntendeditemPojo.getCostitemjson())) busIntendeditemPojo.setCostitemjson("{}");
        if (isBlank(busIntendeditemPojo.getCostgroupjson())) busIntendeditemPojo.setCostgroupjson("{}");
        if (busIntendeditemPojo.getVirtualitem() == null) busIntendeditemPojo.setVirtualitem(0);
        if (busIntendeditemPojo.getFinishmark() == null) busIntendeditemPojo.setFinishmark(0);
        if (busIntendeditemPojo.getDisannulmark() == null) busIntendeditemPojo.setDisannulmark(0);
        if (busIntendeditemPojo.getDisannullisterid() == null) busIntendeditemPojo.setDisannullisterid("");
        if (busIntendeditemPojo.getDisannullister() == null) busIntendeditemPojo.setDisannullister("");
        if (busIntendeditemPojo.getDisannuldate() == null) busIntendeditemPojo.setDisannuldate(new Date());
        if (busIntendeditemPojo.getCustom1() == null) busIntendeditemPojo.setCustom1("");
        if (busIntendeditemPojo.getCustom2() == null) busIntendeditemPojo.setCustom2("");
        if (busIntendeditemPojo.getCustom3() == null) busIntendeditemPojo.setCustom3("");
        if (busIntendeditemPojo.getCustom4() == null) busIntendeditemPojo.setCustom4("");
        if (busIntendeditemPojo.getCustom5() == null) busIntendeditemPojo.setCustom5("");
        if (busIntendeditemPojo.getCustom6() == null) busIntendeditemPojo.setCustom6("");
        if (busIntendeditemPojo.getCustom7() == null) busIntendeditemPojo.setCustom7("");
        if (busIntendeditemPojo.getCustom8() == null) busIntendeditemPojo.setCustom8("");
        if (busIntendeditemPojo.getCustom9() == null) busIntendeditemPojo.setCustom9("");
        if (busIntendeditemPojo.getCustom10() == null) busIntendeditemPojo.setCustom10("");
        if (busIntendeditemPojo.getTenantid() == null) busIntendeditemPojo.setTenantid("");
        if (busIntendeditemPojo.getRevision() == null) busIntendeditemPojo.setRevision(0);
        return busIntendeditemPojo;
    }
}
