package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusDeliplanPojo;
import inks.service.std.sale.domain.pojo.BusDeliplanitemdetailPojo;
import inks.service.std.sale.domain.BusDeliplanEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 发货计划(BusDeliplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-12 10:57:04
 */
@Mapper
public interface BusDeliplanMapper {

    BusDeliplanPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusDeliplanitemdetailPojo> getPageList(QueryParam queryParam);

    List<BusDeliplanPojo> getPageTh(QueryParam queryParam);

    int insert(BusDeliplanEntity busDeliplanEntity);

    int update(BusDeliplanEntity busDeliplanEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(BusDeliplanPojo busDeliplanPojo);

    int approval(BusDeliplanEntity busDeliplanEntity);

    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);

    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    String getWkWpNameByMachitemid(String machitemid, String tid);
}

