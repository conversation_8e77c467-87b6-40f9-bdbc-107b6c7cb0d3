package inks.service.std.sale.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.sale.domain.pojo.BusInvocarryoverPojo;
import inks.service.std.sale.domain.BusInvocarryoverEntity;
import inks.service.std.sale.mapper.BusInvocarryoverMapper;
import inks.service.std.sale.service.BusInvocarryoverService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 货品账单:发货>发票(BusInvocarryover)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-10 11:30:20
 */
@Service("busInvocarryoverService")
public class BusInvocarryoverServiceImpl implements BusInvocarryoverService {
    @Resource
    private BusInvocarryoverMapper busInvocarryoverMapper;

    @Override
    public BusInvocarryoverPojo getEntity(String key, String tid) {
        return this.busInvocarryoverMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<BusInvocarryoverPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusInvocarryoverPojo> lst = busInvocarryoverMapper.getPageList(queryParam);
            PageInfo<BusInvocarryoverPojo> pageInfo = new PageInfo<BusInvocarryoverPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public BusInvocarryoverPojo insert(BusInvocarryoverPojo busInvocarryoverPojo) {
        //初始化NULL字段
        cleanNull(busInvocarryoverPojo);
        BusInvocarryoverEntity busInvocarryoverEntity = new BusInvocarryoverEntity(); 
        BeanUtils.copyProperties(busInvocarryoverPojo,busInvocarryoverEntity);
          //生成雪花id
          busInvocarryoverEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busInvocarryoverEntity.setRevision(1);  //乐观锁
          this.busInvocarryoverMapper.insert(busInvocarryoverEntity);
        return this.getEntity(busInvocarryoverEntity.getId(),busInvocarryoverEntity.getTenantid());
    }


    @Override
    public BusInvocarryoverPojo update(BusInvocarryoverPojo busInvocarryoverPojo) {
        BusInvocarryoverEntity busInvocarryoverEntity = new BusInvocarryoverEntity(); 
        BeanUtils.copyProperties(busInvocarryoverPojo,busInvocarryoverEntity);
        this.busInvocarryoverMapper.update(busInvocarryoverEntity);
        return this.getEntity(busInvocarryoverEntity.getId(),busInvocarryoverEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.busInvocarryoverMapper.delete(key,tid) ;
    }
    

    public static void cleanNull(BusInvocarryoverPojo busInvocarryoverPojo) {
        if(busInvocarryoverPojo.getRecid()==null) busInvocarryoverPojo.setRecid("");
        if(busInvocarryoverPojo.getGroupid()==null) busInvocarryoverPojo.setGroupid("");
        if(busInvocarryoverPojo.getGroupname()==null) busInvocarryoverPojo.setGroupname("");
        if(busInvocarryoverPojo.getGroupuid()==null) busInvocarryoverPojo.setGroupuid("");
        if(busInvocarryoverPojo.getGoodsid()==null) busInvocarryoverPojo.setGoodsid("");
        if(busInvocarryoverPojo.getItemcode()==null) busInvocarryoverPojo.setItemcode("");
        if(busInvocarryoverPojo.getItemname()==null) busInvocarryoverPojo.setItemname("");
        if(busInvocarryoverPojo.getItemspec()==null) busInvocarryoverPojo.setItemspec("");
        if(busInvocarryoverPojo.getItemunit()==null) busInvocarryoverPojo.setItemunit("");
        if(busInvocarryoverPojo.getOpenqty()==null) busInvocarryoverPojo.setOpenqty(0D);
        if(busInvocarryoverPojo.getOpenamount()==null) busInvocarryoverPojo.setOpenamount(0D);
        if(busInvocarryoverPojo.getInqty()==null) busInvocarryoverPojo.setInqty(0D);
        if(busInvocarryoverPojo.getInamount()==null) busInvocarryoverPojo.setInamount(0D);
        if(busInvocarryoverPojo.getOutqty()==null) busInvocarryoverPojo.setOutqty(0D);
        if(busInvocarryoverPojo.getOutamount()==null) busInvocarryoverPojo.setOutamount(0D);
        if(busInvocarryoverPojo.getCloseqty()==null) busInvocarryoverPojo.setCloseqty(0D);
        if(busInvocarryoverPojo.getCloseamount()==null) busInvocarryoverPojo.setCloseamount(0D);
        if(busInvocarryoverPojo.getSkuid()==null) busInvocarryoverPojo.setSkuid("");
        if(busInvocarryoverPojo.getAttributejson()==null) busInvocarryoverPojo.setAttributejson("");
        if(busInvocarryoverPojo.getClosemark()==null) busInvocarryoverPojo.setClosemark(0);
        if(busInvocarryoverPojo.getRownum()==null) busInvocarryoverPojo.setRownum(0);
        if(busInvocarryoverPojo.getCreateby()==null) busInvocarryoverPojo.setCreateby("");
        if(busInvocarryoverPojo.getCreatebyid()==null) busInvocarryoverPojo.setCreatebyid("");
        if(busInvocarryoverPojo.getCreatedate()==null) busInvocarryoverPojo.setCreatedate(new Date());
        if(busInvocarryoverPojo.getLister()==null) busInvocarryoverPojo.setLister("");
        if(busInvocarryoverPojo.getListerid()==null) busInvocarryoverPojo.setListerid("");
        if(busInvocarryoverPojo.getModifydate()==null) busInvocarryoverPojo.setModifydate(new Date());
        if(busInvocarryoverPojo.getCustom1()==null) busInvocarryoverPojo.setCustom1("");
        if(busInvocarryoverPojo.getCustom2()==null) busInvocarryoverPojo.setCustom2("");
        if(busInvocarryoverPojo.getCustom3()==null) busInvocarryoverPojo.setCustom3("");
        if(busInvocarryoverPojo.getCustom4()==null) busInvocarryoverPojo.setCustom4("");
        if(busInvocarryoverPojo.getCustom5()==null) busInvocarryoverPojo.setCustom5("");
        if(busInvocarryoverPojo.getCustom6()==null) busInvocarryoverPojo.setCustom6("");
        if(busInvocarryoverPojo.getCustom7()==null) busInvocarryoverPojo.setCustom7("");
        if(busInvocarryoverPojo.getCustom8()==null) busInvocarryoverPojo.setCustom8("");
        if(busInvocarryoverPojo.getCustom9()==null) busInvocarryoverPojo.setCustom9("");
        if(busInvocarryoverPojo.getCustom10()==null) busInvocarryoverPojo.setCustom10("");
        if(busInvocarryoverPojo.getTenantid()==null) busInvocarryoverPojo.setTenantid("");
        if(busInvocarryoverPojo.getRevision()==null) busInvocarryoverPojo.setRevision(0);
   }

}
