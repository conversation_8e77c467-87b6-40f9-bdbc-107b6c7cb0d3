package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.log.annotation.OperLog;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusBudgetPojo;
import inks.service.std.sale.domain.pojo.BusBudgetitemPojo;
import inks.service.std.sale.domain.pojo.BusBudgetitemdetailPojo;
import inks.service.std.sale.mapper.BusBudgetMapper;
import inks.service.std.sale.service.BusBudgetService;
import inks.service.std.sale.service.BusBudgetitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 成本预算(BusBudget)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-02 09:26:55
 */

public class BusBudgetController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BusBudgetController.class);
    private final String moduleCode = "D01M16B1";
    /**
     * 服务对象
     */
    @Resource
    private BusBudgetService busBudgetService;
    @Resource
    private BusBudgetMapper busBudgetMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private BusBudgetitemService busBudgetitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private UtilsFeignService utilsFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取成本预算详细信息", notes = "获取成本预算详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Budget.List")
    public R<BusBudgetPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busBudgetService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.List")
    public R<PageInfo<BusBudgetitemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Budget.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());

            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busBudgetService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取成本预算详细信息", notes = "获取成本预算详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Budget.List")
    public R<BusBudgetPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busBudgetService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.List")
    public R<PageInfo<BusBudgetPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Budget.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busBudgetService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.List")
    public R<PageInfo<BusBudgetPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Budget.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busBudgetService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增成本预算", notes = "新增成本预算", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.Add")
    public R<BusBudgetPojo> create(@RequestBody String json) {
        try {
            BusBudgetPojo busBudgetPojo = JSONArray.parseObject(json, BusBudgetPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //检查该订单号是否已经存在busBudgetPojo.getMachbillid  改为校验订单子表 (订单子表会分批次转订单预算)
//            int i = busBudgetMapper.checkMachbillid(busBudgetPojo.getMachbillid(), loginUser.getTenantid());
            List<String> machitemIds = busBudgetPojo.getItem().stream()
                    .map(BusBudgetitemPojo::getMachitemid)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(machitemIds)) return R.fail("物料machitemIds不能为空");

            List<String> repeatGoodsNames = busBudgetMapper.checkMachItemIds(machitemIds, loginUser.getTenantid());
            if (CollectionUtils.isNotEmpty(repeatGoodsNames)) {
                return R.fail("以下物料已经存在成本预算中：" + repeatGoodsNames);
            }
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Bus_Budget", null, loginUser.getTenantid());
            busBudgetPojo.setRefno(refno);
            busBudgetPojo.setCreateby(loginUser.getRealName());   // 创建者
            busBudgetPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busBudgetPojo.setCreatedate(new Date());   // 创建时间
            busBudgetPojo.setLister(loginUser.getRealname());   // 制表
            busBudgetPojo.setListerid(loginUser.getUserid());    // 制表id
            busBudgetPojo.setModifydate(new Date());   //修改时间
            busBudgetPojo.setTenantid(loginUser.getTenantid());   //租户id
            BusBudgetPojo insert = this.busBudgetService.insert(busBudgetPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());// 保存单据编码RefNoUtils
            return R.ok(insert);
        } catch (
                Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改成本预算", notes = "修改成本预算", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.Edit")
    public R<BusBudgetPojo> update(@RequestBody String json) {
        try {
            BusBudgetPojo busBudgetPojo = JSONArray.parseObject(json, BusBudgetPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busBudgetPojo.setLister(loginUser.getRealname());   // 制表
            busBudgetPojo.setListerid(loginUser.getUserid());    // 制表id
            busBudgetPojo.setModifydate(new Date());   //修改时间
            busBudgetPojo.setAssessor(""); //审核员
            busBudgetPojo.setAssessorid(""); //审核员
            busBudgetPojo.setAssessdate(new Date()); //审核时间
            busBudgetPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busBudgetService.update(busBudgetPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除成本预算", notes = "删除成本预算", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Budget.Delete")
    @OperLog(title = "删除成本预算")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            String refno = this.busBudgetService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取成本预算子表 详细信息", notes = "获取成本预算详细信息", produces = "application/json")
    @RequestMapping(value = "/getItemEntityByMachitemid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Budget.List")
    public R<BusBudgetitemPojo> getItemEntityByMachitemid(String machitemid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busBudgetitemService.getEntityByMachitemid(machitemid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增成本预算Item", notes = "新增成本预算Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.Add")
    public R<BusBudgetitemPojo> createItem(@RequestBody String json) {
        try {
            BusBudgetitemPojo busBudgetitemPojo = JSONArray.parseObject(json, BusBudgetitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busBudgetitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busBudgetitemService.insert(busBudgetitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增成本预算Item", notes = "新增成本预算Item", produces = "application/json")
    @RequestMapping(value = "/updateItemByMach", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.Add")
    public R<BusBudgetitemPojo> updateItemByMach(@RequestBody String json) {
        try {
            BusBudgetitemPojo busBudgetitemPojo = JSONArray.parseObject(json, BusBudgetitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            BusBudgetitemPojo entityDB = busBudgetitemService.getEntityByMachitemid(busBudgetitemPojo.getMachitemid(), loginUser.getTenantid());
            busBudgetitemPojo.setTenantid(loginUser.getTenantid());
            if (entityDB == null) {
                return R.ok(this.busBudgetitemService.insert(busBudgetitemPojo));
            } else {
                busBudgetitemPojo.setId(entityDB.getId());
                return R.ok(this.busBudgetitemService.update(busBudgetitemPojo));
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除成本预算Item", notes = "删除成本预算Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Budget.Delete")
    public R<Integer> deleteItem(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busBudgetitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核成本预算", notes = "审核成本预算", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Budget.Approval")
    public R<BusBudgetPojo> approval(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            BusBudgetPojo busBudgetPojo = this.busBudgetService.getEntity(key, loginUser.getTenantid());
            if (busBudgetPojo.getAssessor().equals("")) {
                busBudgetPojo.setAssessor(loginUser.getRealname()); //审核员
                busBudgetPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busBudgetPojo.setAssessor(""); //审核员
                busBudgetPojo.setAssessorid(""); //审核员
            }
            busBudgetPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busBudgetService.approval(busBudgetPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印BusBudget明细报表(分页PageList)", notes = "打印明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Budget.CreateDate");

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Budget.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusBudgetitemdetailPojo> lst = this.busBudgetService.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusBudgetitemdetailPojo itemdetailPojo = new BusBudgetitemdetailPojo();
                    lst.add(itemdetailPojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印BusBudget明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Budget.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Budget.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Budget.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusBudgetitemdetailPojo> lstitem = this.busBudgetService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BusBudget明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Budget.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusBudgetPojo busBudgetPojo = this.busBudgetService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busBudgetPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busBudgetPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusBudgetitemPojo busBudgetitemPojo = new BusBudgetitemPojo();
                    busBudgetPojo.getItem().add(busBudgetitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(busBudgetPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

