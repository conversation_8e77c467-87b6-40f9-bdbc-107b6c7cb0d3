package inks.service.std.sale.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票项目(BusInvoiceitem)Entity
 *
 * <AUTHOR>
 * @since 2024-04-10 13:58:25
 */
public class BusInvoiceitemEntity implements Serializable {
    private static final long serialVersionUID = 237231300262568928L;
     // id
    private String id;
     // Pid
    private String pid;
     // 送货单号
    private String deliuid;
     // 操作日期
    private Date delidate;
     // 单据类型
    private String delitype;
     // DeliItemid
    private String deliitemid;
     // 商品id
    private String goodsid;
     // 单据数量
    private Double billqty;
     // 本次数量
    private Double quantity;
     // 含税单价
    private Double taxprice;
     // 含税金额
    private Double taxamount;
     // 未税单价
    private Double price;
     // 未税金额
    private Double amount;
     // 税率(备用)
    private Integer itemtaxrate;
     // 税额
    private Double taxtotal;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 销售单号
    private String machuid;
     // 销售子项id
    private String machitemid;
     // 客户PO
    private String custpo;
     // 平均预收额
    private Double avgfirstamt;
     // 平均收款额
    private Double avglastamt;
     // 属性Josn
    private String attributejson;
     // 自定义1
    private String custom1;
     // 订单批次
    private String machbatch;
     // 标准销价
    private Double stdprice;
     // 标准金额
    private Double stdamount;
     // 折扣
    private Double rebate;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 自定义11
    private String custom11;
     // 自定义12
    private String custom12;
     // 自定义13
    private String custom13;
     // 自定义14
    private String custom14;
     // 自定义15
    private String custom15;
     // 自定义16
    private String custom16;
     // 自定义17
    private String custom17;
     // 自定义18
    private String custom18;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 送货单号
    public String getDeliuid() {
        return deliuid;
    }
    
    public void setDeliuid(String deliuid) {
        this.deliuid = deliuid;
    }
        
   // 操作日期
    public Date getDelidate() {
        return delidate;
    }
    
    public void setDelidate(Date delidate) {
        this.delidate = delidate;
    }
        
   // 单据类型
    public String getDelitype() {
        return delitype;
    }
    
    public void setDelitype(String delitype) {
        this.delitype = delitype;
    }
        
   // DeliItemid
    public String getDeliitemid() {
        return deliitemid;
    }
    
    public void setDeliitemid(String deliitemid) {
        this.deliitemid = deliitemid;
    }
        
   // 商品id
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 单据数量
    public Double getBillqty() {
        return billqty;
    }
    
    public void setBillqty(Double billqty) {
        this.billqty = billqty;
    }
        
   // 本次数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
   // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
   // 未税单价
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
   // 未税金额
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
   // 税率(备用)
    public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
   // 税额
    public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
   // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
   // 客户PO
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
   // 平均预收额
    public Double getAvgfirstamt() {
        return avgfirstamt;
    }
    
    public void setAvgfirstamt(Double avgfirstamt) {
        this.avgfirstamt = avgfirstamt;
    }
        
   // 平均收款额
    public Double getAvglastamt() {
        return avglastamt;
    }
    
    public void setAvglastamt(Double avglastamt) {
        this.avglastamt = avglastamt;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 订单批次
    public String getMachbatch() {
        return machbatch;
    }

    public void setMachbatch(String machbatch) {
        this.machbatch = machbatch;
    }

   // 标准销价
    public Double getStdprice() {
        return stdprice;
    }
    
    public void setStdprice(Double stdprice) {
        this.stdprice = stdprice;
    }
        
   // 标准金额
    public Double getStdamount() {
        return stdamount;
    }
    
    public void setStdamount(Double stdamount) {
        this.stdamount = stdamount;
    }
        
   // 折扣
    public Double getRebate() {
        return rebate;
    }
    
    public void setRebate(Double rebate) {
        this.rebate = rebate;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 自定义11
    public String getCustom11() {
        return custom11;
    }
    
    public void setCustom11(String custom11) {
        this.custom11 = custom11;
    }
        
   // 自定义12
    public String getCustom12() {
        return custom12;
    }
    
    public void setCustom12(String custom12) {
        this.custom12 = custom12;
    }
        
   // 自定义13
    public String getCustom13() {
        return custom13;
    }
    
    public void setCustom13(String custom13) {
        this.custom13 = custom13;
    }
        
   // 自定义14
    public String getCustom14() {
        return custom14;
    }
    
    public void setCustom14(String custom14) {
        this.custom14 = custom14;
    }
        
   // 自定义15
    public String getCustom15() {
        return custom15;
    }
    
    public void setCustom15(String custom15) {
        this.custom15 = custom15;
    }
        
   // 自定义16
    public String getCustom16() {
        return custom16;
    }
    
    public void setCustom16(String custom16) {
        this.custom16 = custom16;
    }
        
   // 自定义17
    public String getCustom17() {
        return custom17;
    }
    
    public void setCustom17(String custom17) {
        this.custom17 = custom17;
    }
        
   // 自定义18
    public String getCustom18() {
        return custom18;
    }
    
    public void setCustom18(String custom18) {
        this.custom18 = custom18;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

