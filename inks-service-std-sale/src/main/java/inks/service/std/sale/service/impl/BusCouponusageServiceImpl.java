package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusCouponusageEntity;
import inks.service.std.sale.domain.pojo.BusCouponPojo;
import inks.service.std.sale.domain.pojo.BusCouponusagePojo;
import inks.service.std.sale.mapper.BusCouponMapper;
import inks.service.std.sale.mapper.BusCouponusageMapper;
import inks.service.std.sale.service.BusCouponusageService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 优惠券使用记录(BusCouponusage)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
@Service("busCouponusageService")
public class BusCouponusageServiceImpl implements BusCouponusageService {
    @Resource
    private BusCouponusageMapper busCouponusageMapper;
    @Resource
    private BusCouponMapper busCouponMapper;

    @Override
    public BusCouponusagePojo getEntity(String key, String tid) {
        return this.busCouponusageMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<BusCouponusagePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCouponusagePojo> lst = busCouponusageMapper.getPageList(queryParam);
            PageInfo<BusCouponusagePojo> pageInfo = new PageInfo<BusCouponusagePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public BusCouponusagePojo insert(BusCouponusagePojo busCouponusagePojo) {
        String tid = busCouponusagePojo.getTenantid();
        //初始化NULL字段
        cleanNull(busCouponusagePojo);
        BusCouponusageEntity busCouponusageEntity = new BusCouponusageEntity();
        BeanUtils.copyProperties(busCouponusagePojo, busCouponusageEntity);
        //生成雪花id
        busCouponusageEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busCouponusageEntity.setRevision(1);  //乐观锁

        // 校验累计使用(优惠)金额不能超过优惠券激活金额
        String couponid = busCouponusagePojo.getCouponid();
        BusCouponPojo couponPojo = busCouponMapper.getEntity(couponid, tid);
        if (couponPojo == null) {
            throw new BaseBusinessException("优惠券不存在");
        }
        if (couponPojo.getUsedamount() + busCouponusagePojo.getUsedamount() > couponPojo.getActiveamount()) {
            throw new BaseBusinessException("累计使用优惠金额：" + (couponPojo.getUsedamount() + busCouponusagePojo.getUsedamount()) + " 超过优惠券激活金额：" + couponPojo.getActiveamount());
        }
        this.busCouponusageMapper.insert(busCouponusageEntity);
        // 优惠券主表同步下累计使用优惠金额
        busCouponusageMapper.syncCouponUsedAmount(couponid, tid);
        return this.getEntity(busCouponusageEntity.getId(), tid);

    }


    @Override
    public BusCouponusagePojo update(BusCouponusagePojo busCouponusagePojo) {
        String tid = busCouponusagePojo.getTenantid();
        BusCouponusageEntity busCouponusageEntity = new BusCouponusageEntity();
        BeanUtils.copyProperties(busCouponusagePojo, busCouponusageEntity);
        // 校验累计使用(优惠)金额不能超过优惠券激活金额
        String couponid = busCouponusagePojo.getCouponid();
        BusCouponPojo couponPojo = busCouponMapper.getEntity(couponid, tid);
        if (couponPojo==null) {
            throw new BaseBusinessException("优惠券不存在");
        }
        BusCouponusagePojo orgUsageDB = busCouponusageMapper.getEntity(busCouponusageEntity.getId(), tid);
        if (couponPojo.getUsedamount() + busCouponusagePojo.getUsedamount() - orgUsageDB.getUsedamount() > couponPojo.getActiveamount()) {
            throw new BaseBusinessException("累计使用优惠金额：" + (couponPojo.getUsedamount() + busCouponusagePojo.getUsedamount() - orgUsageDB.getUsedamount()) + " 超过优惠券激活金额：" + couponPojo.getActiveamount());
        }
        this.busCouponusageMapper.update(busCouponusageEntity);
        // 优惠券主表同步下累计使用优惠金额
        busCouponusageMapper.syncCouponUsedAmount(couponid, tid);
        return this.getEntity(busCouponusageEntity.getId(), tid);
    }


    @Override
    public int delete(String key, String tid) {
        BusCouponusagePojo couponusageDB = busCouponusageMapper.getEntity(key, tid);
        int delete = this.busCouponusageMapper.delete(key, tid);
        // 优惠券主表同步下累计使用优惠金额
        String couponid = couponusageDB.getCouponid();
        busCouponusageMapper.syncCouponUsedAmount(couponid, tid);
        return delete;
    }


    private static void cleanNull(BusCouponusagePojo busCouponusagePojo) {
        if (busCouponusagePojo.getCouponid() == null) busCouponusagePojo.setCouponid("");
        if (busCouponusagePojo.getModulecode() == null) busCouponusagePojo.setModulecode("");
        if (busCouponusagePojo.getCiteuid() == null) busCouponusagePojo.setCiteuid("");
        if (busCouponusagePojo.getCiteid() == null) busCouponusagePojo.setCiteid("");
        if (busCouponusagePojo.getUsedamount() == null) busCouponusagePojo.setUsedamount(0D);
        if (busCouponusagePojo.getRemark() == null) busCouponusagePojo.setRemark("");
        if (busCouponusagePojo.getRownum() == null) busCouponusagePojo.setRownum(0);
        if (busCouponusagePojo.getCreateby() == null) busCouponusagePojo.setCreateby("");
        if (busCouponusagePojo.getCreatebyid() == null) busCouponusagePojo.setCreatebyid("");
        if (busCouponusagePojo.getCreatedate() == null) busCouponusagePojo.setCreatedate(new Date());
        if (busCouponusagePojo.getLister() == null) busCouponusagePojo.setLister("");
        if (busCouponusagePojo.getListerid() == null) busCouponusagePojo.setListerid("");
        if (busCouponusagePojo.getModifydate() == null) busCouponusagePojo.setModifydate(new Date());
        if (busCouponusagePojo.getCustom1() == null) busCouponusagePojo.setCustom1("");
        if (busCouponusagePojo.getCustom2() == null) busCouponusagePojo.setCustom2("");
        if (busCouponusagePojo.getCustom3() == null) busCouponusagePojo.setCustom3("");
        if (busCouponusagePojo.getCustom4() == null) busCouponusagePojo.setCustom4("");
        if (busCouponusagePojo.getCustom5() == null) busCouponusagePojo.setCustom5("");
        if (busCouponusagePojo.getCustom6() == null) busCouponusagePojo.setCustom6("");
        if (busCouponusagePojo.getCustom7() == null) busCouponusagePojo.setCustom7("");
        if (busCouponusagePojo.getCustom8() == null) busCouponusagePojo.setCustom8("");
        if (busCouponusagePojo.getCustom9() == null) busCouponusagePojo.setCustom9("");
        if (busCouponusagePojo.getCustom10() == null) busCouponusagePojo.setCustom10("");
        if (busCouponusagePojo.getTenantid() == null) busCouponusagePojo.setTenantid("");
        if (busCouponusagePojo.getTenantname() == null) busCouponusagePojo.setTenantname("");
        if (busCouponusagePojo.getRevision() == null) busCouponusagePojo.setRevision(0);
    }

    @Override
    public void deleteByCouponidAndCiteid(String couponid, String id, String tid) {
        this.busCouponusageMapper.deleteByCouponidAndCiteid(couponid, id,tid);
        // 优惠券主表同步下累计使用优惠金额
        busCouponusageMapper.syncCouponUsedAmount(couponid,tid);
    }

    @Override
    public void deleteByCouponCodeAndCiteid(String couponcode, String id,String tid) {
        String couponid = busCouponMapper.getidByCode(couponcode,tid);
        deleteByCouponidAndCiteid(couponid, id,tid);
    }
}
