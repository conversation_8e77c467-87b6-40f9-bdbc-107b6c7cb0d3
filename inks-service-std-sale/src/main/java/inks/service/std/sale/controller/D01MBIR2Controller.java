package inks.service.std.sale.controller; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/3
 * @param 销售大屏
 */

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.AppWorkgroupPojo;
import inks.service.std.sale.service.AppWorkgroupService;
import inks.service.std.sale.service.D01MBIR2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("D01MBIR2")
@Api(tags = "D01MBIR2:送货通大屏")
public class D01MBIR2Controller {
    @Resource
    private D01MBIR2Service d01MBIR2Service;

    /**
     * 服务对象
     */
    @Resource
    private AppWorkgroupService appWorkgroupService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取客户销售额排名", notes = "获取客户销售额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGroupMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR2Service.getSumAmtByGroupMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @param * @param null
     * @return
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     */
    @ApiOperation(value = " 获取销售单货品金额排名", notes = "获取销售单货品金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGoodsMax", method = RequestMethod.POST)
    public R<List<ChartPojo>> getSumAmtByGoodsMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR2Service.getSumAmtByGoodsMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description
     * @date 2021/12/30
     * @param * @param null
     * @return 业务员订单金额占比
     */
    @ApiOperation(value = "业务员订单金额占比", notes = "业务员订单金额占比", produces = "application/json")
    @RequestMapping(value = "/getSumAmtBySalesman", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtBySalesman(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR2Service.getSumAmtBySalesman(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售单统计by年", notes = "销售单统计by年 trend=1为趋势", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByYear", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByYear(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR2Service.getSumAmtByYear(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售单统计by月", notes = "销售单统计by月 trend=1为趋势", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByMonth", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByMonth(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR2Service.getSumAmtByMonth(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售单统计by7天", notes = "销售单统计by天 trend=1为趋势", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByDay", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByDay(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR2Service.getSumAmtByDay(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "标签：根据日期范围汇总销售额、货品数量、记录数", notes = "销售额(value)、货品数量(valueb)、明细数(valuec)", produces = "application/json")
    @RequestMapping(value = "/getTagSumAmtQtyByDate", method = RequestMethod.POST)
    public R<ChartPojo> getTagSumAmtQtyByDate(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR2Service.getTagSumAmtQtyByDate(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "标签：当前客户总数", notes = "当前客户总数", produces = "application/json")
    @RequestMapping(value = "/getTagGroupCount", method = RequestMethod.GET)
    public R<ChartPojo> getTagGroupCount() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = new QueryParam();
            queryParam.setPageNum(1);
            queryParam.setPageSize(1);
            queryParam.setOrderBy("CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            PageInfo<AppWorkgroupPojo> lst = this.appWorkgroupService.getPageList(queryParam);
            ChartPojo chartPojo = new ChartPojo();
            chartPojo.setName("客户总数");
            chartPojo.setValue(lst.getTotal() + 0.0);
            return R.ok(chartPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图年
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "销售饼状图年", notes = "销售饼状图年", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByYearMax", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<List<ChartPojo>> getSumAmtByYearMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR2Service.getSumAmtByYearMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图月
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "销售饼状图月", notes = "销售饼状图月", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByMonthMax", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<List<ChartPojo>> getSumAmtByMonthMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR2Service.getSumAmtByMonthMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图周
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "销售饼状图周", notes = "销售饼状图周", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByDayMax", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<List<ChartPojo>> getSumAmtByDayMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR2Service.getSumAmtByDayMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "根据当前月查询本月开票", notes = "根据当前月查询本月开票", produces = "application/json")
//    @RequestMapping(value = "/getTagSumAmtByMonth", method = RequestMethod.GET)
////    @PreAuthorize(hasPermi = "Bus_Invoice.List")
//    public R<List<ChartPojo>> getTagSumAmtByMonth() {
//        try {
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.d01MBIR2Service.getTagSumAmtByMonth(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 订单逾期
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "订单逾期", notes = "订单逾期", produces = "application/json")
//    @RequestMapping(value = "/getPageList", method = RequestMethod.GET)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<ChartPojo> getPageList() {
//        try {
//            
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.d01MBIR2Service.getPageList(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 热销产品
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "热销产品", notes = "热销产品", produces = "application/json")
//    @RequestMapping(value = "/getSumByGoodsMax", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<List<ChartPojo>> getSumByGoodsMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR2Service.getSumByGoodsMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//

//
//    /*
//     *
//     * <AUTHOR>
//     * @description 查询在线订单数
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "查询在线订单数", notes = "查询在线订单数", produces = "application/json")
//    @RequestMapping(value = "/getListSize", method = RequestMethod.GET)
//    public R<Integer> getCountMachItemOnline() {
//        try {
//            
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.d01MBIR2Service.getCountMachItemOnline(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
}
