package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusReceiptcashEntity;
import inks.service.std.sale.domain.pojo.BusReceiptcashPojo;
import inks.service.std.sale.mapper.BusReceiptcashMapper;
import inks.service.std.sale.service.BusReceiptcashService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 现金项目(BusReceiptcash)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-20 13:13:44
 */
@Service("busReceiptcashService")
public class BusReceiptcashServiceImpl implements BusReceiptcashService {
    @Resource
    private BusReceiptcashMapper busReceiptcashMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusReceiptcashPojo getEntity(String key,String tid) {
        return this.busReceiptcashMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceiptcashPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceiptcashPojo> lst = busReceiptcashMapper.getPageList(queryParam);
            PageInfo<BusReceiptcashPojo> pageInfo = new PageInfo<BusReceiptcashPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusReceiptcashPojo> getList(String Pid,String tid) { 
        try {
            List<BusReceiptcashPojo> lst = busReceiptcashMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busReceiptcashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusReceiptcashPojo insert(BusReceiptcashPojo busReceiptcashPojo) {
        //初始化item的NULL
        BusReceiptcashPojo itempojo =this.clearNull(busReceiptcashPojo);
        BusReceiptcashEntity busReceiptcashEntity = new BusReceiptcashEntity(); 
        BeanUtils.copyProperties(itempojo,busReceiptcashEntity);
        
          busReceiptcashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busReceiptcashEntity.setRevision(1);  //乐观锁      
          this.busReceiptcashMapper.insert(busReceiptcashEntity);
        return this.getEntity(busReceiptcashEntity.getId(),busReceiptcashEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busReceiptcashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusReceiptcashPojo update(BusReceiptcashPojo busReceiptcashPojo) {
        BusReceiptcashEntity busReceiptcashEntity = new BusReceiptcashEntity(); 
        BeanUtils.copyProperties(busReceiptcashPojo,busReceiptcashEntity);
        this.busReceiptcashMapper.update(busReceiptcashEntity);
        return this.getEntity(busReceiptcashEntity.getId(),busReceiptcashEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busReceiptcashMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busReceiptcashPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusReceiptcashPojo clearNull(BusReceiptcashPojo busReceiptcashPojo){
     //初始化NULL字段
     if(busReceiptcashPojo.getPid()==null) busReceiptcashPojo.setPid("");
     if(busReceiptcashPojo.getCashaccid()==null) busReceiptcashPojo.setCashaccid("");
     if(busReceiptcashPojo.getCashaccname()==null) busReceiptcashPojo.setCashaccname("");
     if(busReceiptcashPojo.getAmount()==null) busReceiptcashPojo.setAmount(0D);
     if(busReceiptcashPojo.getRownum()==null) busReceiptcashPojo.setRownum(0);
     if(busReceiptcashPojo.getRemark()==null) busReceiptcashPojo.setRemark("");
     if(busReceiptcashPojo.getCustom1()==null) busReceiptcashPojo.setCustom1("");
     if(busReceiptcashPojo.getCustom2()==null) busReceiptcashPojo.setCustom2("");
     if(busReceiptcashPojo.getCustom3()==null) busReceiptcashPojo.setCustom3("");
     if(busReceiptcashPojo.getCustom4()==null) busReceiptcashPojo.setCustom4("");
     if(busReceiptcashPojo.getCustom5()==null) busReceiptcashPojo.setCustom5("");
     if(busReceiptcashPojo.getCustom6()==null) busReceiptcashPojo.setCustom6("");
     if(busReceiptcashPojo.getCustom7()==null) busReceiptcashPojo.setCustom7("");
     if(busReceiptcashPojo.getCustom8()==null) busReceiptcashPojo.setCustom8("");
     if(busReceiptcashPojo.getCustom9()==null) busReceiptcashPojo.setCustom9("");
     if(busReceiptcashPojo.getCustom10()==null) busReceiptcashPojo.setCustom10("");
     if(busReceiptcashPojo.getTenantid()==null) busReceiptcashPojo.setTenantid("");
     if(busReceiptcashPojo.getRevision()==null) busReceiptcashPojo.setRevision(0);
     return busReceiptcashPojo;
     }
}
