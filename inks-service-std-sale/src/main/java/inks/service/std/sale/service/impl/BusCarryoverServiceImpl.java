package inks.service.std.sale.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.sale.domain.BusCarryoverEntity;
import inks.service.std.sale.domain.BusCarryoverinvoEntity;
import inks.service.std.sale.domain.BusCarryoveritemEntity;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.mapper.BusCarryoverMapper;
import inks.service.std.sale.mapper.BusCarryoverinvoMapper;
import inks.service.std.sale.mapper.BusCarryoveritemMapper;
import inks.service.std.sale.service.BusCarryoverService;
import inks.service.std.sale.service.BusCarryoverinvoService;
import inks.service.std.sale.service.BusCarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 货品账单(BusCarryover)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-27 08:50:19
 */
@Service("busCarryoverService")
public class BusCarryoverServiceImpl implements BusCarryoverService {
    @Resource
    private BusCarryoverinvoService busCarryoverinvoService;
    @Resource
    private BusCarryoverinvoMapper busCarryoverinvoMapper;
    @Resource
    private BusCarryoverMapper busCarryoverMapper;

    @Resource
    private BusCarryoveritemMapper busCarryoveritemMapper;


    @Resource
    private BusCarryoveritemService busCarryoveritemService;

    @Override
    public BusCarryoverPojo getEntity(String key, String tid) {
        return this.busCarryoverMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<BusCarryoveritemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCarryoveritemdetailPojo> lst = busCarryoverMapper.getPageList(queryParam);
            PageInfo<BusCarryoveritemdetailPojo> pageInfo = new PageInfo<BusCarryoveritemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<BusCarryoverinvodetailPojo> getInvoPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCarryoverinvodetailPojo> lst = busCarryoverMapper.getInvoPageList(queryParam);
            PageInfo<BusCarryoverinvodetailPojo> pageInfo = new PageInfo<BusCarryoverinvodetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public BusCarryoverPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusCarryoverPojo busCarryoverPojo = this.busCarryoverMapper.getEntity(key, tid);
            //读取子表
            busCarryoverPojo.setItem(busCarryoveritemMapper.getList(busCarryoverPojo.getId(), tid));
            return busCarryoverPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<BusCarryoverPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCarryoverPojo> lst = busCarryoverMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (BusCarryoverPojo item : lst) {
                item.setItem(busCarryoveritemMapper.getList(item.getId(), tid));
            }
            PageInfo<BusCarryoverPojo> pageInfo = new PageInfo<BusCarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<BusCarryoverPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusCarryoverPojo> lst = busCarryoverMapper.getPageTh(queryParam);
            PageInfo<BusCarryoverPojo> pageInfo = new PageInfo<BusCarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public BusCarryoverPojo insert(BusCarryoverPojo busCarryoverPojo) {
        String tid = busCarryoverPojo.getTenantid();
        //初始化NULL字段
        cleanNull(busCarryoverPojo);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusCarryoverEntity busCarryoverEntity = new BusCarryoverEntity();
        BeanUtils.copyProperties(busCarryoverPojo, busCarryoverEntity);

        //设置id和新建日期
        busCarryoverEntity.setId(id);
        busCarryoverEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busCarryoverMapper.insert(busCarryoverEntity);
        //Item子表处理
        List<BusCarryoveritemPojo> lst = busCarryoverPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (BusCarryoveritemPojo item : lst) {
                //初始化item的NULL
                BusCarryoveritemPojo itemPojo = this.busCarryoveritemService.clearNull(item);
                BusCarryoveritemEntity busCarryoveritemEntity = new BusCarryoveritemEntity();
                BeanUtils.copyProperties(itemPojo, busCarryoveritemEntity);
                //设置id和Pid
                busCarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busCarryoveritemEntity.setPid(id);
                busCarryoveritemEntity.setTenantid(tid);
                busCarryoveritemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busCarryoveritemMapper.insert(busCarryoveritemEntity);
            }

        }
        // invo子表处理
        List<BusCarryoverinvoPojo> invoLst = busCarryoverPojo.getInvo();
        if (invoLst != null) {
            //循环每个invo子表
            for (BusCarryoverinvoPojo invo : invoLst) {
                //初始化invo的NULL
                BusCarryoverinvoPojo invoPojo = this.busCarryoverinvoService.clearNull(invo);
                BusCarryoverinvoEntity busCarryoverinvoEntity = new BusCarryoverinvoEntity();
                BeanUtils.copyProperties(invoPojo, busCarryoverinvoEntity);
                //设置id和Pid
                busCarryoverinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busCarryoverinvoEntity.setPid(id);
                busCarryoverinvoEntity.setTenantid(tid);
                busCarryoverinvoEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busCarryoverinvoMapper.insert(busCarryoverinvoEntity);
            }
        }

        //返回Bill实例
        return this.getBillEntity(busCarryoverEntity.getId(), tid);
    }


    @Override
    @Transactional
    public BusCarryoverPojo update(BusCarryoverPojo busCarryoverPojo) {
        String tid = busCarryoverPojo.getTenantid();
        //主表更改
        BusCarryoverEntity busCarryoverEntity = new BusCarryoverEntity();
        BeanUtils.copyProperties(busCarryoverPojo, busCarryoverEntity);
        this.busCarryoverMapper.update(busCarryoverEntity);
        //Item子表处理
        if (busCarryoverPojo.getItem() != null) {
            List<BusCarryoveritemPojo> lst = busCarryoverPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busCarryoverMapper.getDelItemIds(busCarryoverPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String delId : lstDelIds) {
                    this.busCarryoveritemMapper.delete(delId, tid);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (BusCarryoveritemPojo item : lst) {
                    BusCarryoveritemEntity busCarryoveritemEntity = new BusCarryoveritemEntity();
                    if ("".equals(item.getId()) || item.getId() == null) {
                        //初始化item的NULL
                        BusCarryoveritemPojo itemPojo = this.busCarryoveritemService.clearNull(item);
                        BeanUtils.copyProperties(itemPojo, busCarryoveritemEntity);
                        //设置id和Pid
                        busCarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busCarryoveritemEntity.setPid(busCarryoverEntity.getId());  // 主表 id
                        busCarryoveritemEntity.setTenantid(tid);   // 租户id
                        busCarryoveritemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busCarryoveritemMapper.insert(busCarryoveritemEntity);
                    } else {
                        BeanUtils.copyProperties(item, busCarryoveritemEntity);
                        busCarryoveritemEntity.setTenantid(tid);
                        this.busCarryoveritemMapper.update(busCarryoveritemEntity);
                    }
                }
            }
        }

        // invo子表处理
        if (busCarryoverPojo.getInvo() != null) {
            List<BusCarryoverinvoPojo> invoLst = busCarryoverPojo.getInvo();
            //获取被删除的invo
            List<String> lstDelIds = busCarryoverMapper.getDelInvoIds(busCarryoverPojo);
            if (lstDelIds != null) {
                //循环每个删除invo子表
                for (String delId : lstDelIds) {
                    this.busCarryoverinvoMapper.delete(delId, tid);
                }
            }
            if (invoLst != null) {
                //循环每个invo子表
                for (BusCarryoverinvoPojo invo : invoLst) {
                    BusCarryoverinvoEntity busCarryoverinvoEntity = new BusCarryoverinvoEntity();
                    if ("".equals(invo.getId()) || invo.getId() == null) {
                        //初始化invo的NULL
                        BusCarryoverinvoPojo invoPojo = this.busCarryoverinvoService.clearNull(invo);
                        BeanUtils.copyProperties(invoPojo, busCarryoverinvoEntity);
                        //设置id和Pid
                        busCarryoverinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // invo id
                        busCarryoverinvoEntity.setPid(busCarryoverEntity.getId());  // 主表 id
                        busCarryoverinvoEntity.setTenantid(tid);   // 租户id
                        busCarryoverinvoEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busCarryoverinvoMapper.insert(busCarryoverinvoEntity);
                    } else {
                        BeanUtils.copyProperties(invo, busCarryoverinvoEntity);
                        busCarryoverinvoEntity.setTenantid(tid);
                        this.busCarryoverinvoMapper.update(busCarryoverinvoEntity);
                    }
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(busCarryoverEntity.getId(), tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusCarryoverPojo busCarryoverPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusCarryoveritemPojo> lst = busCarryoverPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusCarryoveritemPojo item : lst) {
                this.busCarryoveritemMapper.delete(item.getId(), tid);
            }
        }
        // invo子表处理
        List<BusCarryoverinvoPojo> invoLst = busCarryoverPojo.getInvo();
        if (invoLst != null) {
            //循环每个删除invo子表
            for (BusCarryoverinvoPojo invo : invoLst) {
                this.busCarryoverinvoMapper.delete(invo.getId(), tid);
            }
        }
        return this.busCarryoverMapper.delete(key, tid);
    }


    private static void cleanNull(BusCarryoverPojo busCarryoverPojo) {
        if (busCarryoverPojo.getRefno() == null) busCarryoverPojo.setRefno("");
        if (busCarryoverPojo.getBilltype() == null) busCarryoverPojo.setBilltype("");
        if (busCarryoverPojo.getBilldate() == null) busCarryoverPojo.setBilldate(new Date());
        if (busCarryoverPojo.getBilltitle() == null) busCarryoverPojo.setBilltitle("");
        if (busCarryoverPojo.getGroupid() == null) busCarryoverPojo.setGroupid("");
        if (busCarryoverPojo.getCarryyear() == null) busCarryoverPojo.setCarryyear(0);
        if (busCarryoverPojo.getCarrymonth() == null) busCarryoverPojo.setCarrymonth(0);
        if (busCarryoverPojo.getStartdate() == null) busCarryoverPojo.setStartdate(new Date());
        if (busCarryoverPojo.getEnddate() == null) busCarryoverPojo.setEnddate(new Date());
        if (busCarryoverPojo.getOperator() == null) busCarryoverPojo.setOperator("");
        if (busCarryoverPojo.getOperatorid() == null) busCarryoverPojo.setOperatorid("");
        if (busCarryoverPojo.getRownum() == null) busCarryoverPojo.setRownum(0);
        if (busCarryoverPojo.getSummary() == null) busCarryoverPojo.setSummary("");
        if (busCarryoverPojo.getCreateby() == null) busCarryoverPojo.setCreateby("");
        if (busCarryoverPojo.getCreatebyid() == null) busCarryoverPojo.setCreatebyid("");
        if (busCarryoverPojo.getCreatedate() == null) busCarryoverPojo.setCreatedate(new Date());
        if (busCarryoverPojo.getLister() == null) busCarryoverPojo.setLister("");
        if (busCarryoverPojo.getListerid() == null) busCarryoverPojo.setListerid("");
        if (busCarryoverPojo.getModifydate() == null) busCarryoverPojo.setModifydate(new Date());
        if (busCarryoverPojo.getBillopenamount() == null) busCarryoverPojo.setBillopenamount(0D);
        if (busCarryoverPojo.getBillinamount() == null) busCarryoverPojo.setBillinamount(0D);
        if (busCarryoverPojo.getBilloutamount() == null) busCarryoverPojo.setBilloutamount(0D);
        if (busCarryoverPojo.getBillcloseamount() == null) busCarryoverPojo.setBillcloseamount(0D);
        if (busCarryoverPojo.getInvoopenamount() == null) busCarryoverPojo.setInvoopenamount(0D);
        if (busCarryoverPojo.getInvoinamount() == null) busCarryoverPojo.setInvoinamount(0D);
        if (busCarryoverPojo.getInvooutamount() == null) busCarryoverPojo.setInvooutamount(0D);
        if (busCarryoverPojo.getInvocloseamount() == null) busCarryoverPojo.setInvocloseamount(0D);
        if (busCarryoverPojo.getPrintcount() == null) busCarryoverPojo.setPrintcount(0);
        if (busCarryoverPojo.getCustom1() == null) busCarryoverPojo.setCustom1("");
        if (busCarryoverPojo.getCustom2() == null) busCarryoverPojo.setCustom2("");
        if (busCarryoverPojo.getCustom3() == null) busCarryoverPojo.setCustom3("");
        if (busCarryoverPojo.getCustom4() == null) busCarryoverPojo.setCustom4("");
        if (busCarryoverPojo.getCustom5() == null) busCarryoverPojo.setCustom5("");
        if (busCarryoverPojo.getCustom6() == null) busCarryoverPojo.setCustom6("");
        if (busCarryoverPojo.getCustom7() == null) busCarryoverPojo.setCustom7("");
        if (busCarryoverPojo.getCustom8() == null) busCarryoverPojo.setCustom8("");
        if (busCarryoverPojo.getCustom9() == null) busCarryoverPojo.setCustom9("");
        if (busCarryoverPojo.getCustom10() == null) busCarryoverPojo.setCustom10("");
        if (busCarryoverPojo.getTenantid() == null) busCarryoverPojo.setTenantid("");
        if (busCarryoverPojo.getTenantname() == null) busCarryoverPojo.setTenantname("");
        if (busCarryoverPojo.getRevision() == null) busCarryoverPojo.setRevision(0);
    }

}
