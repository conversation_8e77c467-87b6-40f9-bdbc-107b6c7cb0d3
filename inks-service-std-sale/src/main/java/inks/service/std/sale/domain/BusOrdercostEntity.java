package inks.service.std.sale.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单成本(BusOrdercost)实体类
 *
 * <AUTHOR>
 * @since 2022-10-13 13:14:36
 */
public class BusOrdercostEntity implements Serializable {
    private static final long serialVersionUID = -89627461917423129L;
      // id
      private String id;
      // 单号编码
      private String refno;
      // 单据类型
      private String billtype;
      // 单据标题
      private String billtitle;
      // 单据日期
      private Date billdate;
      // 机会率
      private String probability;
      // 客户ID
      private String groupid;
      // 客户Code
      private String tradercode;
      // 客户Name
      private String tradername;
      // 地址
      private String custaddress;
      // 联系人
      private String linkman;
      // 电话
      private String custtel;
      // 传真
      private String custfax;
      // 项目周期
      private String periods;
      // 有效期
    private String validitydate;
     // 截止日期
    private Date expiredate;
     // 有效期描述
    private String validitydesc;
      // 货币
      private String currency;
      // 交货方式
      private String delivery;
      // 结款方式
      private String payment;
      // 附加条款
      private String billclause;
      // 经办人员
      private String operator;
      // 含税金额
      private Double billtaxamount;
      // 未税金额
      private Double billamount;
      // 税额
      private Double billtaxtotal;
      // 摘要
      private String summary;
      // 创建者
      private String createby;
      // 创建者id
      private String createbyid;
      // 新建日期
      private Date createdate;
      // 制表
      private String lister;
      // 制表id
      private String listerid;
      // 修改日期
      private Date modifydate;
      // 提交人id
      private String submitterid;
      // 提交人
      private String submitter;
      // 审核日期
      private Date submitdate;
      // 审核员
      private String assessor;
      // 审核员id
      private String assessorid;
      // 审核日期
      private Date assessdate;
      // item行数
      private Integer itemcount;
      // 完成行数 转订单+关闭
      private Integer finishcount;
      // 作废行数
      private Integer disannulcount;
      // 打印次数
      private Integer printcount;
      // 业务员
      private String salesman;
      // 业务员id
      private String salesmanid;
      // 自定义1
      private String custom1;
      // 自定义2
      private String custom2;
      // 自定义3
      private String custom3;
      // 自定义4
      private String custom4;
      // 自定义5
      private String custom5;
      // 自定义6
      private String custom6;
      // 自定义7
      private String custom7;
      // 自定义8
      private String custom8;
      // 自定义9
      private String custom9;
      // 自定义10
      private String custom10;
      // 部门id
      private String deptid;
      // 租户id
      private String tenantid;
      // 租户名称
      private String tenantname;
      // 乐观锁
      private Integer revision;

       // id
         public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
       // 单号编码
         public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
       // 单据类型
         public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
       // 单据标题
         public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
       // 单据日期
         public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
       // 机会率
         public String getProbability() {
        return probability;
    }
    
    public void setProbability(String probability) {
        this.probability = probability;
    }
        
       // 客户ID
         public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
        
       // 客户Code
         public String getTradercode() {
        return tradercode;
    }
    
    public void setTradercode(String tradercode) {
        this.tradercode = tradercode;
    }
        
       // 客户Name
         public String getTradername() {
        return tradername;
    }
    
    public void setTradername(String tradername) {
        this.tradername = tradername;
    }
        
       // 地址
         public String getCustaddress() {
        return custaddress;
    }
    
    public void setCustaddress(String custaddress) {
        this.custaddress = custaddress;
    }
        
       // 联系人
         public String getLinkman() {
        return linkman;
    }
    
    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }
        
       // 电话
         public String getCusttel() {
        return custtel;
    }
    
    public void setCusttel(String custtel) {
        this.custtel = custtel;
    }
        
       // 传真
         public String getCustfax() {
        return custfax;
    }
    
    public void setCustfax(String custfax) {
        this.custfax = custfax;
    }
        
       // 项目周期
         public String getPeriods() {
        return periods;
    }
    
    public void setPeriods(String periods) {
        this.periods = periods;
    }
        
       // 有效期
         public String getValiditydate() {
        return validitydate;
    }
    
    public void setValiditydate(String validitydate) {
        this.validitydate = validitydate;
    }
        
   // 截止日期
    public Date getExpiredate() {
        return expiredate;
    }

    public void setExpiredate(Date expiredate) {
        this.expiredate = expiredate;
    }

   // 有效期描述
    public String getValiditydesc() {
        return validitydesc;
    }

    public void setValiditydesc(String validitydesc) {
        this.validitydesc = validitydesc;
    }

       // 货币
         public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
        
       // 交货方式
         public String getDelivery() {
        return delivery;
    }
    
    public void setDelivery(String delivery) {
        this.delivery = delivery;
    }
        
       // 结款方式
         public String getPayment() {
        return payment;
    }
    
    public void setPayment(String payment) {
        this.payment = payment;
    }
        
       // 附加条款
         public String getBillclause() {
        return billclause;
    }
    
    public void setBillclause(String billclause) {
        this.billclause = billclause;
    }
        
       // 经办人员
         public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
        
       // 含税金额
         public Double getBilltaxamount() {
        return billtaxamount;
    }
    
    public void setBilltaxamount(Double billtaxamount) {
        this.billtaxamount = billtaxamount;
    }
        
       // 未税金额
         public Double getBillamount() {
        return billamount;
    }
    
    public void setBillamount(Double billamount) {
        this.billamount = billamount;
    }
        
       // 税额
         public Double getBilltaxtotal() {
        return billtaxtotal;
    }
    
    public void setBilltaxtotal(Double billtaxtotal) {
        this.billtaxtotal = billtaxtotal;
    }
        
       // 摘要
         public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
       // 创建者
         public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
       // 创建者id
         public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
       // 新建日期
         public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
       // 制表
         public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
       // 制表id
         public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
       // 修改日期
         public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
       // 提交人id
         public String getSubmitterid() {
        return submitterid;
    }
    
    public void setSubmitterid(String submitterid) {
        this.submitterid = submitterid;
    }
        
       // 提交人
         public String getSubmitter() {
        return submitter;
    }
    
    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }
        
       // 审核日期
         public Date getSubmitdate() {
        return submitdate;
    }
    
    public void setSubmitdate(Date submitdate) {
        this.submitdate = submitdate;
    }
        
       // 审核员
         public String getAssessor() {
        return assessor;
    }
    
    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
        
       // 审核员id
         public String getAssessorid() {
        return assessorid;
    }
    
    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
        
       // 审核日期
         public Date getAssessdate() {
        return assessdate;
    }
    
    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
        
       // item行数
         public Integer getItemcount() {
        return itemcount;
    }
    
    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }
        
       // 完成行数 转订单+关闭
         public Integer getFinishcount() {
        return finishcount;
    }
    
    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }
        
       // 作废行数
         public Integer getDisannulcount() {
        return disannulcount;
    }
    
    public void setDisannulcount(Integer disannulcount) {
        this.disannulcount = disannulcount;
    }
        
       // 打印次数
         public Integer getPrintcount() {
        return printcount;
    }
    
    public void setPrintcount(Integer printcount) {
        this.printcount = printcount;
    }
        
       // 业务员
         public String getSalesman() {
        return salesman;
    }
    
    public void setSalesman(String salesman) {
        this.salesman = salesman;
    }
        
       // 业务员id
         public String getSalesmanid() {
        return salesmanid;
    }
    
    public void setSalesmanid(String salesmanid) {
        this.salesmanid = salesmanid;
    }
        
       // 自定义1
         public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
       // 自定义2
         public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
       // 自定义3
         public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
       // 自定义4
         public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
       // 自定义5
         public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
       // 自定义6
         public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
       // 自定义7
         public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
       // 自定义8
         public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
       // 自定义9
         public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
       // 自定义10
         public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
       // 部门id
         public String getDeptid() {
        return deptid;
    }
    
    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
        
       // 租户id
         public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
       // 租户名称
         public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
       // 乐观锁
         public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

