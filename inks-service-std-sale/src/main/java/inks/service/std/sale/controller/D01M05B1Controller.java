package inks.service.std.sale.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.log.annotation.OperLog;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusInvoicePojo;
import inks.service.std.sale.domain.pojo.BusInvoiceitemPojo;
import inks.service.std.sale.domain.pojo.BusInvoiceitemdetailPojo;
import inks.service.std.sale.service.BusInvoiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 销售开票(Bus_Invoice)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:30:26
 */
@RestController
@RequestMapping("D01M05B1")
@Api(tags = "D01M05B1:销售开票")
public class D01M05B1Controller extends BusInvoiceController {
    /**
     * 服务对象
     */
    @Resource
    private BusInvoiceService busInvoiceService;


    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待收款发票明细", notes = "按条件分页查询待收款发票明细", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<PageInfo<BusInvoiceitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Bus_Invoice.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_Invoice.DisannulMark=0 and Bus_Invoice.Closed=0 and Bus_Invoice.Receipted<Bus_Invoice.TaxAmount";
            if (groupid != null) {
                qpfilter += " and Bus_Invoice.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvoiceService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待收款发票单含未审核", notes = "按条件分页查询待收款发票单含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<PageInfo<BusInvoicePojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Invoice.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Invoice.DisannulMark=0 and Bus_Invoice.Closed=0 and Bus_Invoice.Receipted<Bus_Invoice.TaxAmount";
            if (groupid != null) {
                qpfilter += " and Bus_Invoice.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvoiceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待收款发票单含未审核", notes = "按条件分页查询待收款发票单含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDocPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<PageInfo<BusInvoicePojo>> getOnlineDocPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Invoice.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Invoice.DisannulMark=0 ";
            qpfilter += " and Bus_Invoice.FmDocMark !=1";
            if (groupid != null) {
                qpfilter += " and Bus_Invoice.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvoiceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询待收款发票单", notes = "按条件分页查询待收款发票单?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineRecePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<PageInfo<BusInvoicePojo>> getOnlineRecePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Invoice.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Invoice.DisannulMark=0 and Bus_Invoice.Closed=0 and Bus_Invoice.Receipted<Bus_Invoice.TaxAmount";
            qpfilter += " and Bus_Invoice.Assessor<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Invoice.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvoiceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "拉取客户所有项目", notes = "拉取客户所有项目?groupid,json为标准分页时间", produces = "application/json")
    @RequestMapping(value = "/pullItem", method = RequestMethod.POST)
    public R<List<BusInvoiceitemPojo>> pullItem(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = new QueryParam();
            if (json != null) queryParam = JSONArray.parseObject(json, QueryParam.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            if (groupid == null) {
                return R.fail("请加入groupid");
            }
            return R.ok(this.busInvoiceService.pullItem(queryParam, groupid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除销售开票", notes = "删除销售开票", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Invoice.Delete")
    @OperLog(title = "删除销售开票")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<String> lstcite = this.busInvoiceService.getCiteBillName(key, loginUser.getTenantid());
            if (!lstcite.isEmpty()) {
                return R.fail(403, "禁止删除,被以下单据引用:" + lstcite);
            }
            String refno = this.busInvoiceService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo("D01M05B1", loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "（返回进度条RedisKey）新增销售开票", notes = "新增销售开票", produces = "application/json")
    @RequestMapping(value = "/createStart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.Add")
    public R<String> createStart(@RequestBody String json) {
        try {
            BusInvoicePojo busInvoicePojo = JSONArray.parseObject(json, BusInvoicePojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            R r = systemFeignService.getBillCode("D01M05B1", loginUser.getToken());
            if (r.getCode() == 200)
                busInvoicePojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            busInvoicePojo.setCreateby(loginUser.getRealname());   // 创建者
            busInvoicePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busInvoicePojo.setCreatedate(new Date());   // 创建时间
            busInvoicePojo.setLister(loginUser.getRealname());   // 制表
            busInvoicePojo.setListerid(loginUser.getUserid());    // 制表id
            busInvoicePojo.setModifydate(new Date());   //修改时间
            busInvoicePojo.setTenantid(loginUser.getTenantid());   //租户id
            // 读取指定系统参数module.sale.invoamttomach，是否同步销售订单   转换为布尔值，只有值等于 "false" 时返回 false，其余情况均默认为 true
            String configValue_MachSync = systemFeignService.getConfigValue("module.sale.invoamttomach", loginUser.getTenantid(), loginUser.getToken()).getData();
            boolean isMachSyncAllowed = !"false".equalsIgnoreCase(configValue_MachSync);
            return R.ok(this.busInvoiceService.insertStart(busInvoicePojo, isMachSyncAllowed));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "（返回进度条RedisKey）修改销售开票", notes = "修改销售开票", produces = "application/json")
    @RequestMapping(value = "/updateStart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.Edit")
    public R<String> updateStart(@RequestBody String json) {
        try {
            BusInvoicePojo busInvoicePojo = JSONArray.parseObject(json, BusInvoicePojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            busInvoicePojo.setLister(loginUser.getRealname());   // 制表
            busInvoicePojo.setListerid(loginUser.getUserid());    // 制表id
            busInvoicePojo.setModifydate(new Date());   //修改时间
            busInvoicePojo.setAssessor(""); //审核员
            busInvoicePojo.setAssessorid(""); //审核员
            busInvoicePojo.setAssessdate(new Date()); //审核时间
            busInvoicePojo.setTenantid(loginUser.getTenantid());   //租户id
            // 读取指定系统参数module.sale.invoamttomach，是否同步销售订单   转换为布尔值，只有值等于 "false" 时返回 false，其余情况均默认为 true
            String configValue_MachSync = systemFeignService.getConfigValue("module.sale.invoamttomach", loginUser.getTenantid(), loginUser.getToken()).getData();
            boolean isMachSyncAllowed = !"false".equalsIgnoreCase(configValue_MachSync);
            return R.ok(this.busInvoiceService.updateStart(busInvoicePojo, isMachSyncAllowed));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除销售开票", notes = "删除销售开票", produces = "application/json")
    @RequestMapping(value = "/deleteStart", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Invoice.Delete")
    @OperLog(title = "删除销售开票")
    public R<Integer> deleteStart(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 读取指定系统参数module.sale.invoamttomach，是否同步销售订单   转换为布尔值，只有值等于 "false" 时返回 false，其余情况均默认为 true
            String configValue_MachSync = systemFeignService.getConfigValue("module.sale.invoamttomach", loginUser.getTenantid(), loginUser.getToken()).getData();
            boolean isMachSyncAllowed = !"false".equalsIgnoreCase(configValue_MachSync);
            String refno = this.busInvoiceService.deleteStart(key, loginUser.getTenantid(), loginUser.getUserid(), isMachSyncAllowed);
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
