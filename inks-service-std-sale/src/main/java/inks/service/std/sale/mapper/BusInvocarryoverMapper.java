package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCarryoverinvoPojo;
import inks.service.std.sale.domain.pojo.BusInvocarryoverPojo;
import inks.service.std.sale.domain.BusInvocarryoverEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 货品账单:发货>发票(Bus_InvoCarryover)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-10 11:30:19
 */
@Mapper
public interface BusInvocarryoverMapper {

    BusInvocarryoverPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusInvocarryoverPojo> getPageList(QueryParam queryParam);

    int insert(BusInvocarryoverEntity busInvocarryoverEntity);

    int update(BusInvocarryoverEntity busInvocarryoverEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<BusInvocarryoverPojo> getListByMonth(String recid, String goodsid,String tid);

    int batchInsert(List<BusInvocarryoverPojo> busInvocarryoverPojos);

    BusInvocarryoverPojo getEntityByRecidAndGoodsid(String recidNew, String goodsid, String tid);

    List<BusInvocarryoverPojo> getListByids( List<String> ids,String tid);
}

