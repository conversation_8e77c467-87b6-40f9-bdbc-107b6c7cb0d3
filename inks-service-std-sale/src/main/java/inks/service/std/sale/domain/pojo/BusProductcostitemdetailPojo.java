package inks.service.std.sale.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 成本项目(BusProductcostitem)Pojo
 *
 * <AUTHOR>
 * @since 2021-11-20 13:53:08
 */
public class BusProductcostitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -92919793719602445L;
         // ID
       @Excel(name = "ID")    
  private String id;
         // Pid
       @Excel(name = "Pid")    
  private String pid;
         // 商品ID
       @Excel(name = "商品ID")    
  private String goodsid;
         // 数量
       @Excel(name = "数量")    
  private Double quantity;
         // 标准含税价
       @Excel(name = "标准含税价")    
  private Double stdtaxprice;
         // 标准含税额
       @Excel(name = "标准含税额")    
  private Double stdtaxamount;
         // 折扣
       @Excel(name = "折扣")    
  private Double rebate;
         // 二级折扣
       @Excel(name = "二级折扣")    
  private Double rebatesec;
         // 含税单价
       @Excel(name = "含税单价")    
  private Double taxprice;
         // 含税金额
       @Excel(name = "含税金额")    
  private Double taxamount;
         // 税额
       @Excel(name = "税额")    
  private Double taxtotal;
         // 税率(备用)
       @Excel(name = "税率(备用)")    
  private Integer itemtaxrate;
         // 未税单价
       @Excel(name = "未税单价")    
  private Double price;
         // 未税金额
       @Excel(name = "未税金额")    
  private Double amount;
         // 计划交期
       @Excel(name = "计划交期")    
  private Date plandate;
         // 行号
       @Excel(name = "行号")    
  private Integer rownum;
         // 备注
       @Excel(name = "备注")    
  private String remark;
         // 有效
       @Excel(name = "有效")    
  private Integer enabledmark;
         // 作废
       @Excel(name = "作废")    
  private Integer disannulmark;
         // 虚拟货品
       @Excel(name = "虚拟货品")    
  private Integer virtualitem;
         // 明细
       @Excel(name = "明细")    
  private String costdetail;
         // 项目1
       @Excel(name = "项目1")    
  private String costitem1;
         // 项目2
       @Excel(name = "项目2")    
  private String costitem2;
         // 项目3
       @Excel(name = "项目3")    
  private String costitem3;
         // 项目4
       @Excel(name = "项目4")    
  private String costitem4;
         // 项目5
       @Excel(name = "项目5")    
  private String costitem5;
         // 项目6
       @Excel(name = "项目6")    
  private String costitem6;
         // 项目7
       @Excel(name = "项目7")    
  private String costitem7;
         // 项目8
       @Excel(name = "项目8")    
  private String costitem8;
         // 项目9
       @Excel(name = "项目9")    
  private String costitem9;
         // 项目10
       @Excel(name = "项目10")    
  private String costitem10;
         // 项目11
       @Excel(name = "项目11")    
  private String costitem11;
         // 项目12
       @Excel(name = "项目12")    
  private String costitem12;
         // 项目13
       @Excel(name = "项目13")    
  private String costitem13;
         // 项目14
       @Excel(name = "项目14")    
  private String costitem14;
         // 项目15
       @Excel(name = "项目15")    
  private String costitem15;
         // 项目16
       @Excel(name = "项目16")    
  private String costitem16;
         // 项目17
       @Excel(name = "项目17")    
  private String costitem17;
         // 项目18
       @Excel(name = "项目18")    
  private String costitem18;
         // 项目19
       @Excel(name = "项目19")    
  private String costitem19;
         // 项目20
       @Excel(name = "项目20")    
  private String costitem20;
         // 项目21
       @Excel(name = "项目21")    
  private String costitem21;
         // 项目22
       @Excel(name = "项目22")    
  private String costitem22;
         // 项目23
       @Excel(name = "项目23")    
  private String costitem23;
         // 项目24
       @Excel(name = "项目24")    
  private String costitem24;
         // 项目25
       @Excel(name = "项目25")    
  private String costitem25;
         // 项目26
       @Excel(name = "项目26")    
  private String costitem26;
         // 项目27
       @Excel(name = "项目27")    
  private String costitem27;
         // 项目28
       @Excel(name = "项目28")    
  private String costitem28;
         // 项目29
       @Excel(name = "项目29")    
  private String costitem29;
         // 项目30
       @Excel(name = "项目30")    
  private String costitem30;
         // 租户id
       @Excel(name = "租户id")    
  private String tenantid;
         // 乐观锁
       @Excel(name = "乐观锁")    
  private Integer revision;

     // ID
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // Pid
       public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
     // 商品ID
       public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
     // 数量
       public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
     // 标准含税价
       public Double getStdtaxprice() {
        return stdtaxprice;
    }
    
    public void setStdtaxprice(Double stdtaxprice) {
        this.stdtaxprice = stdtaxprice;
    }
        
     // 标准含税额
       public Double getStdtaxamount() {
        return stdtaxamount;
    }
    
    public void setStdtaxamount(Double stdtaxamount) {
        this.stdtaxamount = stdtaxamount;
    }
        
     // 折扣
       public Double getRebate() {
        return rebate;
    }
    
    public void setRebate(Double rebate) {
        this.rebate = rebate;
    }
        
     // 二级折扣
       public Double getRebatesec() {
        return rebatesec;
    }
    
    public void setRebatesec(Double rebatesec) {
        this.rebatesec = rebatesec;
    }
        
     // 含税单价
       public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
     // 含税金额
       public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
     // 税额
       public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
     // 税率(备用)
       public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
     // 未税单价
       public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
     // 未税金额
       public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
     // 计划交期
       public Date getPlandate() {
        return plandate;
    }
    
    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
        
     // 行号
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 有效
       public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
     // 作废
       public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
     // 虚拟货品
       public Integer getVirtualitem() {
        return virtualitem;
    }
    
    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }
        
     // 明细
       public String getCostdetail() {
        return costdetail;
    }
    
    public void setCostdetail(String costdetail) {
        this.costdetail = costdetail;
    }
        
     // 项目1
       public String getCostitem1() {
        return costitem1;
    }
    
    public void setCostitem1(String costitem1) {
        this.costitem1 = costitem1;
    }
        
     // 项目2
       public String getCostitem2() {
        return costitem2;
    }
    
    public void setCostitem2(String costitem2) {
        this.costitem2 = costitem2;
    }
        
     // 项目3
       public String getCostitem3() {
        return costitem3;
    }
    
    public void setCostitem3(String costitem3) {
        this.costitem3 = costitem3;
    }
        
     // 项目4
       public String getCostitem4() {
        return costitem4;
    }
    
    public void setCostitem4(String costitem4) {
        this.costitem4 = costitem4;
    }
        
     // 项目5
       public String getCostitem5() {
        return costitem5;
    }
    
    public void setCostitem5(String costitem5) {
        this.costitem5 = costitem5;
    }
        
     // 项目6
       public String getCostitem6() {
        return costitem6;
    }
    
    public void setCostitem6(String costitem6) {
        this.costitem6 = costitem6;
    }
        
     // 项目7
       public String getCostitem7() {
        return costitem7;
    }
    
    public void setCostitem7(String costitem7) {
        this.costitem7 = costitem7;
    }
        
     // 项目8
       public String getCostitem8() {
        return costitem8;
    }
    
    public void setCostitem8(String costitem8) {
        this.costitem8 = costitem8;
    }
        
     // 项目9
       public String getCostitem9() {
        return costitem9;
    }
    
    public void setCostitem9(String costitem9) {
        this.costitem9 = costitem9;
    }
        
     // 项目10
       public String getCostitem10() {
        return costitem10;
    }
    
    public void setCostitem10(String costitem10) {
        this.costitem10 = costitem10;
    }
        
     // 项目11
       public String getCostitem11() {
        return costitem11;
    }
    
    public void setCostitem11(String costitem11) {
        this.costitem11 = costitem11;
    }
        
     // 项目12
       public String getCostitem12() {
        return costitem12;
    }
    
    public void setCostitem12(String costitem12) {
        this.costitem12 = costitem12;
    }
        
     // 项目13
       public String getCostitem13() {
        return costitem13;
    }
    
    public void setCostitem13(String costitem13) {
        this.costitem13 = costitem13;
    }
        
     // 项目14
       public String getCostitem14() {
        return costitem14;
    }
    
    public void setCostitem14(String costitem14) {
        this.costitem14 = costitem14;
    }
        
     // 项目15
       public String getCostitem15() {
        return costitem15;
    }
    
    public void setCostitem15(String costitem15) {
        this.costitem15 = costitem15;
    }
        
     // 项目16
       public String getCostitem16() {
        return costitem16;
    }
    
    public void setCostitem16(String costitem16) {
        this.costitem16 = costitem16;
    }
        
     // 项目17
       public String getCostitem17() {
        return costitem17;
    }
    
    public void setCostitem17(String costitem17) {
        this.costitem17 = costitem17;
    }
        
     // 项目18
       public String getCostitem18() {
        return costitem18;
    }
    
    public void setCostitem18(String costitem18) {
        this.costitem18 = costitem18;
    }
        
     // 项目19
       public String getCostitem19() {
        return costitem19;
    }
    
    public void setCostitem19(String costitem19) {
        this.costitem19 = costitem19;
    }
        
     // 项目20
       public String getCostitem20() {
        return costitem20;
    }
    
    public void setCostitem20(String costitem20) {
        this.costitem20 = costitem20;
    }
        
     // 项目21
       public String getCostitem21() {
        return costitem21;
    }
    
    public void setCostitem21(String costitem21) {
        this.costitem21 = costitem21;
    }
        
     // 项目22
       public String getCostitem22() {
        return costitem22;
    }
    
    public void setCostitem22(String costitem22) {
        this.costitem22 = costitem22;
    }
        
     // 项目23
       public String getCostitem23() {
        return costitem23;
    }
    
    public void setCostitem23(String costitem23) {
        this.costitem23 = costitem23;
    }
        
     // 项目24
       public String getCostitem24() {
        return costitem24;
    }
    
    public void setCostitem24(String costitem24) {
        this.costitem24 = costitem24;
    }
        
     // 项目25
       public String getCostitem25() {
        return costitem25;
    }
    
    public void setCostitem25(String costitem25) {
        this.costitem25 = costitem25;
    }
        
     // 项目26
       public String getCostitem26() {
        return costitem26;
    }
    
    public void setCostitem26(String costitem26) {
        this.costitem26 = costitem26;
    }
        
     // 项目27
       public String getCostitem27() {
        return costitem27;
    }
    
    public void setCostitem27(String costitem27) {
        this.costitem27 = costitem27;
    }
        
     // 项目28
       public String getCostitem28() {
        return costitem28;
    }
    
    public void setCostitem28(String costitem28) {
        this.costitem28 = costitem28;
    }
        
     // 项目29
       public String getCostitem29() {
        return costitem29;
    }
    
    public void setCostitem29(String costitem29) {
        this.costitem29 = costitem29;
    }
        
     // 项目30
       public String getCostitem30() {
        return costitem30;
    }
    
    public void setCostitem30(String costitem30) {
        this.costitem30 = costitem30;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

