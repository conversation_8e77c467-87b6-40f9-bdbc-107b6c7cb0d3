package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusCouponusagePojo;
import inks.service.std.sale.domain.BusCouponusageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 优惠券使用记录(Bus_CouponUsage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-20 12:57:24
 */
@Mapper
public interface BusCouponusageMapper {

    BusCouponusagePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<BusCouponusagePojo> getPageList(QueryParam queryParam);

    int insert(BusCouponusageEntity busCouponusageEntity);

    int update(BusCouponusageEntity busCouponusageEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<String> getItemCiteBillName(String couponid, String tid);

    void syncCouponUsedAmount(String couponid, String tid);

    void deleteByCouponidAndCiteid(String couponid, String citeid, String tid);
}

