package inks.service.std.sale.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.exception.WarnException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.StreamUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.config.InksConfigThreadLocal;
import inks.service.std.sale.domain.BusDelieryEntity;
import inks.service.std.sale.domain.BusDelieryitemEntity;
import inks.service.std.sale.domain.pojo.*;
import inks.service.std.sale.mapper.*;
import inks.service.std.sale.service.BusDelieryService;
import inks.service.std.sale.service.BusDelieryitemService;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;
import static org.apache.commons.lang3.StringUtils.*;

/**
 * 发出商品(BusDeliery)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 15:16:12
 */
@Service("busDelieryService")
public class BusDelieryServiceImpl implements BusDelieryService {
    @Resource
    private BusDelieryMapper busDelieryMapper;

    @Resource
    private BusDelieryitemMapper busDelieryitemMapper;

    @Resource
    private BusMachiningitemMapper busMachiningitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private BusDelieryitemService busDelieryitemService;

    @Resource
    private BusAccountrecMapper busAccountrecMapper;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;


    @Resource
    private sale_SyncMapper saleSyncMapper;
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDelieryPojo getEntity(String key, String tid) {
        return this.busDelieryMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryitemdetailPojo> lst = busDelieryMapper.getPageList(queryParam);
            PageInfo<BusDelieryitemdetailPojo> pageInfo = new PageInfo<BusDelieryitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDelieryPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusDelieryPojo busDelieryPojo = this.busDelieryMapper.getEntity(key, tid);
            //读取子表
            busDelieryPojo.setItem(busDelieryitemMapper.getList(busDelieryPojo.getId(), busDelieryPojo.getTenantid()));
            return busDelieryPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryPojo> lst = busDelieryMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busDelieryitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusDelieryPojo> pageInfo = new PageInfo<BusDelieryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryPojo> lst = busDelieryMapper.getPageTh(queryParam);
            PageInfo<BusDelieryPojo> pageInfo = new PageInfo<BusDelieryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busDelieryPojo 实例对象
     * @return 实例对象
     */
    @Override
    // @Transactional
    public BusDelieryPojo insert(BusDelieryPojo busDelieryPojo, Integer warn) {
//初始化NULL字段
        cleanNull(busDelieryPojo);
        String billType = busDelieryPojo.getBilltype();
        Date billdate = busDelieryPojo.getBilldate();
        String tid = busDelieryPojo.getTenantid();
        // 结账检查
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(billdate) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止新建结账前单据");
        }

        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
        BeanUtils.copyProperties(busDelieryPojo, busDelieryEntity);
        //设置id和新建日期
        busDelieryEntity.setId(id);
        busDelieryEntity.setRevision(1);  //乐观锁
        List<BusDelieryitemPojo> lst = busDelieryPojo.getItem();
        // 获取attributestr公式
        String attrstrExpr = InksConfigThreadLocal.getConfig("system.bill.attributestr");
        //  库存提醒 na不控制 reject禁止 warning警告422
        String delichkinveqty = InksConfigThreadLocal.getConfig("module.sale.delichkinveqty");
        boolean isReject = "reject".equals(delichkinveqty);
        boolean isWarning = "warning".equals(delichkinveqty);
        // 数量检查
        if ("发出商品".equals(billType) && busDelieryPojo.getItem() != null) {
            //module.sale.delichkmachqty warning 警告* reject 禁止 na 为不控制
            String delichkmachqty = InksConfigThreadLocal.getConfig("module.sale.delichkmachqty");
            boolean nacheckmach = "na".equals(delichkmachqty);
            int num = 0;
            List<BusDelieryitemPojo> warnMsgList = new ArrayList<>();
            for (BusDelieryitemPojo item : lst) {
                num++;
                // 超数检查 非na时检查
                if (!nacheckmach && !"".equals(item.getCiteitemid()) && !"".equals(item.getCiteuid())) {
                    BusMachiningitemPojo machiningitemPojo = this.busMachiningitemMapper.getEntity(item.getCiteitemid(), tid);
                    if (machiningitemPojo != null) {
                        BigDecimal finishQty = BigDecimal.valueOf(machiningitemPojo.getFinishqty());
                        BigDecimal quantity = BigDecimal.valueOf(item.getQuantity());
                        BigDecimal totalQty = finishQty.add(quantity);

                        // 默认对比MachiningItem.Quantity, 如果有最大发货数MaxQty, 就对比最大发货数
                        double qtyFinal = machiningitemPojo.getQuantity();
                        if (machiningitemPojo.getMaxqty() != 0) {
                            qtyFinal = machiningitemPojo.getMaxqty();
                        }

                        if (totalQty.compareTo(BigDecimal.valueOf(qtyFinal)) > 0) {
                            throw new RuntimeException(num + "行,发货总数:" + totalQty + "超出订单数:" + qtyFinal);
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + item.getCiteuid());
                    }
                }

                // warn=0 跳过警告
                if (warn == 1 && (isReject || isWarning)) {
                    // —— 计算库存invQuantity ——
                    String goodsid = item.getGoodsid();
                    Map<String, Object> info = busDelieryMapper.getGoodsInfo(goodsid, tid);
                    Integer skuMark = (Integer) info.get("SkuMark");
                    Integer virtualItem = (Integer) info.get("VirtualItem");
                    if (!Objects.equals(virtualItem, 1)) {
                        double ivQuantity;
                        if (Objects.equals(skuMark, 1)) {
                            String attrJson = item.getAttributejson();
                            if (isBlank(attrJson)) {
                                throw new RuntimeException("第" + num + "行:" + item.getGoodsuid() + ":缺少SKU属性信息");
                            }
                            String newAttrSku = getAttrSku(attrJson, tid);
                            String skuid = getEntityByAtte(goodsid, newAttrSku, tid);
                            // skuid=null ivQuantity会返回0
                            ivQuantity = busDelieryMapper.getSumInventoryQuantity(goodsid, skuid, tid);
                        } else {
                            Object ivObj = info.get("IvQuantity");
                            ivQuantity = ivObj != null ? ((Number) ivObj).doubleValue() : 0.0;
                        }

                        // —— 库存对比 ——
                        if (ivQuantity < item.getQuantity()) {
                            if (isReject) {
                                throw new RuntimeException(num + "行,库存数量:" + ivQuantity + "不足,请检查库存!");
                            }
                            // warning：收集完整异常库存信息，循环完统一抛出
                            item.setIvquantity(ivQuantity);
                            warnMsgList.add(item);
                        }
                    }
                }

            }
            // —— 循环结束，若为 warning 且存在不足记录，则一次性抛出 ——
            if (CollectionUtils.isNotEmpty(warnMsgList)) {
                throw new WarnException("库存不足列表", warnMsgList);
            }
        }

        transactionTemplate.execute((status) -> {

            if (lst != null) {
                //循环每个item子表
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    //初始化item的NULL
                    BusDelieryitemPojo itemPojo = this.busDelieryitemService.clearNull(busDelieryitemPojo);
                    BusDelieryitemEntity busDelieryitemEntity = new BusDelieryitemEntity();
                    BeanUtils.copyProperties(itemPojo, busDelieryitemEntity);
                    //设置id和Pid
                    busDelieryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    busDelieryitemEntity.setPid(id);
                    busDelieryitemEntity.setTenantid(tid);
                    busDelieryitemEntity.setRevision(1);  //乐观锁
                    // 计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
                    if (isNotBlank(attrstrExpr) && isNotBlank(busDelieryitemEntity.getAttributejson())) {
                        busDelieryitemEntity.setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(busDelieryitemEntity.getAttributejson(), attrstrExpr));
                    }
                    //插入子表
                    this.busDelieryitemMapper.insert(busDelieryitemEntity);
                }
            }
            //插入主表
            this.busDelieryMapper.insert(busDelieryEntity);

            if (lst != null) {
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    if ("发出商品".equals(billType) || "订单退货".equals(billType)) { // 更新订单已发货
                        if (!"".equals(busDelieryitemPojo.getCiteitemid()) && !"".equals(busDelieryitemPojo.getCiteuid())) {
                            this.busDelieryMapper.updateMachItemFinishQty(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                            this.busDelieryMapper.updateMachFinishCount(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                            // 同步销售订单主表的最终成交金额FinishAmount FinishTaxAmount
                            this.busDelieryMapper.updateMachFinishAmount(busDelieryitemPojo.getCiteitemid(), tid);
                        }
                        // 同步发货计划子表的FinishQty
                        String deliplanitemid = busDelieryitemPojo.getDeliplanitemid();
                        if (isBlank(deliplanitemid)) {
                            // 读取系统参数：检查发货计划的的计划时间不能晚于发货单BillDate
                            String pairdeliplan = InksConfigThreadLocal.getConfig("module.sale.autopairingdeliplan");
                            if ("true".equals(pairdeliplan)) {
                                String machitemid = busDelieryitemPojo.getMachitemid();
                                this.busDelieryMapper.syncDeliPlanItemFinishQtyByPlan(machitemid, billdate, tid);
                                this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                            }
                        } else {
                            this.busDelieryMapper.syncDeliPlanItemFinishQty(deliplanitemid, tid);
                            this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                        }
                    } else if ("返工补发".equals(billType)) { //更新"退货返工"的ReturnQty
                        if (!"".equals(busDelieryitemPojo.getCiteitemid())) {
                            this.busDelieryMapper.updateDeliItemReturnQty(busDelieryitemPojo.getCiteitemid(), tid);
                            this.busDelieryMapper.updateDeliReturnCount(busDelieryitemPojo.getCiteitemid(), tid);
                        }
                    }
                }
            }

            // 更新完工款数
            this.busDelieryMapper.updateFinishCount(busDelieryEntity.getId(), busDelieryEntity.getTenantid());


            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });


        //返回Bill实例
        return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());

    }

    private String getAttrSku(String json, String tid) {
        List<Map<String, Object>> listObjectSec = JSONArray.parseObject(json, List.class);
        //将listObjectSec转换为k::v
        Map<String, Object> collect = listObjectSec.stream()
                .collect(Collectors.toMap(
                        map -> String.valueOf(map.get("key")),
                        map -> map.get("value"),
                        (existing, replacement) -> {
                            throw new RuntimeException("AttributeJson有重复键");
                        }
                ));

        // SELECT id, AttrKey, AttrName, SkuMark FROM Mat_Attribute
        List<Map<String, Object>> attrList = this.busDelieryMapper.getAttrList(tid);

        attrList.forEach(attr -> {
            if ("0".equals(String.valueOf(attr.get("SkuMark"))) && collect.containsKey(attr.get("AttrKey").toString())) {
                collect.remove(attr.get("AttrKey").toString());
            }
        });

        listObjectSec = collect.entrySet().stream().map(item -> {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("key", item.getKey());
            map.put("value", item.getValue());
            return map;
        }).collect(Collectors.toList());

        return JSONArray.toJSONString(listObjectSec);
    }

    // 返回skuid
    public String getEntityByAtte(String goodsid, String json, String tid) {
        try {
            List<Map<String, Object>> listObjectSec = JSONArray.parseObject(json, List.class);

            // SELECT Mat_Sku.id, Mat_Sku.SkuCode, Mat_Sku.Goodsid, Mat_Sku.AttributeJson
            List<Map<String, String>> lst = this.busDelieryMapper.getListByGoodsAttr(goodsid, listObjectSec, tid);
            if (!lst.isEmpty()) {
                String skuid = null;
                if (lst.get(0).get("AttributeJson").length() == json.length()) skuid = lst.get(0).get("Skuid");
                if (lst.size() > 1) {
                    for (Map<String, String> skuPojo : lst) {
                        if (skuPojo.get("AttributeJson").length() == json.length()) {
                            skuid = skuPojo.get("Skuid");
                            break;
                        }
                    }
                }
                return skuid;
            } else {
                return null;
            }

        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 修改数据
     *
     * @param busDelieryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDelieryPojo update(BusDelieryPojo busDelieryPojo, Integer warn) {
        String tid = busDelieryPojo.getTenantid();
        String billType = busDelieryPojo.getBilltype();
        Date billdate = busDelieryPojo.getBilldate();
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(billdate) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止修改结账前单据");
        }
        BusDelieryPojo orgPojo = this.busDelieryMapper.getEntity(busDelieryPojo.getId(), tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止修改结账前单据");
        }
        //主表更改
        BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
        BeanUtils.copyProperties(busDelieryPojo, busDelieryEntity);
        busDelieryEntity.setItemcount(busDelieryPojo.getItem().size());
        // 获取attributestr公式
        Map<String, Object> tencfg = this.redisService.getCacheObject("tenant_config:" + tid);
        String attrstrExpr;
        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
            attrstrExpr = tencfg.get("system.bill.attributestr").toString();
        } else {
            attrstrExpr = null;
        }
        //  库存提醒 na不控制 reject禁止 warning警告422
        String delichkinveqty = InksConfigThreadLocal.getConfig("module.sale.delichkinveqty");
        boolean isReject = "reject".equals(delichkinveqty);
        boolean isWarning = "warning".equals(delichkinveqty);
        // 数量检查
        if (billType.equals("发出商品") && busDelieryPojo.getItem() != null) {
            //module.sale.delichkmachqty warning 警告* reject 禁止 na 为不控制
            String delichkmachqty = InksConfigThreadLocal.getConfig("module.sale.delichkmachqty");
            boolean nacheckmach = "na".equals(delichkmachqty);
            List<BusDelieryitemPojo> lst = busDelieryPojo.getItem();
            int num = 0;
            // warning：收集完整异常库存信息，循环完统一抛出
            List<BusDelieryitemPojo> warnMsgList = new ArrayList<>();
            for (BusDelieryitemPojo item : lst) {
                num++;
                BigDecimal orgQty = BigDecimal.ZERO;
                if (item.getId() != null && !"".equals(item.getId())) {
                    BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(item.getId(), busDelieryEntity.getTenantid());
                    if (busDelieryitemPojo != null) {
                        orgQty = BigDecimal.valueOf(busDelieryitemPojo.getQuantity());
                    }
                }

                // 超数检查 非na时检查
                if (!nacheckmach && !"".equals(item.getCiteitemid()) && !"".equals(item.getCiteuid())) {
                    BusMachiningitemPojo machiningitemPojo = this.busMachiningitemMapper.getEntity(item.getCiteitemid(), tid);
                    if (machiningitemPojo != null) {
                        BigDecimal finishQty = BigDecimal.valueOf(machiningitemPojo.getFinishqty());
                        BigDecimal quantity = BigDecimal.valueOf(item.getQuantity());
                        BigDecimal totalQty = finishQty.subtract(orgQty).add(quantity);
                        Double qtyFinal = machiningitemPojo.getQuantity();
                        if (machiningitemPojo.getMaxqty() != 0) {
                            qtyFinal = machiningitemPojo.getMaxqty();
                        }

                        if (totalQty.compareTo(BigDecimal.valueOf(qtyFinal)) > 0) {
                            throw new RuntimeException(num + "行,发货总数:" + totalQty + "超出订单数:" + qtyFinal);
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + item.getCiteuid());
                    }
                }

                // warn=0 跳过警告
                if (warn == 1 && (isReject || isWarning)) {
                    // —— 计算库存invQuantity ——
                    String goodsid = item.getGoodsid();
                    Map<String, Object> info = busDelieryMapper.getGoodsInfo(goodsid, tid);
                    Integer skuMark = (Integer) info.get("SkuMark");
                    Integer virtualItem = (Integer) info.get("VirtualItem");
                    if (!Objects.equals(virtualItem, 1)) {
                        double ivQuantity;
                        if (Objects.equals(skuMark, 1)) {
                            String attrJson = item.getAttributejson();
                            if (isBlank(attrJson)) {
                                throw new RuntimeException("第" + num + "行:" + item.getGoodsuid() + ":缺少SKU属性信息");
                            }
                            String newAttrSku = getAttrSku(attrJson, tid);
                            String skuid = getEntityByAtte(goodsid, newAttrSku, tid);
                            // skuid=null ivQuantity会返回0
                            ivQuantity = busDelieryMapper.getSumInventoryQuantity(goodsid, skuid, tid);
                        } else {
                            Object ivObj = info.get("IvQuantity");
                            ivQuantity = ivObj != null ? ((Number) ivObj).doubleValue() : 0.0;
                        }

                        // —— 库存对比 ——（注意统一使用 BigDecimal 运算）
                        if (ivQuantity < item.getQuantity()) {
                            if (isReject) {
                                throw new RuntimeException(num + "行,库存数量:" + ivQuantity + "不足,请检查库存!");
                            }
                            item.setIvquantity(ivQuantity); // 原值为 double，保留即可
                            warnMsgList.add(item);
                        }
                    }
                }
            }
            // 循环结束，若为 warning 且存在不足记录，则一次性抛出异常货品列表
            if (CollectionUtils.isNotEmpty(warnMsgList)) {
                throw new WarnException("库存不足列表", warnMsgList);
            }

        }

        //Item子表处理
        List<BusDelieryitemPojo> lst = busDelieryPojo.getItem();
        if (lst == null) throw new

                RuntimeException("单据内容不能为空");

        //获取被删除的Item
        List<BusDelieryitemPojo> lstDel = new ArrayList<>();
        transactionTemplate.execute((status) ->

        {
            if (busDelieryPojo.getItem() != null) {
                List<String> lstDelIds = busDelieryMapper.getDelItemIds(busDelieryPojo);
                if (lstDelIds != null) {
                    //循环每个删除item子表
                    for (String lstDelId : lstDelIds) {
                        //读取待删除项目
                        BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(lstDelId, busDelieryEntity.getTenantid());
                        lstDel.add(busDelieryitemPojo);
                        List<String> lstcite = getItemCiteBillName(lstDelId, busDelieryitemPojo.getPid(), busDelieryEntity.getTenantid());
                        if (!lstcite.isEmpty()) {
                            throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                        }
                        //删除项目
                        this.busDelieryitemMapper.delete(lstDelId, busDelieryEntity.getTenantid());
                        //更新订单已发货 Eric20211213
                        this.busDelieryMapper.updateMachItemFinishQty(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                    }
                }
                //循环每个item子表
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    BusDelieryitemEntity busDelieryitemEntity = new BusDelieryitemEntity();
                    // 先计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
                    if (isNotBlank(attrstrExpr) && isNotBlank(busDelieryitemEntity.getAttributejson())) {
                        busDelieryitemPojo.setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(busDelieryitemPojo.getAttributejson(), attrstrExpr));
                    }
                    if ("".equals(busDelieryitemPojo.getId()) || busDelieryitemPojo.getId() == null) {
                        //初始化item的NULL
                        BusDelieryitemPojo itemPojo = this.busDelieryitemService.clearNull(busDelieryitemPojo);
                        BeanUtils.copyProperties(itemPojo, busDelieryitemEntity);
                        //设置id和Pid
                        busDelieryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busDelieryitemEntity.setPid(busDelieryEntity.getId());  // 主表 id
                        busDelieryitemEntity.setTenantid(tid);   // 租户id
                        busDelieryitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busDelieryitemMapper.insert(busDelieryitemEntity);

                    } else {
                        BeanUtils.copyProperties(busDelieryitemPojo, busDelieryitemEntity);
                        busDelieryitemEntity.setTenantid(tid);
                        this.busDelieryitemMapper.update(busDelieryitemEntity);
                    }


                }
            }
            this.busDelieryMapper.update(busDelieryEntity);


            //循环每个删除item子表
            for (int i = 0; i < lstDel.size(); i++) {
                if ("发出商品".equals(billType) || "订单退货".equals(billType)
                        && !"".equals(lst.get(i).getCiteitemid()) && !"".equals(lst.get(i).getCiteuid())) {
                    this.busDelieryMapper.updateMachItemFinishQty(lstDel.get(i).getCiteitemid(), lstDel.get(i).getCiteuid(), tid);
                    this.busDelieryMapper.updateMachFinishCount(lstDel.get(i).getCiteitemid(), lstDel.get(i).getCiteuid(), tid);
                    // 同步销售订单主表的最终成交金额FinishAmount FinishTaxAmount
                    this.busDelieryMapper.updateMachFinishAmount(lstDel.get(i).getCiteitemid(), tid);
                    // 同步发货计划子表的FinishQty
                    String deliplanitemid = lstDel.get(i).getDeliplanitemid();
                    if (isBlank(deliplanitemid)) {
                        // 读取系统参数：检查发货计划的的计划时间不能晚于发货单BillDate
                        String pairdeliplan = InksConfigThreadLocal.getConfig("module.sale.autopairingdeliplan");
                        if ("true".equals(pairdeliplan)) {
                            String machitemid = lstDel.get(i).getMachitemid();
                            this.busDelieryMapper.syncDeliPlanItemFinishQtyByPlan(machitemid, billdate, tid);
                            this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                        }
                    } else {
                        this.busDelieryMapper.syncDeliPlanItemFinishQty(deliplanitemid, tid);
                        this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                    }
                } else if ("返工补发".equals(billType)) { //更新"退货返工"的ReturnQty
                    if (!"".equals(lst.get(i).getCiteitemid())) {
                        this.busDelieryMapper.updateDeliItemReturnQty(lst.get(i).getCiteitemid(), tid);
                        this.busDelieryMapper.updateDeliReturnCount(lst.get(i).getCiteitemid(), tid);

                    }
                }
            }

            //循环每个item子表
            for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                if ("发出商品".equals(billType) || "订单退货".equals(billType)
                        && !"".equals(busDelieryitemPojo.getCiteitemid()) && !"".equals(busDelieryitemPojo.getCiteuid())) {
                    this.busDelieryMapper.updateMachItemFinishQty(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                    this.busDelieryMapper.updateMachFinishCount(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                    // 同步销售订单主表的最终成交金额FinishAmount FinishTaxAmount
                    this.busDelieryMapper.updateMachFinishAmount(busDelieryitemPojo.getCiteitemid(), tid);
                    // 同步发货计划子表的FinishQty
                    String deliplanitemid = busDelieryitemPojo.getDeliplanitemid();
                    if (isBlank(deliplanitemid)) {
                        // 读取系统参数：检查发货计划的的计划时间不能晚于发货单BillDate
                        String pairdeliplan = InksConfigThreadLocal.getConfig("module.sale.autopairingdeliplan");
                        if ("true".equals(pairdeliplan)) {
                            String machitemid = busDelieryitemPojo.getMachitemid();
                            this.busDelieryMapper.syncDeliPlanItemFinishQtyByPlan(machitemid, billdate, tid);
                            this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                        }
                    } else {
                        this.busDelieryMapper.syncDeliPlanItemFinishQty(deliplanitemid, tid);
                        this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                    }
                } else if ("返工补发".equals(billType)) { //更新"退货返工"的ReturnQty
                    if (!"".equals(busDelieryitemPojo.getCiteitemid())) {
                        this.busDelieryMapper.updateDeliItemReturnQty(busDelieryitemPojo.getCiteitemid(), tid);
                        this.busDelieryMapper.updateDeliReturnCount(busDelieryitemPojo.getCiteitemid(), tid);
                    }
                }
            }

            // 更新完工款数
            this.busDelieryMapper.updateFinishCount(busDelieryEntity.getId(), busDelieryEntity.getTenantid());

            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
            Set<String> goodsidLstDelSet = lstDel.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
            goodsidLstSet.addAll(goodsidLstDelSet);
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
            });

            return Boolean.TRUE;
        });


        //返回Bill实例
        return this.

                getBillEntity(busDelieryEntity.getId(), busDelieryEntity.

                        getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusDelieryPojo busDelieryPojo = this.getBillEntity(key, tid);
        Date billdate = busDelieryPojo.getBilldate();
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busDelieryPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(billdate) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getSdfEndDate() + ",禁止删除结账前单据");
        }
        //Item子表处理
        List<BusDelieryitemPojo> lst = busDelieryPojo.getItem();
        Integer delNum;
        String billType = busDelieryPojo.getBilltype();
        delNum = transactionTemplate.execute((status) -> {
            if (lst != null) {
                //循环每个删除item子表
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    // 检查是否被引用
                    List<String> lstcite = getItemCiteBillName(busDelieryitemPojo.getId(), busDelieryitemPojo.getPid(), tid);
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.busDelieryitemMapper.delete(busDelieryitemPojo.getId(), tid);
                    //更新订单已发货 Eric20211213
                    String citeitemid = busDelieryitemPojo.getCiteitemid();
                    if (isNotBlank(citeitemid)) {
                        if ("发出商品".equals(billType) || "订单退货".equals(billType)) {
                            this.busDelieryMapper.updateMachItemFinishQty(citeitemid, busDelieryitemPojo.getCiteuid(), tid);
                            this.busDelieryMapper.updateMachFinishCount(citeitemid, busDelieryitemPojo.getCiteuid(), tid);
                            // 同步销售订单主表的最终成交金额FinishAmount FinishTaxAmount
                            this.busDelieryMapper.updateMachFinishAmount(citeitemid, tid);
                            // 同步发货计划子表的FinishQty
                            String deliplanitemid = busDelieryitemPojo.getDeliplanitemid();
                            if (isBlank(deliplanitemid)) {
                                // 读取系统参数：检查发货计划的的计划时间不能晚于发货单BillDate
                                String pairdeliplan = InksConfigThreadLocal.getConfig("module.sale.autopairingdeliplan");
                                if ("true".equals(pairdeliplan)) {
                                    String machitemid = busDelieryitemPojo.getMachitemid();
                                    this.busDelieryMapper.syncDeliPlanItemFinishQtyByPlan(machitemid, billdate, tid);
                                    this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                                }
                            } else {
                                this.busDelieryMapper.syncDeliPlanItemFinishQty(deliplanitemid, tid);
                                this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                            }
                        } else if ("返工补发".equals(billType)) { //更新"退货返工"的ReturnQty
                            this.busDelieryMapper.updateDeliItemReturnQty(busDelieryitemPojo.getCiteitemid(), tid);
                            this.busDelieryMapper.updateDeliReturnCount(busDelieryitemPojo.getCiteitemid(), tid);
                        }
                    }
                }
            }
            int deleteCount = this.busDelieryMapper.delete(key, tid);
            this.busDelieryMapper.updateFinishCount(key, tid);
            return deleteCount;
        });

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
        });
        return delNum;
    }


    /**
     * 审核数据
     *
     * @param busDelieryPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDelieryPojo approval(BusDelieryPojo busDelieryPojo) {
        //主表更改
        BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
        BeanUtils.copyProperties(busDelieryPojo, busDelieryEntity);
        this.busDelieryMapper.approval(busDelieryEntity);
        //返回Bill实例
        return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDelieryPojo disannul(List<BusDelieryitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BusDelieryitemPojo Pojo = lst.get(i);
            BusDelieryitemPojo dbPojo = this.busDelieryitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                String billType = busDelieryitemMapper.getBillType(dbPojo.getPid(), tid);
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getFinishclosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getInvoqty() > 0 || dbPojo.getPickqty() > 0 || dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    BusDelieryitemEntity entity = new BusDelieryitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.busDelieryitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
                if ("发出商品".equals(billType) || "订单退货".equals(billType)) {            //更新订单已发货
                    if (!"".equals(dbPojo.getCiteitemid()) && !"".equals(dbPojo.getCiteuid())) {
                        this.busDelieryMapper.updateMachItemFinishQty(dbPojo.getCiteitemid(), dbPojo.getCiteuid(), tid);
                        this.busDelieryMapper.updateMachFinishCount(dbPojo.getCiteitemid(), dbPojo.getCiteuid(), tid);
                        // 同步发货计划子表的FinishQty
                        String deliplanitemid = dbPojo.getDeliplanitemid();
                        if (isBlank(deliplanitemid)) {
                            // 读取系统参数：检查发货计划的的计划时间不能晚于发货单BillDate
                            String pairdeliplan = InksConfigThreadLocal.getConfig("module.sale.autopairingdeliplan");
                            //if ("true".equals(pairdeliplan)) {
                            //    String machitemid = dbPojo.getMachitemid();
                            //    this.busDelieryMapper.syncDeliPlanItemFinishQtyByPlan(machitemid, billdate, tid);
                            //    this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                            //}
                        } else {
                            this.busDelieryMapper.syncDeliPlanItemFinishQty(deliplanitemid, tid);
                            this.busDelieryMapper.syncDeliPlanFinishCount(deliplanitemid, tid);
                        }
                    }
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.busDelieryMapper.updateDisannulCountAndAmount(Pid, tid);
            //主表更改
            BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
            busDelieryEntity.setId(Pid);
            busDelieryEntity.setLister(loginUser.getRealname());
            busDelieryEntity.setListerid(loginUser.getUserid());
            busDelieryEntity.setModifydate(new Date());
            busDelieryEntity.setTenantid(loginUser.getTenantid());
            this.busDelieryMapper.update(busDelieryEntity);
            //返回Bill实例
            return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDelieryPojo closed(List<BusDelieryitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "中止" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BusDelieryitemPojo Pojo = lst.get(i);
            BusDelieryitemPojo dbPojo = this.busDelieryitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getFinishclosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BusDelieryitemEntity entity = new BusDelieryitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setFinishclosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.busDelieryitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            saleSyncMapper.updateGoodsBusRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.busDelieryMapper.updateFinishCount(Pid, tid);
            //主表更改
            BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
            busDelieryEntity.setId(Pid);
            busDelieryEntity.setLister(loginUser.getRealname());
            busDelieryEntity.setListerid(loginUser.getUserid());
            busDelieryEntity.setModifydate(new Date());
            busDelieryEntity.setTenantid(loginUser.getTenantid());
            this.busDelieryMapper.update(busDelieryEntity);
            //返回Bill实例
            return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
// 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.busDelieryMapper.getItemCiteBillName(key, pid, tid);
    }

    private final String PRINTBATCH_STATE = "printbatch_codes:";


    @Override
    @Async
// 开始批量打印
    public void printBatchBillStart(List<String> ids, String uuid, String printapproved, ReportsPojo reportsPojo, LoginUser loginUser) {

        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100"); //开始处理代码
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("totalCount", ids.size());
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(PRINTBATCH_STATE, uuid, missionMsg);
            //数据填充
            JasperPrint printAll = new JasperPrint();
            List<JasperPrint> lstPrint = new ArrayList<>();
            String content = reportsPojo.getRptdata();
            int successCount = 0;
            int failCount = 0;
            for (int a = 0; a < ids.size(); a++) {
                String key = ids.get(a);
                //获取单据信息
                BusDelieryPojo busDelieryPojo = this.getBillEntity(key, loginUser.getTenantid());
                if (busDelieryPojo == null) {
                    failCount++;
                } else if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {
                    failCount++;
                } else {
                    //表头转MAP
                    Map<String, Object> map = inks.common.core.utils.bean.BeanUtils.beanToMap(busDelieryPojo);
                    // 加入公司信息
                    inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                    // 判定是否需要追行
                    if (reportsPojo.getPagerow() > 0) {
                        int index;
                        // 取行余数
                        index = busDelieryPojo.getItem().size() % reportsPojo.getPagerow();
                        if (index > 0) {
                            // 补全空白行
                            for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                                BusDelieryitemPojo busDelieryitemPojo = new BusDelieryitemPojo();
                                busDelieryPojo.getItem().add(busDelieryitemPojo);
                            }
                        }
                    }
                    // 带属性List转为Map  EricRen 20220427
                    List<Map<String, Object>> lst = attrcostListToMaps(busDelieryPojo.getItem());
                    //item转数据源
                    JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
                    //报表生成
                    InputStream stream = new ByteArrayInputStream(content.getBytes());
                    //编译报表
                    JasperReport jasperReport = JasperCompileManager.compileReport(stream);
                    //数据填充
                    JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                    if (a == 0) {
                        printAll = print;
                    } else {
                        List<JRPrintPage> pages = print.getPages();
                        for (JRPrintPage page : pages) {
                            printAll.addPage(page);
                        }
                    }
                    lstPrint.add(print);
                }
                successCount++;
                missionMsg.put("code", "150"); //任务处理中代码
                missionMsg.put("msg", "任务处理中");
                missionMsg.put("totalCount", ids.size());
                missionMsg.put("successCount", successCount);
                this.redisService.setCacheMapValue(PRINTBATCH_STATE, uuid, missionMsg);
            }

//            //输出文件
//            String pdfPath = "D:\\test.pdf";
//            JasperExportManager.exportReportToPdfFile(printAll,pdfPath);


            byte[] base64File = StreamUtils.toByteArray(printAll);

            String cachekey = "report_pages:" + uuid;
            this.redisService.setCacheObject(cachekey, base64File, 60L, TimeUnit.MINUTES);

            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("successCount", successCount);
            missionMsg.put("failCount", failCount);
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.redisService.setCacheMapValue(PRINTBATCH_STATE, uuid, missionMsg);

        } catch (Exception ignored) {

        }
    }

    /**
     * 获取批量打印状态
     *
     * @param key
     * @return
     */
    @Override
    public Map<String, Object> getPrintBatchBillState(String key) {
        return this.redisService.getCacheMapValue(PRINTBATCH_STATE, key);
    }

    @Override
    public List<BusDelieryitemPojo> getItemListByIds(String ids, String pid, String tid) {
        return this.busDelieryMapper.getItemListByIds(ids, pid, tid);
    }

    private static void cleanNull(BusDelieryPojo busDelieryPojo) {
        if (busDelieryPojo.getRefno() == null) busDelieryPojo.setRefno("");
        if (busDelieryPojo.getBilltitle() == null) busDelieryPojo.setBilltitle("");
        if (busDelieryPojo.getBilldate() == null) busDelieryPojo.setBilldate(new Date());
        if (busDelieryPojo.getBilltype() == null) busDelieryPojo.setBilltype("");
        if (busDelieryPojo.getGroupid() == null) busDelieryPojo.setGroupid("");
        if (busDelieryPojo.getTelephone() == null) busDelieryPojo.setTelephone("");
        if (busDelieryPojo.getLinkman() == null) busDelieryPojo.setLinkman("");
        if (busDelieryPojo.getDeliadd() == null) busDelieryPojo.setDeliadd("");
        if (busDelieryPojo.getTaxrate() == null) busDelieryPojo.setTaxrate(0);
        if (busDelieryPojo.getTransport() == null) busDelieryPojo.setTransport("");
        if (busDelieryPojo.getSalesman() == null) busDelieryPojo.setSalesman("");
        if (busDelieryPojo.getSalesmanid() == null) busDelieryPojo.setSalesmanid("");
        if (busDelieryPojo.getOperator() == null) busDelieryPojo.setOperator("");
        if (busDelieryPojo.getOperatorid() == null) busDelieryPojo.setOperatorid("");
        if (busDelieryPojo.getSummary() == null) busDelieryPojo.setSummary("");
        if (busDelieryPojo.getCreateby() == null) busDelieryPojo.setCreateby("");
        if (busDelieryPojo.getCreatebyid() == null) busDelieryPojo.setCreatebyid("");
        if (busDelieryPojo.getCreatedate() == null) busDelieryPojo.setCreatedate(new Date());
        if (busDelieryPojo.getLister() == null) busDelieryPojo.setLister("");
        if (busDelieryPojo.getListerid() == null) busDelieryPojo.setListerid("");
        if (busDelieryPojo.getModifydate() == null) busDelieryPojo.setModifydate(new Date());
        if (busDelieryPojo.getAssessor() == null) busDelieryPojo.setAssessor("");
        if (busDelieryPojo.getAssessorid() == null) busDelieryPojo.setAssessorid("");
        if (busDelieryPojo.getAssessdate() == null) busDelieryPojo.setAssessdate(new Date());
        if (busDelieryPojo.getDisannulcount() == null) busDelieryPojo.setDisannulcount(0);
        if (busDelieryPojo.getBillstatecode() == null) busDelieryPojo.setBillstatecode("");
        if (busDelieryPojo.getBillstatedate() == null) busDelieryPojo.setBillstatedate(new Date());
        if (busDelieryPojo.getBilltaxamount() == null) busDelieryPojo.setBilltaxamount(0D);
        if (busDelieryPojo.getBilltaxtotal() == null) busDelieryPojo.setBilltaxtotal(0D);
        if (busDelieryPojo.getBillamount() == null) busDelieryPojo.setBillamount(0D);
        if (busDelieryPojo.getBillreceived() == null) busDelieryPojo.setBillreceived(0D);
        if (busDelieryPojo.getItemcount() == null) busDelieryPojo.setItemcount(busDelieryPojo.getItem().size());
        if (busDelieryPojo.getPickcount() == null) busDelieryPojo.setPickcount(0);
        if (busDelieryPojo.getFinishcount() == null) busDelieryPojo.setFinishcount(0);
        if (busDelieryPojo.getInvocount() == null) busDelieryPojo.setInvocount(0);
        if (busDelieryPojo.getReturncount() == null) busDelieryPojo.setReturncount(0);
        if (busDelieryPojo.getPrintcount() == null) busDelieryPojo.setPrintcount(0);
        if (busDelieryPojo.getOaflowmark() == null) busDelieryPojo.setOaflowmark(0);
        if (busDelieryPojo.getCustom1() == null) busDelieryPojo.setCustom1("");
        if (busDelieryPojo.getCustom2() == null) busDelieryPojo.setCustom2("");
        if (busDelieryPojo.getCustom3() == null) busDelieryPojo.setCustom3("");
        if (busDelieryPojo.getCustom4() == null) busDelieryPojo.setCustom4("");
        if (busDelieryPojo.getCustom5() == null) busDelieryPojo.setCustom5("");
        if (busDelieryPojo.getCustom6() == null) busDelieryPojo.setCustom6("");
        if (busDelieryPojo.getCustom7() == null) busDelieryPojo.setCustom7("");
        if (busDelieryPojo.getCustom8() == null) busDelieryPojo.setCustom8("");
        if (busDelieryPojo.getCustom9() == null) busDelieryPojo.setCustom9("");
        if (busDelieryPojo.getCustom10() == null) busDelieryPojo.setCustom10("");
        if (busDelieryPojo.getTenantname() == null) busDelieryPojo.setTenantname("");
        if (busDelieryPojo.getTenantid() == null) busDelieryPojo.setTenantid("");
        if (busDelieryPojo.getRevision() == null) busDelieryPojo.setRevision(0);
    }
}
