package inks.service.std.sale.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.service.std.sale.domain.pojo.BusCostmodelPojo;
import inks.service.std.sale.service.BusCostmodelService;
import inks.service.std.sale.service.BusCostmodelitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 预算模型(Bus_CostModel)表控制层
 *
 * <AUTHOR>
 * @since 2022-06-06 07:57:25
 */
@RestController
@RequestMapping("D01M02S2")
@Api(tags = "D01M02S2:核价单模型")
public class D01M02S2Controller extends BusCostmodelController {
    /**
     * 服务对象
     */
    @Resource
    private BusCostmodelService busCostmodelService;

    /**
     * 服务对象Item
     */
    @Resource
    private BusCostmodelitemService busCostmodelitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取预算默认模型详细信息", notes = "获取预算默认模型详细信息", produces = "application/json")
    @RequestMapping(value = "/getDefBillEntity", method = RequestMethod.GET)
    public R<BusCostmodelPojo> getDefBillEntity() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.busCostmodelService.getDefBillEntity(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
