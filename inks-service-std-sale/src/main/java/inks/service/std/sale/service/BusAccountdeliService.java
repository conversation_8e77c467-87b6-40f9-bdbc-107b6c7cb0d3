package inks.service.std.sale.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusAccountdeliPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 销售订单to发货单(BusAccountdeli)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-13 16:40:26
 */
public interface BusAccountdeliService {

    BusAccountdeliPojo getEntity(String key,String tid);

    PageInfo<BusAccountdeliPojo> getPageList(QueryParam queryParam);

    List<BusAccountdeliPojo> getList(String Pid,String tid);  

    BusAccountdeliPojo insert(BusAccountdeliPojo busAccountdeliPojo);

    BusAccountdeliPojo update(BusAccountdeliPojo busAccountdelipojo);

    int delete(String key,String tid);

    BusAccountdeliPojo clearNull(BusAccountdeliPojo busAccountdelipojo);
}
