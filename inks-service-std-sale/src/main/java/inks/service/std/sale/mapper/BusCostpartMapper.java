package inks.service.std.sale.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.BusCostpartEntity;
import inks.service.std.sale.domain.pojo.BusCostpartPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成本组件(BusCostpart)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-07 13:29:30
 */
@Mapper
public interface BusCostpartMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusCostpartPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusCostpartPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param busCostpartEntity 实例对象
     * @return 影响行数
     */
    int insert(BusCostpartEntity busCostpartEntity);

    
    /**
     * 修改数据
     *
     * @param busCostpartEntity 实例对象
     * @return 影响行数
     */
    int update(BusCostpartEntity busCostpartEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                                                                         }

