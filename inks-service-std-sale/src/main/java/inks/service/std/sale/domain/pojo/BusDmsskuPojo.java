package inks.service.std.sale.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * Dms商品Sku(BusDmssku)实体类
 *
 * <AUTHOR>
 * @since 2024-01-05 11:10:13
 */
public class BusDmsskuPojo implements Serializable {
    private static final long serialVersionUID = -55985237961651042L;
     // id
    @Excel(name = "id") 
    private String id;
     // Sku编码
    @Excel(name = "Sku编码") 
    private String skucode;
     // Sku序号
    @Excel(name = "Sku序号") 
    private Integer skunum;
     // Goodsid
    @Excel(name = "Goodsid") 
    private String goodsid;
    // oms货品id
    @Excel(name = "oms货品id")
    private String omsgoodsid;
     // 产品编码
    @Excel(name = "Dms产品编码")
    private String itemcode;
     // 产品名称
    @Excel(name = "Dms产品名称")
    private String itemname;
     // 属性Josn
    @Excel(name = "属性Josn") 
    private String attributejson;
     // 条形码
    @Excel(name = "条形码") 
    private String barcode;
     // 安全库存
    @Excel(name = "安全库存") 
    private Double safestock;
     // 建议进价
    @Excel(name = "建议进价") 
    private Double inprice;
     // 建议售价
    @Excel(name = "建议售价") 
    private Double outprice;
     // 库存数量
    @Excel(name = "库存数量") 
    private Double ivquantity;
     // 库存金额
    @Excel(name = "库存金额") 
    private Double ivamount;
     // 库存单价
    @Excel(name = "库存单价") 
    private Double ageprice;
     // 附图Url
    @Excel(name = "附图Url") 
    private String skuphoto;
     // 商品图片，以,分割
    @Excel(name = "商品图片，以,分割") 
    private String imgs;
     // 指数
    @Excel(name = "指数") 
    private Integer exponent;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;
    //                Bus_DmsGoods.MainMark,
    //               Bus_DmsGoods.Status
    //goodsid查询Bus_DmsGoods信息:
    private String mainmark;
    private Integer status;

    //omsgoodsid 关联查询货品信息
    private String omsgoodsname;//oms货品名称
    private String omsgoodsspec;//oms货品规格
    private String omsgoodsunit;//oms货品单位
    private String omsgoodsuid;//oms货品编码
//    Mat_Goods.IvQuantity  as OmsIvQuantity
    private Double omsivquantity;//oms货品库存数量

    //               Bus_DmsGoods.GoodsSpec                                       as ItemSpec,
    //               Bus_DmsGoods.GoodsUnit                                       as ItemUnit
    private String itemspec;//Dms产品规格
    private String itemunit;//Dms产品单位

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    public String getOmsgoodsid() {
        return omsgoodsid;
    }

    public Double getOmsivquantity() {
        return omsivquantity;
    }

    public void setOmsivquantity(Double omsivquantity) {
        this.omsivquantity = omsivquantity;
    }

    public void setOmsgoodsid(String omsgoodsid) {
        this.omsgoodsid = omsgoodsid;
    }

    public String getOmsgoodsname() {
        return omsgoodsname;
    }

    public void setOmsgoodsname(String omsgoodsname) {
        this.omsgoodsname = omsgoodsname;
    }

    public String getOmsgoodsspec() {
        return omsgoodsspec;
    }

    public void setOmsgoodsspec(String omsgoodsspec) {
        this.omsgoodsspec = omsgoodsspec;
    }

    public String getOmsgoodsunit() {
        return omsgoodsunit;
    }

    public void setOmsgoodsunit(String omsgoodsunit) {
        this.omsgoodsunit = omsgoodsunit;
    }

    public String getOmsgoodsuid() {
        return omsgoodsuid;
    }

    public void setOmsgoodsuid(String omsgoodsuid) {
        this.omsgoodsuid = omsgoodsuid;
    }

    // Sku编码
    public String getSkucode() {
        return skucode;
    }
    
    public void setSkucode(String skucode) {
        this.skucode = skucode;
    }
        
   // Sku序号
    public Integer getSkunum() {
        return skunum;
    }
    
    public void setSkunum(Integer skunum) {
        this.skunum = skunum;
    }
        
   // Goodsid
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    public String getMainmark() {
        return mainmark;
    }

    public void setMainmark(String mainmark) {
        this.mainmark = mainmark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    // 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
   // 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // 条形码
    public String getBarcode() {
        return barcode;
    }
    
    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }
        
   // 安全库存
    public Double getSafestock() {
        return safestock;
    }
    
    public void setSafestock(Double safestock) {
        this.safestock = safestock;
    }
        
   // 建议进价
    public Double getInprice() {
        return inprice;
    }
    
    public void setInprice(Double inprice) {
        this.inprice = inprice;
    }
        
   // 建议售价
    public Double getOutprice() {
        return outprice;
    }
    
    public void setOutprice(Double outprice) {
        this.outprice = outprice;
    }
        
   // 库存数量
    public Double getIvquantity() {
        return ivquantity;
    }
    
    public void setIvquantity(Double ivquantity) {
        this.ivquantity = ivquantity;
    }
        
   // 库存金额
    public Double getIvamount() {
        return ivamount;
    }
    
    public void setIvamount(Double ivamount) {
        this.ivamount = ivamount;
    }
        
   // 库存单价
    public Double getAgeprice() {
        return ageprice;
    }
    
    public void setAgeprice(Double ageprice) {
        this.ageprice = ageprice;
    }
        
   // 附图Url
    public String getSkuphoto() {
        return skuphoto;
    }
    
    public void setSkuphoto(String skuphoto) {
        this.skuphoto = skuphoto;
    }
        
   // 商品图片，以,分割
    public String getImgs() {
        return imgs;
    }
    
    public void setImgs(String imgs) {
        this.imgs = imgs;
    }
        
   // 指数
    public Integer getExponent() {
        return exponent;
    }
    
    public void setExponent(Integer exponent) {
        this.exponent = exponent;
    }

        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

