package inks.service.std.sale.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.sale.domain.pojo.BusBudgetitemPojo;

import java.util.List;
/**
 * 预算项目(BusBudgetitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-02 09:27:23
 */
public interface BusBudgetitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusBudgetitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusBudgetitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusBudgetitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busBudgetitemPojo 实例对象
     * @return 实例对象
     */
    BusBudgetitemPojo insert(BusBudgetitemPojo busBudgetitemPojo);

    /**
     * 修改数据
     *
     * @param busBudgetitempojo 实例对象
     * @return 实例对象
     */
    BusBudgetitemPojo update(BusBudgetitemPojo busBudgetitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busBudgetitempojo 实例对象
     * @return 实例对象
     */
    BusBudgetitemPojo clearNull(BusBudgetitemPojo busBudgetitempojo);

    BusBudgetitemPojo getEntityByMachitemid(String machitemid, String tid);
}
