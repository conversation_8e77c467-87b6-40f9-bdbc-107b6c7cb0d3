server:
  tomcat:
    uri-encoding: UTF-8
  address: *************
#spring数据源
spring:
  cloud:
    nacos:
      discovery:
        # Nacos 注册中心 8848
        server-addr: **************:8848
#        server-addr: **************:8848
        username: nacos
        password: inks0820
        ip: *************
  #        server-addr: *************:8848
  datasource:
    #MYsql连接字符串  3306
    url: ***************************************************************************************************************************************************
#    url: *************************************************************************************************************************************************
    username: root
    password: asd@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
#    host: ************
    # Redis服务器连接端口 6379
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
#  rabbitmq:
#    host: *************
#    port: 5672
#    username: inks
#    password: inks
#    virtual-host: inksoms
#    ##开启发布确认  (三种模式)
#    publisher-confirm-type: correlated
#    # 开启发送端抵达队列确认，消息未被队列接收时触发回调【发送端确认机制+本地事务表】
#    publisher-returns: true
#    #消费者监听器
#    listener:
#      simple:
#        #设置消费端手动 ack
#        acknowledge-mode: manual
#        #消费者 消息预取值 basicQOS
#        prefetch: 1
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.system.**.domain
  #配置打印SQL语句到控制台
#雪花算法:  数据中心id,工作机器id
snowflake:
  dataCenterId: 1
  workerId: 1

#inks:
##  svcfeign: http://dev.inksyun.com:31080
#  svcfeign: http://api.inksyun.com

