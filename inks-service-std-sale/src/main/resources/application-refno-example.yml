# RefNo AOP 配置示例
# 可以将这些配置添加到主配置文件中

inks:
  refno:
    # 是否启用RefNo AOP功能
    enabled: true
    
    # 是否启用详细日志
    verbose-logging: false
    
    # 默认的编码字段名
    default-field: refno
    
    # 是否默认保存到Redis
    default-save-to-redis: true
    
    # 是否默认忽略清理异常
    default-ignore-cleanup-exception: true
    
    # 编码生成超时时间（毫秒）
    generate-timeout: 5000
    
    # Redis清理超时时间（毫秒）
    cleanup-timeout: 3000

# 日志配置示例
logging:
  level:
    # RefNo相关日志级别
    inks.service.std.sale.aspect.RefNoAspect: INFO
    inks.service.std.sale.utils.RefNoAopUtils: DEBUG
    
    # 开发时可以设置为DEBUG查看详细信息
    # inks.service.std.sale.aspect: DEBUG
