<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusInvoiceMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusInvoicePojo">
        <include refid="selectbillVo"/>
        where Bus_Invoice.id = #{key}
          and Bus_Invoice.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_Invoice.id,
               Bus_Invoice.RefNo,
               Bus_Invoice.BillType,
               Bus_Invoice.BillTitle,
               Bus_Invoice.BillDate,
               Bus_Invoice.Groupid,
               Bus_Invoice.TaxAmount,
               Bus_Invoice.Amount,
               Bus_Invoice.TaxTotal,
               Bus_Invoice.Taxrate,
               Bus_Invoice.InvoCode,
               Bus_Invoice.InvoDate,
               Bus_Invoice.AimDate,
               Bus_Invoice.Receipted,
               Bus_Invoice.Summary,
               Bus_Invoice.CreateBy,
               Bus_Invoice.CreateByid,
               Bus_Invoice.CreateDate,
               Bus_Invoice.Lister,
               Bus_Invoice.Listerid,
               Bus_Invoice.ModifyDate,
               Bus_Invoice.Assessor,
               Bus_Invoice.Assessorid,
               Bus_Invoice.AssessDate,
               Bus_Invoice.StateCode,
               Bus_Invoice.StateDate,
               Bus_Invoice.Closed,
               Bus_Invoice.DisannulMark,
               Bus_Invoice.FmDocMark,
               Bus_Invoice.FmDocCode,
               Bus_Invoice.Operator,
               Bus_Invoice.FirstAmt,
               Bus_Invoice.LastAmt,
               Bus_Invoice.BillTaxAmount,
               Bus_Invoice.BillTaxTotal,
               Bus_Invoice.BillAmount,
               Bus_Invoice.Custom1,
               Bus_Invoice.Custom2,
               Bus_Invoice.Custom3,
               Bus_Invoice.Custom4,
               Bus_Invoice.Custom5,
               Bus_Invoice.Custom6,
               Bus_Invoice.Custom7,
               Bus_Invoice.Custom8,
               Bus_Invoice.Custom9,
               Bus_Invoice.Custom10,
               Bus_Invoice.Tenantid,
               Bus_Invoice.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.Linkman,
               App_Workgroup.Telephone
        FROM Bus_Invoice
                 LEFT JOIN App_Workgroup ON Bus_Invoice.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Bus_InvoiceItem.id,
               Bus_InvoiceItem.Pid,
               Bus_InvoiceItem.DeliUid,
               Bus_InvoiceItem.DeliDate,
               Bus_InvoiceItem.DeliType,
               Bus_InvoiceItem.DeliItemid,
               Bus_InvoiceItem.Goodsid,
               Bus_InvoiceItem.BillQty,
               Bus_InvoiceItem.Quantity,
               Bus_InvoiceItem.TaxPrice,
               Bus_InvoiceItem.TaxAmount,
               Bus_InvoiceItem.Price,
               Bus_InvoiceItem.Amount,
               Bus_InvoiceItem.ItemTaxrate,
               Bus_InvoiceItem.TaxTotal,
               Bus_InvoiceItem.RowNum,
               Bus_InvoiceItem.Remark,
               Bus_InvoiceItem.MachUid,
               Bus_InvoiceItem.MachItemid,
               Bus_InvoiceItem.CustPO,
               Bus_InvoiceItem.AttributeJson,
               Bus_InvoiceItem.Custom1,
               Bus_InvoiceItem.MachBatch,
               Bus_InvoiceItem.StdPrice,
               Bus_InvoiceItem.StdAmount,
               Bus_InvoiceItem.Rebate,
               Bus_InvoiceItem.Custom2,
               Bus_InvoiceItem.Custom3,
               Bus_InvoiceItem.Custom4,
               Bus_InvoiceItem.Custom5,
               Bus_InvoiceItem.Custom6,
               Bus_InvoiceItem.Custom7,
               Bus_InvoiceItem.Custom8,
               Bus_InvoiceItem.Custom9,
               Bus_InvoiceItem.Custom10,
               Bus_InvoiceItem.Custom11,
               Bus_InvoiceItem.Custom12,
               Bus_InvoiceItem.Custom13,
               Bus_InvoiceItem.Custom14,
               Bus_InvoiceItem.Custom15,
               Bus_InvoiceItem.Custom16,
               Bus_InvoiceItem.Custom17,
               Bus_InvoiceItem.Custom18,
               Bus_InvoiceItem.Tenantid,
               Bus_InvoiceItem.Revision,
               Bus_Invoice.RefNo,
               Bus_Invoice.BillType,
               Bus_Invoice.BillTitle,
               Bus_Invoice.BillDate,
               Bus_Invoice.TaxAmount,
               Bus_Invoice.Amount,
               Bus_Invoice.InvoCode,
               Bus_Invoice.InvoDate,
               Bus_Invoice.AimDate,
               Bus_Invoice.CreateBy,
               Bus_Invoice.Lister,
               Bus_Invoice.Assessor,
               Bus_Invoice.Closed,
               Bus_Invoice.FmDocMark,
               Bus_Invoice.FmDocCode,
               Bus_Invoice.Operator,
               Bus_Invoice.FirstAmt,
               Bus_Invoice.LastAmt,
               Bus_Invoice.BillTaxAmount,
               Bus_Invoice.BillTaxTotal,
               Bus_Invoice.BillAmount,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM Bus_Invoice
                 LEFT JOIN App_Workgroup ON Bus_Invoice.Groupid = App_Workgroup.id
                 RIGHT JOIN Bus_InvoiceItem ON Bus_Invoice.id = Bus_InvoiceItem.Pid
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_InvoiceItem.Goodsid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusInvoiceitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Invoice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Invoice.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Bus_Invoice.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Bus_Invoice.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Bus_Invoice.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Bus_Invoice.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.invocode != null and SearchPojo.invocode != ''">
            and Bus_Invoice.invocode like concat('%', #{SearchPojo.invocode}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Bus_Invoice.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Bus_Invoice.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Bus_Invoice.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Bus_Invoice.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Bus_Invoice.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Bus_Invoice.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Bus_Invoice.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Bus_Invoice.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_Invoice.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_Invoice.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_Invoice.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_Invoice.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_Invoice.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_Invoice.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_Invoice.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_Invoice.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_Invoice.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_Invoice.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
        <if test="SearchPojo.deliuid != null and SearchPojo.deliuid != ''">
            and Bus_InvoiceItem.DeliUid like concat('%', #{SearchPojo.deliuid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Bus_Invoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Bus_Invoice.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Bus_Invoice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Bus_Invoice.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.invocode != null and SearchPojo.invocode != ''">
                or Bus_Invoice.InvoCode like concat('%', #{SearchPojo.invocode}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Bus_Invoice.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Bus_Invoice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Bus_Invoice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Bus_Invoice.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Bus_Invoice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Bus_Invoice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Bus_Invoice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Bus_Invoice.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_Invoice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_Invoice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_Invoice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_Invoice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_Invoice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_Invoice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_Invoice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_Invoice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_Invoice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_Invoice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
            <if test="SearchPojo.deliuid != null and SearchPojo.deliuid != ''">
                or Bus_InvoiceItem.DeliUid like concat('%', #{SearchPojo.deliuid}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusInvoicePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Invoice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Invoice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Bus_Invoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Bus_Invoice.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Bus_Invoice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Bus_Invoice.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.invocode != null and SearchPojo.invocode != ''">
            and Bus_Invoice.InvoCode like concat('%', #{SearchPojo.invocode}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Bus_Invoice.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Bus_Invoice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Bus_Invoice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Bus_Invoice.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Bus_Invoice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Bus_Invoice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Bus_Invoice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Bus_Invoice.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_Invoice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_Invoice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_Invoice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_Invoice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_Invoice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_Invoice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_Invoice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_Invoice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_Invoice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_Invoice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Bus_Invoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Bus_Invoice.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Bus_Invoice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Bus_Invoice.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.invocode != null and SearchPojo.invocode != ''">
                or Bus_Invoice.InvoCode like concat('%', #{SearchPojo.invocode}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Bus_Invoice.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Bus_Invoice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Bus_Invoice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Bus_Invoice.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Bus_Invoice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Bus_Invoice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Bus_Invoice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Bus_Invoice.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_Invoice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_Invoice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_Invoice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_Invoice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_Invoice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_Invoice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_Invoice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_Invoice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_Invoice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_Invoice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_Invoice(id, RefNo, BillType, BillTitle, BillDate, Groupid, TaxAmount, Amount, TaxTotal, Taxrate,
                                InvoCode, InvoDate, AimDate, Receipted, Summary, CreateBy, CreateByid, CreateDate,
                                Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, StateCode, StateDate,
                                Closed, DisannulMark, FmDocMark, FmDocCode, Operator, FirstAmt, LastAmt, BillTaxAmount,
                                BillTaxTotal, BillAmount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7,
                                Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{groupid}, #{taxamount}, #{amount},
                #{taxtotal}, #{taxrate}, #{invocode}, #{invodate}, #{aimdate}, #{receipted}, #{summary}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid},
                #{assessdate}, #{statecode}, #{statedate}, #{closed}, #{disannulmark}, #{fmdocmark}, #{fmdoccode},
                #{operator}, #{firstamt}, #{lastamt}, #{billtaxamount}, #{billtaxtotal}, #{billamount}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Invoice
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="taxamount != null">
                TaxAmount =#{taxamount},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal =#{taxtotal},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="invocode != null ">
                InvoCode =#{invocode},
            </if>
            <if test="invodate != null">
                InvoDate =#{invodate},
            </if>
            <if test="aimdate != null">
                AimDate =#{aimdate},
            </if>
            <if test="receipted != null">
                Receipted =#{receipted},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="fmdocmark != null">
                FmDocMark =#{fmdocmark},
            </if>
            <if test="fmdoccode != null ">
                FmDocCode =#{fmdoccode},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="firstamt != null">
                FirstAmt =#{firstamt},
            </if>
            <if test="lastamt != null">
                LastAmt =#{lastamt},
            </if>
            <if test="billtaxamount != null">
            BillTaxAmount =#{billtaxamount},
        </if>
            <if test="billtaxtotal != null">
            BillTaxTotal =#{billtaxtotal},
        </if>
            <if test="billamount != null">
            BillAmount =#{billamount},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>


    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Invoice
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Invoice
        SET Assessor   = #{assessor},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusInvoicePojo">
        select
        id
        from Bus_InvoiceItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateDeliInvoFinish">
        update Bus_DelieryItem
        SET InvoQty =COALESCE((SELECT SUM(Bus_InvoiceItem.quantity)
                               FROM Bus_InvoiceItem
                                        LEFT OUTER JOIN Bus_Invoice
                                                        ON Bus_InvoiceItem.pid = Bus_Invoice.id
                               where Bus_InvoiceItem.Deliitemid = #{key}
                                 and Bus_InvoiceItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <!--    <update id="updateDeliInvoCount">-->
    <!--        update Bus_Deliery-->
    <!--        SET InvoCount =COALESCE((SELECT COUNT(0)-->
    <!--                                 FROM Bus_DelieryItem-->
    <!--                                 where Bus_DelieryItem.Pid = (SELECT Pid FROM Bus_DelieryItem where id = #{key})-->
    <!--                                   and Bus_DelieryItem.Tenantid = #{tid}-->
    <!--                                   and (Bus_DelieryItem.InvoClosed = 1-->
    <!--                                     or Bus_DelieryItem.InvoQty >= Bus_DelieryItem.Quantity)), 0)-->
    <!--        where id = (SELECT Pid FROM Bus_DelieryItem where id = #{key})-->
    <!--          and Tenantid = #{tid}-->
    <!--    </update>-->
    <update id="updateDeliInvoCount">
        WITH TargetPid AS (SELECT Pid
                           FROM Bus_DelieryItem
                           WHERE id = #{key})
        UPDATE Bus_Deliery
        SET InvoCount = COALESCE((SELECT COUNT(1)
                                  FROM Bus_DelieryItem BDI
                                           JOIN TargetPid TP ON BDI.Pid = TP.Pid
                                  WHERE BDI.Tenantid = #{tid}
                                    AND (BDI.InvoClosed = 1 OR BDI.InvoQty >= BDI.Quantity)), 0)
        WHERE id = (SELECT Pid FROM TargetPid)
          AND Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateDeduInvoFinish">
        update Bus_DeductionItem
        SET InvoQty =COALESCE((SELECT SUM(Bus_InvoiceItem.quantity)
                               FROM Bus_InvoiceItem
                                        LEFT OUTER JOIN Bus_Invoice
                                                        ON Bus_InvoiceItem.pid = Bus_Invoice.id
                               where Bus_InvoiceItem.Deliitemid = #{key}
                                 and Bus_InvoiceItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateDeduInvoCount">
        update Bus_Deduction
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Bus_DeductionItem
                                   where Bus_DeductionItem.Pid = (SELECT Pid FROM Bus_DeductionItem where id = #{key})
                                     and Bus_DeductionItem.Tenantid = #{tid}
                                     and (Bus_DeductionItem.InvoClosed = 1
                                       or Bus_DeductionItem.InvoQty >= Bus_DeductionItem.Quantity)), 0)
        where id = (SELECT Pid FROM Bus_DeductionItem where id = #{key})
          and Tenantid = #{tid}
    </update>
    <!--  查询往来单位是否被引用  -->
    <select id="getCiteBillName" resultType="string">
        (SELECT '销售收款' as billname From Bus_ReceiptItem where Invoid = #{key} and Tenantid = #{tid} LIMIT 1)
    </select>

    <!--查询List-->
    <select id="pullItem" resultType="inks.service.std.sale.domain.pojo.BusInvoiceitemPojo">
        SELECT
        Bus_Deliery.RefNo AS DeliUid,
        Bus_Deliery.BillDate AS DeliDate,
        Bus_Deliery.BillType AS DeliType,
        Bus_Deliery.BillTitle,
        Bus_DelieryItem.id AS DeliItemid,
        Bus_DelieryItem.Goodsid,
        Bus_DelieryItem.Quantity AS BillQty,
        Bus_DelieryItem.Quantity - Bus_DelieryItem.InvoQty AS Quantity,
        Bus_DelieryItem.TaxPrice,
        Bus_DelieryItem.TaxAmount,
        Bus_DelieryItem.TaxTotal,
        Bus_DelieryItem.ItemTaxrate,
        Bus_DelieryItem.Price,
        Bus_DelieryItem.Amount,
        Bus_DelieryItem.Remark,
        Bus_DelieryItem.MachUid,
        Bus_DelieryItem.MachItemid,
        Bus_DelieryItem.CustPo,
        Bus_DelieryItem.MachType,
        Bus_DelieryItem.AttributeJson,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid
        FROM Bus_Deliery
        RIGHT JOIN Bus_DelieryItem ON Bus_DelieryItem.Pid = Bus_Deliery.id
        LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_DelieryItem.Goodsid
        where Bus_Deliery.Groupid = #{groupid}
        AND Bus_DelieryItem.InvoQty&lt;Bus_DelieryItem.Quantity
        AND Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.InvoClosed=0
        AND Bus_Deliery.Assessor != ''
        AND Bus_Deliery.BillReceived=0
        <if test="startdate !=null or enddate !=null">
            and (Bus_Deliery.BillDate BETWEEN #{startdate} and #{enddate})
        </if>
        union all
        SELECT
        Bus_Deduction.RefNo AS DeliUid,
        Bus_Deduction.BillDate AS DeliDate,
        Bus_Deduction.BillType AS DeliType,
        Bus_Deduction.BillTitle,
        Bus_DeductionItem.id AS DeliItemid,
        Bus_DeductionItem.Goodsid,
        Bus_DeductionItem.Quantity AS BillQty,
        Bus_DeductionItem.Quantity- Bus_DeductionItem.InvoQty AS Quantity,
        Bus_DeductionItem.TaxPrice,
        Bus_DeductionItem.TaxAmount,
        Bus_DeductionItem.TaxTotal,
        Bus_DeductionItem.ItemTaxrate,
        Bus_DeductionItem.Price,
        Bus_DeductionItem.Amount,
        Bus_DeductionItem.Remark,
        Bus_DeductionItem.MachUid,
        Bus_DeductionItem.MachItemid,
        Bus_DeductionItem.CustPO,
        '' AS MachType,
        '' AS AttributeJson,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid
        FROM Bus_Deduction
        RIGHT JOIN Bus_DeductionItem ON Bus_DeductionItem.Pid = Bus_Deduction.id
        LEFT JOIN Mat_Goods ON Bus_DeductionItem.Goodsid = Bus_Deduction.id
        where Bus_Deduction.Groupid = #{groupid}
        AND Bus_DeductionItem.InvoQty&lt;Bus_DeductionItem.Quantity
        AND Bus_DeductionItem.DisannulMark=0 and Bus_DeductionItem.InvoClosed=0
        AND Bus_Deduction.Assessor != ''
        <if test="startdate !=null or enddate !=null">
            and (Bus_Deduction.BillDate BETWEEN #{startdate} and #{enddate})
        </if>
        order by DeliDate
    </select>


    <update id="updateMachItemInvoQty">
        update Bus_MachiningItem
        SET InvoQty =COALESCE((SELECT SUM(Bus_InvoiceItem.quantity)
                               FROM Bus_InvoiceItem
                               where Bus_InvoiceItem.MachItemid = #{key}
                                 and Bus_InvoiceItem.Tenantid = #{tid}), 0),
            InvoAmt =COALESCE((SELECT SUM(Bus_InvoiceItem.TaxAmount)
                               FROM Bus_InvoiceItem
                               where Bus_InvoiceItem.MachItemid = #{key}
                                 and Bus_InvoiceItem.Tenantid = #{tid})
                , 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <update id="updateMachInvoCountInvoAmt">
        update Bus_Machining
        SET InvoCount =COALESCE((SELECT COUNT(0)
                                 FROM Bus_MachiningItem
                                 where Bus_MachiningItem.Pid = (SELECT Pid FROM Bus_MachiningItem where id = #{key})
                                   and Bus_MachiningItem.Tenantid = #{tid}
                                   and (Bus_MachiningItem.InvoClosed = 1
                                     or Bus_MachiningItem.InvoQty >= Bus_MachiningItem.Quantity)), 0),
            InvoAmt   =COALESCE((SELECT SUM(Bus_InvoiceItem.TaxAmount)
                                 FROM Bus_InvoiceItem
                                 where Bus_InvoiceItem.MachUid = #{refno}
                                   and Bus_InvoiceItem.Tenantid = #{tid})
                , 0)
        where id = (SELECT Pid FROM Bus_MachiningItem where id = #{key})
          and Tenantid = #{tid}
    </update>

    <update id="updateMachItemAvgInvoAmt">
        UPDATE Bus_MachiningItem
        SET AvgInvoAmt = (SELECT tmp.InvoAmt / tmp.ItemCount
                           FROM (SELECT (SELECT InvoAmt FROM Bus_Machining WHERE id = #{machid}) AS InvoAmt,
                                        COUNT(*)                                                  AS ItemCount
                                 FROM Bus_MachiningItem
                                 WHERE Pid = #{machid}) AS tmp)
        WHERE Bus_MachiningItem.Pid = #{machid}
          and Tenantid = #{tid}
    </update>

    <select id="getMachId" resultType="java.lang.String">
        select Pid
        from Bus_MachiningItem
        where id = #{machitemid}
          and Tenantid = #{tid}
    </select>

    <!-- 批量更新DeliInvoFinish -->
    <update id="updateDeliInvoFinishBatch">
        UPDATE Bus_DelieryItem
        SET InvoQty = COALESCE(
                (SELECT SUM(Bus_InvoiceItem.quantity)
                 FROM Bus_InvoiceItem
                          LEFT JOIN Bus_Invoice ON Bus_InvoiceItem.pid = Bus_Invoice.id
                 WHERE Bus_InvoiceItem.Deliitemid = Bus_DelieryItem.id
                   AND Bus_InvoiceItem.Tenantid = #{tid}), 0)
        WHERE id IN
        <foreach collection="deliItemIds" item="deliitemid" open="(" separator="," close=")">
            #{deliitemid}
        </foreach>
        AND Tenantid = #{tid}
    </update>
    <update id="updateDeliInvoCountBatch">
        WITH TargetPids AS (
        SELECT DISTINCT Pid
        FROM Bus_DelieryItem
        WHERE id IN
        <foreach collection="deliItemIds" item="deliitemid" open="(" separator="," close=")">
            #{deliitemid}
        </foreach>
        AND Tenantid = #{tid}
        )
        UPDATE Bus_Deliery
        SET InvoCount = COALESCE(
                (SELECT COUNT(1)
                 FROM Bus_DelieryItem BDI
                          JOIN TargetPids TP ON BDI.Pid = TP.Pid
                 WHERE BDI.Tenantid = #{tid}
                   AND (BDI.InvoClosed = 1 OR BDI.InvoQty >= BDI.Quantity)), 0)
        WHERE id IN (SELECT Pid FROM TargetPids)
          AND Tenantid = #{tid}
    </update>


    <update id="updateDeduInvoFinishBatch">
        UPDATE Bus_DeductionItem
        SET InvoQty = COALESCE(
                (SELECT SUM(Bus_InvoiceItem.quantity)
                 FROM Bus_InvoiceItem
                          LEFT OUTER JOIN Bus_Invoice ON Bus_InvoiceItem.pid = Bus_Invoice.id
                 WHERE Bus_InvoiceItem.Deliitemid = Bus_DeductionItem.id
                   AND Bus_InvoiceItem.Tenantid = #{tid}), 0)
        WHERE id IN
        <foreach collection="deliItemIdInDedus" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
        AND Tenantid = #{tid}
    </update>
    <update id="updateDeduInvoCountBatch">
        WITH TargetPids AS (
        SELECT DISTINCT Pid
        FROM Bus_DeductionItem
        WHERE id IN
        <foreach collection="deliItemIdInDedus" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
        AND Tenantid = #{tid}
        )
        UPDATE Bus_Deduction
        SET FinishCount = COALESCE(
                (SELECT COUNT(0)
                 FROM Bus_DeductionItem
                 WHERE Bus_DeductionItem.Pid = Bus_Deduction.id
                   AND Bus_DeductionItem.Tenantid = #{tid}
                   AND (Bus_DeductionItem.InvoClosed = 1 OR Bus_DeductionItem.InvoQty >= Bus_DeductionItem.Quantity)),
                0)
        WHERE id IN (SELECT Pid FROM TargetPids)
          AND Tenantid = #{tid}
    </update>


    <select id="getMachIds" resultType="java.lang.String">
        SELECT DISTINCT Pid
        FROM Bus_MachiningItem
        WHERE id IN
        <foreach collection="machitemids" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
        AND Tenantid = #{tid}
    </select>

    <update id="updateMachItemInvoQtyBatch">
        UPDATE Bus_MachiningItem
        SET InvoQty = COALESCE((SELECT SUM(Bus_InvoiceItem.quantity)
                                FROM Bus_InvoiceItem
                                WHERE Bus_InvoiceItem.MachItemid = Bus_MachiningItem.id
                                  AND Bus_InvoiceItem.Tenantid = #{tid}), 0),
            InvoAmt = COALESCE((SELECT SUM(Bus_InvoiceItem.TaxAmount)
                                FROM Bus_InvoiceItem
                                WHERE Bus_InvoiceItem.MachItemid = Bus_MachiningItem.id
                                  AND Bus_InvoiceItem.Tenantid = #{tid}), 0)
        WHERE id IN
        <foreach collection="machitemids" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
        AND Tenantid = #{tid}
    </update>


    <update id="updateMachInvoCountInvoAmtBatch">
        UPDATE Bus_Machining bm
            JOIN (
        SELECT Bus_MachiningItem.Pid,
               COUNT(0)                       AS InvoCount,
               SUM(Bus_InvoiceItem.TaxAmount) AS InvoAmt
        FROM Bus_MachiningItem
                 LEFT JOIN Bus_InvoiceItem ON Bus_InvoiceItem.MachItemid = Bus_MachiningItem.id
                 LEFT JOIN Bus_Machining bm2 ON bm2.RefNo = Bus_InvoiceItem.MachUid
        WHERE
        Bus_MachiningItem.Pid IN
        <foreach collection="machIds" item="machId" open="(" close=")" separator=",">
            #{machId}
        </foreach>
        AND Bus_MachiningItem.Tenantid = #{tid}
        AND (Bus_MachiningItem.InvoClosed = 1
            OR Bus_MachiningItem.InvoQty >= Bus_MachiningItem.Quantity)
        GROUP BY Bus_MachiningItem.Pid
        ) subquery ON bm.id = subquery.Pid
        SET bm.InvoCount = COALESCE(subquery.InvoCount, 0),
            bm.InvoAmt   = COALESCE(subquery.InvoAmt, 0)
        WHERE
        bm.id IN
        <foreach collection="machIds" item="machId" open="(" close=")" separator=",">
            #{machId}
        </foreach>
        AND bm.Tenantid = #{tid}
    </update>


    <update id="updateMachItemAvgInvoAmtBatch">
        UPDATE Bus_MachiningItem mi
        SET AvgInvoAmt = (SELECT CASE WHEN tmp.ItemCount > 0 THEN tmp.InvoAmt / tmp.ItemCount ELSE 0 END
                          FROM (SELECT (SELECT InvoAmt FROM Bus_Machining WHERE id = mi.Pid)       AS InvoAmt,
                                       (SELECT COUNT(*) FROM Bus_MachiningItem WHERE Pid = mi.Pid) AS ItemCount) AS tmp)
        WHERE mi.Pid IN
        <foreach collection="machIds" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
        AND Tenantid = #{tid}
    </update>



</mapper>

