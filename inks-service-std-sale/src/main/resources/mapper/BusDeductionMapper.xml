<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusDeductionMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusDeductionPojo">
        SELECT Bus_Deduction.id,
        Bus_Deduction.RefNo,
        Bus_Deduction.BillType,
        Bus_Deduction.BillDate,
        Bus_Deduction.BillTitle,
        Bus_Deduction.Groupid,
        Bus_Deduction.Operator,
        Bus_Deduction.Taxrate,
        Bus_Deduction.BillTaxAmount,
        Bus_Deduction.BillAmount,
        Bus_Deduction.BillTaxTotal,
        Bus_Deduction.Summary,
        Bus_Deduction.CreateBy,
        Bus_Deduction.CreateByid,
        Bus_Deduction.CreateDate,
        Bus_Deduction.Lister,
        Bus_Deduction.Listerid,
        Bus_Deduction.ModifyDate,
        Bus_Deduction.Assessor,
        Bus_Deduction.Assessorid,
        Bus_Deduction.AssessDate,
        Bus_Deduction.ItemCount,
        Bus_Deduction.DisannulCount,
        Bus_Deduction.FinishCount,
        Bus_Deduction.PrintCount,
        Bus_Deduction.Custom1,
        Bus_Deduction.Custom2,
        Bus_Deduction.Custom3,
        Bus_Deduction.Custom4,
        Bus_Deduction.Custom5,
        Bus_Deduction.Custom6,
        Bus_Deduction.Custom7,
        Bus_Deduction.Custom8,
        Bus_Deduction.Custom9,
        Bus_Deduction.Custom10,
        Bus_Deduction.Tenantid,
        Bus_Deduction.TenantName,
        Bus_Deduction.Revision,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM App_Workgroup
        RIGHT JOIN Bus_Deduction ON App_Workgroup.id = Bus_Deduction.Groupid
        where Bus_Deduction.id = #{key}
        and Bus_Deduction.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_Deduction.id,
               Bus_Deduction.RefNo,
               Bus_Deduction.BillType,
               Bus_Deduction.BillDate,
               Bus_Deduction.BillTitle,
               Bus_Deduction.Groupid,
               Bus_Deduction.Operator,
               Bus_Deduction.Taxrate,
               Bus_Deduction.BillTaxAmount,
               Bus_Deduction.BillAmount,
               Bus_Deduction.BillTaxTotal,
               Bus_Deduction.Summary,
               Bus_Deduction.CreateBy,
               Bus_Deduction.CreateByid,
               Bus_Deduction.CreateDate,
               Bus_Deduction.Lister,
               Bus_Deduction.Listerid,
               Bus_Deduction.ModifyDate,
               Bus_Deduction.Assessor,
               Bus_Deduction.Assessorid,
               Bus_Deduction.AssessDate,
               Bus_Deduction.ItemCount,
               Bus_Deduction.DisannulCount,
               Bus_Deduction.FinishCount,
               Bus_Deduction.PrintCount,
               Bus_Deduction.Custom1,
               Bus_Deduction.Custom2,
               Bus_Deduction.Custom3,
               Bus_Deduction.Custom4,
               Bus_Deduction.Custom5,
               Bus_Deduction.Custom6,
               Bus_Deduction.Custom7,
               Bus_Deduction.Custom8,
               Bus_Deduction.Custom9,
               Bus_Deduction.Custom10,
               Bus_Deduction.Tenantid,
               Bus_Deduction.TenantName,
               Bus_Deduction.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_Deduction ON App_Workgroup.id = Bus_Deduction.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        Bus_DeductionItem.id,
        Bus_DeductionItem.Pid,
        Bus_DeductionItem.Goodsid,
        Bus_DeductionItem.Quantity,
        Bus_DeductionItem.TaxPrice,
        Bus_DeductionItem.TaxAmount,
        Bus_DeductionItem.TaxTotal,
        Bus_DeductionItem.ItemTaxrate,
        Bus_DeductionItem.Price,
        Bus_DeductionItem.Amount,
        Bus_DeductionItem.Remark,
        Bus_DeductionItem.CiteUid,
        Bus_DeductionItem.CiteItemid,
        Bus_DeductionItem.MachUid,
        Bus_DeductionItem.MachItemid,
        Bus_DeductionItem.CustPO,
        Bus_DeductionItem.RowNum,
        Bus_DeductionItem.InvoQty,
        Bus_DeductionItem.InvoClosed,
        Bus_DeductionItem.Custom1,
        Bus_DeductionItem.Custom2,
        Bus_DeductionItem.Custom3,
        Bus_DeductionItem.Custom4,
        Bus_DeductionItem.Custom5,
        Bus_DeductionItem.Custom6,
        Bus_DeductionItem.Custom7,
        Bus_DeductionItem.Custom8,
        Bus_DeductionItem.Custom9,
        Bus_DeductionItem.Custom10,
        Bus_DeductionItem.Tenantid,
        Bus_DeductionItem.Revision,
        Bus_Deduction.RefNo,
        Bus_Deduction.BillType,
        Bus_Deduction.BillDate,
        Bus_Deduction.BillTitle,
        Bus_Deduction.Operator,
        Bus_Deduction.CreateBy,
        Bus_Deduction.Lister,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM Bus_Deduction
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Bus_Deduction.Groupid
        RIGHT JOIN Bus_DeductionItem ON Bus_DeductionItem.Pid = Bus_Deduction.id
        LEFT JOIN Mat_Goods ON Bus_DeductionItem.Goodsid = Bus_Deduction.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDeductionitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Deduction.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_Deduction.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Bus_Deduction.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_Deduction.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_Deduction.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_Deduction.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Bus_Deduction.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_Deduction.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_Deduction.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_Deduction.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_Deduction.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_Deduction.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Bus_Deduction.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Bus_Deduction.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_Deduction.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_Deduction.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_Deduction.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_Deduction.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_Deduction.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_Deduction.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_Deduction.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_Deduction.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_Deduction.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_Deduction.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_Deduction.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_Deduction.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_Deduction.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_Deduction.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_Deduction.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Bus_Deduction.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_Deduction.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_Deduction.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_Deduction.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_Deduction.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_Deduction.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Bus_Deduction.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Bus_Deduction.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_Deduction.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_Deduction.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_Deduction.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_Deduction.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_Deduction.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_Deduction.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_Deduction.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_Deduction.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_Deduction.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_Deduction.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_Deduction.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDeductionPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Deduction.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_Deduction.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Bus_Deduction.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_Deduction.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_Deduction.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_Deduction.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Bus_Deduction.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_Deduction.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_Deduction.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_Deduction.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_Deduction.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_Deduction.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Bus_Deduction.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Bus_Deduction.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_Deduction.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_Deduction.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_Deduction.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_Deduction.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_Deduction.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_Deduction.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_Deduction.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_Deduction.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_Deduction.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_Deduction.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_Deduction.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_Deduction.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_Deduction.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_Deduction.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_Deduction.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Bus_Deduction.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_Deduction.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_Deduction.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_Deduction.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_Deduction.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_Deduction.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Bus_Deduction.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Bus_Deduction.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_Deduction.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_Deduction.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_Deduction.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_Deduction.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_Deduction.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_Deduction.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_Deduction.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_Deduction.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_Deduction.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_Deduction.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_Deduction.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_Deduction(id, RefNo, BillType, BillDate, BillTitle, Groupid, Operator, Taxrate, BillTaxAmount,
        BillAmount, BillTaxTotal, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid,
        ModifyDate, Assessor, Assessorid, AssessDate, ItemCount, DisannulCount, FinishCount,
        PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
        Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{groupid}, #{operator}, #{taxrate},
        #{billtaxamount}, #{billamount}, #{billtaxtotal}, #{summary}, #{createby}, #{createbyid}, #{createdate},
        #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{itemcount},
        #{disannulcount}, #{finishcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
        #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname},
        #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Deduction
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Deduction
        where id = #{key}
        and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Deduction
        SET Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
    <update id="updatePrintcount">
        update Bus_Deduction
        SET PrintCount = #{printcount}
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusDeductionPojo">
        select
        id
        from Bus_DeductionItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--  查询往来单位是否被引用  -->
    <select id="getItemCiteBillName" resultType="string">
        (SELECT '销售开票' as billname From Bus_InvoiceItem where DeliItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '销售账单' as billname From Bus_AccountItem where Billid = #{pid} and Tenantid = #{tid} LIMIT 1)
    </select>
</mapper>

