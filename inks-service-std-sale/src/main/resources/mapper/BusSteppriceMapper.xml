<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusSteppriceMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusSteppricePojo">
        SELECT Bus_StepPrice.id,
               Bus_StepPrice.RefNo,
               Bus_StepPrice.BillType,
               Bus_StepPrice.BillTitle,
               Bus_StepPrice.BillDate,
               Bus_StepPrice.Groupid,
               Bus_StepPrice.GroupLevel,
               Bus_StepPrice.MachType,
               Bus_StepPrice.Operator,
               Bus_StepPrice.StartDate,
               Bus_StepPrice.EndDate,
               Bus_StepPrice.CreateBy,
               Bus_StepPrice.CreateByid,
               Bus_StepPrice.CreateDate,
               Bus_StepPrice.Lister,
               Bus_StepPrice.Listerid,
               Bus_StepPrice.ModifyDate,
               Bus_StepPrice.Assessor,
               Bus_StepPrice.Assessorid,
               Bus_StepPrice.AssessDate,
               Bus_StepPrice.Summary,
               Bus_StepPrice.Custom1,
               Bus_StepPrice.Custom2,
               Bus_StepPrice.Custom3,
               Bus_StepPrice.Custom4,
               Bus_StepPrice.Custom5,
               Bus_StepPrice.Custom6,
               Bus_StepPrice.Custom7,
               Bus_StepPrice.Custom8,
               Bus_StepPrice.Custom9,
               Bus_StepPrice.Custom10,
               Bus_StepPrice.Tenantid,
               Bus_StepPrice.TenantName,
               Bus_StepPrice.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_StepPrice ON Bus_StepPrice.Groupid = App_Workgroup.id
        where Bus_StepPrice.id = #{key}
          and Bus_StepPrice.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_StepPrice.id,
               Bus_StepPrice.RefNo,
               Bus_StepPrice.BillType,
               Bus_StepPrice.BillTitle,
               Bus_StepPrice.BillDate,
               Bus_StepPrice.Groupid,
               Bus_StepPrice.GroupLevel,
               Bus_StepPrice.MachType,
               Bus_StepPrice.Operator,
               Bus_StepPrice.StartDate,
               Bus_StepPrice.EndDate,
               Bus_StepPrice.CreateBy,
               Bus_StepPrice.CreateByid,
               Bus_StepPrice.CreateDate,
               Bus_StepPrice.Lister,
               Bus_StepPrice.Listerid,
               Bus_StepPrice.ModifyDate,
               Bus_StepPrice.Assessor,
               Bus_StepPrice.Assessorid,
               Bus_StepPrice.AssessDate,
               Bus_StepPrice.Summary,
               Bus_StepPrice.Custom1,
               Bus_StepPrice.Custom2,
               Bus_StepPrice.Custom3,
               Bus_StepPrice.Custom4,
               Bus_StepPrice.Custom5,
               Bus_StepPrice.Custom6,
               Bus_StepPrice.Custom7,
               Bus_StepPrice.Custom8,
               Bus_StepPrice.Custom9,
               Bus_StepPrice.Custom10,
               Bus_StepPrice.Tenantid,
               Bus_StepPrice.TenantName,
               Bus_StepPrice.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_StepPrice ON Bus_StepPrice.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT Bus_StepPrice.id,
               Bus_StepPrice.RefNo,
               Bus_StepPrice.BillType,
               Bus_StepPrice.BillTitle,
               Bus_StepPrice.BillDate,
               Bus_StepPrice.Groupid,
               Bus_StepPrice.GroupLevel,
               Bus_StepPrice.MachType,
               Bus_StepPrice.Operator,
               Bus_StepPrice.StartDate,
               Bus_StepPrice.EndDate,
               Bus_StepPrice.CreateBy,
               Bus_StepPrice.CreateByid,
               Bus_StepPrice.CreateDate,
               Bus_StepPrice.Lister,
               Bus_StepPrice.Listerid,
               Bus_StepPrice.ModifyDate,
               Bus_StepPrice.Assessor,
               Bus_StepPrice.Assessorid,
               Bus_StepPrice.AssessDate,
               Bus_StepPrice.Summary,
               Bus_StepPrice.Custom1,
               Bus_StepPrice.Custom2,
               Bus_StepPrice.Custom3,
               Bus_StepPrice.Custom4,
               Bus_StepPrice.Custom5,
               Bus_StepPrice.Custom6,
               Bus_StepPrice.Custom7,
               Bus_StepPrice.Custom8,
               Bus_StepPrice.Custom9,
               Bus_StepPrice.Custom10,
               Bus_StepPrice.Tenantid,
               Bus_StepPrice.TenantName,
               Bus_StepPrice.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_StepPrice ON Bus_StepPrice.Groupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusSteppriceitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_StepPrice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_StepPrice.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Bus_StepPrice.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_StepPrice.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_StepPrice.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_StepPrice.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.grouplevel != null ">
            and Bus_StepPrice.grouplevel like concat('%', #{SearchPojo.grouplevel}, '%')
        </if>
        <if test="SearchPojo.machtype != null ">
            and Bus_StepPrice.machtype like concat('%', #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_StepPrice.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_StepPrice.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_StepPrice.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_StepPrice.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_StepPrice.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_StepPrice.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_StepPrice.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_StepPrice.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_StepPrice.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_StepPrice.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_StepPrice.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_StepPrice.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_StepPrice.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_StepPrice.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_StepPrice.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_StepPrice.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_StepPrice.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_StepPrice.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_StepPrice.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_StepPrice.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_StepPrice.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_StepPrice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_StepPrice.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.grouplevel != null ">
                or Bus_StepPrice.GroupLevel like concat('%', #{SearchPojo.grouplevel}, '%')
            </if>
            <if test="SearchPojo.machtype != null ">
                or Bus_StepPrice.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_StepPrice.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_StepPrice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_StepPrice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_StepPrice.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_StepPrice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_StepPrice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_StepPrice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_StepPrice.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_StepPrice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_StepPrice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_StepPrice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_StepPrice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_StepPrice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_StepPrice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_StepPrice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_StepPrice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_StepPrice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_StepPrice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_StepPrice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusSteppricePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_StepPrice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_StepPrice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Bus_StepPrice.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_StepPrice.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_StepPrice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_StepPrice.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.grouplevel != null ">
            and Bus_StepPrice.GroupLevel like concat('%', #{SearchPojo.grouplevel}, '%')
        </if>
        <if test="SearchPojo.machtype != null ">
            and Bus_StepPrice.MachType like concat('%', #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_StepPrice.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_StepPrice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_StepPrice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_StepPrice.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_StepPrice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_StepPrice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_StepPrice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_StepPrice.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_StepPrice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_StepPrice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_StepPrice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_StepPrice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_StepPrice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_StepPrice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_StepPrice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_StepPrice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_StepPrice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_StepPrice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_StepPrice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_StepPrice.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_StepPrice.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_StepPrice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_StepPrice.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.grouplevel != null ">
                or Bus_StepPrice.GroupLevel like concat('%', #{SearchPojo.grouplevel}, '%')
            </if>
            <if test="SearchPojo.machtype != null ">
                or Bus_StepPrice.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_StepPrice.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_StepPrice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_StepPrice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_StepPrice.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_StepPrice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_StepPrice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_StepPrice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_StepPrice.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_StepPrice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_StepPrice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_StepPrice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_StepPrice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_StepPrice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_StepPrice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_StepPrice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_StepPrice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_StepPrice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_StepPrice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_StepPrice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_StepPrice(id, RefNo, BillType, BillTitle, BillDate, Groupid, GroupLevel, MachType, Operator,
                                  StartDate, EndDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
                                  Assessor, Assessorid, AssessDate, Summary, Custom1, Custom2, Custom3, Custom4,
                                  Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{groupid}, #{grouplevel}, #{machtype},
                #{operator}, #{startdate}, #{enddate}, #{createby}, #{createbyid}, #{createdate}, #{lister},
                #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{summary}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_StepPrice
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="grouplevel != null ">
                GroupLevel =#{grouplevel},
            </if>
            <if test="machtype != null ">
                MachType =#{machtype},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_StepPrice
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_StepPrice
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusSteppricePojo">
        select
        id
        from Bus_StepPriceItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--查询DelListIds-->
    <select id="getDelObjIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusSteppricePojo">
        select
        id
        from Bus_StepPriceObj
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="obj" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--查询单个-->
    <select id="getStepPriceByGroupid" resultType="inks.service.std.sale.domain.pojo.BusSteppriceitemPojo">
        SELECT Bus_StepPriceItem.id,
               Bus_StepPriceItem.Pid,
               Bus_StepPriceItem.StartQty,
               Bus_StepPriceItem.EndQty,
               Bus_StepPriceItem.Price,
               Bus_StepPriceItem.Amount,
               Bus_StepPriceItem.Rebate,
               Bus_StepPriceItem.RowNum,
               Bus_StepPriceItem.Remark,
               Bus_StepPriceItem.Custom1,
               Bus_StepPriceItem.Custom2,
               Bus_StepPriceItem.Custom3,
               Bus_StepPriceItem.Custom4,
               Bus_StepPriceItem.Custom5,
               Bus_StepPriceItem.Custom6,
               Bus_StepPriceItem.Custom7,
               Bus_StepPriceItem.Custom8,
               Bus_StepPriceItem.Custom9,
               Bus_StepPriceItem.Custom10,
               Bus_StepPriceItem.Tenantid,
               Bus_StepPriceItem.Revision
        FROM Bus_StepPrice
                 RIGHT JOIN Bus_StepPriceItem
                            ON Bus_StepPriceItem.Pid = Bus_StepPrice.id
                 LEFT JOIN Bus_StepPriceObj ON Bus_StepPriceObj.Pid = Bus_StepPrice.id
        where Bus_StepPriceObj.Goodsid = #{goodsid}
          AND Bus_StepPrice.Groupid = #{groupid}
          AND Bus_StepPriceItem.StartQty &lt;= #{qty}
          AND Bus_StepPriceItem.EndQty &gt;= #{qty}
          and Bus_StepPriceItem.Tenantid = #{tid} LIMIT 1
    </select>
    <!--查询单个-->
    <select id="getStepPriceByGroupLevel" resultType="inks.service.std.sale.domain.pojo.BusSteppriceitemPojo">
        SELECT Bus_StepPriceItem.id,
               Bus_StepPriceItem.Pid,
               Bus_StepPriceItem.StartQty,
               Bus_StepPriceItem.EndQty,
               Bus_StepPriceItem.Price,
               Bus_StepPriceItem.Amount,
               Bus_StepPriceItem.Rebate,
               Bus_StepPriceItem.RowNum,
               Bus_StepPriceItem.Remark,
               Bus_StepPriceItem.Custom1,
               Bus_StepPriceItem.Custom2,
               Bus_StepPriceItem.Custom3,
               Bus_StepPriceItem.Custom4,
               Bus_StepPriceItem.Custom5,
               Bus_StepPriceItem.Custom6,
               Bus_StepPriceItem.Custom7,
               Bus_StepPriceItem.Custom8,
               Bus_StepPriceItem.Custom9,
               Bus_StepPriceItem.Custom10,
               Bus_StepPriceItem.Tenantid,
               Bus_StepPriceItem.Revision
        FROM Bus_StepPrice
                 RIGHT JOIN Bus_StepPriceItem
                            ON Bus_StepPriceItem.Pid = Bus_StepPrice.id
                 LEFT JOIN Bus_StepPriceObj ON Bus_StepPriceObj.Pid = Bus_StepPrice.id
        where Bus_StepPriceObj.Goodsid = #{goodsid}
          AND Bus_StepPrice.GroupLevel = #{grouplevel}
          AND Bus_StepPriceItem.StartQty &lt;= #{qty}
          AND Bus_StepPriceItem.EndQty &gt;= #{qty}
          and Bus_StepPriceItem.Tenantid = #{tid} LIMIT 1
    </select>
</mapper>

