<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusDelicarryoverMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusDelicarryoverPojo">
        <include refid="selectBusDelicarryoverVo"/>
        where Bus_DeliCarryover.id = #{key} and Bus_DeliCarryover.Tenantid=#{tid}
    </select>
    <sql id="selectBusDelicarryoverVo">
        select id,
               Recid,
               Groupid,
               GroupName,
               GroupUid,
               Abbreviate,
               Goodsid,
               ItemCode,
               ItemName,
               ItemSpec,
               ItemUnit,
               PcsX,
               PcsY,
               SetX,
               SetY,
               Set2Pcs,
               PnlX,
               PnlY,
               Pnl2Pcs,
               OpenQty,
               OpenAmount,
               InQty,
               InAmount,
               OutQty,
               OutAmount,
               CloseQty,
               CloseAmount,
               Skuid,
               AttributeJson,
               CloseMark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Bus_DeliCarryover
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusDelicarryoverPojo">
        <include refid="selectBusDelicarryoverVo"/>
         where 1 = 1 and Bus_DeliCarryover.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_DeliCarryover.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.recid != null ">
   and Bus_DeliCarryover.Recid like concat('%', #{SearchPojo.recid}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Bus_DeliCarryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Bus_DeliCarryover.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.groupuid != null ">
   and Bus_DeliCarryover.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   and Bus_DeliCarryover.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null ">
   and Bus_DeliCarryover.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   and Bus_DeliCarryover.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null ">
   and Bus_DeliCarryover.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null ">
   and Bus_DeliCarryover.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.skuid != null ">
   and Bus_DeliCarryover.Skuid like concat('%', #{SearchPojo.skuid}, '%')
</if>
<if test="SearchPojo.attributejson != null ">
   and Bus_DeliCarryover.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Bus_DeliCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Bus_DeliCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Bus_DeliCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Bus_DeliCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Bus_DeliCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Bus_DeliCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Bus_DeliCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Bus_DeliCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Bus_DeliCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Bus_DeliCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Bus_DeliCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Bus_DeliCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Bus_DeliCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Bus_DeliCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.recid != null ">
   or Bus_DeliCarryover.Recid like concat('%', #{SearchPojo.recid}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Bus_DeliCarryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Bus_DeliCarryover.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.groupuid != null ">
   or Bus_DeliCarryover.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
</if>
<if test="SearchPojo.abbreviate != null ">
   or Bus_DeliCarryover.Abbreviate like concat('%', #{SearchPojo.abbreviate}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   or Bus_DeliCarryover.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null ">
   or Bus_DeliCarryover.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   or Bus_DeliCarryover.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null ">
   or Bus_DeliCarryover.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null ">
   or Bus_DeliCarryover.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.skuid != null ">
   or Bus_DeliCarryover.Skuid like concat('%', #{SearchPojo.skuid}, '%')
</if>
<if test="SearchPojo.attributejson != null ">
   or Bus_DeliCarryover.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Bus_DeliCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Bus_DeliCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Bus_DeliCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Bus_DeliCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Bus_DeliCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Bus_DeliCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Bus_DeliCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Bus_DeliCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Bus_DeliCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Bus_DeliCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Bus_DeliCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Bus_DeliCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Bus_DeliCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Bus_DeliCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_DeliCarryover(id, Recid, Groupid, GroupName, GroupUid, Abbreviate, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, PcsX, PcsY, SetX, SetY, Set2Pcs, PnlX, PnlY, Pnl2Pcs, OpenQty, OpenAmount, InQty, InAmount, OutQty, OutAmount, CloseQty, CloseAmount, Skuid, AttributeJson, CloseMark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{recid}, #{groupid}, #{groupname}, #{groupuid}, #{abbreviate}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{pcsx}, #{pcsy}, #{setx}, #{sety}, #{set2pcs}, #{pnlx}, #{pnly}, #{pnl2pcs}, #{openqty}, #{openamount}, #{inqty}, #{inamount}, #{outqty}, #{outamount}, #{closeqty}, #{closeamount}, #{skuid}, #{attributejson}, #{closemark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_DeliCarryover
        <set>
            <if test="recid != null ">
                Recid =#{recid},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="groupuid != null ">
                GroupUid =#{groupuid},
            </if>
            <if test="abbreviate != null ">
                Abbreviate =#{abbreviate},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="pcsx != null">
                PcsX =#{pcsx},
            </if>
            <if test="pcsy != null">
                PcsY =#{pcsy},
            </if>
            <if test="setx != null">
                SetX =#{setx},
            </if>
            <if test="sety != null">
                SetY =#{sety},
            </if>
            <if test="set2pcs != null">
                Set2Pcs =#{set2pcs},
            </if>
            <if test="pnlx != null">
                PnlX =#{pnlx},
            </if>
            <if test="pnly != null">
                PnlY =#{pnly},
            </if>
            <if test="pnl2pcs != null">
                Pnl2Pcs =#{pnl2pcs},
            </if>
            <if test="openqty != null">
                OpenQty =#{openqty},
            </if>
            <if test="openamount != null">
                OpenAmount =#{openamount},
            </if>
            <if test="inqty != null">
                InQty =#{inqty},
            </if>
            <if test="inamount != null">
                InAmount =#{inamount},
            </if>
            <if test="outqty != null">
                OutQty =#{outqty},
            </if>
            <if test="outamount != null">
                OutAmount =#{outamount},
            </if>
            <if test="closeqty != null">
                CloseQty =#{closeqty},
            </if>
            <if test="closeamount != null">
                CloseAmount =#{closeamount},
            </if>
            <if test="skuid != null ">
                Skuid =#{skuid},
            </if>
            <if test="attributejson != null ">
                AttributeJson =#{attributejson},
            </if>
            <if test="closemark != null">
                CloseMark =#{closemark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_DeliCarryover where id = #{key} and Tenantid=#{tid}
    </delete>
</mapper>

