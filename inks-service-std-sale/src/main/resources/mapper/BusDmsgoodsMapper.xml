<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusDmsgoodsMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusDmsgoodsPojo">
        <include refid="selectBusDmsgoodsVo"/>
        where Bus_DmsGoods.id = #{key}
          and Bus_DmsGoods.Tenantid = #{tid}
    </select>

    <sql id="selectBusDmsgoodsVo">
        select Bus_DmsGoods.id,
               Bus_DmsGoods.GoodsUid,
               Bus_DmsGoods.GoodsName,
               Bus_DmsGoods.GoodsSpec,
               Bus_DmsGoods.GoodsUnit,
               Bus_DmsGoods.OriPrice,
               Bus_DmsGoods.Price,
               Bus_DmsGoods.Structure,
               Bus_DmsGoods.Material,
               Bus_DmsGoods.Exponent,
               Bus_DmsGoods.ProdCycle,
               Bus_DmsGoods.Process,
               Bus_DmsGoods.Brief,
               Bus_DmsGoods.Content,
               Bus_DmsGoods.Color,
               Bus_DmsGoods.Level,
               Bus_DmsGoods.AttributeJson,
               Bus_DmsGoods.AttributeStr,
               Bus_DmsGoods.Pic,
               Bus_DmsGoods.Imgs,
               Bus_DmsGoods.Status,
               Bus_DmsGoods.Categoryid,
               Bus_DmsGoods.SoldNum,
               Bus_DmsGoods.TotalStocks,
               Bus_DmsGoods.DeliveryMode,
               Bus_DmsGoods.PutawayDate,
               Bus_DmsGoods.MainMark,
               Bus_DmsGoods.RowNum,
               Bus_DmsGoods.Remark,
               Bus_DmsGoods.CreateBy,
               Bus_DmsGoods.CreateByid,
               Bus_DmsGoods.CreateDate,
               Bus_DmsGoods.Lister,
               Bus_DmsGoods.Listerid,
               Bus_DmsGoods.ModifyDate,
               Bus_DmsGoods.Custom1,
               Bus_DmsGoods.Custom2,
               Bus_DmsGoods.Custom3,
               Bus_DmsGoods.Custom4,
               Bus_DmsGoods.Custom5,
               Bus_DmsGoods.Custom6,
               Bus_DmsGoods.Custom7,
               Bus_DmsGoods.Custom8,
               Bus_DmsGoods.Custom9,
               Bus_DmsGoods.Custom10,
               Bus_DmsGoods.Tenantid,
               Bus_DmsGoods.TenantName,
               Bus_DmsGoods.Revision
        from Bus_DmsGoods
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDmsgoodsPojo">
        <include refid="selectBusDmsgoodsVo"/>
        where 1 = 1
          and Bus_DmsGoods.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_DmsGoods.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.goodsuid != null">
            and Bus_DmsGoods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null">
            and Bus_DmsGoods.GoodsName like concat('%',
                #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null">
            and Bus_DmsGoods.GoodsSpec like concat('%',
                #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.goodsunit != null">
            and Bus_DmsGoods.GoodsUnit like concat('%',
                #{SearchPojo.goodsunit}, '%')
        </if>
        <if test="SearchPojo.structure != null">
            and Bus_DmsGoods.Structure like concat('%',
                #{SearchPojo.structure}, '%')
        </if>
        <if test="SearchPojo.material != null">
            and Bus_DmsGoods.Material like concat('%',
                #{SearchPojo.material}, '%')
        </if>
        <if test="SearchPojo.prodcycle != null">
            and Bus_DmsGoods.ProdCycle like concat('%',
                #{SearchPojo.prodcycle}, '%')
        </if>
        <if test="SearchPojo.process != null">
            and Bus_DmsGoods.Process like concat('%',
                #{SearchPojo.process}, '%')
        </if>
        <if test="SearchPojo.brief != null">
            and Bus_DmsGoods.Brief like concat('%',
                #{SearchPojo.brief}, '%')
        </if>
        <if test="SearchPojo.content != null">
            and Bus_DmsGoods.Content like concat('%',
                #{SearchPojo.content}, '%')
        </if>
        <if test="SearchPojo.color != null">
            and Bus_DmsGoods.Color like concat('%',
                #{SearchPojo.color}, '%')
        </if>
        <if test="SearchPojo.level != null">
            and Bus_DmsGoods.Level like concat('%',
                #{SearchPojo.level}, '%')
        </if>
        <if test="SearchPojo.attributejson != null">
            and Bus_DmsGoods.AttributeJson like concat('%',
                #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.attributestr != null">
            and Bus_DmsGoods.AttributeStr like concat('%',
                #{SearchPojo.attributestr}, '%')
        </if>
        <if test="SearchPojo.pic != null">
            and Bus_DmsGoods.Pic like concat('%',
                #{SearchPojo.pic}, '%')
        </if>
        <if test="SearchPojo.imgs != null">
            and Bus_DmsGoods.Imgs like concat('%',
                #{SearchPojo.imgs}, '%')
        </if>
        <if test="SearchPojo.categoryid != null">
            and Bus_DmsGoods.Categoryid like concat('%',
                #{SearchPojo.categoryid}, '%')
        </if>
        <if test="SearchPojo.deliverymode != null">
            and Bus_DmsGoods.DeliveryMode like concat('%',
                #{SearchPojo.deliverymode}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Bus_DmsGoods.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_DmsGoods.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_DmsGoods.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_DmsGoods.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_DmsGoods.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_DmsGoods.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_DmsGoods.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_DmsGoods.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_DmsGoods.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_DmsGoods.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_DmsGoods.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_DmsGoods.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_DmsGoods.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_DmsGoods.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_DmsGoods.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_DmsGoods.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.goodsuid != null">
                or Bus_DmsGoods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
            </if>
            <if test="SearchPojo.goodsname != null">
                or Bus_DmsGoods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
            </if>
            <if test="SearchPojo.goodsspec != null">
                or Bus_DmsGoods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
            </if>
            <if test="SearchPojo.goodsunit != null">
                or Bus_DmsGoods.GoodsUnit like concat('%', #{SearchPojo.goodsunit}, '%')
            </if>
            <if test="SearchPojo.structure != null">
                or Bus_DmsGoods.Structure like concat('%', #{SearchPojo.structure}, '%')
            </if>
            <if test="SearchPojo.material != null">
                or Bus_DmsGoods.Material like concat('%', #{SearchPojo.material}, '%')
            </if>
            <if test="SearchPojo.prodcycle != null">
                or Bus_DmsGoods.ProdCycle like concat('%', #{SearchPojo.prodcycle}, '%')
            </if>
            <if test="SearchPojo.process != null">
                or Bus_DmsGoods.Process like concat('%', #{SearchPojo.process}, '%')
            </if>
            <if test="SearchPojo.brief != null">
                or Bus_DmsGoods.Brief like concat('%', #{SearchPojo.brief}, '%')
            </if>
            <if test="SearchPojo.content != null">
                or Bus_DmsGoods.Content like concat('%', #{SearchPojo.content}, '%')
            </if>
            <if test="SearchPojo.color != null">
                or Bus_DmsGoods.Color like concat('%', #{SearchPojo.color}, '%')
            </if>
            <if test="SearchPojo.level != null">
                or Bus_DmsGoods.Level like concat('%', #{SearchPojo.level}, '%')
            </if>
            <if test="SearchPojo.attributejson != null">
                or Bus_DmsGoods.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.attributestr != null">
                or Bus_DmsGoods.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
            </if>
            <if test="SearchPojo.pic != null">
                or Bus_DmsGoods.Pic like concat('%', #{SearchPojo.pic}, '%')
            </if>
            <if test="SearchPojo.imgs != null">
                or Bus_DmsGoods.Imgs like concat('%', #{SearchPojo.imgs}, '%')
            </if>
            <if test="SearchPojo.categoryid != null">
                or Bus_DmsGoods.Categoryid like concat('%', #{SearchPojo.categoryid}, '%')
            </if>
            <if test="SearchPojo.deliverymode != null">
                or Bus_DmsGoods.DeliveryMode like concat('%', #{SearchPojo.deliverymode}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Bus_DmsGoods.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_DmsGoods.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_DmsGoods.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_DmsGoods.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_DmsGoods.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_DmsGoods.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_DmsGoods.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_DmsGoods.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_DmsGoods.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_DmsGoods.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_DmsGoods.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_DmsGoods.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_DmsGoods.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_DmsGoods.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_DmsGoods.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_DmsGoods.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_DmsGoods(id, GoodsUid, GoodsName, GoodsSpec, GoodsUnit, OriPrice, Price, Structure,
                                 Material,
                                 Exponent, ProdCycle, Process, Brief, Content, Color, Level, AttributeJson,
                                 AttributeStr, Pic, Imgs, Status, Categoryid, SoldNum, TotalStocks, DeliveryMode,
                                 PutawayDate, MainMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister,
                                 Listerid,
                                 ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                 Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{goodsuid}, #{goodsname}, #{goodsspec}, #{goodsunit}, #{oriprice}, #{price},
                #{structure},
                #{material}, #{exponent}, #{prodcycle}, #{process}, #{brief}, #{content}, #{color}, #{level},
                #{attributejson}, #{attributestr}, #{pic}, #{imgs}, #{status}, #{categoryid}, #{soldnum},
                #{totalstocks}, #{deliverymode}, #{putawaydate}, #{mainmark}, #{rownum}, #{remark}, #{createby},
                #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_DmsGoods
        <set>
            <if test="goodsuid != null">
                GoodsUid =#{goodsuid},
            </if>
            <if test="goodsname != null">
                GoodsName =#{goodsname},
            </if>
            <if test="goodsspec != null">
                GoodsSpec =#{goodsspec},
            </if>
            <if test="goodsunit != null">
                GoodsUnit =#{goodsunit},
            </if>
            <if test="oriprice != null">
                OriPrice =#{oriprice},
            </if>
            <if test="price != null">
                Price =#{price},
            </if>
            <if test="structure != null">
                Structure =#{structure},
            </if>
            <if test="material != null">
                Material =#{material},
            </if>
            <if test="exponent != null">
                Exponent =#{exponent},
            </if>
            <if test="prodcycle != null">
                ProdCycle =#{prodcycle},
            </if>
            <if test="process != null">
                Process =#{process},
            </if>
            <if test="brief != null">
                Brief =#{brief},
            </if>
            <if test="content != null">
                Content =#{content},
            </if>
            <if test="color != null">
                Color =#{color},
            </if>
            <if test="level != null">
                Level =#{level},
            </if>
            <if test="attributejson != null">
                AttributeJson =#{attributejson},
            </if>
            <if test="attributestr != null">
                AttributeStr =#{attributestr},
            </if>
            <if test="pic != null">
                Pic =#{pic},
            </if>
            <if test="imgs != null">
                Imgs =#{imgs},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="categoryid != null">
                Categoryid =#{categoryid},
            </if>
            <if test="soldnum != null">
                SoldNum =#{soldnum},
            </if>
            <if test="totalstocks != null">
                TotalStocks =#{totalstocks},
            </if>
            <if test="deliverymode != null">
                DeliveryMode =#{deliverymode},
            </if>
            <if test="putawaydate != null">
                PutawayDate =#{putawaydate},
            </if>
            <if test="mainmark != null">
                MainMark =#{mainmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_DmsGoods
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <select id="getMaxCode" resultType="String">
        select GoodsUid
        From Bus_DmsGoods
        where Tenantid = #{tenantid}
        Order By GoodsUid DESC
        LIMIT 1
    </select>

    <select id="checkGoodsUid" resultType="int">
        select count(1)
        From Bus_DmsGoods
        where Tenantid = #{tid}
          and GoodsUid = #{goodsuid}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
</mapper>

