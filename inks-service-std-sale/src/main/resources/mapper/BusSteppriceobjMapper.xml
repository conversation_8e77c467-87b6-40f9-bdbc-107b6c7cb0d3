<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusSteppriceobjMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusSteppriceobjPojo">
        SELECT
            Bus_StepPriceObj.id,
            Bus_StepPriceObj.Pid,
            Bus_StepPriceObj.Goodsid,
            Bus_StepPriceObj.ItemCode,
            Bus_StepPriceObj.ItemName,
            Bus_StepPriceObj.ItemSpec,
            Bus_StepPriceObj.ItemUnit,
            Bus_StepPriceObj.RowNum,
            Bus_StepPriceObj.Remark,
            Bus_StepPriceObj.Custom1,
            Bus_StepPriceObj.Custom2,
            Bus_StepPriceObj.Custom3,
            Bus_StepPriceObj.Custom4,
            Bus_StepPriceObj.Custom5,
            Bus_StepPriceObj.Custom6,
            Bus_StepPriceObj.Custom7,
            Bus_StepPriceObj.Custom8,
            Bus_StepPriceObj.Custom9,
            Bus_StepPriceObj.Custom10,
            Bus_StepPriceObj.Tenantid,
            Bus_StepPriceObj.Revision,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM
            Mat_Goods
                RIGHT JOIN Bus_StepPriceObj ON Mat_Goods.id = Bus_StepPriceObj.Goodsid
        where Bus_StepPriceObj.id = #{key}
          and Bus_StepPriceObj.Tenantid = #{tid}
    </select>
    <sql id="selectBusSteppriceobjVo">
        SELECT
            Bus_StepPriceObj.id,
            Bus_StepPriceObj.Pid,
            Bus_StepPriceObj.Goodsid,
            Bus_StepPriceObj.ItemCode,
            Bus_StepPriceObj.ItemName,
            Bus_StepPriceObj.ItemSpec,
            Bus_StepPriceObj.ItemUnit,
            Bus_StepPriceObj.RowNum,
            Bus_StepPriceObj.Remark,
            Bus_StepPriceObj.Custom1,
            Bus_StepPriceObj.Custom2,
            Bus_StepPriceObj.Custom3,
            Bus_StepPriceObj.Custom4,
            Bus_StepPriceObj.Custom5,
            Bus_StepPriceObj.Custom6,
            Bus_StepPriceObj.Custom7,
            Bus_StepPriceObj.Custom8,
            Bus_StepPriceObj.Custom9,
            Bus_StepPriceObj.Custom10,
            Bus_StepPriceObj.Tenantid,
            Bus_StepPriceObj.Revision,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM
            Mat_Goods
                RIGHT JOIN Bus_StepPriceObj ON Mat_Goods.id = Bus_StepPriceObj.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusSteppriceobjPojo">
        <include refid="selectBusSteppriceobjVo"/>
        where 1 = 1 and Bus_StepPriceObj.Tenantid =#{Tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_StepPriceObj.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_StepPriceObj.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_StepPriceObj.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Bus_StepPriceObj.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_StepPriceObj.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_StepPriceObj.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_StepPriceObj.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_StepPriceObj.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_StepPriceObj.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_StepPriceObj.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_StepPriceObj.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_StepPriceObj.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_StepPriceObj.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_StepPriceObj.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_StepPriceObj.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_StepPriceObj.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_StepPriceObj.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_StepPriceObj.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_StepPriceObj.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_StepPriceObj.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Bus_StepPriceObj.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_StepPriceObj.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_StepPriceObj.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_StepPriceObj.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_StepPriceObj.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_StepPriceObj.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_StepPriceObj.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_StepPriceObj.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_StepPriceObj.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_StepPriceObj.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_StepPriceObj.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_StepPriceObj.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_StepPriceObj.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_StepPriceObj.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_StepPriceObj.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.sale.domain.pojo.BusSteppriceobjPojo">
        SELECT
            Bus_StepPriceObj.id,
            Bus_StepPriceObj.Pid,
            Bus_StepPriceObj.Goodsid,
            Bus_StepPriceObj.ItemCode,
            Bus_StepPriceObj.ItemName,
            Bus_StepPriceObj.ItemSpec,
            Bus_StepPriceObj.ItemUnit,
            Bus_StepPriceObj.RowNum,
            Bus_StepPriceObj.Remark,
            Bus_StepPriceObj.Custom1,
            Bus_StepPriceObj.Custom2,
            Bus_StepPriceObj.Custom3,
            Bus_StepPriceObj.Custom4,
            Bus_StepPriceObj.Custom5,
            Bus_StepPriceObj.Custom6,
            Bus_StepPriceObj.Custom7,
            Bus_StepPriceObj.Custom8,
            Bus_StepPriceObj.Custom9,
            Bus_StepPriceObj.Custom10,
            Bus_StepPriceObj.Tenantid,
            Bus_StepPriceObj.Revision,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM
            Mat_Goods
                RIGHT JOIN Bus_StepPriceObj ON Mat_Goods.id = Bus_StepPriceObj.Goodsid
        where Bus_StepPriceObj.Pid = #{Pid}
          and Bus_StepPriceObj.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_StepPriceObj(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, RowNum, Remark, Custom1,
                                     Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                     Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{rownum}, #{remark},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_StepPriceObj
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_StepPriceObj
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

