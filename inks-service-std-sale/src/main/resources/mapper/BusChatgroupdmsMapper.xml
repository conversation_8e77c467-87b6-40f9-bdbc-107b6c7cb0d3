<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusChatgroupdmsMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusChatgroupdmsPojo">
        select
          id, Pid, Dmsid, DmsName, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision        from Bus_ChatGroupDms
        where Bus_ChatGroupDms.id = #{key} and Bus_ChatGroupDms.Tenantid=#{tid}
    </select>
    <sql id="selectBusChatgroupdmsVo">
         select
          id, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision        from Bus_ChatGroupDms
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusChatgroupdmsPojo">
        <include refid="selectBusChatgroupdmsVo"/>
         where 1 = 1 and Bus_ChatGroupDms.Tenantid =#{Tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_ChatGroupDms.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Bus_ChatGroupDms.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.dmsid != null and SearchPojo.dmsid != ''">
   and Bus_ChatGroupDms.dmsid like concat('%', #{SearchPojo.dmsid}, '%')
</if>
<if test="SearchPojo.dmsname != null and SearchPojo.dmsname != ''">
   and Bus_ChatGroupDms.dmsname like concat('%', #{SearchPojo.dmsname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Bus_ChatGroupDms.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   and Bus_ChatGroupDms.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   and Bus_ChatGroupDms.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Bus_ChatGroupDms.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   and Bus_ChatGroupDms.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Bus_ChatGroupDms.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Bus_ChatGroupDms.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Bus_ChatGroupDms.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Bus_ChatGroupDms.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   and Bus_ChatGroupDms.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Bus_ChatGroupDms.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.dmsid != null and SearchPojo.dmsid != ''">
   or Bus_ChatGroupDms.Dmsid like concat('%', #{SearchPojo.dmsid}, '%')
</if>
<if test="SearchPojo.dmsname != null and SearchPojo.dmsname != ''">
   or Bus_ChatGroupDms.DmsName like concat('%', #{SearchPojo.dmsname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Bus_ChatGroupDms.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or Bus_ChatGroupDms.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or Bus_ChatGroupDms.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Bus_ChatGroupDms.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Bus_ChatGroupDms.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Bus_ChatGroupDms.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Bus_ChatGroupDms.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Bus_ChatGroupDms.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Bus_ChatGroupDms.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   or Bus_ChatGroupDms.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.sale.domain.pojo.BusChatgroupdmsPojo">
        select
          id, Pid, Dmsid, DmsName, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision        from Bus_ChatGroupDms
        where Bus_ChatGroupDms.Pid = #{Pid} and Bus_ChatGroupDms.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_ChatGroupDms(id, Pid, Dmsid, DmsName, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{dmsid}, #{dmsname}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_ChatGroupDms
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="dmsid != null ">
                Dmsid = #{dmsid},
            </if>
            <if test="dmsname != null ">
                DmsName = #{dmsname},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="createby != null ">
                CreateBy = #{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid = #{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="listerid != null ">
                Listerid = #{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_ChatGroupDms where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

