<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusCarryoveritemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusCarryoveritemPojo">
        <include refid="selectBusCarryoveritemVo"/>
        where Bus_CarryoverItem.id = #{key} and Bus_CarryoverItem.Tenantid=#{tid}
    </select>
    <sql id="selectBusCarryoveritemVo">
         select
id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, OpenQty, OpenAmount, InQty, InAmount, OutQty, OutAmount, CloseQty, CloseAmount, Skuid, Attri<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Bus_CarryoverItem
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.sale.domain.pojo.BusCarryoveritemPojo">
        <include refid="selectBusCarryoveritemVo"/>
        where Bus_CarryoverItem.Pid = #{Pid} and Bus_CarryoverItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusCarryoveritemPojo">
        <include refid="selectBusCarryoveritemVo"/>
         where 1 = 1 and Bus_CarryoverItem.Tenantid =#{tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_CarryoverItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Bus_CarryoverItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Bus_CarryoverItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Bus_CarryoverItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Bus_CarryoverItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Bus_CarryoverItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Bus_CarryoverItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
   and Bus_CarryoverItem.skuid like concat('%', #{SearchPojo.skuid}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   and Bus_CarryoverItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Bus_CarryoverItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Bus_CarryoverItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Bus_CarryoverItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Bus_CarryoverItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Bus_CarryoverItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Bus_CarryoverItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Bus_CarryoverItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Bus_CarryoverItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Bus_CarryoverItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Bus_CarryoverItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Bus_CarryoverItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Bus_CarryoverItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Bus_CarryoverItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Bus_CarryoverItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Bus_CarryoverItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Bus_CarryoverItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
   or Bus_CarryoverItem.Skuid like concat('%', #{SearchPojo.skuid}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   or Bus_CarryoverItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Bus_CarryoverItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Bus_CarryoverItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Bus_CarryoverItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Bus_CarryoverItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Bus_CarryoverItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Bus_CarryoverItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Bus_CarryoverItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Bus_CarryoverItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Bus_CarryoverItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Bus_CarryoverItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_CarryoverItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, OpenQty, OpenAmount, InQty, InAmount, OutQty, OutAmount, CloseQty, CloseAmount, Skuid, AttributeJson, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{openqty}, #{openamount}, #{inqty}, #{inamount}, #{outqty}, #{outamount}, #{closeqty}, #{closeamount}, #{skuid}, #{attributejson}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_CarryoverItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="openqty != null">
                OpenQty = #{openqty},
            </if>
            <if test="openamount != null">
                OpenAmount = #{openamount},
            </if>
            <if test="inqty != null">
                InQty = #{inqty},
            </if>
            <if test="inamount != null">
                InAmount = #{inamount},
            </if>
            <if test="outqty != null">
                OutQty = #{outqty},
            </if>
            <if test="outamount != null">
                OutAmount = #{outamount},
            </if>
            <if test="closeqty != null">
                CloseQty = #{closeqty},
            </if>
            <if test="closeamount != null">
                CloseAmount = #{closeamount},
            </if>
            <if test="skuid != null ">
                Skuid = #{skuid},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_CarryoverItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

