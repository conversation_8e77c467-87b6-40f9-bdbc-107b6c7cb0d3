<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusCouponMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusCouponPojo">
        <include refid="selectBusCouponVo"/>
        where Bus_Coupon.id = #{key} and Bus_Coupon.Tenantid=#{tid}
    </select>
    <sql id="selectBusCouponVo">
         select
id, Groupid, CouponName, CouponCode, CouponType, CouponAmount, ActiveAmount, UsedAmount, UseLimitRate, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Remark, RowNum, DisannulMark, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, As<PERSON>sDate, <PERSON><PERSON><PERSON><PERSON>, <PERSON>reate<PERSON>yid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Bus_Coupon
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusCouponPojo">
        <include refid="selectBusCouponVo"/>
         where 1 = 1 and Bus_Coupon.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_Coupon.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.groupid != null ">
   and Bus_Coupon.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.couponname != null ">
   and Bus_Coupon.CouponName like concat('%', #{SearchPojo.couponname}, '%')
</if>
<if test="SearchPojo.couponcode != null ">
   and Bus_Coupon.CouponCode like concat('%', #{SearchPojo.couponcode}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   and Bus_Coupon.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null ">
   and Bus_Coupon.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   and Bus_Coupon.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null ">
   and Bus_Coupon.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null ">
   and Bus_Coupon.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Bus_Coupon.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.disannulby != null ">
   and Bus_Coupon.DisannulBy like concat('%', #{SearchPojo.disannulby}, '%')
</if>
<if test="SearchPojo.disannulbyid != null ">
   and Bus_Coupon.DisannulByid like concat('%', #{SearchPojo.disannulbyid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Bus_Coupon.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Bus_Coupon.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Bus_Coupon.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Bus_Coupon.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Bus_Coupon.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Bus_Coupon.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Bus_Coupon.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Bus_Coupon.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Bus_Coupon.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Bus_Coupon.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Bus_Coupon.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Bus_Coupon.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Bus_Coupon.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Bus_Coupon.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Bus_Coupon.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Bus_Coupon.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Bus_Coupon.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.groupid != null ">
   or Bus_Coupon.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.couponname != null ">
   or Bus_Coupon.CouponName like concat('%', #{SearchPojo.couponname}, '%')
</if>
<if test="SearchPojo.couponcode != null ">
   or Bus_Coupon.CouponCode like concat('%', #{SearchPojo.couponcode}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   or Bus_Coupon.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null ">
   or Bus_Coupon.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   or Bus_Coupon.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null ">
   or Bus_Coupon.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null ">
   or Bus_Coupon.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Bus_Coupon.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.disannulby != null ">
   or Bus_Coupon.DisannulBy like concat('%', #{SearchPojo.disannulby}, '%')
</if>
<if test="SearchPojo.disannulbyid != null ">
   or Bus_Coupon.DisannulByid like concat('%', #{SearchPojo.disannulbyid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Bus_Coupon.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Bus_Coupon.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Bus_Coupon.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Bus_Coupon.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Bus_Coupon.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Bus_Coupon.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Bus_Coupon.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Bus_Coupon.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Bus_Coupon.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Bus_Coupon.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Bus_Coupon.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Bus_Coupon.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Bus_Coupon.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Bus_Coupon.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Bus_Coupon.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Bus_Coupon.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Bus_Coupon.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_Coupon(id, Groupid, CouponName, CouponCode, CouponType, CouponAmount, ActiveAmount, UsedAmount, UseLimitRate, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Remark, RowNum, DisannulMark, DisannulBy, DisannulByid, DisannulDate, Assessor, Assessorid, AssessDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{groupid}, #{couponname}, #{couponcode}, #{coupontype}, #{couponamount}, #{activeamount}, #{usedamount}, #{uselimitrate}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{remark}, #{rownum}, #{disannulmark}, #{disannulby}, #{disannulbyid}, #{disannuldate}, #{assessor}, #{assessorid}, #{assessdate}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Coupon
        <set>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="couponname != null ">
                CouponName =#{couponname},
            </if>
            <if test="couponcode != null ">
                CouponCode =#{couponcode},
            </if>
            <if test="coupontype != null">
                CouponType =#{coupontype},
            </if>
            <if test="couponamount != null">
                CouponAmount =#{couponamount},
            </if>
            <if test="activeamount != null">
                ActiveAmount =#{activeamount},
            </if>
            <if test="usedamount != null">
                UsedAmount =#{usedamount},
            </if>
            <if test="uselimitrate != null">
                UseLimitRate =#{uselimitrate},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="disannulby != null ">
                DisannulBy =#{disannulby},
            </if>
            <if test="disannulbyid != null ">
                DisannulByid =#{disannulbyid},
            </if>
            <if test="disannuldate != null">
                DisannulDate =#{disannuldate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_Coupon where id = #{key} and Tenantid=#{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Coupon SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <select id="getidByCode" resultType="java.lang.String">
        select id from Bus_Coupon where CouponCode = #{couponcode} and Tenantid = #{tid}
    </select>
</mapper>

