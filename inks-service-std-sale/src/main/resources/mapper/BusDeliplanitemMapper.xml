<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusDeliplanitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusDeliplanitemPojo">
        <include refid="selectBusDeliplanitemVo"/>
        where Bus_DeliPlanItem.id = #{key} and Bus_DeliPlanItem.Tenantid=#{tid}
    </select>
    <sql id="selectBusDeliplanitemVo">
        select
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoods"/>
        Bus_DeliPlanItem.id,
        Bus_DeliPlanItem.Pid,
        Bus_DeliPlanItem.Goodsid,
        Bus_DeliPlanItem.ItemCode,
        Bus_DeliPlanItem.ItemName,
        Bus_DeliPlanItem.ItemSpec,
        Bus_DeliPlanItem.ItemUnit,
        Bus_DeliPlanItem.Quantity,
        Bus_DeliPlanItem.TaxPrice,
        Bus_DeliPlanItem.TaxAmount,
        Bus_DeliPlanItem.Price,
        Bus_DeliPlanItem.Amount,
        Bus_DeliPlanItem.ItemTaxrate,
        Bus_DeliPlanItem.TaxTotal,
        Bus_DeliPlanItem.StdPrice,
        Bus_DeliPlanItem.StdAmount,
        Bus_DeliPlanItem.Rebate,
        Bus_DeliPlanItem.FreeQty,
        Bus_DeliPlanItem.PickQty,
        Bus_DeliPlanItem.FinishQty,
        Bus_DeliPlanItem.Closed,
        Bus_DeliPlanItem.RowNum,
        Bus_DeliPlanItem.Remark,
        Bus_DeliPlanItem.CiteUid,
        Bus_DeliPlanItem.CiteItemid,
        Bus_DeliPlanItem.CustPo,
        Bus_DeliPlanItem.StateCode,
        Bus_DeliPlanItem.StateDate,
        Bus_DeliPlanItem.MachType,
        Bus_DeliPlanItem.VirtualItem,
        Bus_DeliPlanItem.Location,
        Bus_DeliPlanItem.BatchNo,
        Bus_DeliPlanItem.MachUid,
        Bus_DeliPlanItem.MachItemid,
        Bus_DeliPlanItem.DisannulMark,
        Bus_DeliPlanItem.DisannulLister,
        Bus_DeliPlanItem.DisannulDate,
        Bus_DeliPlanItem.AttributeJson,
        Bus_DeliPlanItem.MachDate,
        Bus_DeliPlanItem.SourceType,
        Bus_DeliPlanItem.ItemPlanDate,
        Bus_DeliPlanItem.Custom1,
        Bus_DeliPlanItem.Custom2,
        Bus_DeliPlanItem.Custom3,
        Bus_DeliPlanItem.Custom4,
        Bus_DeliPlanItem.Custom5,
        Bus_DeliPlanItem.Custom6,
        Bus_DeliPlanItem.Custom7,
        Bus_DeliPlanItem.Custom8,
        Bus_DeliPlanItem.Custom9,
        Bus_DeliPlanItem.Custom10,
        Bus_DeliPlanItem.Tenantid,
        Bus_DeliPlanItem.Revision
        from Bus_DeliPlanItem
                 left join Mat_Goods on Bus_DeliPlanItem.Goodsid = Mat_Goods.id
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.sale.domain.pojo.BusDeliplanitemPojo">
        <include refid="selectBusDeliplanitemVo"/>
        where Bus_DeliPlanItem.Pid = #{Pid} and Bus_DeliPlanItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusDeliplanitemPojo">
        <include refid="selectBusDeliplanitemVo"/>
         where 1 = 1 and Bus_DeliPlanItem.Tenantid =#{tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_DeliPlanItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Bus_DeliPlanItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Bus_DeliPlanItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Bus_DeliPlanItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Bus_DeliPlanItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Bus_DeliPlanItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Bus_DeliPlanItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Bus_DeliPlanItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
   and Bus_DeliPlanItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
   and Bus_DeliPlanItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   and Bus_DeliPlanItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   and Bus_DeliPlanItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
   and Bus_DeliPlanItem.machtype like concat('%', #{SearchPojo.machtype}, '%')
</if>
<if test="SearchPojo.location != null and SearchPojo.location != ''">
   and Bus_DeliPlanItem.location like concat('%', #{SearchPojo.location}, '%')
</if>
<if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
   and Bus_DeliPlanItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   and Bus_DeliPlanItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
   and Bus_DeliPlanItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
   and Bus_DeliPlanItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   and Bus_DeliPlanItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Bus_DeliPlanItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Bus_DeliPlanItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Bus_DeliPlanItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Bus_DeliPlanItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Bus_DeliPlanItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Bus_DeliPlanItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Bus_DeliPlanItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Bus_DeliPlanItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Bus_DeliPlanItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Bus_DeliPlanItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Bus_DeliPlanItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Bus_DeliPlanItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Bus_DeliPlanItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Bus_DeliPlanItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Bus_DeliPlanItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Bus_DeliPlanItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Bus_DeliPlanItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
   or Bus_DeliPlanItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
   or Bus_DeliPlanItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   or Bus_DeliPlanItem.CustPo like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   or Bus_DeliPlanItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
   or Bus_DeliPlanItem.MachType like concat('%', #{SearchPojo.machtype}, '%')
</if>
<if test="SearchPojo.location != null and SearchPojo.location != ''">
   or Bus_DeliPlanItem.Location like concat('%', #{SearchPojo.location}, '%')
</if>
<if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
   or Bus_DeliPlanItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   or Bus_DeliPlanItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
   or Bus_DeliPlanItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
   or Bus_DeliPlanItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   or Bus_DeliPlanItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Bus_DeliPlanItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Bus_DeliPlanItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Bus_DeliPlanItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Bus_DeliPlanItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Bus_DeliPlanItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Bus_DeliPlanItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Bus_DeliPlanItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Bus_DeliPlanItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Bus_DeliPlanItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Bus_DeliPlanItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_DeliPlanItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, Price, Amount, ItemTaxrate, TaxTotal, StdPrice, StdAmount, Rebate, FreeQty, PickQty, FinishQty, Closed, RowNum, Remark, CiteUid, CiteItemid, CustPo, StateCode, StateDate, MachType, VirtualItem, Location, BatchNo, MachUid, MachItemid, DisannulMark, DisannulLister, DisannulDate, AttributeJson, MachDate, SourceType, ItemPlanDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice}, #{taxamount}, #{price}, #{amount}, #{itemtaxrate}, #{taxtotal}, #{stdprice}, #{stdamount}, #{rebate}, #{freeqty}, #{pickqty}, #{finishqty}, #{closed}, #{rownum}, #{remark}, #{citeuid}, #{citeitemid}, #{custpo}, #{statecode}, #{statedate}, #{machtype}, #{virtualitem}, #{location}, #{batchno}, #{machuid}, #{machitemid}, #{disannulmark}, #{disannullister}, #{disannuldate}, #{attributejson}, #{machdate}, #{sourcetype}, #{itemplandate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_DeliPlanItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="stdprice != null">
                StdPrice = #{stdprice},
            </if>
            <if test="stdamount != null">
                StdAmount = #{stdamount},
            </if>
            <if test="rebate != null">
                Rebate = #{rebate},
            </if>
            <if test="freeqty != null">
                FreeQty = #{freeqty},
            </if>
            <if test="pickqty != null">
                PickQty = #{pickqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="custpo != null ">
                CustPo = #{custpo},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="machtype != null ">
                MachType = #{machtype},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="machdate != null">
                MachDate = #{machdate},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="itemplandate != null">
                ItemPlanDate = #{itemplandate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_DeliPlanItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

