<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusBudgetitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusBudgetitemPojo">
        select id,
               Pid,
               Goodsid,
               ItemCode,
               ItemName,
               ItemSpec,
               ItemUnit,
               Quantity,
               StdPrice,
               StdAmount,
               Rebate,
               TaxPrice,
               TaxAmount,
               ItemTaxrate,
               TaxTotal,
               Price,
               Amount,
               AttributeJson,
               ItemOrgDate,
               ItemPlanDate,
               StoQty,
               WkQty,
               RowNum,
               VirtualItem,
               MaxQty,
               Remark,
               MachUid,
               MachItemid,
               MatCost,
               LaborCost,
               DirectCost,
               IndirectCost,
               MatItemJson,
               LaborItemJson,
               DirectItemJson,
               IndirectItemJson,
               MatAmt,
               LaborAmt,
               DirectAmt,
               IndirectAmt,
               SourceType,
               AttaCount,
               DisannulMark,
               DisannulListerid,
               DisannulLister,
               DisannulDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Bus_BudgetItem
        where Bus_BudgetItem.id = #{key}
          and Bus_BudgetItem.Tenantid = #{tid}
    </select>
    <sql id="selectBusBudgetitemVo">
        select id,
               Pid,
               Goodsid,
               ItemCode,
               ItemName,
               ItemSpec,
               ItemUnit,
               Quantity,
               StdPrice,
               StdAmount,
               Rebate,
               TaxPrice,
               TaxAmount,
               ItemTaxrate,
               TaxTotal,
               Price,
               Amount,
               AttributeJson,
               ItemOrgDate,
               ItemPlanDate,
               StoQty,
               WkQty,
               RowNum,
               VirtualItem,
               MaxQty,
               Remark,
               MachUid,
               MachItemid,
               MatCost,
               LaborCost,
               DirectCost,
               IndirectCost,
               MatItemJson,
               LaborItemJson,
               DirectItemJson,
               IndirectItemJson,
               MatAmt,
               LaborAmt,
               DirectAmt,
               IndirectAmt,
               SourceType,
               AttaCount,
               DisannulMark,
               DisannulListerid,
               DisannulLister,
               DisannulDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Bus_BudgetItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusBudgetitemPojo">
        <include refid="selectBusBudgetitemVo"/>
        where 1 = 1 and Bus_BudgetItem.Tenantid =#{Tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_BudgetItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_BudgetItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_BudgetItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Bus_BudgetItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_BudgetItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_BudgetItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_BudgetItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Bus_BudgetItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_BudgetItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Bus_BudgetItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Bus_BudgetItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.matitemjson != null and SearchPojo.matitemjson != ''">
            and Bus_BudgetItem.matitemjson like concat('%', #{SearchPojo.matitemjson}, '%')
        </if>
        <if test="SearchPojo.laboritemjson != null and SearchPojo.laboritemjson != ''">
            and Bus_BudgetItem.laboritemjson like concat('%', #{SearchPojo.laboritemjson}, '%')
        </if>
        <if test="SearchPojo.directitemjson != null and SearchPojo.directitemjson != ''">
            and Bus_BudgetItem.directitemjson like concat('%', #{SearchPojo.directitemjson}, '%')
        </if>
        <if test="SearchPojo.indirectitemjson != null and SearchPojo.indirectitemjson != ''">
            and Bus_BudgetItem.indirectitemjson like concat('%', #{SearchPojo.indirectitemjson}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Bus_BudgetItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Bus_BudgetItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_BudgetItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_BudgetItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_BudgetItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_BudgetItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_BudgetItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_BudgetItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_BudgetItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_BudgetItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_BudgetItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_BudgetItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_BudgetItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_BudgetItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Bus_BudgetItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_BudgetItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_BudgetItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_BudgetItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Bus_BudgetItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_BudgetItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Bus_BudgetItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Bus_BudgetItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.matitemjson != null and SearchPojo.matitemjson != ''">
                or Bus_BudgetItem.MatItemJson like concat('%', #{SearchPojo.matitemjson}, '%')
            </if>
            <if test="SearchPojo.laboritemjson != null and SearchPojo.laboritemjson != ''">
                or Bus_BudgetItem.LaborItemJson like concat('%', #{SearchPojo.laboritemjson}, '%')
            </if>
            <if test="SearchPojo.directitemjson != null and SearchPojo.directitemjson != ''">
                or Bus_BudgetItem.DirectItemJson like concat('%', #{SearchPojo.directitemjson}, '%')
            </if>
            <if test="SearchPojo.indirectitemjson != null and SearchPojo.indirectitemjson != ''">
                or Bus_BudgetItem.IndirectItemJson like concat('%', #{SearchPojo.indirectitemjson}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Bus_BudgetItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Bus_BudgetItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_BudgetItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_BudgetItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_BudgetItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_BudgetItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_BudgetItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_BudgetItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_BudgetItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_BudgetItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_BudgetItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_BudgetItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.sale.domain.pojo.BusBudgetitemPojo">
        select Bus_BudgetItem.id,
               Bus_BudgetItem.Pid,
               Bus_BudgetItem.Goodsid,
               Bus_BudgetItem.ItemCode,
               Bus_BudgetItem.ItemName,
               Bus_BudgetItem.ItemSpec,
               Bus_BudgetItem.ItemUnit,
               Bus_BudgetItem.Quantity,
               Bus_BudgetItem.StdPrice,
               Bus_BudgetItem.StdAmount,
               Bus_BudgetItem.Rebate,
               Bus_BudgetItem.TaxPrice,
               Bus_BudgetItem.TaxAmount,
               Bus_BudgetItem.ItemTaxrate,
               Bus_BudgetItem.TaxTotal,
               Bus_BudgetItem.Price,
               Bus_BudgetItem.Amount,
               Bus_BudgetItem.AttributeJson,
               Bus_BudgetItem.ItemOrgDate,
               Bus_BudgetItem.ItemPlanDate,
               Bus_BudgetItem.StoQty,
               Bus_BudgetItem.WkQty,
               Bus_BudgetItem.RowNum,
               Bus_BudgetItem.VirtualItem,
               Bus_BudgetItem.MaxQty,
               Bus_BudgetItem.Remark,
               Bus_BudgetItem.MachUid,
               Bus_BudgetItem.MachItemid,
               Bus_BudgetItem.MatCost,
               Bus_BudgetItem.LaborCost,
               Bus_BudgetItem.DirectCost,
               Bus_BudgetItem.IndirectCost,
               Bus_BudgetItem.MatItemJson,
               Bus_BudgetItem.LaborItemJson,
               Bus_BudgetItem.DirectItemJson,
               Bus_BudgetItem.IndirectItemJson,
               Bus_BudgetItem.MatAmt,
               Bus_BudgetItem.LaborAmt,
               Bus_BudgetItem.DirectAmt,
               Bus_BudgetItem.IndirectAmt,
               Bus_BudgetItem.SourceType,
               Bus_BudgetItem.AttaCount,
               Bus_BudgetItem.DisannulMark,
               Bus_BudgetItem.DisannulListerid,
               Bus_BudgetItem.DisannulLister,
               Bus_BudgetItem.DisannulDate,
               Bus_BudgetItem.Custom1,
               Bus_BudgetItem.Custom2,
               Bus_BudgetItem.Custom3,
               Bus_BudgetItem.Custom4,
               Bus_BudgetItem.Custom5,
               Bus_BudgetItem.Custom6,
               Bus_BudgetItem.Custom7,
               Bus_BudgetItem.Custom8,
               Bus_BudgetItem.Custom9,
               Bus_BudgetItem.Custom10,
               Bus_BudgetItem.Tenantid,
               Bus_BudgetItem.Revision,
               Mat_Goods.GoodsUid as itemcode,
               Mat_Goods.GoodsName as itemname,
               Mat_Goods.GoodsSpec as itemspec,
               Mat_Goods.GoodsUnit as itemunit
        from Bus_BudgetItem LEFT JOIN Mat_Goods ON Bus_BudgetItem.Goodsid = Mat_Goods.id
        where Bus_BudgetItem.Pid = #{Pid}
          and Bus_BudgetItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_BudgetItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, StdPrice,
                                   StdAmount, Rebate, TaxPrice, TaxAmount, ItemTaxrate, TaxTotal, Price, Amount,
                                   AttributeJson, ItemOrgDate, ItemPlanDate, StoQty, WkQty, RowNum, VirtualItem, MaxQty,
                                   Remark, MachUid, MachItemid, MatCost, LaborCost, DirectCost, IndirectCost,
                                   MatItemJson, LaborItemJson, DirectItemJson, IndirectItemJson, MatAmt, LaborAmt,
                                   DirectAmt, IndirectAmt, SourceType, AttaCount, DisannulMark, DisannulListerid,
                                   DisannulLister, DisannulDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
                                   Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{stdprice},
                #{stdamount}, #{rebate}, #{taxprice}, #{taxamount}, #{itemtaxrate}, #{taxtotal}, #{price}, #{amount},
                #{attributejson}, #{itemorgdate}, #{itemplandate}, #{stoqty}, #{wkqty}, #{rownum}, #{virtualitem},
                #{maxqty}, #{remark}, #{machuid}, #{machitemid}, #{matcost}, #{laborcost}, #{directcost},
                #{indirectcost}, #{matitemjson}, #{laboritemjson}, #{directitemjson}, #{indirectitemjson}, #{matamt},
                #{laboramt}, #{directamt}, #{indirectamt}, #{sourcetype}, #{attacount}, #{disannulmark},
                #{disannullisterid}, #{disannullister}, #{disannuldate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_BudgetItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="stdprice != null">
                StdPrice = #{stdprice},
            </if>
            <if test="stdamount != null">
                StdAmount = #{stdamount},
            </if>
            <if test="rebate != null">
                Rebate = #{rebate},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="itemorgdate != null">
                ItemOrgDate = #{itemorgdate},
            </if>
            <if test="itemplandate != null">
                ItemPlanDate = #{itemplandate},
            </if>
            <if test="stoqty != null">
                StoQty = #{stoqty},
            </if>
            <if test="wkqty != null">
                WkQty = #{wkqty},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="maxqty != null">
                MaxQty = #{maxqty},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="matcost != null">
                MatCost = #{matcost},
            </if>
            <if test="laborcost != null">
                LaborCost = #{laborcost},
            </if>
            <if test="directcost != null">
                DirectCost = #{directcost},
            </if>
            <if test="indirectcost != null">
                IndirectCost = #{indirectcost},
            </if>
            <if test="matitemjson != null ">
                MatItemJson = #{matitemjson},
            </if>
            <if test="laboritemjson != null ">
                LaborItemJson = #{laboritemjson},
            </if>
            <if test="directitemjson != null ">
                DirectItemJson = #{directitemjson},
            </if>
            <if test="indirectitemjson != null ">
                IndirectItemJson = #{indirectitemjson},
            </if>
            <if test="matamt != null">
                MatAmt = #{matamt},
            </if>
            <if test="laboramt != null">
                LaborAmt = #{laboramt},
            </if>
            <if test="directamt != null">
                DirectAmt = #{directamt},
            </if>
            <if test="indirectamt != null">
                IndirectAmt = #{indirectamt},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="attacount != null">
                AttaCount = #{attacount},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_BudgetItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <select id="getEntityByMachitemid" resultType="inks.service.std.sale.domain.pojo.BusBudgetitemPojo">
        <include refid="selectBusBudgetitemVo"/>
        where Bus_BudgetItem.MachItemid = #{machitemid}
          and Bus_BudgetItem.Tenantid = #{tid}
        limit 1
    </select>
</mapper>

