<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusCostmodelitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusCostmodelitemPojo">
        select
          id, Pid, PartGroupid, ItemType, ItemName, ItemSpec, ItemUnit, BasePrice, Quantity, Rebate, RebateSec, Price, Amount, PriceMin, PriceMax, Remark, RowNum, AllowDelete, AllowEdit, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Bus_CostModelItem
        where Bus_CostModelItem.id = #{key} and Bus_CostModelItem.Tenantid=#{tid}
    </select>
    <sql id="selectBusCostmodelitemVo">
         select
          id, Pid, PartGroupid, ItemType, ItemName, ItemSpec, ItemUnit, BasePrice, Quantity, Rebate, RebateSec, Price, Amount, PriceMin, PriceMax, Remark, RowNum, AllowDelete, AllowEdit, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Bus_CostModelItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusCostmodelitemPojo">
        <include refid="selectBusCostmodelitemVo"/>
         where 1 = 1 and Bus_CostModelItem.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_CostModelItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Bus_CostModelItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.partgroupid != null and SearchPojo.partgroupid != ''">
   and Bus_CostModelItem.partgroupid like concat('%', #{SearchPojo.partgroupid}, '%')
</if>
<if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
   and Bus_CostModelItem.itemtype like concat('%', #{SearchPojo.itemtype}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Bus_CostModelItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Bus_CostModelItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Bus_CostModelItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Bus_CostModelItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Bus_CostModelItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Bus_CostModelItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Bus_CostModelItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Bus_CostModelItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Bus_CostModelItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Bus_CostModelItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Bus_CostModelItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Bus_CostModelItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Bus_CostModelItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Bus_CostModelItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Bus_CostModelItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.partgroupid != null and SearchPojo.partgroupid != ''">
   or Bus_CostModelItem.PartGroupid like concat('%', #{SearchPojo.partgroupid}, '%')
</if>
<if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
   or Bus_CostModelItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Bus_CostModelItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Bus_CostModelItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Bus_CostModelItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Bus_CostModelItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Bus_CostModelItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Bus_CostModelItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Bus_CostModelItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Bus_CostModelItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Bus_CostModelItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Bus_CostModelItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Bus_CostModelItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Bus_CostModelItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Bus_CostModelItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Bus_CostModelItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.sale.domain.pojo.BusCostmodelitemPojo">
        select
          id, Pid, PartGroupid, ItemType, ItemName, ItemSpec, ItemUnit, BasePrice, Quantity, Rebate, RebateSec, Price, Amount, PriceMin, PriceMax, Remark, RowNum, AllowDelete, AllowEdit, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Bus_CostModelItem
        where Bus_CostModelItem.Pid = #{Pid} and Bus_CostModelItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_CostModelItem(id, Pid, PartGroupid, ItemType, ItemName, ItemSpec, ItemUnit, BasePrice, Quantity, Rebate, RebateSec, Price, Amount, PriceMin, PriceMax, Remark, RowNum, AllowDelete, AllowEdit, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{partgroupid}, #{itemtype}, #{itemname}, #{itemspec}, #{itemunit}, #{baseprice}, #{quantity}, #{rebate}, #{rebatesec}, #{price}, #{amount}, #{pricemin}, #{pricemax}, #{remark}, #{rownum}, #{allowdelete}, #{allowedit}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_CostModelItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="partgroupid != null ">
                PartGroupid = #{partgroupid},
            </if>
            <if test="itemtype != null ">
                ItemType = #{itemtype},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="baseprice != null">
                BasePrice = #{baseprice},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="rebate != null">
                Rebate = #{rebate},
            </if>
            <if test="rebatesec != null">
                RebateSec = #{rebatesec},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="pricemin != null">
                PriceMin = #{pricemin},
            </if>
            <if test="pricemax != null">
                PriceMax = #{pricemax},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="allowdelete != null">
                AllowDelete = #{allowdelete},
            </if>
            <if test="allowedit != null">
                AllowEdit = #{allowedit},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_CostModelItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

