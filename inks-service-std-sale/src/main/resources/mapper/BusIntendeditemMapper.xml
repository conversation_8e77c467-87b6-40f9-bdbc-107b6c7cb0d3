<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusIntendeditemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusIntendeditemPojo">
        <include refid="selectBusIntendeditemVo"/>
        where Bus_IntendedItem.id = #{key}
          and Bus_IntendedItem.Tenantid = #{tid}
    </select>
    <sql id="selectBusIntendeditemVo">
        select Bus_IntendedItem.id,
               Bus_IntendedItem.Pid,
               Bus_IntendedItem.Goodsid,
               Bus_IntendedItem.ItemType,
               Bus_IntendedItem.ItemName,
               Bus_IntendedItem.ItemSpec,
               Bus_IntendedItem.ItemUnit,
               Bus_IntendedItem.DmsGoodsid,
               Bus_IntendedItem.Quantity,
               Bus_IntendedItem.StdPrice,
               Bus_IntendedItem.StdAmount,
               Bus_IntendedItem.Rebate,
               Bus_IntendedItem.Price,
               Bus_IntendedItem.Amount,
               Bus_IntendedItem.ItemTaxrate,
               Bus_IntendedItem.TaxPrice,
               Bus_IntendedItem.TaxTotal,
               Bus_IntendedItem.TaxAmount,
               Bus_IntendedItem.PlanDate,
               Bus_IntendedItem.Remark,
               Bus_IntendedItem.RowNum,
               Bus_IntendedItem.AttributeJson,
               Bus_IntendedItem.CostItemJson,
               Bus_IntendedItem.CostGroupJson,
               Bus_IntendedItem.VirtualItem,
               Bus_IntendedItem.FinishMark,
               Bus_IntendedItem.DisannulMark,
               Bus_IntendedItem.DisannulListerid,
               Bus_IntendedItem.DisannulLister,
               Bus_IntendedItem.DisannulDate,
               Bus_IntendedItem.Custom1,
               Bus_IntendedItem.Custom2,
               Bus_IntendedItem.Custom3,
               Bus_IntendedItem.Custom4,
               Bus_IntendedItem.Custom5,
               Bus_IntendedItem.Custom6,
               Bus_IntendedItem.Custom7,
               Bus_IntendedItem.Custom8,
               Bus_IntendedItem.Custom9,
               Bus_IntendedItem.Custom10,
               Bus_IntendedItem.Tenantid,
               Bus_IntendedItem.Revision,
               Bus_DmsGoods.GoodsUid  as DmsGoodsUid,
               Bus_DmsGoods.GoodsName as DmsGoodsName,
               Bus_DmsGoods.GoodsSpec as DmsGoodsSpec,
               Bus_DmsGoods.GoodsUnit as DmsGoodsUnit
        from Bus_IntendedItem
                 Left join Bus_DmsGoods on Bus_IntendedItem.DmsGoodsid = Bus_DmsGoods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusIntendeditemPojo">
        <include refid="selectBusIntendeditemVo"/>
        where 1 = 1 and Bus_IntendedItem.Tenantid = #{Tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_IntendedItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_IntendedItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_IntendedItem.goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
            and Bus_IntendedItem.itemtype like concat('%',
                #{SearchPojo.itemtype}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_IntendedItem.itemname like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_IntendedItem.itemspec like concat('%',
                #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_IntendedItem.itemunit like concat('%',
                #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_IntendedItem.remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Bus_IntendedItem.attributejson like concat('%',
                #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.costitemjson != null and SearchPojo.costitemjson != ''">
            and Bus_IntendedItem.costitemjson like concat('%',
                #{SearchPojo.costitemjson}, '%')
        </if>
        <if test="SearchPojo.costgroupjson != null and SearchPojo.costgroupjson != ''">
            and Bus_IntendedItem.costgroupjson like concat('%',
                #{SearchPojo.costgroupjson}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Bus_IntendedItem.disannullisterid like concat('%',
                #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Bus_IntendedItem.disannullister like concat('%',
                #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_IntendedItem.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_IntendedItem.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_IntendedItem.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_IntendedItem.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_IntendedItem.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_IntendedItem.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_IntendedItem.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_IntendedItem.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_IntendedItem.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_IntendedItem.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_IntendedItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_IntendedItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
                or Bus_IntendedItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_IntendedItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_IntendedItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_IntendedItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_IntendedItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Bus_IntendedItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.costitemjson != null and SearchPojo.costitemjson != ''">
                or Bus_IntendedItem.CostItemJson like concat('%', #{SearchPojo.costitemjson}, '%')
            </if>
            <if test="SearchPojo.costgroupjson != null and SearchPojo.costgroupjson != ''">
                or Bus_IntendedItem.CostGroupJson like concat('%', #{SearchPojo.costgroupjson}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Bus_IntendedItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Bus_IntendedItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_IntendedItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_IntendedItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_IntendedItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_IntendedItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_IntendedItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_IntendedItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_IntendedItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_IntendedItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_IntendedItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_IntendedItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.sale.domain.pojo.BusIntendeditemPojo">
        select Bus_IntendedItem.id,
               Bus_IntendedItem.Pid,
               Bus_IntendedItem.Goodsid,
               Bus_IntendedItem.ItemType,
               Bus_IntendedItem.ItemName,
               Bus_IntendedItem.ItemSpec,
               Bus_IntendedItem.ItemUnit,
               Bus_IntendedItem.DmsGoodsid,
               Bus_IntendedItem.Quantity,
               Bus_IntendedItem.StdPrice,
               Bus_IntendedItem.StdAmount,
               Bus_IntendedItem.Rebate,
               Bus_IntendedItem.Price,
               Bus_IntendedItem.Amount,
               Bus_IntendedItem.ItemTaxrate,
               Bus_IntendedItem.TaxPrice,
               Bus_IntendedItem.TaxTotal,
               Bus_IntendedItem.TaxAmount,
               Bus_IntendedItem.PlanDate,
               Bus_IntendedItem.Remark,
               Bus_IntendedItem.RowNum,
               Bus_IntendedItem.AttributeJson,
               Bus_IntendedItem.CostItemJson,
               Bus_IntendedItem.CostGroupJson,
               Bus_IntendedItem.VirtualItem,
               Bus_IntendedItem.FinishMark,
               Bus_IntendedItem.DisannulMark,
               Bus_IntendedItem.DisannulListerid,
               Bus_IntendedItem.DisannulLister,
               Bus_IntendedItem.DisannulDate,
               Bus_IntendedItem.Custom1,
               Bus_IntendedItem.Custom2,
               Bus_IntendedItem.Custom3,
               Bus_IntendedItem.Custom4,
               Bus_IntendedItem.Custom5,
               Bus_IntendedItem.Custom6,
               Bus_IntendedItem.Custom7,
               Bus_IntendedItem.Custom8,
               Bus_IntendedItem.Custom9,
               Bus_IntendedItem.Custom10,
               Bus_IntendedItem.Tenantid,
               Bus_IntendedItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Bus_DmsGoods.GoodsUid  as DmsGoodsUid,
               Bus_DmsGoods.GoodsName as DmsGoodsName,
               Bus_DmsGoods.GoodsSpec as DmsGoodsSpec,
               Bus_DmsGoods.GoodsUnit as DmsGoodsUnit
        from Bus_IntendedItem
                 Left join Mat_Goods on Bus_IntendedItem.Goodsid = Mat_Goods.id
                 Left join Bus_DmsGoods on Bus_IntendedItem.DmsGoodsid = Bus_DmsGoods.id
        where Bus_IntendedItem.Pid = #{Pid}
          and Bus_IntendedItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_IntendedItem(id, Pid, Goodsid, ItemType, ItemName, ItemSpec, ItemUnit,DmsGoodsid, Quantity, StdPrice,
                                     StdAmount, Rebate, Price, Amount, ItemTaxrate, TaxPrice, TaxTotal, TaxAmount,
                                     PlanDate, Remark, RowNum, AttributeJson, CostItemJson, CostGroupJson, VirtualItem,
                                     FinishMark, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, Custom1,
                                     Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                     Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemtype}, #{itemname}, #{itemspec}, #{itemunit}, #{dmsgoodsid},
                #{quantity}, #{stdprice},
                #{stdamount}, #{rebate}, #{price}, #{amount}, #{itemtaxrate}, #{taxprice}, #{taxtotal}, #{taxamount},
                #{plandate}, #{remark}, #{rownum}, #{attributejson}, #{costitemjson}, #{costgroupjson}, #{virtualitem},
                #{finishmark}, #{disannulmark}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_IntendedItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="goodsid != null">
                Goodsid = #{goodsid},
            </if>
            <if test="itemtype != null">
                ItemType = #{itemtype},
            </if>
            <if test="itemname != null">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null">
                ItemUnit = #{itemunit},
            </if>
            <if test="dmsgoodsid != null">
                DmsGoodsid = #{dmsgoodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="stdprice != null">
                StdPrice = #{stdprice},
            </if>
            <if test="stdamount != null">
                StdAmount = #{stdamount},
            </if>
            <if test="rebate != null">
                Rebate = #{rebate},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="attributejson != null">
                AttributeJson = #{attributejson},
            </if>
            <if test="costitemjson != null">
                CostItemJson = #{costitemjson},
            </if>
            <if test="costgroupjson != null">
                CostGroupJson = #{costgroupjson},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="finishmark != null">
                FinishMark = #{finishmark},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 = #{custom10},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_IntendedItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
</mapper>

