<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusCarryoverMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusCarryoverPojo">
        <include refid="selectbillVo"/>
        where Bus_Carryover.id = #{key}
          and Bus_Carryover.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Bus_Carryover.id,
               Bus_Carryover.RefNo,
               Bus_Carryover.BillType,
               Bus_Carryover.BillDate,
               Bus_Carryover.BillTitle,
               Bus_Carryover.Groupid,
               Bus_Carryover.CarryYear,
               Bus_Carryover.CarryMonth,
               Bus_Carryover.StartDate,
               Bus_Carryover.EndDate,
               Bus_Carryover.Operator,
               Bus_Carryover.Operatorid,
               Bus_Carryover.RowNum,
               Bus_Carryover.Summary,
               Bus_Carryover.CreateBy,
               Bus_Carryover.CreateByid,
               Bus_Carryover.CreateDate,
               Bus_Carryover.Lister,
               Bus_Carryover.Listerid,
               Bus_Carryover.ModifyDate,
               Bus_Carryover.BillOpenAmount,
               Bus_Carryover.BillInAmount,
               Bus_Carryover.BillOutAmount,
               Bus_Carryover.BillCloseAmount,
               Bus_Carryover.InvoOpenAmount,
               Bus_Carryover.InvoInAmount,
               Bus_Carryover.InvoOutAmount,
               Bus_Carryover.InvoCloseAmount,
               Bus_Carryover.PrintCount,
               Bus_Carryover.Custom1,
               Bus_Carryover.Custom2,
               Bus_Carryover.Custom3,
               Bus_Carryover.Custom4,
               Bus_Carryover.Custom5,
               Bus_Carryover.Custom6,
               Bus_Carryover.Custom7,
               Bus_Carryover.Custom8,
               Bus_Carryover.Custom9,
               Bus_Carryover.Custom10,
               Bus_Carryover.Tenantid,
               Bus_Carryover.TenantName,
               Bus_Carryover.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        from Bus_Carryover
                 Left join App_Workgroup on Bus_Carryover.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        select Bus_Carryover.RefNo,
               Bus_Carryover.BillType,
               Bus_Carryover.BillDate,
               Bus_Carryover.BillTitle,
               Bus_Carryover.Groupid,
               Bus_Carryover.Operator,
               Bus_Carryover.CreateBy,
               Bus_Carryover.Lister,
               Bus_CarryoverItem.*,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        from Bus_CarryoverItem
                 left join Bus_Carryover on Bus_Carryover.id = Bus_CarryoverItem.Pid
                 left join Mat_Goods on Bus_CarryoverItem.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusCarryoveritemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Carryover.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_Carryover.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>


    <sql id="selectInvodetailVo">
        select Bus_Carryover.RefNo,
        Bus_Carryover.BillType,
        Bus_Carryover.BillDate,
        Bus_Carryover.BillTitle,
        Bus_Carryover.Groupid,
        Bus_Carryover.Operator,
        Bus_Carryover.CreateBy,
        Bus_Carryover.Lister,
        Bus_CarryoverInvo.*,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        from Bus_CarryoverInvo
        left join Bus_Carryover on Bus_Carryover.id = Bus_CarryoverInvo.Pid
        left join Mat_Goods on Bus_CarryoverInvo.Goodsid = Mat_Goods.id
    </sql>
<!--    查询invo子表为主题的pagelist数据-->
    <select id="getInvoPageList" resultType="inks.service.std.sale.domain.pojo.BusCarryoverinvodetailPojo">
        <include refid="selectInvodetailVo"/>
        where 1 = 1 and Bus_Carryover.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_Carryover.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>


    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Bus_Carryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_Carryover.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_Carryover.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_Carryover.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Bus_Carryover.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Bus_Carryover.Operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_Carryover.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_Carryover.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_Carryover.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_Carryover.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_Carryover.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_Carryover.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_Carryover.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_Carryover.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_Carryover.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_Carryover.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_Carryover.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_Carryover.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_Carryover.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_Carryover.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_Carryover.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_Carryover.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_Carryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_Carryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_Carryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_Carryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Bus_Carryover.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Bus_Carryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_Carryover.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_Carryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_Carryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_Carryover.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_Carryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_Carryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_Carryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_Carryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_Carryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_Carryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_Carryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_Carryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_Carryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_Carryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_Carryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_Carryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusCarryoverPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Carryover.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_Carryover.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Bus_Carryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_Carryover.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_Carryover.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_Carryover.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Bus_Carryover.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Bus_Carryover.Operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_Carryover.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_Carryover.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_Carryover.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_Carryover.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_Carryover.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_Carryover.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_Carryover.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_Carryover.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_Carryover.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_Carryover.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_Carryover.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_Carryover.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_Carryover.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_Carryover.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_Carryover.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_Carryover.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_Carryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_Carryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_Carryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_Carryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Bus_Carryover.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Bus_Carryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_Carryover.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_Carryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_Carryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_Carryover.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_Carryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_Carryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_Carryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_Carryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_Carryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_Carryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_Carryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_Carryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_Carryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_Carryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_Carryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_Carryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_Carryover(id, RefNo, BillType, BillDate, BillTitle, Groupid, CarryYear, CarryMonth, StartDate,
                                  EndDate, Operator, Operatorid, RowNum, Summary, CreateBy, CreateByid, CreateDate,
                                  Lister, Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount,
                                  BillCloseAmount, InvoOpenAmount, InvoInAmount, InvoOutAmount, InvoCloseAmount,
                                  PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                  Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{groupid}, #{carryyear}, #{carrymonth},
                #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{rownum}, #{summary}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{billopenamount}, #{billinamount},
                #{billoutamount}, #{billcloseamount}, #{invoopenamount}, #{invoinamount}, #{invooutamount},
                #{invocloseamount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Carryover
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="invoopenamount != null">
                InvoOpenAmount =#{invoopenamount},
            </if>
            <if test="invoinamount != null">
                InvoInAmount =#{invoinamount},
            </if>
            <if test="invooutamount != null">
                InvoOutAmount =#{invooutamount},
            </if>
            <if test="invocloseamount != null">
                InvoCloseAmount =#{invocloseamount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Carryover
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusCarryoverPojo">
        select id
        from Bus_CarryoverItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDelInvoIds" resultType="java.lang.String">
        select id
        from Bus_CarryoverInvo
        where Pid = #{id}
        <if test="invo != null and invo.size() > 0">
            and id not in
            <foreach collection="invo" open="(" close=")" separator="," item="invo">
                <if test="invo.id != null">
                    #{invo.id}
                </if>
                <if test="invo.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <delete id="deleteByMonth">
        delete Bus_Carryover,Bus_CarryoverItem,Bus_CarryoverInvo
        from Bus_Carryover
                 join Bus_CarryoverItem on Bus_CarryoverItem.Pid = Bus_Carryover.id
                 join Bus_CarryoverInvo on Bus_CarryoverInvo.Pid = Bus_Carryover.id
        where Bus_Carryover.id in
        <foreach collection="carryoverIds" item="id" open="(" close=")">
            #{id}
        </foreach>
          and Bus_Carryover.Tenantid = #{tid}
    </delete>

    <select id="getCarryoverIdsForDeletion" resultType="java.lang.String">
        SELECT id
        FROM Bus_Carryover
        WHERE CarryYear = #{carryyear}
        AND CarryMonth = #{carrymonth}
        AND Tenantid = #{tid}
        LIMIT #{batchSize}
    </select>

    <!-- 先删除从表数据 -->
    <delete id="deleteCarryoverItemByMonth">
        DELETE FROM Bus_CarryoverItem
        WHERE Pid IN
        <foreach collection="carryoverIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND Tenantid = #{tid}
    </delete>

    <delete id="deleteCarryoverInvoByMonth">
        DELETE FROM Bus_CarryoverInvo
        WHERE Pid IN
        <foreach collection="carryoverIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND Tenantid = #{tid}
    </delete>

    <!-- 最后删除主表数据 -->
    <delete id="deleteCarryoverByMonth">
        DELETE FROM Bus_Carryover
        WHERE id IN
        <foreach collection="carryoverIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND Tenantid = #{tid}
    </delete>

    <delete id="deleteInvoCarryoverByRecid">
        delete from Bus_InvoCarryover
        where Recid = #{recid} and Tenantid = #{tid}
    </delete>

    <delete id="deleteDeliCarryoverByRecid">
        delete from Bus_DeliCarryover
        where Recid = #{recid} and Tenantid = #{tid}
    </delete>
</mapper>

