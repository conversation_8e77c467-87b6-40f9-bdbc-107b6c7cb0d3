<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusInvocarryoverMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusInvocarryoverPojo">
        <include refid="selectBusInvocarryoverVo"/>
        where Bus_InvoCarryover.id = #{key} and Bus_InvoCarryover.Tenantid=#{tid}
    </select>
    <sql id="selectBusInvocarryoverVo">
        select Bus_InvoCarryover.id,
               Bus_InvoCarryover.Recid,
               Bus_InvoCarryover.Groupid,
               Bus_InvoCarryover.Goodsid,

               Bus_InvoCarryover.OpenQty,
               Bus_InvoCarryover.OpenAmount,
               Bus_InvoCarryover.InQty,
               Bus_InvoCarryover.InAmount,
               Bus_InvoCarryover.OutQty,
               Bus_InvoCarryover.OutAmount,
               Bus_InvoCarryover.CloseQty,
               Bus_InvoCarryover.CloseAmount,
               Bus_InvoCarryover.Skuid,
               Bus_InvoCarryover.AttributeJson,
               Bus_InvoCarryover.CloseMark,
               Bus_InvoCarryover.RowNum,
               Bus_InvoCarryover.CreateBy,
               Bus_InvoCarryover.CreateByid,
               Bus_InvoCarryover.CreateDate,
               Bus_InvoCarryover.Lister,
               Bus_InvoCarryover.Listerid,
               Bus_InvoCarryover.ModifyDate,
               Bus_InvoCarryover.Custom1,
               Bus_InvoCarryover.Custom2,
               Bus_InvoCarryover.Custom3,
               Bus_InvoCarryover.Custom4,
               Bus_InvoCarryover.Custom5,
               Bus_InvoCarryover.Custom6,
               Bus_InvoCarryover.Custom7,
               Bus_InvoCarryover.Custom8,
               Bus_InvoCarryover.Custom9,
               Bus_InvoCarryover.Custom10,
               Bus_InvoCarryover.Tenantid,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoods"/>
               Mat_Goods.GoodsUid as ItemCode,
               Mat_Goods.GoodsName as ItemName,
               Mat_Goods.GoodsSpec as ItemSpec,
               Mat_Goods.GoodsUnit as ItemUnit,
               Mat_Goods.WeightQty,
               Mat_Goods.WeightUnit,
               Mat_Goods.AreaQty,
               Mat_Goods.AreaUnit,
               Mat_Goods.VolumeQty,
               Mat_Goods.VolumeUnit,
               Mat_Goods.PackQty,
               Mat_Goods.PackUnit,
               App_Workgroup.GroupName,
               App_Workgroup.GroupUid,
               App_Workgroup.Abbreviate,
               Bus_InvoCarryover.Revision
        from Bus_InvoCarryover
        join Mat_Goods on Bus_InvoCarryover.Goodsid = Mat_Goods.id
        JOIN App_Workgroup ON Mat_Goods.Groupid = App_Workgroup.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusInvocarryoverPojo">
        <include refid="selectBusInvocarryoverVo"/>
         where 1 = 1 and Bus_InvoCarryover.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_InvoCarryover.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
         <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsandfilter"/>
<if test="SearchPojo.recid != null ">
   and Bus_InvoCarryover.Recid like concat('%', #{SearchPojo.recid}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Bus_InvoCarryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Bus_InvoCarryover.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.groupuid != null ">
   and Bus_InvoCarryover.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   and Bus_InvoCarryover.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null ">
   and Bus_InvoCarryover.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   and Bus_InvoCarryover.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null ">
   and Bus_InvoCarryover.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null ">
   and Bus_InvoCarryover.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.skuid != null ">
   and Bus_InvoCarryover.Skuid like concat('%', #{SearchPojo.skuid}, '%')
</if>
<if test="SearchPojo.attributejson != null ">
   and Bus_InvoCarryover.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Bus_InvoCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Bus_InvoCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Bus_InvoCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Bus_InvoCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Bus_InvoCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Bus_InvoCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Bus_InvoCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Bus_InvoCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Bus_InvoCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Bus_InvoCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Bus_InvoCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Bus_InvoCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Bus_InvoCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Bus_InvoCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
      <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsorfilter"/>
      <if test="SearchPojo.recid != null ">
   or Bus_InvoCarryover.Recid like concat('%', #{SearchPojo.recid}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Bus_InvoCarryover.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Bus_InvoCarryover.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.groupuid != null ">
   or Bus_InvoCarryover.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
</if>
<if test="SearchPojo.goodsid != null ">
   or Bus_InvoCarryover.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null ">
   or Bus_InvoCarryover.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   or Bus_InvoCarryover.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null ">
   or Bus_InvoCarryover.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null ">
   or Bus_InvoCarryover.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.skuid != null ">
   or Bus_InvoCarryover.Skuid like concat('%', #{SearchPojo.skuid}, '%')
</if>
<if test="SearchPojo.attributejson != null ">
   or Bus_InvoCarryover.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Bus_InvoCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Bus_InvoCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Bus_InvoCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Bus_InvoCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Bus_InvoCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Bus_InvoCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Bus_InvoCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Bus_InvoCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Bus_InvoCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Bus_InvoCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Bus_InvoCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Bus_InvoCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Bus_InvoCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Bus_InvoCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_InvoCarryover(id, Recid, Groupid, GroupName, GroupUid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, OpenQty, OpenAmount, InQty, InAmount, OutQty, OutAmount, CloseQty, CloseAmount, Skuid, AttributeJson, CloseMark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{recid}, #{groupid}, #{groupname}, #{groupuid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{openqty}, #{openamount}, #{inqty}, #{inamount}, #{outqty}, #{outamount}, #{closeqty}, #{closeamount}, #{skuid}, #{attributejson}, #{closemark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO Bus_InvoCarryover (
        id, Recid, Groupid, GroupName, GroupUid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit,
        OpenQty, OpenAmount, InQty, InAmount,
        OutQty, OutAmount, CloseQty, CloseAmount, Skuid, AttributeJson, CloseMark, RowNum,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
        Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.recid}, #{item.groupid}, #{item.groupname}, #{item.groupuid},
            #{item.goodsid}, #{item.itemcode}, #{item.itemname}, #{item.itemspec}, #{item.itemunit},
            #{item.openqty}, #{item.openamount}, #{item.inqty}, #{item.inamount},
            #{item.outqty}, #{item.outamount}, #{item.closeqty}, #{item.closeamount}, #{item.skuid},
            #{item.attributejson}, #{item.closemark}, #{item.rownum}, #{item.createby}, #{item.createbyid},
            #{item.createdate}, #{item.lister}, #{item.listerid}, #{item.modifydate}, #{item.custom1},
            #{item.custom2}, #{item.custom3}, #{item.custom4}, #{item.custom5}, #{item.custom6},
            #{item.custom7}, #{item.custom8}, #{item.custom9}, #{item.custom10}, #{item.tenantid}, #{item.revision}
            )
        </foreach>
    </insert>



    <!--通过主键修改数据-->
    <update id="update">
        update Bus_InvoCarryover
        <set>
            <if test="recid != null ">
                Recid =#{recid},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="groupuid != null ">
                GroupUid =#{groupuid},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec =#{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit =#{itemunit},
            </if>
            <if test="openqty != null">
                OpenQty =#{openqty},
            </if>
            <if test="openamount != null">
                OpenAmount =#{openamount},
            </if>
            <if test="inqty != null">
                InQty =#{inqty},
            </if>
            <if test="inamount != null">
                InAmount =#{inamount},
            </if>
            <if test="outqty != null">
                OutQty =#{outqty},
            </if>
            <if test="outamount != null">
                OutAmount =#{outamount},
            </if>
            <if test="closeqty != null">
                CloseQty =#{closeqty},
            </if>
            <if test="closeamount != null">
                CloseAmount =#{closeamount},
            </if>
            <if test="skuid != null ">
                Skuid =#{skuid},
            </if>
            <if test="attributejson != null ">
                AttributeJson =#{attributejson},
            </if>
            <if test="closemark != null">
                CloseMark =#{closemark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_InvoCarryover where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getListByMonth" resultType="inks.service.std.sale.domain.pojo.BusInvocarryoverPojo">
        select Bus_InvoCarryover.*
        from Bus_InvoCarryover
                 join Bus_AccountRec on Bus_InvoCarryover.Recid = Bus_AccountRec.id
        where Bus_AccountRec.Tenantid = #{tid}
          and Bus_InvoCarryover.Recid = #{recid}
        <if test="goodsid != null and goodsid != ''">
            and Bus_InvoCarryover.Goodsid = #{goodsid}
        </if>
    </select>

    <select id="getEntityByRecidAndGoodsid" resultType="inks.service.std.sale.domain.pojo.BusInvocarryoverPojo">
        <include refid="selectBusInvocarryoverVo"/>
        where Bus_InvoCarryover.Recid = #{recidNew} and Bus_InvoCarryover.Goodsid = #{goodsid} and Bus_InvoCarryover.Tenantid=#{tid}
    </select>


    <select id="getListByids" resultType="inks.service.std.sale.domain.pojo.BusInvocarryoverPojo">
        select Bus_InvoCarryover.*
        from Bus_InvoCarryover
        join Bus_AccountRec on Bus_InvoCarryover.Recid = Bus_AccountRec.id
        where Bus_AccountRec.Tenantid = #{tid}
        <if test="ids != null and ids.size() > 0">
            and Bus_InvoCarryover.id IN
            <foreach item="id" index="index" collection="ids" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>

