<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusChatgroupMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusChatgroupPojo">
        select
          id, Parentid, GroupType, GroupCode, GroupName, GroupLevel, StateCode, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision
        from Bus_ChatGroup
        where Bus_ChatGroup.id = #{key} and Bus_ChatGroup.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
          id, Parentid, GroupType, GroupCode, GroupName, GroupLevel, StateCode, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision        from Bus_ChatGroup
    </sql>
    <sql id="selectdetailVo">
         select
          id, Parentid, GroupType, GroupCode, GroupName, GroupLevel, StateCode, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision        from Bus_ChatGroup
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusChatgroupitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 and Bus_ChatGroup.Tenantid =#{tenantid}
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_ChatGroup.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.parentid != null ">
   and Bus_ChatGroup.parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.grouptype != null ">
   and Bus_ChatGroup.grouptype like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   and Bus_ChatGroup.groupcode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Bus_ChatGroup.groupname like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Bus_ChatGroup.statecode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Bus_ChatGroup.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Bus_ChatGroup.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Bus_ChatGroup.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Bus_ChatGroup.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Bus_ChatGroup.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Bus_ChatGroup.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Bus_ChatGroup.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Bus_ChatGroup.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Bus_ChatGroup.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Bus_ChatGroup.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.parentid != null ">
   or Bus_ChatGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.grouptype != null ">
   or Bus_ChatGroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   or Bus_ChatGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Bus_ChatGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Bus_ChatGroup.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Bus_ChatGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Bus_ChatGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Bus_ChatGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Bus_ChatGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Bus_ChatGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Bus_ChatGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Bus_ChatGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Bus_ChatGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Bus_ChatGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Bus_ChatGroup.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusChatgroupPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 and Bus_ChatGroup.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_ChatGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.parentid != null ">
   and Bus_ChatGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.grouptype != null ">
   and Bus_ChatGroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   and Bus_ChatGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Bus_ChatGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Bus_ChatGroup.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Bus_ChatGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Bus_ChatGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Bus_ChatGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Bus_ChatGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Bus_ChatGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Bus_ChatGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Bus_ChatGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Bus_ChatGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Bus_ChatGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Bus_ChatGroup.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.parentid != null ">
   or Bus_ChatGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.grouptype != null ">
   or Bus_ChatGroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   or Bus_ChatGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Bus_ChatGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Bus_ChatGroup.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Bus_ChatGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Bus_ChatGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Bus_ChatGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Bus_ChatGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Bus_ChatGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Bus_ChatGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Bus_ChatGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Bus_ChatGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Bus_ChatGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Bus_ChatGroup.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_ChatGroup(id, Parentid, GroupType, GroupCode, GroupName, GroupLevel, StateCode, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Tenantid, TenantName, Revision)
        values (#{id}, #{parentid}, #{grouptype}, #{groupcode}, #{groupname}, #{grouplevel}, #{statecode}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_ChatGroup
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="grouptype != null ">
                GroupType =#{grouptype},
            </if>
            <if test="groupcode != null ">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="grouplevel != null">
                GroupLevel =#{grouplevel},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_ChatGroup where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                            <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.sale.domain.pojo.BusChatgroupPojo">
        select
          id
        from Bus_ChatGroupItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

    <select id="getDelDmsIds" resultType="java.lang.String" parameterType="inks.service.std.sale.domain.pojo.BusChatgroupPojo">
        select
          id
        from Bus_ChatGroupDms
        where Pid = #{id}
        <if test="dms !=null and dms.size()>0">
         and id not in
        <foreach collection="dms" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>
</mapper>

