<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusBudgetMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusBudgetPojo">
        select Bus_Budget.id,
               Bus_Budget.RefNo,
               Bus_Budget.BillType,
               Bus_Budget.BillDate,
               Bus_Budget.BillTitle,
               Bus_Budget.MachBillid,
               Bus_Budget.MachBillCode,
               Bus_Budget.Groupid,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Bus_Budget.Workshop,
               Bus_Budget.BillMatCost,
               Bus_Budget.BillLaborCost,
               Bus_Budget.BillDirectCost,
               Bus_Budget.BillIndirectCost,
               Bus_Budget.BillMatAmt,
               Bus_Budget.BillLaborAmt,
               Bus_Budget.BillDirectAmt,
               Bus_Budget.BillIndirectAmt,
               Bus_Budget.Operator,
               Bus_Budget.Summary,
               Bus_Budget.CreateBy,
               Bus_Budget.CreateByid,
               Bus_Budget.CreateDate,
               Bus_Budget.Lister,
               Bus_Budget.Listerid,
               Bus_Budget.ModifyDate,
               Bus_Budget.Assessor,
               Bus_Budget.Assessorid,
               Bus_Budget.AssessDate,
               Bus_Budget.Custom1,
               Bus_Budget.Custom2,
               Bus_Budget.Custom3,
               Bus_Budget.Custom4,
               Bus_Budget.Custom5,
               Bus_Budget.Custom6,
               Bus_Budget.Custom7,
               Bus_Budget.Custom8,
               Bus_Budget.Custom9,
               Bus_Budget.Custom10,
               Bus_Budget.Tenantid,
               Bus_Budget.TenantName,
               Bus_Budget.Revision
        from App_Workgroup
                 RIGHT JOIN Bus_Budget ON Bus_Budget.Groupid = App_Workgroup.id
        where Bus_Budget.id = #{key}
          and Bus_Budget.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               MachBillid,
               MachBillCode,
               Groupid,
               Workshop,
               BillMatCost,
               BillLaborCost,
               BillDirectCost,
               BillIndirectCost,
               BillMatAmt,
               BillLaborAmt,
               BillDirectAmt,
               BillIndirectAmt,
               Operator,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Bus_Budget
    </sql>
    <sql id="selectdetailVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               MachBillid,
               MachBillCode,
               Groupid,
               Workshop,
               BillMatCost,
               BillLaborCost,
               BillDirectCost,
               BillIndirectCost,
               BillMatAmt,
               BillLaborAmt,
               BillDirectAmt,
               BillIndirectAmt,
               Operator,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Bus_Budget
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusBudgetitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Budget.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Budget.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Bus_Budget.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Budget.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Budget.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.machbillid != null ">
            and Bus_Budget.machbillid like concat('%', #{SearchPojo.machbillid}, '%')
        </if>
        <if test="SearchPojo.machbillcode != null ">
            and Bus_Budget.machbillcode like concat('%', #{SearchPojo.machbillcode}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Budget.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.workshop != null ">
            and Bus_Budget.workshop like concat('%', #{SearchPojo.workshop}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_Budget.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Budget.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Budget.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Budget.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Budget.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Budget.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_Budget.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_Budget.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Budget.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Budget.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Budget.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Budget.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Budget.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Budget.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Budget.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Budget.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Budget.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Budget.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Budget.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Budget.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Budget.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Budget.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.machbillid != null ">
                or Bus_Budget.MachBillid like concat('%', #{SearchPojo.machbillid}, '%')
            </if>
            <if test="SearchPojo.machbillcode != null ">
                or Bus_Budget.MachBillCode like concat('%', #{SearchPojo.machbillcode}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Budget.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.workshop != null ">
                or Bus_Budget.Workshop like concat('%', #{SearchPojo.workshop}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_Budget.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Budget.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Budget.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Budget.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Budget.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Budget.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_Budget.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_Budget.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Budget.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Budget.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Budget.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Budget.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Budget.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Budget.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Budget.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Budget.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Budget.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Budget.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Budget.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusBudgetPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Budget.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Budget.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Bus_Budget.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Budget.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Budget.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.machbillid != null ">
            and Bus_Budget.MachBillid like concat('%', #{SearchPojo.machbillid}, '%')
        </if>
        <if test="SearchPojo.machbillcode != null ">
            and Bus_Budget.MachBillCode like concat('%', #{SearchPojo.machbillcode}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Budget.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.workshop != null ">
            and Bus_Budget.Workshop like concat('%', #{SearchPojo.workshop}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_Budget.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Budget.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Budget.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Budget.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Budget.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Budget.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_Budget.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_Budget.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Budget.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Budget.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Budget.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Budget.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Budget.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Budget.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Budget.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Budget.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Budget.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Budget.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Budget.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Budget.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Budget.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Budget.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.machbillid != null ">
                or Bus_Budget.MachBillid like concat('%', #{SearchPojo.machbillid}, '%')
            </if>
            <if test="SearchPojo.machbillcode != null ">
                or Bus_Budget.MachBillCode like concat('%', #{SearchPojo.machbillcode}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Budget.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.workshop != null ">
                or Bus_Budget.Workshop like concat('%', #{SearchPojo.workshop}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_Budget.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Budget.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Budget.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Budget.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Budget.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Budget.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_Budget.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_Budget.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Budget.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Budget.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Budget.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Budget.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Budget.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Budget.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Budget.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Budget.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Budget.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Budget.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Budget.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_Budget(id, RefNo, BillType, BillDate, BillTitle, MachBillid, MachBillCode, Groupid, Workshop,
                               BillMatCost, BillLaborCost, BillDirectCost, BillIndirectCost, BillMatAmt, BillLaborAmt,
                               BillDirectAmt, BillIndirectAmt, Operator, Summary, CreateBy, CreateByid, CreateDate,
                               Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2,
                               Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                               TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{machbillid}, #{machbillcode}, #{groupid},
                #{workshop}, #{billmatcost}, #{billlaborcost}, #{billdirectcost}, #{billindirectcost}, #{billmatamt},
                #{billlaboramt}, #{billdirectamt}, #{billindirectamt}, #{operator}, #{summary}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid},
                #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Budget
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="machbillid != null ">
                MachBillid =#{machbillid},
            </if>
            <if test="machbillcode != null ">
                MachBillCode =#{machbillcode},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="workshop != null ">
                Workshop =#{workshop},
            </if>
            <if test="billmatcost != null">
                BillMatCost =#{billmatcost},
            </if>
            <if test="billlaborcost != null">
                BillLaborCost =#{billlaborcost},
            </if>
            <if test="billdirectcost != null">
                BillDirectCost =#{billdirectcost},
            </if>
            <if test="billindirectcost != null">
                BillIndirectCost =#{billindirectcost},
            </if>
            <if test="billmatamt != null">
                BillMatAmt =#{billmatamt},
            </if>
            <if test="billlaboramt != null">
                BillLaborAmt =#{billlaboramt},
            </if>
            <if test="billdirectamt != null">
                BillDirectAmt =#{billdirectamt},
            </if>
            <if test="billindirectamt != null">
                BillIndirectAmt =#{billindirectamt},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Budget
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Budget
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusBudgetPojo">
        select
        id
        from Bus_BudgetItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="checkMachbillid" resultType="int">
        select count(1)
        from Bus_Budget
        where MachBillid = #{machbillid}
          and Tenantid = #{tid}
    </select>

    <select id="checkMachItemIds" resultType="java.lang.String">
        select Mat_Goods.GoodsName
        from Bus_BudgetItem
                 Left join Mat_Goods on Bus_BudgetItem.Goodsid = Mat_Goods.id
        where Bus_BudgetItem.Tenantid = #{tid}
          and Bus_BudgetItem.MachItemid in
        <foreach collection="machitemIds" open="(" close=")" separator="," item="machitemId">
            #{machitemId}
        </foreach>
    </select>
</mapper>

