<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusSalesmanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusSalesmanPojo">
        select
id, Userid, Name, NickName, Avatar, Age, Gender, Phone, Email, Address, Level, LeaderMark, Status, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Bus_Salesman
        where Bus_Salesman.id = #{key} and Bus_Salesman.Tenantid=#{tid}
    </select>
    <sql id="selectBusSalesmanVo">
         select
id, Userid, Name, NickName, Avatar, Age, Gender, Phone, Email, Address, Level, LeaderMark, Status, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Bus_Salesman
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.sale.domain.pojo.BusSalesmanPojo">
        <include refid="selectBusSalesmanVo"/>
         where 1 = 1 and Bus_Salesman.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Bus_Salesman.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.userid != null ">
   and Bus_Salesman.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.name != null ">
   and Bus_Salesman.Name like concat('%', #{SearchPojo.name}, '%')
</if>
<if test="SearchPojo.nickname != null ">
   and Bus_Salesman.NickName like concat('%', #{SearchPojo.nickname}, '%')
</if>
<if test="SearchPojo.avatar != null ">
   and Bus_Salesman.Avatar like concat('%', #{SearchPojo.avatar}, '%')
</if>
<if test="SearchPojo.phone != null ">
   and Bus_Salesman.Phone like concat('%', #{SearchPojo.phone}, '%')
</if>
<if test="SearchPojo.email != null ">
   and Bus_Salesman.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.address != null ">
   and Bus_Salesman.Address like concat('%', #{SearchPojo.address}, '%')
</if>
<if test="SearchPojo.level != null ">
   and Bus_Salesman.Level like concat('%', #{SearchPojo.level}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Bus_Salesman.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Bus_Salesman.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Bus_Salesman.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Bus_Salesman.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Bus_Salesman.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Bus_Salesman.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Bus_Salesman.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Bus_Salesman.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Bus_Salesman.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Bus_Salesman.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Bus_Salesman.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Bus_Salesman.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Bus_Salesman.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Bus_Salesman.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Bus_Salesman.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Bus_Salesman.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.userid != null ">
   or Bus_Salesman.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.name != null ">
   or Bus_Salesman.Name like concat('%', #{SearchPojo.name}, '%')
</if>
<if test="SearchPojo.nickname != null ">
   or Bus_Salesman.NickName like concat('%', #{SearchPojo.nickname}, '%')
</if>
<if test="SearchPojo.avatar != null ">
   or Bus_Salesman.Avatar like concat('%', #{SearchPojo.avatar}, '%')
</if>
<if test="SearchPojo.phone != null ">
   or Bus_Salesman.Phone like concat('%', #{SearchPojo.phone}, '%')
</if>
<if test="SearchPojo.email != null ">
   or Bus_Salesman.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.address != null ">
   or Bus_Salesman.Address like concat('%', #{SearchPojo.address}, '%')
</if>
<if test="SearchPojo.level != null ">
   or Bus_Salesman.Level like concat('%', #{SearchPojo.level}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Bus_Salesman.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Bus_Salesman.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Bus_Salesman.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Bus_Salesman.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Bus_Salesman.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Bus_Salesman.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Bus_Salesman.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Bus_Salesman.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Bus_Salesman.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Bus_Salesman.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Bus_Salesman.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Bus_Salesman.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Bus_Salesman.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Bus_Salesman.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Bus_Salesman.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Bus_Salesman.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_Salesman(id, Userid, Name, NickName, Avatar, Age, Gender, Phone, Email, Address, Level, LeaderMark, Status, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{userid}, #{name}, #{nickname}, #{avatar}, #{age}, #{gender}, #{phone}, #{email}, #{address}, #{level}, #{leadermark}, #{status}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Salesman
        <set>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="name != null ">
                Name =#{name},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="avatar != null ">
                Avatar =#{avatar},
            </if>
            <if test="age != null">
                Age =#{age},
            </if>
            <if test="gender != null">
                Gender =#{gender},
            </if>
            <if test="phone != null ">
                Phone =#{phone},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="address != null ">
                Address =#{address},
            </if>
            <if test="level != null ">
                Level =#{level},
            </if>
            <if test="leadermark != null">
                LeaderMark =#{leadermark},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_Salesman where id = #{key} and Tenantid=#{tid}
    </delete>
</mapper>

