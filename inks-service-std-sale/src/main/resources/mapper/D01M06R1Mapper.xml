<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.D01M06R1Mapper">
    <!--    /*客户订单金额排名*/-->
    <select id="getSumPageListByGroup" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo">
        select App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as TaxAmount
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Bus_DelieryItem.Goodsid = Mat_Goods.id
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by App_Workgroup.Abbreviate, App_Workgroup.GroupName, App_Workgroup.GroupUid
        order by GroupUid
    </select>

    <!--    /*客户订单金额排名*/-->
    <select id="getSumPageListByGoods" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo">
        select Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as TaxAmount
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Bus_DelieryItem.Goodsid = Mat_Goods.id
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by Mat_Goods.GoodsUid,Mat_Goods.GoodsName, Mat_Goods.GoodsSpec, Mat_Goods.GoodsUnit
        order by GoodsUid
    </select>

    <!--    /*客户送货单金额排名*/-->
    <select id="getSumAmtByGroupMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select sum(Bus_DelieryItem.TaxAmount) as value,(case when App_Workgroup.Abbreviate is null or
        App_Workgroup.Abbreviate = '' then App_Workgroup.GroupName else Abbreviate end) as name,
        App_Workgroup.GroupUid as code ,App_Workgroup.GroupName as spec
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
            LEFT JOIN Mat_Goods ON Bus_DelieryItem.Goodsid = Mat_Goods.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{tenantid}
          and (${DateRange.DateColumn} BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
        group by App_Workgroup.Abbreviate, App_Workgroup.GroupName, App_Workgroup.GroupUid
        order by value desc
    </select>
<!--    (按省份/地级市)获取送货单客户金额排名-->
    <select id="getSumAmtByGroupProvince"  resultType="java.util.Map">
        select (case when #{province} is null or #{province} = '' then App_Workgroup.Province
        else App_Workgroup.City end) as name,
        sum(Bus_DelieryItem.TaxAmount) as value
        FROM Bus_Deliery
        RIGHT JOIN Bus_DelieryItem ON Bus_DelieryItem.Pid = Bus_Deliery.id
        LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark = 0
        and Bus_Deliery.Tenantid = #{queryParam.tenantid}
        and (${queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate} and
        #{queryParam.DateRange.EndDate})
        <if test="province != null and province != ''">
            and App_Workgroup.Province = #{province}
        </if>
        group by
        (case when #{province} is null or #{province} = '' then App_Workgroup.Province
        else App_Workgroup.City end)
        order by value desc
    </select>
    <!--    (按省份/地级市)获取销售订单单客户金额排名-->
    <select id="getSumAmtByGroupProvinceMach" resultType="java.util.Map">
        select (case when #{province} is null or #{province} = '' then App_Workgroup.Province
        else App_Workgroup.City end) as name,
        sum(Bus_MachiningItem.TaxAmount) as value
        FROM Bus_Machining
        RIGHT JOIN Bus_MachiningItem ON Bus_MachiningItem.Pid = Bus_Machining.id
        LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
        where Bus_MachiningItem.disannulmark = 0 and Bus_MachiningItem.Closed=0
        and Bus_Machining.Tenantid = #{queryParam.tenantid}
        and (${queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate} and
        #{queryParam.DateRange.EndDate})
        <if test="province != null and province != ''">
            and App_Workgroup.Province = #{province}
        </if>
        group by
        (case when #{province} is null or #{province} = '' then App_Workgroup.Province
        else App_Workgroup.City end)
        order by value desc
    </select>
    <!--    (按国家名)获取销售订单单客户金额排名-->
    <select id="getSumAmtGroupByCountry" resultType="java.util.Map">
        select App_Workgroup.Country as name,
        sum(Bus_MachiningItem.TaxAmount) as value
        FROM Bus_Machining
        RIGHT JOIN Bus_MachiningItem ON Bus_MachiningItem.Pid = Bus_Machining.id
        LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
        where Bus_MachiningItem.disannulmark = 0 and Bus_MachiningItem.Closed=0
        and Bus_Machining.Tenantid = #{queryParam.tenantid}
        and (${queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate})
        group by App_Workgroup.Country
        order by value desc
    </select>
    <!--    (按省份/地级市)获取【销售收款单+预收款】客户金额排名
    UNION ALL将两个查询的结果合并成一个结果集，再结果集group by name累加value-->
    <select id="getSumAmtByGroupProvinceCollection" resultType="java.util.Map">
        SELECT name, SUM(value) AS value
        FROM (
        SELECT (CASE WHEN #{province} IS NULL OR #{province} = '' THEN App_Workgroup.Province
        ELSE App_Workgroup.City END) AS name,
        SUM(Bus_DepositItem.Amount) AS value
        FROM Bus_Deposit
        RIGHT JOIN Bus_DepositItem ON Bus_DepositItem.Pid = Bus_Deposit.id
        LEFT JOIN App_Workgroup ON Bus_Deposit.Groupid = App_Workgroup.id
        WHERE Bus_Deposit.Tenantid = #{queryParam.tenantid}
        AND (Bus_Deposit.${queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate})
        <if test="province != null and province != ''">
            AND App_Workgroup.Province = #{province}
        </if>
        GROUP BY (CASE WHEN #{province} IS NULL OR #{province} = '' THEN App_Workgroup.Province
        ELSE App_Workgroup.City END)
        UNION ALL
        SELECT (CASE WHEN #{province} IS NULL OR #{province} = '' THEN App_Workgroup.Province
        ELSE App_Workgroup.City END) AS name,
        SUM(Bus_ReceiptItem.Amount) AS value
        FROM Bus_Receipt
        RIGHT JOIN Bus_ReceiptItem ON Bus_ReceiptItem.Pid = Bus_Receipt.id
        LEFT JOIN App_Workgroup ON Bus_Receipt.Groupid = App_Workgroup.id
        WHERE Bus_Receipt.Tenantid = #{queryParam.tenantid}
        AND (Bus_Receipt.${queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate})
        <if test="province != null and province != ''">
            AND App_Workgroup.Province = #{province}
        </if>
        GROUP BY (CASE WHEN #{province} IS NULL OR #{province} = '' THEN App_Workgroup.Province
        ELSE App_Workgroup.City END)
        ) AS combined_results
        GROUP BY name
        ORDER BY value DESC
    </select>



<!--    List<ChartPojo> getSumAmtByGoodsMax(@Param("queryParam")QueryParam queryParam,@Param("province")String province,@Param("city")String city);-->
    <!--    /*客户送货单金额排名*/-->
    <select id="getSumAmtByGoodsMax" parameterType="inks.common.core.domain.QueryParam" resultType="inks.common.core.domain.ChartPojo">
        select sum(Bus_DelieryItem.TaxAmount) as value,Mat_Goods.GoodsName as name,
        Mat_Goods.GoodsUid as code ,Mat_Goods.GoodsSpec as spec,Mat_Goods.GoodsUnit as unit
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
            LEFT JOIN Mat_Goods ON Bus_DelieryItem.Goodsid = Mat_Goods.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{queryParam.tenantid}
          and (${queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate}
          and #{queryParam.DateRange.EndDate})
        <if test="province != null and province != ''">
            and App_Workgroup.Province = #{province}
        </if>
        <if test="city != null and city != ''">
            and App_Workgroup.City = #{city}
        </if>
        group by Mat_Goods.id, Mat_Goods.GoodsName, Mat_Goods.GoodsUid, Mat_Goods.GoodsSpec, Mat_Goods.GoodsUnit
        order by value desc
    </select>
    <select id="getSumAmtByYear" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT date_format(Bus_Machining.BillDate, '%Y-%m') name,
               IFNULL(SUM(Bus_MachiningItem.TaxAmount), 0) AS value
        FROM
            Bus_MachiningItem
            INNER JOIN Bus_Machining
        ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            1 = 1
          AND date_format( Bus_Machining.BillDate
            , '%Y' )= #{SearchPojo.StartDate}
          and Bus_MachiningItem.Tenantid =#{tenantid}
        GROUP BY date_format( Bus_Machining.BillDate, '%Y-%m' )
    </select>
    <select id="getSumAmtByMonth" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT date_format(Bus_Machining.BillDate, '%Y-%m-%d') name,
               IFNULL(SUM(Bus_MachiningItem.TaxAmount), 0) AS value
        FROM
            Bus_MachiningItem
            INNER JOIN Bus_Machining
        ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            1 = 1
          AND date_format( Bus_Machining.BillDate
            , '%Y-%m' )= date_format(#{DateRange.StartDate}
            , '%Y-%m' )
          and Bus_MachiningItem.Tenantid =#{tenantid}
        GROUP BY date_format( Bus_Machining.BillDate, '%Y-%m-%d' )
    </select>
    <select id="getSumAmtByDay" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT date_format(Bus_Machining.BillDate, '%Y-%m-%d') name,
               IFNULL(SUM(Bus_MachiningItem.TaxAmount), 0) AS value
        FROM
            Bus_MachiningItem
            INNER JOIN Bus_Machining
        ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            1 = 1
          AND (date_format( Bus_Machining.BillDate
            , '%Y-%m-%d' ) BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
          and Bus_MachiningItem.Tenantid =#{tenantid}
        GROUP BY date_format( Bus_Machining.BillDate, '%Y-%m-%d' )
    </select>
    <select id="getTagSumAmtQtyByMonth" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') AS NAME,
               IFNULL(SUM(Bus_MachiningItem.TaxAmount), 0) AS
        VALUE
        FROM
            Bus_Machining
            LEFT JOIN Bus_MachiningItem
        ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            DATE_FORMAT( Bus_Machining.BillDate
            , '%Y-%m' ) = DATE_FORMAT( CURDATE( )
            , '%Y-%m' )
          and Bus_MachiningItem.Tenantid =#{tenantid}
        GROUP BY
            DATE_FORMAT(
            Bus_Machining.BillDate,
            '%Y-%m'
            )
    </select>

    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_Deliery.id,
               Bus_Deliery.RefNo,
               Bus_Deliery.BillType,
               Bus_Deliery.BillTitle,
               Bus_Deliery.BillDate,
               Bus_Deliery.Groupid,
               Bus_Deliery.Telephone,
               Bus_Deliery.Linkman,
               Bus_Deliery.DeliAdd,
               Bus_Deliery.Taxrate,
               Bus_Deliery.TranSport,
               Bus_Deliery.Salesman,
               Bus_Deliery.Salesmanid,
               Bus_Deliery.Operator,
               Bus_Deliery.Operatorid,
               Bus_Deliery.Summary,
               Bus_Deliery.CreateBy,
               Bus_Deliery.CreateByid,
               Bus_Deliery.CreateDate,
               Bus_Deliery.Lister,
               Bus_Deliery.Listerid,
               Bus_Deliery.ModifyDate,
               Bus_Deliery.Assessor,
               Bus_Deliery.Assessorid,
               Bus_Deliery.AssessDate,
               Bus_Deliery.DisannulCount,
               Bus_Deliery.BillStateCode,
               Bus_Deliery.BillStateDate,
               (CASE
                   WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_Deliery.BillTaxAmount
                   ELSE 0 - Bus_Deliery.BillTaxAmount END) as BillTaxAmount,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_Deliery.BillTaxTotal
                    ELSE 0 - Bus_Deliery.BillTaxTotal END) as BillTaxTotal,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_Deliery.BillAmount
                    ELSE 0 - Bus_Deliery.BillAmount END) as BillAmount,
               Bus_Deliery.BillReceived,
               Bus_Deliery.ItemCount,
               Bus_Deliery.PickCount,
               Bus_Deliery.FinishCount,
               Bus_Deliery.InvoCount,
               Bus_Deliery.PrintCount,
               Bus_Deliery.Custom1,
               Bus_Deliery.Custom2,
               Bus_Deliery.Custom3,
               Bus_Deliery.Custom4,
               Bus_Deliery.Custom5,
               Bus_Deliery.Custom6,
               Bus_Deliery.Custom7,
               Bus_Deliery.Custom8,
               Bus_Deliery.Custom9,
               Bus_Deliery.Custom10,
               Bus_Deliery.Tenantid,
               Bus_Deliery.TenantName,
               Bus_Deliery.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_Deliery ON App_Workgroup.id = Bus_Deliery.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Bus_Deliery.RefNo,
               Bus_Deliery.BillType,
               Bus_Deliery.BillTitle,
               Bus_Deliery.BillDate,
               Bus_Deliery.Telephone,
               Bus_Deliery.Linkman,
               Bus_Deliery.Taxrate,
               Bus_Deliery.TranSport,
               Bus_Deliery.Salesman,
               Bus_Deliery.Operator,
               Bus_Deliery.CreateBy,
               Bus_Deliery.Summary,
               Bus_Deliery.Lister,
               Bus_Deliery.Assessor,
               Bus_Deliery.ItemCount,
               Bus_Deliery.PickCount,
               Bus_Deliery.FinishCount,
               Bus_Deliery.InvoCount,
               Bus_Deliery.PrintCount,
               Bus_Deliery.TranSport,
               Bus_DelieryItem.id,
               Bus_DelieryItem.Pid,
               Bus_DelieryItem.Goodsid,
               Bus_DelieryItem.ItemCode,
               Bus_DelieryItem.ItemName,
               Bus_DelieryItem.ItemSpec,
               Bus_DelieryItem.ItemUnit,
               Bus_DelieryItem.Price,
               Bus_DelieryItem.TaxPrice,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_DelieryItem.Quantity
                    ELSE 0 - Bus_DelieryItem.Quantity END) as Quantity,
               (CASE
                   WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_DelieryItem.TaxAmount
                   ELSE 0 - Bus_DelieryItem.TaxAmount END) as TaxAmount,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_DelieryItem.Amount
                    ELSE 0 - Bus_DelieryItem.Amount END) as Amount,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发','销售单') THEN Bus_DelieryItem.TaxTotal
                    ELSE 0 - Bus_DelieryItem.TaxTotal END) as TaxTotal,
               Bus_DelieryItem.ItemTaxrate,
               Bus_DelieryItem.StdPrice,
               Bus_DelieryItem.StdAmount,
               Bus_DelieryItem.Rebate,
               Bus_DelieryItem.FreeQty,
               Bus_DelieryItem.FinishQty,
               Bus_DelieryItem.FinishClosed,
               Bus_DelieryItem.RowNum,
               Bus_DelieryItem.Remark,
               Bus_DelieryItem.CiteUid,
               Bus_DelieryItem.CiteItemid,
               Bus_DelieryItem.CustPo,
               Bus_DelieryItem.StateCode,
               Bus_DelieryItem.StateDate,
               Bus_DelieryItem.BusSQty,
               Bus_DelieryItem.BusSClosed,
               Bus_DelieryItem.MachType,
               Bus_DelieryItem.InvoQty,
               Bus_DelieryItem.InvoClosed,
               Bus_DelieryItem.ReturnQty,
               Bus_DelieryItem.ReturnMatQty,
               Bus_DelieryItem.ReturnClosed,
               Bus_DelieryItem.Salescost,
               Bus_DelieryItem.VirtualItem,
               Bus_DelieryItem.Location,
               Bus_DelieryItem.BatchNo,
               Bus_DelieryItem.MachUid,
               Bus_DelieryItem.MachItemid,
               Bus_DelieryItem.DisannulMark,
               Bus_DelieryItem.DisannulLister,
               Bus_DelieryItem.DisannulDate,
               Bus_DelieryItem.BFItemid,
               Bus_DelieryItem.AttributeJson,
               Bus_DelieryItem.Custom1,
               Bus_DelieryItem.Custom2,
               Bus_DelieryItem.Custom3,
               Bus_DelieryItem.Custom4,
               Bus_DelieryItem.Custom5,
               Bus_DelieryItem.Custom6,
               Bus_DelieryItem.Custom7,
               Bus_DelieryItem.Custom8,
               Bus_DelieryItem.Custom9,
               Bus_DelieryItem.Custom10,
               Bus_DelieryItem.Custom11,
               Bus_DelieryItem.Custom12,
               Bus_DelieryItem.Custom13,
               Bus_DelieryItem.Custom14,
               Bus_DelieryItem.Custom15,
               Bus_DelieryItem.Custom16,
               Bus_DelieryItem.Custom17,
               Bus_DelieryItem.Custom18,
               Bus_DelieryItem.Tenantid,
               Bus_DelieryItem.Revision,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM App_Workgroup
                 RIGHT JOIN Bus_Deliery ON App_Workgroup.id = Bus_Deliery.Groupid
                 RIGHT JOIN Bus_DelieryItem ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_DelieryItem.Goodsid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Deliery.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and (Bus_Deliery.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate})
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and (${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
        ,Bus_DelieryItem.RowNum
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Bus_Deliery.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Deliery.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Deliery.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Deliery.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.telephone != null ">
            and Bus_Deliery.telephone like concat('%', #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and Bus_Deliery.linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.deliadd != null ">
            and Bus_Deliery.deliadd like concat('%', #{SearchPojo.deliadd}, '%')
        </if>
        <if test="SearchPojo.transport != null ">
            and Bus_Deliery.transport like concat('%', #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.salesman != null ">
            and Bus_Deliery.salesman like concat('%', #{SearchPojo.salesman}, '%')
        </if>
        <if test="SearchPojo.salesmanid != null ">
            and Bus_Deliery.salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_Deliery.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Bus_Deliery.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Deliery.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Deliery.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Deliery.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Deliery.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Deliery.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_Deliery.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_Deliery.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Bus_Deliery.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Deliery.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Deliery.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Deliery.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Deliery.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Deliery.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Deliery.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Deliery.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Deliery.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Deliery.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Deliery.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Deliery.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Bus_DelieryItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_DelieryItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_DelieryItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_DelieryItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_DelieryItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_DelieryItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Bus_DelieryItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_DelieryItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_DelieryItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_DelieryItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_DelieryItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Bus_DelieryItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Bus_DelieryItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Bus_DelieryItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Bus_DelieryItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
            and Bus_DelieryItem.machtype like concat('%', #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Bus_DelieryItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Bus_DelieryItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Bus_DelieryItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Bus_DelieryItem.machitemid=#{SearchPojo.machitemid}
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Bus_DelieryItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Bus_DelieryItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.telephone != null ">
                or Bus_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or Bus_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.deliadd != null ">
                or Bus_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
            </if>
            <if test="SearchPojo.transport != null ">
                or Bus_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.salesman != null ">
                or Bus_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
            </if>
            <if test="SearchPojo.salesmanid != null ">
                or Bus_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Bus_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Bus_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Bus_DelieryItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_DelieryItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_DelieryItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_DelieryItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_DelieryItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_DelieryItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Bus_DelieryItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_DelieryItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_DelieryItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_DelieryItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_DelieryItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Bus_DelieryItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Bus_DelieryItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Bus_DelieryItem.CustPo like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Bus_DelieryItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
                or Bus_DelieryItem.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Bus_DelieryItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Bus_DelieryItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Bus_DelieryItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Bus_DelieryItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Bus_DelieryItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Bus_DelieryItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDelieryPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Deliery.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Deliery.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Bus_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.telephone != null ">
            and Bus_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and Bus_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.deliadd != null ">
            and Bus_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
        </if>
        <if test="SearchPojo.transport != null ">
            and Bus_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.salesman != null ">
            and Bus_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
        </if>
        <if test="SearchPojo.salesmanid != null ">
            and Bus_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Bus_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Bus_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.telephone != null ">
                or Bus_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or Bus_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.deliadd != null ">
                or Bus_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
            </if>
            <if test="SearchPojo.transport != null ">
                or Bus_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.salesman != null ">
                or Bus_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
            </if>
            <if test="SearchPojo.salesmanid != null ">
                or Bus_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Bus_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Bus_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>
</mapper>

