<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusIntendedMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusIntendedPojo">
        select Bus_Intended.id,
               Bus_Intended.RefNo,
               Bus_Intended.BillType,
               Bus_Intended.BillTitle,
               Bus_Intended.BillDate,
               Bus_Intended.Probability,
               Bus_Intended.Groupid,
               Bus_Intended.TraderCode,
               Bus_Intended.TraderName,
               Bus_Intended.CustAddress,
               Bus_Intended.Linkman,
               Bus_Intended.Tel,
               Bus_Intended.Fax,
               Bus_Intended.Periods,
               Bus_Intended.ValidityDate,
               Bus_Intended.ExpireDate,
               Bus_Intended.ValidityDesc,
               Bus_Intended.Currency,
               Bus_Intended.Delivery,
               Bus_Intended.Payment,
               Bus_Intended.Seller,
               Bus_Intended.Summary,
               Bus_Intended.BillClause,
               Bus_Intended.BillTaxAmount,
               Bus_Intended.BillAmount,
               Bus_Intended.BillTaxTotal,
               Bus_Intended.CreateBy,
               Bus_Intended.CreateByid,
               Bus_Intended.CreateDate,
               Bus_Intended.Lister,
               Bus_Intended.Listerid,
               Bus_Intended.ModifyDate,
               Bus_Intended.Submitterid,
               Bus_Intended.Submitter,
               Bus_Intended.SubmitDate,
               Bus_Intended.Assessor,
               Bus_Intended.Assessorid,
               Bus_Intended.AssessDate,
               Bus_Intended.ItemCount,
               Bus_Intended.FinishCount,
               Bus_Intended.DisannulCount,
               Bus_Intended.PrintCount,
               Bus_Intended.Custom1,
               Bus_Intended.Custom2,
               Bus_Intended.Custom3,
               Bus_Intended.Custom4,
               Bus_Intended.Custom5,
               Bus_Intended.Custom6,
               Bus_Intended.Custom7,
               Bus_Intended.Custom8,
               Bus_Intended.Custom9,
               Bus_Intended.Custom10,
               Bus_Intended.Deptid,
               Bus_Intended.Tenantid,
               Bus_Intended.TenantName,
               Bus_Intended.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        from Bus_Intended
                 Left Join App_Workgroup on Bus_Intended.Groupid = App_Workgroup.id
        where Bus_Intended.id = #{key}
          and Bus_Intended.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Bus_Intended.id,
               Bus_Intended.RefNo,
               Bus_Intended.BillType,
               Bus_Intended.BillTitle,
               Bus_Intended.BillDate,
               Bus_Intended.Probability,
               Bus_Intended.Groupid,
               Bus_Intended.TraderCode,
               Bus_Intended.TraderName,
               Bus_Intended.CustAddress,
               Bus_Intended.Linkman,
               Bus_Intended.Tel,
               Bus_Intended.Fax,
               Bus_Intended.Periods,
               Bus_Intended.ValidityDate,
               Bus_Intended.ExpireDate,
               Bus_Intended.ValidityDesc,
               Bus_Intended.Currency,
               Bus_Intended.Delivery,
               Bus_Intended.Payment,
               Bus_Intended.Seller,
               Bus_Intended.Summary,
               Bus_Intended.BillClause,
               Bus_Intended.BillTaxAmount,
               Bus_Intended.BillAmount,
               Bus_Intended.BillTaxTotal,
               Bus_Intended.CreateBy,
               Bus_Intended.CreateByid,
               Bus_Intended.CreateDate,
               Bus_Intended.Lister,
               Bus_Intended.Listerid,
               Bus_Intended.ModifyDate,
               Bus_Intended.Submitterid,
               Bus_Intended.Submitter,
               Bus_Intended.SubmitDate,
               Bus_Intended.Assessor,
               Bus_Intended.Assessorid,
               Bus_Intended.AssessDate,
               Bus_Intended.ItemCount,
               Bus_Intended.FinishCount,
               Bus_Intended.DisannulCount,
               Bus_Intended.PrintCount,
               Bus_Intended.Custom1,
               Bus_Intended.Custom2,
               Bus_Intended.Custom3,
               Bus_Intended.Custom4,
               Bus_Intended.Custom5,
               Bus_Intended.Custom6,
               Bus_Intended.Custom7,
               Bus_Intended.Custom8,
               Bus_Intended.Custom9,
               Bus_Intended.Custom10,
               Bus_Intended.Deptid,
               Bus_Intended.Tenantid,
               Bus_Intended.TenantName,
               Bus_Intended.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        from Bus_Intended Left Join App_Workgroup  on Bus_Intended.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        select Bus_IntendedItem.id,
               Bus_IntendedItem.Pid,
               Bus_IntendedItem.Goodsid,
               Bus_IntendedItem.ItemType,
               Bus_IntendedItem.ItemName,
               Bus_IntendedItem.ItemSpec,
               Bus_IntendedItem.ItemUnit,
               Bus_IntendedItem.DmsGoodsid,
               Bus_IntendedItem.Quantity,
               Bus_IntendedItem.StdPrice,
               Bus_IntendedItem.StdAmount,
               Bus_IntendedItem.Rebate,
               Bus_IntendedItem.Price,
               Bus_IntendedItem.Amount,
               Bus_IntendedItem.ItemTaxrate,
               Bus_IntendedItem.TaxPrice,
               Bus_IntendedItem.TaxTotal,
               Bus_IntendedItem.TaxAmount,
               Bus_IntendedItem.PlanDate,
               Bus_IntendedItem.Remark,
               Bus_IntendedItem.RowNum,
               Bus_IntendedItem.AttributeJson,
               Bus_IntendedItem.CostItemJson,
               Bus_IntendedItem.CostGroupJson,
               Bus_IntendedItem.VirtualItem,
               Bus_IntendedItem.FinishMark,
               Bus_IntendedItem.DisannulMark,
               Bus_IntendedItem.DisannulListerid,
               Bus_IntendedItem.DisannulLister,
               Bus_IntendedItem.DisannulDate,
               Bus_IntendedItem.Custom1,
               Bus_IntendedItem.Custom2,
               Bus_IntendedItem.Custom3,
               Bus_IntendedItem.Custom4,
               Bus_IntendedItem.Custom5,
               Bus_IntendedItem.Custom6,
               Bus_IntendedItem.Custom7,
               Bus_IntendedItem.Custom8,
               Bus_IntendedItem.Custom9,
               Bus_IntendedItem.Custom10,
               Bus_IntendedItem.Tenantid,
               Bus_IntendedItem.Revision,
               Bus_Intended.RefNo,
               Bus_Intended.BillType,
               Bus_Intended.BillTitle,
               Bus_Intended.BillDate,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Bus_DmsGoods.GoodsUid  as DmsGoodsUid,
               Bus_DmsGoods.GoodsName as DmsGoodsName,
               Bus_DmsGoods.GoodsSpec as DmsGoodsSpec,
               Bus_DmsGoods.GoodsUnit as DmsGoodsUnit
        from Bus_IntendedItem Left Join Bus_Intended on Bus_IntendedItem.Pid = Bus_Intended.id
        Left Join App_Workgroup  on Bus_Intended.Groupid = App_Workgroup.id
        Left Join Mat_Goods on Bus_IntendedItem.Goodsid = Mat_Goods.id
        Left join Bus_DmsGoods on Bus_IntendedItem.DmsGoodsid = Bus_DmsGoods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusIntendeditemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Intended.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_Intended.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Bus_Intended.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_Intended.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_Intended.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.probability != null">
            and Bus_Intended.probability like concat('%',
                #{SearchPojo.probability}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_Intended.groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.tradercode != null">
            and Bus_Intended.tradercode like concat('%',
                #{SearchPojo.tradercode}, '%')
        </if>
        <if test="SearchPojo.tradername != null">
            and Bus_Intended.tradername like concat('%',
                #{SearchPojo.tradername}, '%')
        </if>
        <if test="SearchPojo.custaddress != null">
            and Bus_Intended.custaddress like concat('%',
                #{SearchPojo.custaddress}, '%')
        </if>
        <if test="SearchPojo.linkman != null">
            and Bus_Intended.linkman like concat('%',
                #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.tel != null">
            and Bus_Intended.tel like concat('%',
                #{SearchPojo.tel}, '%')
        </if>
        <if test="SearchPojo.fax != null">
            and Bus_Intended.fax like concat('%',
                #{SearchPojo.fax}, '%')
        </if>
        <if test="SearchPojo.periods != null">
            and Bus_Intended.periods like concat('%',
                #{SearchPojo.periods}, '%')
        </if>
        <if test="SearchPojo.validitydate != null">
            and Bus_Intended.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
        </if>
        <if test="SearchPojo.validitydesc != null ">
            and Bus_Intended.ValidityDesc like concat('%', #{SearchPojo.validitydesc}, '%')
        </if>
        <if test="SearchPojo.currency != null">
            and Bus_Intended.currency like concat('%',
                #{SearchPojo.currency}, '%')
        </if>
        <if test="SearchPojo.delivery != null">
            and Bus_Intended.delivery like concat('%',
                #{SearchPojo.delivery}, '%')
        </if>
        <if test="SearchPojo.payment != null">
            and Bus_Intended.payment like concat('%',
                #{SearchPojo.payment}, '%')
        </if>
        <if test="SearchPojo.seller != null">
            and Bus_Intended.seller like concat('%',
                #{SearchPojo.seller}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_Intended.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.billclause != null">
            and Bus_Intended.billclause like concat('%',
                #{SearchPojo.billclause}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_Intended.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_Intended.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_Intended.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_Intended.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.submitterid != null">
            and Bus_Intended.submitterid like concat('%',
                #{SearchPojo.submitterid}, '%')
        </if>
        <if test="SearchPojo.submitter != null">
            and Bus_Intended.submitter like concat('%',
                #{SearchPojo.submitter}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Bus_Intended.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Bus_Intended.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_Intended.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_Intended.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_Intended.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_Intended.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_Intended.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_Intended.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_Intended.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_Intended.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_Intended.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_Intended.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Bus_Intended.deptid like concat('%',
                #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_Intended.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_Intended.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_Intended.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_Intended.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.probability != null">
                or Bus_Intended.Probability like concat('%', #{SearchPojo.probability}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_Intended.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.tradercode != null">
                or Bus_Intended.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
            </if>
            <if test="SearchPojo.tradername != null">
                or Bus_Intended.TraderName like concat('%', #{SearchPojo.tradername}, '%')
            </if>
            <if test="SearchPojo.custaddress != null">
                or Bus_Intended.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
            </if>
            <if test="SearchPojo.linkman != null">
                or Bus_Intended.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.tel != null">
                or Bus_Intended.Tel like concat('%', #{SearchPojo.tel}, '%')
            </if>
            <if test="SearchPojo.fax != null">
                or Bus_Intended.Fax like concat('%', #{SearchPojo.fax}, '%')
            </if>
            <if test="SearchPojo.periods != null">
                or Bus_Intended.Periods like concat('%', #{SearchPojo.periods}, '%')
            </if>
            <if test="SearchPojo.validitydate != null">
                or Bus_Intended.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
            </if>
            <if test="SearchPojo.validitydesc != null ">
                or Bus_Intended.ValidityDesc like concat('%', #{SearchPojo.validitydesc}, '%')
            </if>
            <if test="SearchPojo.currency != null">
                or Bus_Intended.Currency like concat('%', #{SearchPojo.currency}, '%')
            </if>
            <if test="SearchPojo.delivery != null">
                or Bus_Intended.Delivery like concat('%', #{SearchPojo.delivery}, '%')
            </if>
            <if test="SearchPojo.payment != null">
                or Bus_Intended.Payment like concat('%', #{SearchPojo.payment}, '%')
            </if>
            <if test="SearchPojo.seller != null">
                or Bus_Intended.Seller like concat('%', #{SearchPojo.seller}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_Intended.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.billclause != null">
                or Bus_Intended.BillClause like concat('%', #{SearchPojo.billclause}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_Intended.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_Intended.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_Intended.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_Intended.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.submitterid != null">
                or Bus_Intended.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
            </if>
            <if test="SearchPojo.submitter != null">
                or Bus_Intended.Submitter like concat('%', #{SearchPojo.submitter}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Bus_Intended.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Bus_Intended.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_Intended.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_Intended.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_Intended.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_Intended.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_Intended.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_Intended.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_Intended.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_Intended.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_Intended.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_Intended.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Bus_Intended.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_Intended.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusIntendedPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Intended.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_Intended.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Bus_Intended.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_Intended.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_Intended.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.probability != null">
            and Bus_Intended.Probability like concat('%',
                #{SearchPojo.probability}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_Intended.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.tradercode != null">
            and Bus_Intended.TraderCode like concat('%',
                #{SearchPojo.tradercode}, '%')
        </if>
        <if test="SearchPojo.tradername != null">
            and Bus_Intended.TraderName like concat('%',
                #{SearchPojo.tradername}, '%')
        </if>
        <if test="SearchPojo.custaddress != null">
            and Bus_Intended.CustAddress like concat('%',
                #{SearchPojo.custaddress}, '%')
        </if>
        <if test="SearchPojo.linkman != null">
            and Bus_Intended.Linkman like concat('%',
                #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.tel != null">
            and Bus_Intended.Tel like concat('%',
                #{SearchPojo.tel}, '%')
        </if>
        <if test="SearchPojo.fax != null">
            and Bus_Intended.Fax like concat('%',
                #{SearchPojo.fax}, '%')
        </if>
        <if test="SearchPojo.periods != null">
            and Bus_Intended.Periods like concat('%',
                #{SearchPojo.periods}, '%')
        </if>
        <if test="SearchPojo.validitydate != null">
            and Bus_Intended.ValidityDate like concat('%',
                #{SearchPojo.validitydate}, '%')
        </if>
        <if test="SearchPojo.validitydesc != null ">
            and Bus_Intended.ValidityDesc like concat('%', #{SearchPojo.validitydesc}, '%')
        </if>
        <if test="SearchPojo.currency != null">
            and Bus_Intended.Currency like concat('%',
                #{SearchPojo.currency}, '%')
        </if>
        <if test="SearchPojo.delivery != null">
            and Bus_Intended.Delivery like concat('%',
                #{SearchPojo.delivery}, '%')
        </if>
        <if test="SearchPojo.payment != null">
            and Bus_Intended.Payment like concat('%',
                #{SearchPojo.payment}, '%')
        </if>
        <if test="SearchPojo.seller != null">
            and Bus_Intended.Seller like concat('%',
                #{SearchPojo.seller}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_Intended.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.billclause != null">
            and Bus_Intended.BillClause like concat('%',
                #{SearchPojo.billclause}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_Intended.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_Intended.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_Intended.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_Intended.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.submitterid != null">
            and Bus_Intended.Submitterid like concat('%',
                #{SearchPojo.submitterid}, '%')
        </if>
        <if test="SearchPojo.submitter != null">
            and Bus_Intended.Submitter like concat('%',
                #{SearchPojo.submitter}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Bus_Intended.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Bus_Intended.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_Intended.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_Intended.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_Intended.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_Intended.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_Intended.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_Intended.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_Intended.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_Intended.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_Intended.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_Intended.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Bus_Intended.Deptid like concat('%',
                #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_Intended.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_Intended.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_Intended.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_Intended.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.probability != null">
                or Bus_Intended.Probability like concat('%', #{SearchPojo.probability}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_Intended.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.tradercode != null">
                or Bus_Intended.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
            </if>
            <if test="SearchPojo.tradername != null">
                or Bus_Intended.TraderName like concat('%', #{SearchPojo.tradername}, '%')
            </if>
            <if test="SearchPojo.custaddress != null">
                or Bus_Intended.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
            </if>
            <if test="SearchPojo.linkman != null">
                or Bus_Intended.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.tel != null">
                or Bus_Intended.Tel like concat('%', #{SearchPojo.tel}, '%')
            </if>
            <if test="SearchPojo.fax != null">
                or Bus_Intended.Fax like concat('%', #{SearchPojo.fax}, '%')
            </if>
            <if test="SearchPojo.periods != null">
                or Bus_Intended.Periods like concat('%', #{SearchPojo.periods}, '%')
            </if>
            <if test="SearchPojo.validitydate != null">
                or Bus_Intended.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
            </if>
            <if test="SearchPojo.validitydesc != null ">
                or Bus_Intended.ValidityDesc like concat('%', #{SearchPojo.validitydesc}, '%')
            </if>
            <if test="SearchPojo.currency != null">
                or Bus_Intended.Currency like concat('%', #{SearchPojo.currency}, '%')
            </if>
            <if test="SearchPojo.delivery != null">
                or Bus_Intended.Delivery like concat('%', #{SearchPojo.delivery}, '%')
            </if>
            <if test="SearchPojo.payment != null">
                or Bus_Intended.Payment like concat('%', #{SearchPojo.payment}, '%')
            </if>
            <if test="SearchPojo.seller != null">
                or Bus_Intended.Seller like concat('%', #{SearchPojo.seller}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_Intended.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.billclause != null">
                or Bus_Intended.BillClause like concat('%', #{SearchPojo.billclause}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_Intended.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_Intended.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_Intended.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_Intended.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.submitterid != null">
                or Bus_Intended.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
            </if>
            <if test="SearchPojo.submitter != null">
                or Bus_Intended.Submitter like concat('%', #{SearchPojo.submitter}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Bus_Intended.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Bus_Intended.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_Intended.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_Intended.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_Intended.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_Intended.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_Intended.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_Intended.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_Intended.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_Intended.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_Intended.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_Intended.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Bus_Intended.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_Intended.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_Intended(id, RefNo, BillType, BillTitle, BillDate, Probability, Groupid, TraderCode, TraderName, CustAddress, Linkman, Tel, Fax, Periods, ValidityDate, ExpireDate, ValidityDesc, Currency, Delivery, Payment, Seller, Summary, BillClause, BillTaxAmount, BillAmount, BillTaxTotal, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Submitterid, Submitter, SubmitDate, Assessor, Assessorid, AssessDate, ItemCount, FinishCount, DisannulCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{probability}, #{groupid}, #{tradercode}, #{tradername}, #{custaddress}, #{linkman}, #{tel}, #{fax}, #{periods}, #{validitydate}, #{expiredate}, #{validitydesc}, #{currency}, #{delivery}, #{payment}, #{seller}, #{summary}, #{billclause}, #{billtaxamount}, #{billamount}, #{billtaxtotal}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{submitterid}, #{submitter}, #{submitdate}, #{assessor}, #{assessorid}, #{assessdate}, #{itemcount}, #{finishcount}, #{disannulcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Intended
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="probability != null">
                Probability =#{probability},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="tradercode != null">
                TraderCode =#{tradercode},
            </if>
            <if test="tradername != null">
                TraderName =#{tradername},
            </if>
            <if test="custaddress != null">
                CustAddress =#{custaddress},
            </if>
            <if test="linkman != null">
                Linkman =#{linkman},
            </if>
            <if test="tel != null">
                Tel =#{tel},
            </if>
            <if test="fax != null">
                Fax =#{fax},
            </if>
            <if test="periods != null">
                Periods =#{periods},
            </if>
            <if test="validitydate != null">
                ValidityDate =#{validitydate},
            </if>
            <if test="expiredate != null">
            ExpireDate =#{expiredate},
        </if>
            <if test="validitydesc != null ">
            ValidityDesc =#{validitydesc},
        </if>
            <if test="currency != null ">
                Currency =#{currency},
            </if>
            <if test="delivery != null">
                Delivery =#{delivery},
            </if>
            <if test="payment != null">
                Payment =#{payment},
            </if>
            <if test="seller != null">
                Seller =#{seller},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="billclause != null">
                BillClause =#{billclause},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="submitterid != null">
                Submitterid =#{submitterid},
            </if>
            <if test="submitter != null">
                Submitter =#{submitter},
            </if>
            <if test="submitdate != null">
                SubmitDate =#{submitdate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Bus_Intended where id = #{key} and Tenantid=#{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Intended SET
        Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision+1
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusIntendedPojo">
        select
        id
        from Bus_IntendedItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
</mapper>

