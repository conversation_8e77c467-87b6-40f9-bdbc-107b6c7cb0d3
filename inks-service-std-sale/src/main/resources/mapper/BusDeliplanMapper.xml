<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusDeliplanMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusDeliplanPojo">
        <include refid="selectbillVo"/>
        where Bus_DeliPlan.id = #{key}
          and Bus_DeliPlan.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.PaymentMethod,
               Bus_DeliPlan.id,
               Bus_DeliPlan.RefNo,
               Bus_DeliPlan.BillType,
               Bus_DeliPlan.BillTitle,
               Bus_DeliPlan.BillDate,
               Bus_DeliPlan.Groupid,
               Bus_DeliPlan.Telephone,
               Bus_DeliPlan.Linkman,
               Bus_DeliPlan.DeliAdd,
               Bus_DeliPlan.Taxrate,
               Bus_DeliPlan.TranSport,
               Bus_DeliPlan.Salesman,
               Bus_DeliPlan.Salesmanid,
               Bus_DeliPlan.Operator,
               Bus_DeliPlan.Operatorid,
               Bus_DeliPlan.Summary,
               Bus_DeliPlan.CreateBy,
               Bus_DeliPlan.CreateByid,
               Bus_DeliPlan.CreateDate,
               Bus_DeliPlan.Lister,
               Bus_DeliPlan.Listerid,
               Bus_DeliPlan.ModifyDate,
               Bus_DeliPlan.Assessor,
               Bus_DeliPlan.Assessorid,
               Bus_DeliPlan.AssessDate,
               Bus_DeliPlan.DisannulCount,
               Bus_DeliPlan.BillStateCode,
               Bus_DeliPlan.BillStateDate,
               Bus_DeliPlan.BillTaxAmount,
               Bus_DeliPlan.BillTaxTotal,
               Bus_DeliPlan.BillAmount,
               Bus_DeliPlan.BillReceived,
               Bus_DeliPlan.ItemCount,
               Bus_DeliPlan.PickCount,
               Bus_DeliPlan.FinishCount,
               Bus_DeliPlan.PrintCount,
               Bus_DeliPlan.OaFlowMark,
               Bus_DeliPlan.BillPlanDate,
               Bus_DeliPlan.Custom1,
               Bus_DeliPlan.Custom2,
               Bus_DeliPlan.Custom3,
               Bus_DeliPlan.Custom4,
               Bus_DeliPlan.Custom5,
               Bus_DeliPlan.Custom6,
               Bus_DeliPlan.Custom7,
               Bus_DeliPlan.Custom8,
               Bus_DeliPlan.Custom9,
               Bus_DeliPlan.Custom10,
               Bus_DeliPlan.Tenantid,
               Bus_DeliPlan.TenantName,
               Bus_DeliPlan.Revision
        from Bus_DeliPlan
                 LEFT JOIN App_Workgroup ON Bus_DeliPlan.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        select Bus_DeliPlan.RefNo,
               Bus_DeliPlan.BillType,
               Bus_DeliPlan.BillTitle,
               Bus_DeliPlan.BillDate,
               Bus_DeliPlan.Groupid,
               Bus_DeliPlan.Operator,
               Bus_DeliPlan.CreateBy,
               Bus_DeliPlan.Lister,
               Bus_DeliPlan.Assessor,
               Bus_DeliPlan.BillPlanDate,
               Bus_DeliPlan.Summary,
               Bus_DeliPlanItem.*,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoods"/>
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        from Bus_DeliPlanItem
                 left join Bus_DeliPlan on Bus_DeliPlan.id = Bus_DeliPlanItem.Pid
                 LEFT JOIN App_Workgroup ON Bus_DeliPlan.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Bus_DeliPlanItem.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDeliplanitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_DeliPlan.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_DeliPlan.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Bus_DeliPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_DeliPlan.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_DeliPlan.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_DeliPlan.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.telephone != null">
            and Bus_DeliPlan.Telephone like concat('%',
                #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.linkman != null">
            and Bus_DeliPlan.Linkman like concat('%',
                #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.deliadd != null">
            and Bus_DeliPlan.DeliAdd like concat('%',
                #{SearchPojo.deliadd}, '%')
        </if>
        <if test="SearchPojo.transport != null">
            and Bus_DeliPlan.TranSport like concat('%',
                #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.salesman != null">
            and Bus_DeliPlan.Salesman like concat('%',
                #{SearchPojo.salesman}, '%')
        </if>
        <if test="SearchPojo.salesmanid != null">
            and Bus_DeliPlan.Salesmanid like concat('%',
                #{SearchPojo.salesmanid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Bus_DeliPlan.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Bus_DeliPlan.Operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_DeliPlan.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_DeliPlan.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_DeliPlan.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_DeliPlan.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_DeliPlan.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Bus_DeliPlan.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Bus_DeliPlan.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null">
            and Bus_DeliPlan.BillStateCode like concat('%',
                #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_DeliPlan.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_DeliPlan.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_DeliPlan.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_DeliPlan.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_DeliPlan.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_DeliPlan.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_DeliPlan.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_DeliPlan.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_DeliPlan.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_DeliPlan.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_DeliPlan.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Bus_DeliPlanItem.machitemid=#{SearchPojo.machitemid}
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Bus_DeliPlanItem.machuid=#{SearchPojo.machuid}
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Bus_DeliPlanItem.citeitemid=#{SearchPojo.citeitemid}
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Bus_DeliPlanItem.citeuid=#{SearchPojo.citeuid}
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_DeliPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_DeliPlan.BillType like concat('%',
                    #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_DeliPlan.BillTitle like concat('%',
                    #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_DeliPlan.Groupid like concat('%',
                    #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.telephone != null">
                or Bus_DeliPlan.Telephone like concat('%',
                    #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.linkman != null">
                or Bus_DeliPlan.Linkman like concat('%',
                    #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.deliadd != null">
                or Bus_DeliPlan.DeliAdd like concat('%',
                    #{SearchPojo.deliadd}, '%')
            </if>
            <if test="SearchPojo.transport != null">
                or Bus_DeliPlan.TranSport like concat('%',
                    #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.salesman != null">
                or Bus_DeliPlan.Salesman like concat('%',
                    #{SearchPojo.salesman}, '%')
            </if>
            <if test="SearchPojo.salesmanid != null">
                or Bus_DeliPlan.Salesmanid like concat('%',
                    #{SearchPojo.salesmanid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Bus_DeliPlan.Operator like concat('%',
                    #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Bus_DeliPlan.Operatorid like concat('%',
                    #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_DeliPlan.Summary like concat('%',
                    #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_DeliPlan.CreateBy like concat('%',
                    #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_DeliPlan.CreateByid like concat('%',
                    #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_DeliPlan.Lister like concat('%',
                    #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_DeliPlan.Listerid like concat('%',
                    #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Bus_DeliPlan.Assessor like concat('%',
                    #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Bus_DeliPlan.Assessorid like concat('%',
                    #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null">
                or Bus_DeliPlan.BillStateCode like concat('%',
                    #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_DeliPlan.Custom1 like concat('%',
                    #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_DeliPlan.Custom2 like concat('%',
                    #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_DeliPlan.Custom3 like concat('%',
                    #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_DeliPlan.Custom4 like concat('%',
                    #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_DeliPlan.Custom5 like concat('%',
                    #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_DeliPlan.Custom6 like concat('%',
                    #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_DeliPlan.Custom7 like concat('%',
                    #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_DeliPlan.Custom8 like concat('%',
                    #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_DeliPlan.Custom9 like concat('%',
                    #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_DeliPlan.Custom10 like concat('%',
                    #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_DeliPlan.TenantName like concat('%',
                    #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Bus_DeliPlanItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Bus_DeliPlanItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                and Bus_DeliPlanItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                and Bus_DeliPlanItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDeliplanPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_DeliPlan.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_DeliPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Bus_DeliPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_DeliPlan.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_DeliPlan.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_DeliPlan.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.telephone != null">
            and Bus_DeliPlan.Telephone like concat('%',
                #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.linkman != null">
            and Bus_DeliPlan.Linkman like concat('%',
                #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.deliadd != null">
            and Bus_DeliPlan.DeliAdd like concat('%',
                #{SearchPojo.deliadd}, '%')
        </if>
        <if test="SearchPojo.transport != null">
            and Bus_DeliPlan.TranSport like concat('%',
                #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.salesman != null">
            and Bus_DeliPlan.Salesman like concat('%',
                #{SearchPojo.salesman}, '%')
        </if>
        <if test="SearchPojo.salesmanid != null">
            and Bus_DeliPlan.Salesmanid like concat('%',
                #{SearchPojo.salesmanid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Bus_DeliPlan.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Bus_DeliPlan.Operatorid like concat('%',
                #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_DeliPlan.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_DeliPlan.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_DeliPlan.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_DeliPlan.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_DeliPlan.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Bus_DeliPlan.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Bus_DeliPlan.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null">
            and Bus_DeliPlan.BillStateCode like concat('%',
                #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_DeliPlan.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_DeliPlan.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_DeliPlan.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_DeliPlan.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_DeliPlan.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_DeliPlan.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_DeliPlan.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_DeliPlan.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_DeliPlan.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_DeliPlan.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_DeliPlan.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_DeliPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_DeliPlan.BillType like concat('%',
                    #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_DeliPlan.BillTitle like concat('%',
                    #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_DeliPlan.Groupid like concat('%',
                    #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.telephone != null">
                or Bus_DeliPlan.Telephone like concat('%',
                    #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.linkman != null">
                or Bus_DeliPlan.Linkman like concat('%',
                    #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.deliadd != null">
                or Bus_DeliPlan.DeliAdd like concat('%',
                    #{SearchPojo.deliadd}, '%')
            </if>
            <if test="SearchPojo.transport != null">
                or Bus_DeliPlan.TranSport like concat('%',
                    #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.salesman != null">
                or Bus_DeliPlan.Salesman like concat('%',
                    #{SearchPojo.salesman}, '%')
            </if>
            <if test="SearchPojo.salesmanid != null">
                or Bus_DeliPlan.Salesmanid like concat('%',
                    #{SearchPojo.salesmanid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Bus_DeliPlan.Operator like concat('%',
                    #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Bus_DeliPlan.Operatorid like concat('%',
                    #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_DeliPlan.Summary like concat('%',
                    #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_DeliPlan.CreateBy like concat('%',
                    #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_DeliPlan.CreateByid like concat('%',
                    #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_DeliPlan.Lister like concat('%',
                    #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_DeliPlan.Listerid like concat('%',
                    #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Bus_DeliPlan.Assessor like concat('%',
                    #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Bus_DeliPlan.Assessorid like concat('%',
                    #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null">
                or Bus_DeliPlan.BillStateCode like concat('%',
                    #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_DeliPlan.Custom1 like concat('%',
                    #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_DeliPlan.Custom2 like concat('%',
                    #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_DeliPlan.Custom3 like concat('%',
                    #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_DeliPlan.Custom4 like concat('%',
                    #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_DeliPlan.Custom5 like concat('%',
                    #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_DeliPlan.Custom6 like concat('%',
                    #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_DeliPlan.Custom7 like concat('%',
                    #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_DeliPlan.Custom8 like concat('%',
                    #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_DeliPlan.Custom9 like concat('%',
                    #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_DeliPlan.Custom10 like concat('%',
                    #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_DeliPlan.TenantName like concat('%',
                    #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_DeliPlan(id, RefNo, BillType, BillTitle, BillDate, Groupid, Telephone, Linkman, DeliAdd, Taxrate, TranSport, Salesman, Salesmanid, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, DisannulCount, BillStateCode, BillStateDate, BillTaxAmount, BillTaxTotal, BillAmount, BillReceived, ItemCount, PickCount, FinishCount, PrintCount, OaFlowMark, BillPlanDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{groupid}, #{telephone}, #{linkman}, #{deliadd}, #{taxrate}, #{transport}, #{salesman}, #{salesmanid}, #{operator}, #{operatorid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{disannulcount}, #{billstatecode}, #{billstatedate}, #{billtaxamount}, #{billtaxtotal}, #{billamount}, #{billreceived}, #{itemcount}, #{pickcount}, #{finishcount}, #{printcount}, #{oaflowmark}, #{billplandate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_DeliPlan
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="telephone != null">
                Telephone =#{telephone},
            </if>
            <if test="linkman != null">
                Linkman =#{linkman},
            </if>
            <if test="deliadd != null">
                DeliAdd =#{deliadd},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="transport != null">
                TranSport =#{transport},
            </if>
            <if test="salesman != null">
                Salesman =#{salesman},
            </if>
            <if test="salesmanid != null">
                Salesmanid =#{salesmanid},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="billstatecode != null">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billreceived != null">
                BillReceived =#{billreceived},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="pickcount != null">
                PickCount =#{pickcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="billplandate != null">
            BillPlanDate =#{billplandate},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_DeliPlan
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_DeliPlan
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusDeliplanPojo">
        select id
        from Bus_DeliPlanItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <update id="updateDisannulCount">
        update Bus_DeliPlan
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Bus_DeliPlanItem
                                     where Bus_DeliPlanItem.Pid = #{key}
                                       and Bus_DeliPlanItem.Tenantid = #{tid}
                                       and Bus_DeliPlanItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <update id="updateFinishCount">
        update Bus_DeliPlan
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Bus_DeliPlanItem
                                   where Bus_DeliPlanItem.Pid = #{key}
                                     and Bus_DeliPlanItem.Tenantid = #{tid}
                                     and (Bus_DeliPlanItem.Closed = 1
                                       or Bus_DeliPlanItem.FinishQty >= Bus_DeliPlanItem.Quantity)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <select id="getWkWpNameByMachitemid" resultType="java.lang.String">
        select WkWpName
        from Bus_MachiningItem
        where id = #{machitemid}
          and Tenantid = #{tid}
    </select>
</mapper>

