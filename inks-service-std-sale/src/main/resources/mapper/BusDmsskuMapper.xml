<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusDmsskuMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusDmsskuPojo">
        <include refid="selectBusDmsskuVo"/>
        where Bus_DmsSku.id = #{key}
          and Bus_DmsSku.Tenantid = #{tid}
    </select>
    <sql id="selectBusDmsskuVo">
        select Bus_DmsSku.id,
               Bus_DmsSku.SkuCode,
               Bus_DmsSku.SkuNum,
               Bus_DmsSku.Goodsid,
               Bus_DmsSku.OmsGoodsid,
               Bus_DmsSku.AttributeJson,
               Bus_DmsSku.BarCode,
               Bus_DmsSku.SafeStock,
               Bus_DmsSku.InPrice,
               Bus_DmsSku.OutPrice,
               Bus_DmsSku.IvQuantity,
               Bus_DmsSku.IvAmount,
               Bus_DmsSku.AgePrice,
               Bus_DmsSku.SkuPhoto,
               IF(Bus_DmsSku.Imgs = '', Bus_DmsGoods.Imgs, Bus_DmsSku.Imgs) AS Imgs,
               Bus_DmsSku.Exponent,
               Bus_DmsSku.Remark,
               Bus_DmsSku.RowNum,
               Bus_DmsSku.CreateBy,
               Bus_DmsSku.CreateByid,
               Bus_DmsSku.CreateDate,
               Bus_DmsSku.Lister,
               Bus_DmsSku.Listerid,
               Bus_DmsSku.ModifyDate,
               Bus_DmsSku.Custom1,
               Bus_DmsSku.Custom2,
               Bus_DmsSku.Custom3,
               Bus_DmsSku.Custom4,
               Bus_DmsSku.Custom5,
               Bus_DmsSku.Custom6,
               Bus_DmsSku.Custom7,
               Bus_DmsSku.Custom8,
               Bus_DmsSku.Custom9,
               Bus_DmsSku.Custom10,
               Bus_DmsSku.Tenantid,
               Bus_DmsSku.TenantName,
               Bus_DmsSku.Revision,
               Bus_DmsGoods.MainMark,
               Bus_DmsGoods.Status,
               Mat_Goods.GoodsName                                          as OmsGoodsName,
               Mat_Goods.GoodsSpec                                          as OmsGoodsSpec,
               Mat_Goods.GoodsUnit                                          as OmsGoodsUnit,
               Mat_Goods.GoodsUid                                           as OmsGoodsUid,
               Mat_Goods.IvQuantity                                         as OmsIvQuantity,
               Bus_DmsGoods.GoodsUid                                        as ItemCode,
               Bus_DmsGoods.GoodsName                                       as ItemName,
               Bus_DmsGoods.GoodsSpec                                       as ItemSpec,
               Bus_DmsGoods.GoodsUnit                                       as ItemUnit
        from Bus_DmsSku
                 left join Bus_DmsGoods on Bus_DmsSku.Goodsid = Bus_DmsGoods.id
                 Left join Mat_Goods on Bus_DmsSku.OmsGoodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDmsskuPojo">
        <include refid="selectBusDmsskuVo"/>
        where 1 = 1
          and Bus_DmsSku.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_DmsSku.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.skucode != null">
            and Bus_DmsSku.SkuCode like concat('%', #{SearchPojo.skucode}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Bus_DmsSku.Goodsid like concat('%',
                #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null">
            and Bus_DmsSku.ItemCode like concat('%',
                #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Bus_DmsSku.ItemName like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.attributejson != null">
            and Bus_DmsSku.AttributeJson like concat('%',
                #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.barcode != null">
            and Bus_DmsSku.BarCode like concat('%',
                #{SearchPojo.barcode}, '%')
        </if>
        <if test="SearchPojo.skuphoto != null">
            and Bus_DmsSku.SkuPhoto like concat('%',
                #{SearchPojo.skuphoto}, '%')
        </if>
        <if test="SearchPojo.imgs != null">
            and Bus_DmsSku.Imgs like concat('%',
                #{SearchPojo.imgs}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Bus_DmsSku.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_DmsSku.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_DmsSku.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_DmsSku.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_DmsSku.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_DmsSku.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_DmsSku.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_DmsSku.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_DmsSku.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_DmsSku.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_DmsSku.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_DmsSku.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_DmsSku.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_DmsSku.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_DmsSku.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_DmsSku.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.skucode != null">
                or Bus_DmsSku.SkuCode like concat('%', #{SearchPojo.skucode}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Bus_DmsSku.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null">
                or Bus_DmsSku.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Bus_DmsSku.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.attributejson != null">
                or Bus_DmsSku.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.barcode != null">
                or Bus_DmsSku.BarCode like concat('%', #{SearchPojo.barcode}, '%')
            </if>
            <if test="SearchPojo.skuphoto != null">
                or Bus_DmsSku.SkuPhoto like concat('%', #{SearchPojo.skuphoto}, '%')
            </if>
            <if test="SearchPojo.imgs != null">
                or Bus_DmsSku.Imgs like concat('%', #{SearchPojo.imgs}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Bus_DmsSku.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_DmsSku.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_DmsSku.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_DmsSku.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_DmsSku.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_DmsSku.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_DmsSku.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_DmsSku.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_DmsSku.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_DmsSku.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_DmsSku.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_DmsSku.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_DmsSku.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_DmsSku.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_DmsSku.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_DmsSku.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_DmsSku(id, SkuCode, SkuNum, Goodsid, OmsGoodsid, ItemCode, ItemName, AttributeJson, BarCode,
                               SafeStock,
                               InPrice, OutPrice, IvQuantity, IvAmount, AgePrice, SkuPhoto, Imgs, Exponent,
                               Remark,
                               RowNum,
                               CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
                               Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                               TenantName, Revision)
        values (#{id}, #{skucode}, #{skunum}, #{goodsid}, #{omsgoodsid}, #{itemcode}, #{itemname}, #{attributejson},
                #{barcode},
                #{safestock}, #{inprice}, #{outprice}, #{ivquantity}, #{ivamount}, #{ageprice}, #{skuphoto}, #{imgs},
                #{exponent},
                #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_DmsSku
        <set>
            <if test="skucode != null">
                SkuCode =#{skucode},
            </if>
            <if test="skunum != null">
                SkuNum =#{skunum},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="omsgoodsid != null">
                OmsGoodsid =#{omsgoodsid},
            </if>
            <if test="itemcode != null">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null">
                ItemName =#{itemname},
            </if>
            <if test="attributejson != null">
                AttributeJson =#{attributejson},
            </if>
            <if test="barcode != null">
                BarCode =#{barcode},
            </if>
            <if test="safestock != null">
                SafeStock =#{safestock},
            </if>
            <if test="inprice != null">
                InPrice =#{inprice},
            </if>
            <if test="outprice != null">
                OutPrice =#{outprice},
            </if>
            <if test="ivquantity != null">
                IvQuantity =#{ivquantity},
            </if>
            <if test="ivamount != null">
                IvAmount =#{ivamount},
            </if>
            <if test="ageprice != null">
                AgePrice =#{ageprice},
            </if>
            <if test="skuphoto != null">
                SkuPhoto =#{skuphoto},
            </if>
            <if test="imgs != null">
                Imgs =#{imgs},
            </if>
            <if test="exponent != null">
                Exponent =#{exponent},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_DmsSku
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
</mapper>

