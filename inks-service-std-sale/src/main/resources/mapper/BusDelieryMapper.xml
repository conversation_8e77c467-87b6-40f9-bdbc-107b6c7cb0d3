<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusDelieryMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusDelieryPojo">
        SELECT Bus_Deliery.id,
               Bus_Deliery.RefNo,
               Bus_Deliery.BillType,
               Bus_Deliery.BillTitle,
               Bus_Deliery.BillDate,
               Bus_Deliery.Groupid,
               Bus_Deliery.Telephone,
               Bus_Deliery.Linkman,
               Bus_Deliery.DeliAdd,
               Bus_Deliery.Taxrate,
               Bus_Deliery.TranSport,
               Bus_Deliery.Salesman,
               Bus_Deliery.Salesmanid,
               Bus_Deliery.Operator,
               Bus_Deliery.Operatorid,
               Bus_Deliery.Summary,
               Bus_Deliery.CreateBy,
               Bus_Deliery.<PERSON>yid,
               Bus_Deliery.CreateDate,
               Bus_Deliery.Lister,
               Bus_Deliery.Listerid,
               Bus_Deliery.ModifyDate,
               Bus_Deliery.Assessor,
               Bus_Deliery.Assessorid,
               Bus_Deliery.AssessDate,
               Bus_Deliery.DisannulCount,
               Bus_Deliery.BillStateCode,
               Bus_Deliery.BillStateDate,
               Bus_Deliery.BillTaxAmount,
               Bus_Deliery.BillTaxTotal,
               Bus_Deliery.BillAmount,
               Bus_Deliery.BillReceived,
               Bus_Deliery.ItemCount,
               Bus_Deliery.PickCount,
               Bus_Deliery.FinishCount,
               Bus_Deliery.InvoCount,
               Bus_Deliery.ReturnCount,
               Bus_Deliery.PrintCount,
               Bus_Deliery.OaFlowMark,
               Bus_Deliery.Custom1,
               Bus_Deliery.Custom2,
               Bus_Deliery.Custom3,
               Bus_Deliery.Custom4,
               Bus_Deliery.Custom5,
               Bus_Deliery.Custom6,
               Bus_Deliery.Custom7,
               Bus_Deliery.Custom8,
               Bus_Deliery.Custom9,
               Bus_Deliery.Custom10,
               Bus_Deliery.Tenantid,
               Bus_Deliery.TenantName,
               Bus_Deliery.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
        App_Workgroup.PaymentMethod,
               App_Workgroup.Linkman AS GroupLink,
               App_Workgroup.Telephone As GroupTel,
               App_Workgroup.GroupAdd
        FROM App_Workgroup
                 RIGHT JOIN Bus_Deliery ON App_Workgroup.id = Bus_Deliery.Groupid
        where Bus_Deliery.id = #{key}
          and Bus_Deliery.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_Deliery.id,
               Bus_Deliery.RefNo,
               Bus_Deliery.BillType,
               Bus_Deliery.BillTitle,
               Bus_Deliery.BillDate,
               Bus_Deliery.Groupid,
               Bus_Deliery.Telephone,
               Bus_Deliery.Linkman,
               Bus_Deliery.DeliAdd,
               Bus_Deliery.Taxrate,
               Bus_Deliery.TranSport,
               Bus_Deliery.Salesman,
               Bus_Deliery.Salesmanid,
               Bus_Deliery.Operator,
               Bus_Deliery.Operatorid,
               Bus_Deliery.Summary,
               Bus_Deliery.CreateBy,
               Bus_Deliery.CreateByid,
               Bus_Deliery.CreateDate,
               Bus_Deliery.Lister,
               Bus_Deliery.Listerid,
               Bus_Deliery.ModifyDate,
               Bus_Deliery.Assessor,
               Bus_Deliery.Assessorid,
               Bus_Deliery.AssessDate,
               Bus_Deliery.DisannulCount,
               Bus_Deliery.BillStateCode,
               Bus_Deliery.BillStateDate,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单') THEN Bus_Deliery.BillTaxAmount
                    ELSE 0 - Bus_Deliery.BillTaxAmount END) as BillTaxAmount,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单') THEN Bus_Deliery.BillTaxTotal
                    ELSE 0 - Bus_Deliery.BillTaxTotal END)  as BillTaxTotal,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单') THEN Bus_Deliery.BillAmount
                    ELSE 0 - Bus_Deliery.BillAmount END)    as BillAmount,
               Bus_Deliery.BillReceived,
               Bus_Deliery.ItemCount,
               Bus_Deliery.PickCount,
               Bus_Deliery.FinishCount,
               Bus_Deliery.InvoCount,
               Bus_Deliery.ReturnCount,
               Bus_Deliery.PrintCount,
               Bus_Deliery.OaFlowMark,
               Bus_Deliery.Custom1,
               Bus_Deliery.Custom2,
               Bus_Deliery.Custom3,
               Bus_Deliery.Custom4,
               Bus_Deliery.Custom5,
               Bus_Deliery.Custom6,
               Bus_Deliery.Custom7,
               Bus_Deliery.Custom8,
               Bus_Deliery.Custom9,
               Bus_Deliery.Custom10,
               Bus_Deliery.Tenantid,
               Bus_Deliery.TenantName,
               Bus_Deliery.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.Linkman AS GroupLink,
               App_Workgroup.PaymentMethod,
               App_Workgroup.Telephone As GroupTel,
               App_Workgroup.GroupAdd
        FROM App_Workgroup
                 RIGHT JOIN Bus_Deliery ON App_Workgroup.id = Bus_Deliery.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.PaymentMethod,
               App_Workgroup.Linkman                        AS GroupLink,
               App_Workgroup.Telephone                      As GroupTel,
               App_Workgroup.GroupAdd,
               Bus_Deliery.RefNo,
               Bus_Deliery.BillType,
               Bus_Deliery.BillTitle,
               Bus_Deliery.BillDate,
               Bus_Deliery.Telephone,
               Bus_Deliery.Linkman,
               Bus_Deliery.Taxrate,
               Bus_Deliery.TranSport,
               Bus_Deliery.Salesman,
               Bus_Deliery.Operator,
               Bus_Deliery.CreateBy,
               Bus_Deliery.Summary,
               Bus_Deliery.Lister,
               Bus_Deliery.Assessor,
               Bus_Deliery.ItemCount,
               Bus_Deliery.PickCount,
               Bus_Deliery.FinishCount,
               Bus_Deliery.InvoCount,
               Bus_Deliery.ReturnCount,
               Bus_Deliery.PrintCount,
               Bus_Deliery.OaFlowMark,
               Bus_Deliery.TranSport,
               Bus_DelieryItem.id,
               Bus_DelieryItem.Pid,
               Bus_DelieryItem.Goodsid,
               Bus_DelieryItem.ItemCode,
               Bus_DelieryItem.ItemName,
               Bus_DelieryItem.ItemSpec,
               Bus_DelieryItem.ItemUnit,
               Bus_DelieryItem.Price,
               Bus_DelieryItem.TaxPrice,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单')
                        THEN Bus_DelieryItem.Quantity
                    ELSE 0 - Bus_DelieryItem.Quantity END)  as Quantity,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单')
                        THEN Bus_DelieryItem.TaxAmount
                    ELSE 0 - Bus_DelieryItem.TaxAmount END) as TaxAmount,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单')
                        THEN Bus_DelieryItem.Amount
                    ELSE 0 - Bus_DelieryItem.Amount END)    as Amount,
               Bus_DelieryItem.ItemTaxrate,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单')
                        THEN Bus_DelieryItem.TaxTotal
                    ELSE 0 - Bus_DelieryItem.TaxTotal END)  as TaxTotal,
               Bus_DelieryItem.ItemTaxrate,
               Bus_DelieryItem.StdPrice,
               Bus_DelieryItem.StdAmount,
               Bus_DelieryItem.Rebate,
               Bus_DelieryItem.FreeQty,
               Bus_DelieryItem.FinishQty,
               Bus_DelieryItem.FinishClosed,
               Bus_DelieryItem.RowNum,
               Bus_DelieryItem.Remark,
               Bus_DelieryItem.CiteUid,
               Bus_DelieryItem.CiteItemid,
               Bus_DelieryItem.CustPo,
               Bus_DelieryItem.StateCode,
               Bus_DelieryItem.StateDate,
               Bus_DelieryItem.BusSQty,
               Bus_DelieryItem.BusSClosed,
               Bus_DelieryItem.MachType,
               Bus_DelieryItem.InvoQty,
               Bus_DelieryItem.InvoClosed,
               Bus_DelieryItem.ReturnQty,
               Bus_DelieryItem.ReturnMatQty,
               Bus_DelieryItem.ReturnClosed,
               Bus_DelieryItem.Salescost,
               Bus_DelieryItem.VirtualItem,
               Bus_DelieryItem.Location,
               Bus_DelieryItem.BatchNo,
               Bus_DelieryItem.MachUid,
               Bus_DelieryItem.MachItemid,
               Bus_DelieryItem.DisannulMark,
               Bus_DelieryItem.DisannulLister,
               Bus_DelieryItem.DisannulDate,
               Bus_DelieryItem.BFItemid,
               Bus_DelieryItem.AttributeJson,
               Bus_DelieryItem.AttributeStr,
               Bus_DelieryItem.CostItemJson,
               Bus_DelieryItem.CostGroupJson,
               Bus_DelieryItem.MachBatch,
               Bus_DelieryItem.DeliPlanUid,
               Bus_DelieryItem.DeliPlanItemid,
               Bus_DelieryItem.Custom1,
               Bus_DelieryItem.Custom2,
               Bus_DelieryItem.Custom3,
               Bus_DelieryItem.Custom4,
               Bus_DelieryItem.Custom5,
               Bus_DelieryItem.Custom6,
               Bus_DelieryItem.Custom7,
               Bus_DelieryItem.Custom8,
               Bus_DelieryItem.Custom9,
               Bus_DelieryItem.Custom10,
               Bus_DelieryItem.Custom11,
               Bus_DelieryItem.Custom12,
               Bus_DelieryItem.Custom13,
               Bus_DelieryItem.Custom14,
               Bus_DelieryItem.Custom15,
               Bus_DelieryItem.Custom16,
               Bus_DelieryItem.Custom17,
               Bus_DelieryItem.Custom18,
               Bus_DelieryItem.Tenantid,
               Bus_DelieryItem.Revision,
               Bus_MachiningItem.Quantity                   as machitemqty,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM App_Workgroup
                 RIGHT JOIN Bus_Deliery ON App_Workgroup.id = Bus_Deliery.Groupid
                 RIGHT JOIN Bus_DelieryItem ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_DelieryItem.Goodsid
                 LEFT JOIN Bus_MachiningItem ON Bus_MachiningItem.id = Bus_DelieryItem.MachItemid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDelieryitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Deliery.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and (Bus_Deliery.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate})
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and (${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
        ,Bus_DelieryItem.RowNum
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Bus_Deliery.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Deliery.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Deliery.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Deliery.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.telephone != null ">
            and Bus_Deliery.telephone like concat('%', #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and Bus_Deliery.linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.deliadd != null ">
            and Bus_Deliery.deliadd like concat('%', #{SearchPojo.deliadd}, '%')
        </if>
        <if test="SearchPojo.transport != null ">
            and Bus_Deliery.transport like concat('%', #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.salesman != null ">
            and Bus_Deliery.salesman like concat('%', #{SearchPojo.salesman}, '%')
        </if>
        <if test="SearchPojo.salesmanid != null ">
            and Bus_Deliery.salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_Deliery.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Bus_Deliery.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Deliery.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Deliery.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Deliery.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Deliery.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Deliery.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_Deliery.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_Deliery.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Bus_Deliery.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Deliery.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Deliery.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Deliery.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Deliery.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Deliery.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Deliery.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Deliery.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Deliery.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Deliery.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Deliery.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Deliery.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Bus_DelieryItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_DelieryItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_DelieryItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_DelieryItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_DelieryItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_DelieryItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Bus_DelieryItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_DelieryItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_DelieryItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_DelieryItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_DelieryItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Bus_DelieryItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Bus_DelieryItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Bus_DelieryItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Bus_DelieryItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
            and Bus_DelieryItem.machtype like concat('%', #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Bus_DelieryItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Bus_DelieryItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Bus_DelieryItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Bus_DelieryItem.machitemid=#{SearchPojo.machitemid}
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Bus_DelieryItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Bus_DelieryItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.deliplanuid != null and SearchPojo.deliplanuid != ''">
            and Bus_DelieryItem.deliplanuid like concat('%', #{SearchPojo.deliplanuid}, '%')
        </if>
        <if test="SearchPojo.deliplanitemid != null and SearchPojo.deliplanitemid != ''">
            and Bus_DelieryItem.deliplanitemid like concat('%', #{SearchPojo.deliplanitemid}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.telephone != null ">
                or Bus_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or Bus_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.deliadd != null ">
                or Bus_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
            </if>
            <if test="SearchPojo.transport != null ">
                or Bus_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.salesman != null ">
                or Bus_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
            </if>
            <if test="SearchPojo.salesmanid != null ">
                or Bus_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Bus_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Bus_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Bus_DelieryItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_DelieryItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_DelieryItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_DelieryItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_DelieryItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_DelieryItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Bus_DelieryItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_DelieryItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_DelieryItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_DelieryItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_DelieryItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Bus_DelieryItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Bus_DelieryItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Bus_DelieryItem.CustPo like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Bus_DelieryItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
                or Bus_DelieryItem.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Bus_DelieryItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Bus_DelieryItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Bus_DelieryItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Bus_DelieryItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Bus_DelieryItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Bus_DelieryItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.deliplanuid != null and SearchPojo.deliplanuid != ''">
                or Bus_DelieryItem.DeliPlanUid like concat('%', #{SearchPojo.deliplanuid}, '%')
            </if>
            <if test="SearchPojo.deliplanitemid != null and SearchPojo.deliplanitemid != ''">
                or Bus_DelieryItem.DeliPlanItemid like concat('%', #{SearchPojo.deliplanitemid}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusDelieryPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Deliery.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Deliery.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Bus_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.telephone != null ">
            and Bus_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and Bus_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.deliadd != null ">
            and Bus_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
        </if>
        <if test="SearchPojo.transport != null ">
            and Bus_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.salesman != null ">
            and Bus_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
        </if>
        <if test="SearchPojo.salesmanid != null ">
            and Bus_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Bus_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Bus_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Deliery.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Deliery.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Deliery.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Deliery.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.telephone != null ">
                or Bus_Deliery.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or Bus_Deliery.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.deliadd != null ">
                or Bus_Deliery.DeliAdd like concat('%', #{SearchPojo.deliadd}, '%')
            </if>
            <if test="SearchPojo.transport != null ">
                or Bus_Deliery.TranSport like concat('%', #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.salesman != null ">
                or Bus_Deliery.Salesman like concat('%', #{SearchPojo.salesman}, '%')
            </if>
            <if test="SearchPojo.salesmanid != null ">
                or Bus_Deliery.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_Deliery.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Bus_Deliery.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Deliery.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Deliery.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Deliery.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Deliery.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Deliery.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_Deliery.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_Deliery.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Bus_Deliery.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Deliery.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Deliery.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Deliery.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Deliery.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Deliery.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Deliery.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Deliery.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Deliery.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Deliery.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Deliery.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Deliery.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_Deliery(id, RefNo, BillType, BillTitle, BillDate, Groupid, Telephone, Linkman, DeliAdd, Taxrate,
                                TranSport, Salesman, Salesmanid, Operator, Operatorid, Summary, CreateBy, CreateByid,
                                CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate,
                                DisannulCount, BillStateCode, BillStateDate, BillTaxAmount, BillTaxTotal, BillAmount,
                                BillReceived, ItemCount, ReturnCount, PickCount, FinishCount, InvoCount, PrintCount,OaFlowMark, Custom1,
                                Custom2, Custom3,
                                Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName,
                                Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{groupid}, #{telephone}, #{linkman},
                #{deliadd}, #{taxrate}, #{transport}, #{salesman}, #{salesmanid}, #{operator}, #{operatorid},
                #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{assessor}, #{assessorid}, #{assessdate}, #{disannulcount}, #{billstatecode}, #{billstatedate},
                #{billtaxamount}, #{billtaxtotal}, #{billamount}, #{billreceived}, #{itemcount}, #{returncount}, #{pickcount},
                #{finishcount}, #{invocount}, #{printcount},#{oaflowmark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{custom6},
                #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Deliery
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="telephone != null ">
                Telephone =#{telephone},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="deliadd != null ">
                DeliAdd =#{deliadd},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="transport != null ">
                TranSport =#{transport},
            </if>
            <if test="salesman != null ">
                Salesman =#{salesman},
            </if>
            <if test="salesmanid != null ">
                Salesmanid =#{salesmanid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billreceived != null">
                BillReceived =#{billreceived},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="returncount != null">
                ReturnCount =#{returncount},
            </if>
            <if test="pickcount != null">
                PickCount =#{pickcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="invocount != null">
                InvoCount =#{invocount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Deliery
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Deliery
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCountAndAmount">
        update Bus_Deliery
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Bus_DelieryItem
                                     where Bus_DelieryItem.Pid = #{key}
                                       and Bus_DelieryItem.Tenantid = #{tid}
                                       and Bus_DelieryItem.DisannulMark = 1), 0),
            BillTaxAmount = COALESCE((SELECT Sum(TaxAmount)
                                      FROM Bus_DelieryItem
                                      where Bus_DelieryItem.Pid = #{key}
                                        and Bus_DelieryItem.Tenantid = #{tid}
                                        and Bus_DelieryItem.DisannulMark = 0), 0),
            BillAmount    =COALESCE((SELECT Sum(Amount)
                                     FROM Bus_DelieryItem
                                     where Bus_DelieryItem.Pid = #{key}
                                       and Bus_DelieryItem.Tenantid = #{tid}
                                       and Bus_DelieryItem.DisannulMark = 0), 0),
            BillTaxTotal=COALESCE((SELECT Sum(TaxTotal)
                                   FROM Bus_DelieryItem
                                   where Bus_DelieryItem.Pid = #{key}
                                     and Bus_DelieryItem.Tenantid = #{tid}
                                     and Bus_DelieryItem.DisannulMark = 0), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateFinishCount">
        update Bus_Deliery
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Bus_DelieryItem
                                   where Bus_DelieryItem.Pid = #{key}
                                     and Bus_DelieryItem.Tenantid = #{tid}
                                     and (Bus_DelieryItem.FinishQty >= Bus_DelieryItem.Quantity or
                                          Bus_DelieryItem.FinishClosed = 1 or
                                          Bus_DelieryItem.VirtualItem = 1)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusDelieryPojo">
        select
        id
        from Bus_DelieryItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateMachItemFinishQty">
        update Bus_MachiningItem
        SET FinishQty =COALESCE((SELECT sum(IF(Bus_Deliery.BillType IN ('发出商品'), Bus_DelieryItem.quantity, 0 -
                                                                                                               Bus_DelieryItem.quantity))
                                 FROM Bus_DelieryItem
                                          LEFT OUTER JOIN Bus_Deliery
                                                          ON Bus_DelieryItem.pid = Bus_Deliery.id
                                 where Bus_Deliery.BillType IN ('发出商品', '订单退货')
                                   and Bus_DelieryItem.citeUid = #{refno}
                                   and Bus_DelieryItem.citeitemid = #{key}
                                   and Bus_DelieryItem.DisannulMark = 0
                                   and Bus_DelieryItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateMachFinishCount">
        update Bus_Machining
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Bus_MachiningItem
                                   where Bus_MachiningItem.Pid = (SELECT Pid FROM Bus_MachiningItem where id = #{key})
                                     and Bus_MachiningItem.Tenantid = #{tid}
                                     and (Bus_MachiningItem.Closed = 1 or Bus_MachiningItem.DisannulMark=1
                                       or Bus_MachiningItem.FinishQty >= Bus_MachiningItem.Quantity)), 0)
        where id = (SELECT Pid FROM Bus_MachiningItem where id = #{key})
          and Tenantid = #{tid}
    </update>

    <!--  查询发出商品是否被引用  -->
    <select id="getItemCiteBillName" resultType="string">
        (SELECT '出入库单' as billname
         From Mat_AccessItem
        LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =  Mat_Access.id
         where Mat_AccessItem.CiteItemid = #{key} AND Mat_Access.ReturnUid ='' AND Mat_Access.OrgUid =''
           and Mat_Access.Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '销售开票' as billname From Bus_InvoiceItem where DeliItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '返工补发' as billname From Bus_DelieryItem where CiteItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '拣货单' as billname From Mat_PickingItem where CiteItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '销售账单' as billname From Bus_AccountItem where Billid = #{pid} and Tenantid = #{tid} LIMIT 1)
    </select>



    <select id="getItemListByIds" resultType="inks.service.std.sale.domain.pojo.BusDelieryitemPojo">
        SELECT Bus_DelieryItem.id,
               Bus_DelieryItem.Pid,
               Bus_DelieryItem.Goodsid,
               Bus_DelieryItem.ItemCode,
               Bus_DelieryItem.ItemName,
               Bus_DelieryItem.ItemSpec,
               Bus_DelieryItem.ItemUnit,
               Bus_DelieryItem.Price,
               Bus_DelieryItem.TaxPrice,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单')
                        THEN Bus_DelieryItem.Quantity
                    ELSE 0 - Bus_DelieryItem.Quantity END)  as Quantity,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单')
                        THEN Bus_DelieryItem.TaxAmount
                    ELSE 0 - Bus_DelieryItem.TaxAmount END) as TaxAmount,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单')
                        THEN Bus_DelieryItem.Amount
                    ELSE 0 - Bus_DelieryItem.Amount END)    as Amount,
               Bus_DelieryItem.ItemTaxrate,
               (CASE
                    WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发', '销售单')
                        THEN Bus_DelieryItem.TaxTotal
                    ELSE 0 - Bus_DelieryItem.TaxTotal END)  as TaxTotal,
               Bus_DelieryItem.ItemTaxrate,
               Bus_DelieryItem.StdPrice,
               Bus_DelieryItem.StdAmount,
               Bus_DelieryItem.Rebate,
               Bus_DelieryItem.FreeQty,
               Bus_DelieryItem.FinishQty,
               Bus_DelieryItem.FinishClosed,
               Bus_DelieryItem.RowNum,
               Bus_DelieryItem.Remark,
               Bus_DelieryItem.CiteUid,
               Bus_DelieryItem.CiteItemid,
               Bus_DelieryItem.CustPo,
               Bus_DelieryItem.StateCode,
               Bus_DelieryItem.StateDate,
               Bus_DelieryItem.BusSQty,
               Bus_DelieryItem.BusSClosed,
               Bus_DelieryItem.MachType,
               Bus_DelieryItem.InvoQty,
               Bus_DelieryItem.InvoClosed,
               Bus_DelieryItem.ReturnQty,
               Bus_DelieryItem.ReturnMatQty,
               Bus_DelieryItem.ReturnClosed,
               Bus_DelieryItem.Salescost,
               Bus_DelieryItem.VirtualItem,
               Bus_DelieryItem.Location,
               Bus_DelieryItem.BatchNo,
               Bus_DelieryItem.MachUid,
               Bus_DelieryItem.MachItemid,
               Bus_DelieryItem.DisannulMark,
               Bus_DelieryItem.DisannulLister,
               Bus_DelieryItem.DisannulDate,
               Bus_DelieryItem.BFItemid,
               Bus_DelieryItem.AttributeJson,
               Bus_DelieryItem.CostItemJson,
               Bus_DelieryItem.CostGroupJson,
               Bus_DelieryItem.SourceType,
               Bus_DelieryItem.MachBatch,
               Bus_DelieryItem.DeliPlanUid,
               Bus_DelieryItem.DeliPlanItemid,
               Bus_DelieryItem.Custom1,
               Bus_DelieryItem.Custom2,
               Bus_DelieryItem.Custom3,
               Bus_DelieryItem.Custom4,
               Bus_DelieryItem.Custom5,
               Bus_DelieryItem.Custom6,
               Bus_DelieryItem.Custom7,
               Bus_DelieryItem.Custom8,
               Bus_DelieryItem.Custom9,
               Bus_DelieryItem.Custom10,
               Bus_DelieryItem.Custom11,
               Bus_DelieryItem.Custom12,
               Bus_DelieryItem.Custom13,
               Bus_DelieryItem.Custom14,
               Bus_DelieryItem.Custom15,
               Bus_DelieryItem.Custom16,
               Bus_DelieryItem.Custom17,
               Bus_DelieryItem.Custom18,
               Bus_DelieryItem.Tenantid,
               Bus_DelieryItem.Revision,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_DelieryItem.Goodsid
        where Bus_DelieryItem.Pid = #{pid}
          and Bus_DelieryItem.id in (${ids})
          and Bus_DelieryItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <update id="syncDeliPlanItemFinishQty">
        update Bus_DeliPlanItem
        set FinishQty = COALESCE(
                (SELECT sum(Bus_DelieryItem.quantity)
                 FROM Bus_DelieryItem
                 where Bus_DelieryItem.DeliPlanItemid = #{deliplanitemid}
                   and Bus_DelieryItem.DisannulMark = 0
                   and Bus_DelieryItem.Tenantid = #{tid}), 0)
        where id = #{deliplanitemid}
          and Tenantid = #{tid}
    </update>

    <update id="syncDeliPlanItemFinishQtyByPlan">
        update Bus_DeliPlanItem
        set FinishQty = COALESCE(
                (SELECT sum(Bus_DelieryItem.quantity)
                 FROM Bus_DelieryItem
                          join Bus_Deliery on Bus_DelieryItem.Pid = Bus_Deliery.id
                 where Bus_DelieryItem.DisannulMark = 0
                   and Bus_DelieryItem.Tenantid = #{tid}
                   and Bus_DelieryItem.MachItemid = #{machitemid}
                   and DATE(Bus_Deliery.BillDate) = DATE(#{billdate})), 0)
        where MachItemid = #{machitemid}
          and DATE(ItemPlanDate) = DATE(#{billdate})
          and Closed = 0
          and Tenantid = #{tid}
    </update>

    <update id="syncDeliPlanFinishCount">
        update Bus_DeliPlan
        set FinishCount = COALESCE(
                (SELECT COUNT(0)
                 FROM Bus_DeliPlanItem
                 WHERE Bus_DeliPlanItem.Pid = (SELECT Pid FROM Bus_DeliPlanItem WHERE id = #{deliplanitemid})
                   AND Bus_DeliPlanItem.Tenantid = #{tid}
                   AND (Bus_DeliPlanItem.Closed = 1 OR Bus_DeliPlanItem.DisannulMark = 1
                        OR Bus_DeliPlanItem.FinishQty >= Bus_DeliPlanItem.Quantity)), 0)
        where id = (SELECT Pid FROM Bus_DeliPlanItem WHERE id = #{deliplanitemid})
          and Tenantid = #{tid}
    </update>



    <update id="updateDeliItemReturnQty">
        update Bus_DelieryItem as BDI
        join (select sum(BDI2.Quantity) as totalQty, BDI2.CiteItemid
        from Bus_DelieryItem as BDI2
        left join Bus_Deliery as BD on BDI2.pid = BD.id
        where BD.BillType = '返工补发'
        and BDI2.DisannulMark = 0
        and BDI2.Tenantid = #{tid}
        group by BDI2.CiteItemid) as subquery
        on BDI.id = subquery.CiteItemid
        set BDI.ReturnQty = subquery.totalQty
        where BDI.id = #{citeitemid}
        and BDI.Tenantid = #{tid};
    </update>



    <update id="updateDeliReturnCount">
        update Bus_Deliery
        set ReturnCount = (select count(0)
        from Bus_DelieryItem
        where Bus_DelieryItem.Pid = (SELECT Pid FROM Bus_DelieryItem where id = #{citeitemid})
        and Bus_DelieryItem.Tenantid = #{tid}
        and (Bus_DelieryItem.DisannulMark = 1 or
        Bus_DelieryItem.ReturnQty >= Bus_DelieryItem.Quantity))
        where id = #{citeitemid}
        and Tenantid = #{tid}
    </update>

    <update id="updateMachFinishAmount">
        WITH ItemPid AS (SELECT Pid
                         FROM Bus_MachiningItem
                         WHERE id = #{machitemid}
                           AND Tenantid = #{tid}
                         LIMIT 1),
             ItemSum AS (SELECT SUM(Amount)    AS totalAmount,
                                SUM(TaxAmount) AS totalTaxAmount
                         FROM Bus_MachiningItem
                         WHERE Pid = (SELECT Pid FROM ItemPid)
                           AND (FinishQty >= Quantity OR Closed = 1)
                           and Tenantid = #{tid})
        UPDATE Bus_Machining
        SET FinishAmount    = COALESCE((SELECT totalAmount FROM ItemSum), 0),
            FinishTaxAmount = COALESCE((SELECT totalTaxAmount FROM ItemSum), 0)
        WHERE id = (SELECT Pid FROM ItemPid)
          AND Tenantid = #{tid}
    </update>

    <select id="getGoodsInfo" resultType="java.util.Map">
        SELECT Storeid,
               StoreListName,
               StoreListGuid,
               BatchMg,
               BatchOnly,
               SkuMark,
               PackSnMark,
               ExpiMark,
               AgePrice,
               IvQuantity,VirtualItem
        From Mat_Goods
        where Mat_Goods.id = #{goodsid}
          and Mat_Goods.Tenantid = #{tid}
    </select>

    <select id="getAttrList" resultType="java.util.Map">
        SELECT id, AttrKey, AttrName, SkuMark
        FROM Mat_Attribute
        WHERE Tenantid = #{tid}
    </select>

    <select id="getListByGoodsAttr" resultType="java.util.Map">
        SELECT Mat_Sku.id as Skuid,
               Mat_Sku.SkuCode,
               Mat_Sku.Goodsid,
               Mat_Sku.AttributeJson
        FROM Mat_Goods
                 RIGHT JOIN Mat_Sku ON Mat_Sku.Goodsid = Mat_Goods.id
        WHERE Mat_Sku.Goodsid = #{goodsid}
          AND Mat_Sku.Tenantid = #{tid}
        <foreach collection="lst" item="item">
            AND AttributeJson LIKE '%{"key":"${item.key}","value":"${item.value}"}%'
        </foreach>
        ORDER BY AttributeJson
    </select>

    <select id="getSumInventoryQuantity" resultType="java.lang.Double">
        SELECT IFNULL(SUM(Quantity), 0)
        from Mat_Inventory
        WHERE Tenantid = #{tid}
        and Goodsid = #{goodsid}
        and Skuid = #{skuid}
    </select>
</mapper>

