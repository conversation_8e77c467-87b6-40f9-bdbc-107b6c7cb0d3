<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusQuotationMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusQuotationPojo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Bus_Quotation.id,
            Bus_Quotation.RefNo,
            Bus_Quotation.BillType,
            Bus_Quotation.BillTitle,
            Bus_Quotation.BillDate,
            Bus_Quotation.Probability,
            Bus_Quotation.Groupid,
            Bus_Quotation.TraderCode,
            Bus_Quotation.TraderName,
            Bus_Quotation.CustAddress,
            Bus_Quotation.Linkman,
            Bus_Quotation.Tel,
            Bus_Quotation.Fax,
            Bus_Quotation.Periods,
            Bus_Quotation.ValidityDate,
            Bus_Quotation.ExpireDate,
            Bus_Quotation.ValidityDesc,
            Bus_Quotation.Currency,
            Bus_Quotation.Delivery,
            Bus_Quotation.Payment,
            Bus_Quotation.Seller,
            Bus_Quotation.Summary,
            Bus_Quotation.BillClause,
            Bus_Quotation.BillTaxAmount,
            Bus_Quotation.BillAmount,
            Bus_Quotation.BillTaxTotal,
            Bus_Quotation.CreateBy,
            Bus_Quotation.CreateByid,
            Bus_Quotation.CreateDate,
            Bus_Quotation.Lister,
            Bus_Quotation.Listerid,
            Bus_Quotation.ModifyDate,
            Bus_Quotation.Submitterid,
            Bus_Quotation.Submitter,
            Bus_Quotation.SubmitDate,
            Bus_Quotation.Assessor,
            Bus_Quotation.Assessorid,
            Bus_Quotation.AssessDate,
            Bus_Quotation.ItemCount,
            Bus_Quotation.FinishCount,
            Bus_Quotation.DisannulCount,
            Bus_Quotation.PrintCount,
            Bus_Quotation.Custom1,
            Bus_Quotation.Custom2,
            Bus_Quotation.Custom3,
            Bus_Quotation.Custom4,
            Bus_Quotation.Custom5,
            Bus_Quotation.Custom6,
            Bus_Quotation.Custom7,
            Bus_Quotation.Custom8,
            Bus_Quotation.Custom9,
            Bus_Quotation.Custom10,
            Bus_Quotation.Deptid,
            Bus_Quotation.Tenantid,
            Bus_Quotation.TenantName,
            Bus_Quotation.Revision
        FROM
            App_Workgroup
                RIGHT JOIN Bus_Quotation ON App_Workgroup.id = Bus_Quotation.Groupid
        where Bus_Quotation.id = #{key}
          and Bus_Quotation.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Bus_Quotation.id,
            Bus_Quotation.RefNo,
            Bus_Quotation.BillType,
            Bus_Quotation.BillTitle,
            Bus_Quotation.BillDate,
            Bus_Quotation.Probability,
            Bus_Quotation.Groupid,
            Bus_Quotation.TraderCode,
            Bus_Quotation.TraderName,
            Bus_Quotation.CustAddress,
            Bus_Quotation.Linkman,
            Bus_Quotation.Tel,
            Bus_Quotation.Fax,
            Bus_Quotation.Periods,
            Bus_Quotation.ValidityDate,
            Bus_Quotation.ExpireDate,
            Bus_Quotation.ValidityDesc,
            Bus_Quotation.Currency,
            Bus_Quotation.Delivery,
            Bus_Quotation.Payment,
            Bus_Quotation.Seller,
            Bus_Quotation.Summary,
            Bus_Quotation.BillClause,
            Bus_Quotation.BillTaxAmount,
            Bus_Quotation.BillAmount,
            Bus_Quotation.BillTaxTotal,
            Bus_Quotation.CreateBy,
            Bus_Quotation.CreateByid,
            Bus_Quotation.CreateDate,
            Bus_Quotation.Lister,
            Bus_Quotation.Listerid,
            Bus_Quotation.ModifyDate,
            Bus_Quotation.Submitterid,
            Bus_Quotation.Submitter,
            Bus_Quotation.SubmitDate,
            Bus_Quotation.Assessor,
            Bus_Quotation.Assessorid,
            Bus_Quotation.AssessDate,
            Bus_Quotation.ItemCount,
            Bus_Quotation.FinishCount,
            Bus_Quotation.DisannulCount,
            Bus_Quotation.PrintCount,
            Bus_Quotation.Custom1,
            Bus_Quotation.Custom2,
            Bus_Quotation.Custom3,
            Bus_Quotation.Custom4,
            Bus_Quotation.Custom5,
            Bus_Quotation.Custom6,
            Bus_Quotation.Custom7,
            Bus_Quotation.Custom8,
            Bus_Quotation.Custom9,
            Bus_Quotation.Custom10,
            Bus_Quotation.Deptid,
            Bus_Quotation.Tenantid,
            Bus_Quotation.TenantName,
            Bus_Quotation.Revision
        FROM
            App_Workgroup
                RIGHT JOIN Bus_Quotation ON App_Workgroup.id = Bus_Quotation.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT
            Bus_QuotationItem.id,
            Bus_QuotationItem.Pid,
            Bus_QuotationItem.Goodsid,
            Bus_QuotationItem.ItemType,
            Bus_QuotationItem.ItemName,
            Bus_QuotationItem.ItemSpec,
            Bus_QuotationItem.ItemUnit,
            Bus_QuotationItem.Quantity,
            Bus_QuotationItem.Price,
            Bus_QuotationItem.Amount,
            Bus_QuotationItem.ItemTaxrate,
            Bus_QuotationItem.TaxPrice,
            Bus_QuotationItem.TaxAmount,
            Bus_QuotationItem.Remark,
            Bus_QuotationItem.RowNum,
            Bus_QuotationItem.AttributeJson,
            Bus_QuotationItem.DisannulMark,
            Bus_QuotationItem.DisannulListerid,
            Bus_QuotationItem.DisannulLister,
            Bus_QuotationItem.DisannulDate,
            Bus_QuotationItem.Closed,
            Bus_QuotationItem.MachMark,
            Bus_QuotationItem.VirtualItem,
            Bus_QuotationItem.Custom1,
            Bus_QuotationItem.Custom2,
            Bus_QuotationItem.Custom3,
            Bus_QuotationItem.Custom4,
            Bus_QuotationItem.Custom5,
            Bus_QuotationItem.Custom6,
            Bus_QuotationItem.Custom7,
            Bus_QuotationItem.Custom8,
            Bus_QuotationItem.Custom9,
            Bus_QuotationItem.Custom10,
            Bus_QuotationItem.Tenantid,
            Bus_QuotationItem.Revision,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoods"/>
            Bus_Quotation.RefNo,
            Bus_Quotation.BillType,
            Bus_Quotation.BillTitle,
            Bus_Quotation.BillDate,
            Bus_Quotation.TraderCode,
            Bus_Quotation.TraderName,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Bus_Quotation.CreateBy,
            Bus_Quotation.Lister,
            Bus_Quotation.Assessor,
            Bus_Quotation.Seller
        FROM
            App_Workgroup
                RIGHT JOIN Bus_Quotation ON App_Workgroup.id = Bus_Quotation.Groupid
                RIGHT JOIN Bus_QuotationItem ON Bus_Quotation.id = Bus_QuotationItem.Pid
                LEFT JOIN Mat_Goods ON Bus_QuotationItem.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusQuotationitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Quotation.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Quotation.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
        ,Bus_QuotationItem.RowNum
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Bus_Quotation.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Quotation.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Quotation.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.probability != null ">
            and Bus_Quotation.probability like concat('%', #{SearchPojo.probability}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Quotation.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.tradercode != null ">
            and Bus_Quotation.tradercode like concat('%', #{SearchPojo.tradercode}, '%')
        </if>
        <if test="SearchPojo.tradername != null ">
            and Bus_Quotation.tradername like concat('%', #{SearchPojo.tradername}, '%')
        </if>
        <if test="SearchPojo.custaddress != null ">
            and Bus_Quotation.custaddress like concat('%', #{SearchPojo.custaddress}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and Bus_Quotation.linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.tel != null ">
            and Bus_Quotation.tel like concat('%', #{SearchPojo.tel}, '%')
        </if>
        <if test="SearchPojo.fax != null ">
            and Bus_Quotation.fax like concat('%', #{SearchPojo.fax}, '%')
        </if>
        <if test="SearchPojo.periods != null ">
            and Bus_Quotation.periods like concat('%', #{SearchPojo.periods}, '%')
        </if>
        <if test="SearchPojo.validitydate != null ">
            and Bus_Quotation.validitydate like concat('%', #{SearchPojo.validitydate}, '%')
        </if>
        <if test="SearchPojo.currency != null ">
            and Bus_Quotation.currency like concat('%', #{SearchPojo.currency}, '%')
        </if>
        <if test="SearchPojo.delivery != null ">
            and Bus_Quotation.delivery like concat('%', #{SearchPojo.delivery}, '%')
        </if>
        <if test="SearchPojo.payment != null ">
            and Bus_Quotation.payment like concat('%', #{SearchPojo.payment}, '%')
        </if>
        <if test="SearchPojo.seller != null ">
            and Bus_Quotation.seller like concat('%', #{SearchPojo.seller}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Quotation.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.billclause != null ">
            and Bus_Quotation.billclause like concat('%', #{SearchPojo.billclause}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Quotation.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Quotation.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Quotation.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Quotation.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.submitterid != null ">
            and Bus_Quotation.submitterid like concat('%', #{SearchPojo.submitterid}, '%')
        </if>
        <if test="SearchPojo.submitter != null ">
            and Bus_Quotation.submitter like concat('%', #{SearchPojo.submitter}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_Quotation.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_Quotation.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Quotation.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Quotation.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Quotation.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Quotation.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Quotation.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Quotation.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Quotation.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Quotation.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Quotation.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Quotation.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Bus_Quotation.deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Quotation.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Quotation.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Quotation.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Quotation.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.probability != null ">
                or Bus_Quotation.Probability like concat('%', #{SearchPojo.probability}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Quotation.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.tradercode != null ">
                or Bus_Quotation.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
            </if>
            <if test="SearchPojo.tradername != null ">
                or Bus_Quotation.TraderName like concat('%', #{SearchPojo.tradername}, '%')
            </if>
            <if test="SearchPojo.custaddress != null ">
                or Bus_Quotation.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or Bus_Quotation.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.tel != null ">
                or Bus_Quotation.Tel like concat('%', #{SearchPojo.tel}, '%')
            </if>
            <if test="SearchPojo.fax != null ">
                or Bus_Quotation.Fax like concat('%', #{SearchPojo.fax}, '%')
            </if>
            <if test="SearchPojo.periods != null ">
                or Bus_Quotation.Periods like concat('%', #{SearchPojo.periods}, '%')
            </if>
            <if test="SearchPojo.validitydate != null ">
                or Bus_Quotation.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
            </if>
            <if test="SearchPojo.currency != null ">
                or Bus_Quotation.Currency like concat('%', #{SearchPojo.currency}, '%')
            </if>
            <if test="SearchPojo.delivery != null ">
                or Bus_Quotation.Delivery like concat('%', #{SearchPojo.delivery}, '%')
            </if>
            <if test="SearchPojo.payment != null ">
                or Bus_Quotation.Payment like concat('%', #{SearchPojo.payment}, '%')
            </if>
            <if test="SearchPojo.seller != null ">
                or Bus_Quotation.Seller like concat('%', #{SearchPojo.seller}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Quotation.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.billclause != null ">
                or Bus_Quotation.BillClause like concat('%', #{SearchPojo.billclause}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Quotation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Quotation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Quotation.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Quotation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.submitterid != null ">
                or Bus_Quotation.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
            </if>
            <if test="SearchPojo.submitter != null ">
                or Bus_Quotation.Submitter like concat('%', #{SearchPojo.submitter}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_Quotation.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_Quotation.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Quotation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Quotation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Quotation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Quotation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Quotation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Quotation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Quotation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Quotation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Quotation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Quotation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Bus_Quotation.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Quotation.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusQuotationPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Quotation.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Quotation.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Bus_Quotation.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Quotation.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Quotation.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.probability != null ">
            and Bus_Quotation.Probability like concat('%', #{SearchPojo.probability}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Quotation.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.tradercode != null ">
            and Bus_Quotation.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
        </if>
        <if test="SearchPojo.tradername != null ">
            and Bus_Quotation.TraderName like concat('%', #{SearchPojo.tradername}, '%')
        </if>
        <if test="SearchPojo.custaddress != null ">
            and Bus_Quotation.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and Bus_Quotation.Linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.tel != null ">
            and Bus_Quotation.Tel like concat('%', #{SearchPojo.tel}, '%')
        </if>
        <if test="SearchPojo.fax != null ">
            and Bus_Quotation.Fax like concat('%', #{SearchPojo.fax}, '%')
        </if>
        <if test="SearchPojo.periods != null ">
            and Bus_Quotation.Periods like concat('%', #{SearchPojo.periods}, '%')
        </if>
        <if test="SearchPojo.validitydate != null ">
            and Bus_Quotation.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
        </if>
        <if test="SearchPojo.currency != null ">
            and Bus_Quotation.Currency like concat('%', #{SearchPojo.currency}, '%')
        </if>
        <if test="SearchPojo.delivery != null ">
            and Bus_Quotation.Delivery like concat('%', #{SearchPojo.delivery}, '%')
        </if>
        <if test="SearchPojo.payment != null ">
            and Bus_Quotation.Payment like concat('%', #{SearchPojo.payment}, '%')
        </if>
        <if test="SearchPojo.seller != null ">
            and Bus_Quotation.Seller like concat('%', #{SearchPojo.seller}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Quotation.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.billclause != null ">
            and Bus_Quotation.BillClause like concat('%', #{SearchPojo.billclause}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Quotation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Quotation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Quotation.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Quotation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.submitterid != null ">
            and Bus_Quotation.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
        </if>
        <if test="SearchPojo.submitter != null ">
            and Bus_Quotation.Submitter like concat('%', #{SearchPojo.submitter}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_Quotation.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_Quotation.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Quotation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Quotation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Quotation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Quotation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Quotation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Quotation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Quotation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Quotation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Quotation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Quotation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Bus_Quotation.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Quotation.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Quotation.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Quotation.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Quotation.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.probability != null ">
                or Bus_Quotation.Probability like concat('%', #{SearchPojo.probability}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Quotation.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.tradercode != null ">
                or Bus_Quotation.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
            </if>
            <if test="SearchPojo.tradername != null ">
                or Bus_Quotation.TraderName like concat('%', #{SearchPojo.tradername}, '%')
            </if>
            <if test="SearchPojo.custaddress != null ">
                or Bus_Quotation.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or Bus_Quotation.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.tel != null ">
                or Bus_Quotation.Tel like concat('%', #{SearchPojo.tel}, '%')
            </if>
            <if test="SearchPojo.fax != null ">
                or Bus_Quotation.Fax like concat('%', #{SearchPojo.fax}, '%')
            </if>
            <if test="SearchPojo.periods != null ">
                or Bus_Quotation.Periods like concat('%', #{SearchPojo.periods}, '%')
            </if>
            <if test="SearchPojo.validitydate != null ">
                or Bus_Quotation.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
            </if>
            <if test="SearchPojo.currency != null ">
                or Bus_Quotation.Currency like concat('%', #{SearchPojo.currency}, '%')
            </if>
            <if test="SearchPojo.delivery != null ">
                or Bus_Quotation.Delivery like concat('%', #{SearchPojo.delivery}, '%')
            </if>
            <if test="SearchPojo.payment != null ">
                or Bus_Quotation.Payment like concat('%', #{SearchPojo.payment}, '%')
            </if>
            <if test="SearchPojo.seller != null ">
                or Bus_Quotation.Seller like concat('%', #{SearchPojo.seller}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Quotation.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.billclause != null ">
                or Bus_Quotation.BillClause like concat('%', #{SearchPojo.billclause}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Quotation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Quotation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Quotation.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Quotation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.submitterid != null ">
                or Bus_Quotation.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
            </if>
            <if test="SearchPojo.submitter != null ">
                or Bus_Quotation.Submitter like concat('%', #{SearchPojo.submitter}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_Quotation.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_Quotation.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Quotation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Quotation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Quotation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Quotation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Quotation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Quotation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Quotation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Quotation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Quotation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Quotation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Bus_Quotation.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Quotation.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_Quotation(id, RefNo, BillType, BillTitle, BillDate, Probability, Groupid, TraderCode, TraderName, CustAddress, Linkman, Tel, Fax, Periods, ValidityDate, ValidityDesc, ExpireDate, Currency, Delivery, Payment, Seller, Summary, BillClause, BillTaxAmount, BillAmount, BillTaxTotal, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Submitterid, Submitter, SubmitDate, Assessor, Assessorid, AssessDate, ItemCount, FinishCount, DisannulCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{probability}, #{groupid}, #{tradercode}, #{tradername}, #{custaddress}, #{linkman}, #{tel}, #{fax}, #{periods}, #{validitydate}, #{validitydesc}, #{expiredate}, #{currency}, #{delivery}, #{payment}, #{seller}, #{summary}, #{billclause}, #{billtaxamount}, #{billamount}, #{billtaxtotal}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{submitterid}, #{submitter}, #{submitdate}, #{assessor}, #{assessorid}, #{assessdate}, #{itemcount}, #{finishcount}, #{disannulcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Quotation
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="probability != null ">
                Probability =#{probability},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="tradercode != null ">
                TraderCode =#{tradercode},
            </if>
            <if test="tradername != null ">
                TraderName =#{tradername},
            </if>
            <if test="custaddress != null ">
                CustAddress =#{custaddress},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="tel != null ">
                Tel =#{tel},
            </if>
            <if test="fax != null ">
                Fax =#{fax},
            </if>
            <if test="periods != null ">
                Periods =#{periods},
            </if>
            <if test="validitydate != null ">
                ValidityDate =#{validitydate},
            </if>
            <if test="validitydesc != null ">
            ValidityDesc =#{validitydesc},
        </if>
            <if test="expiredate != null">
            ExpireDate =#{expiredate},
        </if>
            <if test="currency != null ">
                Currency =#{currency},
            </if>
            <if test="delivery != null ">
                Delivery =#{delivery},
            </if>
            <if test="payment != null ">
                Payment =#{payment},
            </if>
            <if test="seller != null ">
                Seller =#{seller},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="billclause != null ">
                BillClause =#{billclause},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="submitterid != null ">
                Submitterid =#{submitterid},
            </if>
            <if test="submitter != null ">
                Submitter =#{submitter},
            </if>
            <if test="submitdate != null">
                SubmitDate =#{submitdate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Quotation
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Quotation
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusQuotationPojo">
        select
        id
        from Bus_QuotationItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <update id="updateIntendedItemFinishMark">
        update Bus_IntendedItem
        set FinishMark = #{finishMark}
        where id = #{intendeditemid}
          and Tenantid = #{tid}
    </update>

    <update id="updateIntendedFinishCount">
        update Bus_Intended
        set FinishCount = COALESCE((SELECT COUNT(0)
                                    FROM Bus_IntendedItem
                                    where Bus_IntendedItem.Pid = (SELECT Pid FROM Bus_IntendedItem where id = #{intendeditemid})
                                      and Bus_IntendedItem.Tenantid = #{tid}
                                      and Bus_IntendedItem.FinishMark = 1), 0)
        where id = (SELECT Pid FROM Bus_IntendedItem where id = #{intendeditemid})
          and Tenantid = #{tid}
    </update>
</mapper>

