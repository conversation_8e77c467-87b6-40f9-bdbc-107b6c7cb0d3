<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusQuotationitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusQuotationitemPojo">
        <include refid="selectBusQuotationitemVo"/>
        where Bus_QuotationItem.id = #{key}
          and Bus_QuotationItem.Tenantid = #{tid}
    </select>
    <sql id="selectBusQuotationitemVo">
        SELECT
            Bus_QuotationItem.id,
            Bus_QuotationItem.Pid,
            Bus_QuotationItem.Goodsid,
            Bus_QuotationItem.ItemType,
            Bus_QuotationItem.ItemName,
            Bus_QuotationItem.ItemSpec,
            Bus_QuotationItem.ItemUnit,
            Bus_QuotationItem.Quantity,
            Bus_QuotationItem.Price,
            Bus_QuotationItem.Amount,
            Bus_QuotationItem.ItemTaxrate,
            Bus_QuotationItem.TaxPrice,
            Bus_QuotationItem.TaxAmount,
            Bus_QuotationItem.Remark,
            Bus_QuotationItem.RowNum,
            Bus_QuotationItem.AttributeJson,
            Bus_QuotationItem.DisannulMark,
            Bus_QuotationItem.DisannulListerid,
            Bus_QuotationItem.DisannulLister,
            Bus_QuotationItem.DisannulDate,
            Bus_QuotationItem.Closed,
            Bus_QuotationItem.MachMark,
            Bus_QuotationItem.VirtualItem,
            Bus_QuotationItem.IntendedUid,
            Bus_QuotationItem.IntendedItemid,
            Bus_QuotationItem.CostBudgetAmt,
            Bus_QuotationItem.Custom1,
            Bus_QuotationItem.Custom2,
            Bus_QuotationItem.Custom3,
            Bus_QuotationItem.Custom4,
            Bus_QuotationItem.Custom5,
            Bus_QuotationItem.Custom6,
            Bus_QuotationItem.Custom7,
            Bus_QuotationItem.Custom8,
            Bus_QuotationItem.Custom9,
            Bus_QuotationItem.Custom10,
            Bus_QuotationItem.Tenantid,
            Bus_QuotationItem.Revision,
        <include refid="inks.service.std.sale.mapper.sale_ExtFilterMapper.selectGoodsNoComma"/>
        FROM
            Bus_QuotationItem
                LEFT JOIN Mat_Goods ON Bus_QuotationItem.Goodsid = Mat_Goods.id
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.sale.domain.pojo.BusQuotationitemPojo">
        <include refid="selectBusQuotationitemVo"/>
        where Bus_QuotationItem.Pid = #{Pid} and Bus_QuotationItem.Tenantid=#{tid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusQuotationitemPojo">
        <include refid="selectBusQuotationitemVo"/>
        where 1 = 1 and Bus_QuotationItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_QuotationItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_QuotationItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_QuotationItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
            and Bus_QuotationItem.itemtype like concat('%', #{SearchPojo.itemtype}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_QuotationItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_QuotationItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_QuotationItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_QuotationItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Bus_QuotationItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Bus_QuotationItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Bus_QuotationItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.virtualitem != null and SearchPojo.virtualitem != ''">
            and Bus_QuotationItem.virtualitem like concat('%', #{SearchPojo.virtualitem}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_QuotationItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_QuotationItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_QuotationItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_QuotationItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_QuotationItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_QuotationItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_QuotationItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_QuotationItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_QuotationItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_QuotationItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_QuotationItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_QuotationItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
                or Bus_QuotationItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_QuotationItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_QuotationItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_QuotationItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_QuotationItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Bus_QuotationItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Bus_QuotationItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Bus_QuotationItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.virtualitem != null and SearchPojo.virtualitem != ''">
                or Bus_QuotationItem.virtualitem like concat('%', #{SearchPojo.virtualitem}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_QuotationItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_QuotationItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_QuotationItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_QuotationItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_QuotationItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_QuotationItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_QuotationItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_QuotationItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_QuotationItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_QuotationItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>



    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_QuotationItem(id, Pid, Goodsid, ItemType, ItemName, ItemSpec, ItemUnit, Quantity, Price, Amount, ItemTaxrate, TaxPrice, TaxAmount, Remark, RowNum, AttributeJson, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, Closed, MachMark, VirtualItem, IntendedUid, IntendedItemid, CostBudgetAmt, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemtype}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{price}, #{amount}, #{itemtaxrate}, #{taxprice}, #{taxamount}, #{remark}, #{rownum}, #{attributejson}, #{disannulmark}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{closed}, #{machmark}, #{virtualitem}, #{intendeduid}, #{intendeditemid}, #{costbudgetamt}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_QuotationItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemtype != null ">
                ItemType = #{itemtype},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="machmark != null">
                MachMark = #{machmark},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="intendeduid != null">
                IntendedUid = #{intendeduid},
            </if>
            <if test="intendeditemid != null">
                IntendedItemid = #{intendeditemid},
            </if>
            <if test="costbudgetamt != null">
                CostBudgetAmt = #{costbudgetamt},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_QuotationItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

