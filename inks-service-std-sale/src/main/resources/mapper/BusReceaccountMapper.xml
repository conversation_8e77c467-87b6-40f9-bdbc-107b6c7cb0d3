<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.BusReceaccountMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.sale.domain.pojo.BusReceaccountPojo">
        SELECT Bus_ReceAccount.id,
               Bus_ReceAccount.RefNo,
               Bus_ReceAccount.BillType,
               Bus_ReceAccount.BillDate,
               Bus_ReceAccount.BillTitle,
               Bus_ReceAccount.Groupid,
               Bus_ReceAccount.CarryYear,
               Bus_ReceAccount.CarryMonth,
               Bus_ReceAccount.StartDate,
               Bus_ReceAccount.EndDate,
               Bus_ReceAccount.Operator,
               Bus_ReceAccount.Operatorid,
               Bus_ReceAccount.Summary,
               Bus_ReceAccount.CreateBy,
               Bus_ReceAccount.CreateByid,
               Bus_ReceAccount.CreateDate,
               Bus_ReceAccount.Lister,
               Bus_ReceAccount.Listerid,
               Bus_ReceAccount.ModifyDate,
               Bus_ReceAccount.BillOpenAmount,
               Bus_ReceAccount.BillInAmount,
               Bus_ReceAccount.BillOutAmount,
               Bus_ReceAccount.BillCloseAmount,
               Bus_ReceAccount.PrintCount,
               Bus_ReceAccount.Custom1,
               Bus_ReceAccount.Custom2,
               Bus_ReceAccount.Custom3,
               Bus_ReceAccount.Custom4,
               Bus_ReceAccount.Custom5,
               Bus_ReceAccount.Custom6,
               Bus_ReceAccount.Custom7,
               Bus_ReceAccount.Custom8,
               Bus_ReceAccount.Custom9,
               Bus_ReceAccount.Custom10,
               Bus_ReceAccount.Tenantid,
               Bus_ReceAccount.TenantName,
               Bus_ReceAccount.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_ReceAccount ON Bus_ReceAccount.Groupid = App_Workgroup.id
        where Bus_ReceAccount.id = #{key}
          and Bus_ReceAccount.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_ReceAccount.id,
               Bus_ReceAccount.RefNo,
               Bus_ReceAccount.BillType,
               Bus_ReceAccount.BillDate,
               Bus_ReceAccount.BillTitle,
               Bus_ReceAccount.Groupid,
               Bus_ReceAccount.CarryYear,
               Bus_ReceAccount.CarryMonth,
               Bus_ReceAccount.StartDate,
               Bus_ReceAccount.EndDate,
               Bus_ReceAccount.Operator,
               Bus_ReceAccount.Operatorid,
               Bus_ReceAccount.Summary,
               Bus_ReceAccount.CreateBy,
               Bus_ReceAccount.CreateByid,
               Bus_ReceAccount.CreateDate,
               Bus_ReceAccount.Lister,
               Bus_ReceAccount.Listerid,
               Bus_ReceAccount.ModifyDate,
               Bus_ReceAccount.BillOpenAmount,
               Bus_ReceAccount.BillInAmount,
               Bus_ReceAccount.BillOutAmount,
               Bus_ReceAccount.BillCloseAmount,
               Bus_ReceAccount.PrintCount,
               Bus_ReceAccount.Custom1,
               Bus_ReceAccount.Custom2,
               Bus_ReceAccount.Custom3,
               Bus_ReceAccount.Custom4,
               Bus_ReceAccount.Custom5,
               Bus_ReceAccount.Custom6,
               Bus_ReceAccount.Custom7,
               Bus_ReceAccount.Custom8,
               Bus_ReceAccount.Custom9,
               Bus_ReceAccount.Custom10,
               Bus_ReceAccount.Tenantid,
               Bus_ReceAccount.TenantName,
               Bus_ReceAccount.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_ReceAccount ON Bus_ReceAccount.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT Bus_ReceAccount.id,
               Bus_ReceAccount.RefNo,
               Bus_ReceAccount.BillType,
               Bus_ReceAccount.BillDate,
               Bus_ReceAccount.BillTitle,
               Bus_ReceAccount.Groupid,
               Bus_ReceAccount.CarryYear,
               Bus_ReceAccount.CarryMonth,
               Bus_ReceAccount.StartDate,
               Bus_ReceAccount.EndDate,
               Bus_ReceAccount.Operator,
               Bus_ReceAccount.Operatorid,
               Bus_ReceAccount.Summary,
               Bus_ReceAccount.CreateBy,
               Bus_ReceAccount.CreateByid,
               Bus_ReceAccount.CreateDate,
               Bus_ReceAccount.Lister,
               Bus_ReceAccount.Listerid,
               Bus_ReceAccount.ModifyDate,
               Bus_ReceAccount.BillOpenAmount,
               Bus_ReceAccount.BillInAmount,
               Bus_ReceAccount.BillOutAmount,
               Bus_ReceAccount.BillCloseAmount,
               Bus_ReceAccount.PrintCount,
               Bus_ReceAccount.Custom1,
               Bus_ReceAccount.Custom2,
               Bus_ReceAccount.Custom3,
               Bus_ReceAccount.Custom4,
               Bus_ReceAccount.Custom5,
               Bus_ReceAccount.Custom6,
               Bus_ReceAccount.Custom7,
               Bus_ReceAccount.Custom8,
               Bus_ReceAccount.Custom9,
               Bus_ReceAccount.Custom10,
               Bus_ReceAccount.Tenantid,
               Bus_ReceAccount.TenantName,
               Bus_ReceAccount.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_ReceAccount ON Bus_ReceAccount.Groupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusReceaccountitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_ReceAccount.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_ReceAccount.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Bus_ReceAccount.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_ReceAccount.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_ReceAccount.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_ReceAccount.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_ReceAccount.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Bus_ReceAccount.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_ReceAccount.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_ReceAccount.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_ReceAccount.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_ReceAccount.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_ReceAccount.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_ReceAccount.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_ReceAccount.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_ReceAccount.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_ReceAccount.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_ReceAccount.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_ReceAccount.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_ReceAccount.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_ReceAccount.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_ReceAccount.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_ReceAccount.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_ReceAccount.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_ReceAccount.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_ReceAccount.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_ReceAccount.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_ReceAccount.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_ReceAccount.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Bus_ReceAccount.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_ReceAccount.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_ReceAccount.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_ReceAccount.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_ReceAccount.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_ReceAccount.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_ReceAccount.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_ReceAccount.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_ReceAccount.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_ReceAccount.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_ReceAccount.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_ReceAccount.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_ReceAccount.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_ReceAccount.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_ReceAccount.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_ReceAccount.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_ReceAccount.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.sale.domain.pojo.BusReceaccountPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_ReceAccount.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_ReceAccount.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Bus_ReceAccount.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_ReceAccount.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_ReceAccount.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_ReceAccount.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_ReceAccount.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Bus_ReceAccount.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_ReceAccount.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_ReceAccount.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_ReceAccount.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_ReceAccount.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_ReceAccount.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_ReceAccount.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_ReceAccount.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_ReceAccount.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_ReceAccount.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_ReceAccount.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_ReceAccount.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_ReceAccount.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_ReceAccount.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_ReceAccount.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_ReceAccount.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_ReceAccount.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_ReceAccount.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_ReceAccount.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_ReceAccount.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_ReceAccount.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_ReceAccount.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Bus_ReceAccount.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_ReceAccount.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_ReceAccount.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_ReceAccount.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_ReceAccount.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_ReceAccount.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_ReceAccount.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_ReceAccount.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_ReceAccount.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_ReceAccount.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_ReceAccount.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_ReceAccount.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_ReceAccount.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_ReceAccount.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_ReceAccount.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_ReceAccount.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_ReceAccount.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_ReceAccount(id, RefNo, BillType, BillDate, BillTitle, Groupid, CarryYear, CarryMonth, StartDate,
                                    EndDate, Operator, Operatorid, Summary, CreateBy, CreateByid, CreateDate, Lister,
                                    Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount, BillCloseAmount,
                                    PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                    Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{groupid}, #{carryyear}, #{carrymonth},
                #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{summary}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{billopenamount}, #{billinamount},
                #{billoutamount}, #{billcloseamount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_ReceAccount
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_ReceAccount
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.sale.domain.pojo.BusReceaccountPojo">
        select
        id
        from Bus_ReceAccountItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

</mapper>

