<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.sale.mapper.D01MBIR2Mapper">

    <!--    /*客户订单金额排名*/-->
    <select id="getSumAmtByGroupMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select (case
                    when App_Workgroup.Abbreviate is null or
                         App_Workgroup.Abbreviate = '' then App_Workgroup.GroupName
                    else App_Workgroup.Abbreviate end) as name,
               sum(CASE
                       WHEN Bus_Deliery.BillType = '销售单' THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('销售单'
            , '退货单')
        group by App_Workgroup.Abbreviate, App_Workgroup.GroupName
        order by value desc
    </select>

    <!--    /*销售货品金额排名*/-->
    <select id="getSumAmtByGoodsMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select Bus_DelieryItem.ItemName as name,
               sum(CASE
                       WHEN Bus_Deliery.BillType = '销售单' THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('销售单'
            , '退货单')
        group by Bus_DelieryItem.ItemName
        order by value desc
    </select>

    <select id="getSumAmtBySalesman" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT Bus_Deliery.Salesman  AS name,
               sum(CASE
                       WHEN Bus_Deliery.BillType = '销售单' THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('销售单'
            , '退货单')
        GROUP BY Bus_Deliery.Salesman
        ORDER BY value
    </select>

    <select id="getSumAmtByYear" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Bus_Deliery.BillDate, '%Y-%m') name,
               sum(CASE
                       WHEN Bus_Deliery.BillType = '销售单' THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('销售单'
            , '退货单')
        group by date_format(Bus_Deliery.BillDate, '%Y-%m')
    </select>
    <select id="getSumAmtByMonth" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Bus_Deliery.BillDate, '%Y-%m-%d') name,
               sum(CASE
                       WHEN Bus_Deliery.BillType = '销售单' THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('销售单'
            , '退货单')
        group by date_format(Bus_Deliery.BillDate, '%Y-%m-%d')
    </select>
    <select id="getSumAmtByDay" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Bus_Deliery.BillDate, '%Y-%m-%d') name,
               sum(CASE
                       WHEN Bus_Deliery.BillType = '销售单' THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('销售单'
            , '退货单')
        group by date_format(Bus_Deliery.BillDate, '%Y-%m-%d')
    </select>
    <select id="getTagSumAmtQtyByDate" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select sum(CASE
                       WHEN Bus_Deliery.BillType = '销售单' THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value,
        sum(CASE
                        WHEN Bus_Deliery.BillType = '销售单' THEN Bus_DelieryItem.Quantity
                        ELSE 0 - Bus_DelieryItem.Quantity END) as valueb,
        count(0) as valuec
        FROM Bus_Deliery
            RIGHT JOIN Bus_DelieryItem
        ON Bus_DelieryItem.Pid = Bus_Deliery.id
            LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark=0
          and Bus_Deliery.Tenantid =#{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('销售单'
            , '退货单')
    </select>
    <select id="getTagSumAmtByMonth"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT IFNULL(sum(Bus_Invoice.amount), 0) AS
                                                     valueb
             , (
            CASE
                WHEN App_Workgroup.Abbreviate IS NULL
                    OR App_Workgroup.Abbreviate = '' THEN
                    App_Workgroup.GroupName
                ELSE Abbreviate
                END
            )                                     AS NAME
        FROM Bus_Invoice
                 LEFT OUTER JOIN App_Workgroup ON Bus_Invoice.Groupid = App_Workgroup.id
        WHERE DATE_FORMAT(Bus_Invoice.BillDate, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
          AND DisannulMark !=1 AND Bus_Invoice.Tenantid = #{tid}
        GROUP BY
            App_Workgroup.Abbreviate,
            App_Workgroup.GroupName
    </select>

    <select id="getPageList"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT DATE_FORMAT(now(), '%Y-%m-%d') AS NAME,
               count(1) AS
        VALUE
        FROM
            Bus_Machining
            LEFT JOIN Bus_MachiningItem
        ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            Bus_MachiningItem.Quantity
            > Bus_MachiningItem.OutMatQty
          AND DATE_FORMAT( Bus_MachiningItem.ItemOrgDate
            , '%Y-%m-%d' ) &gt;= DATE_FORMAT(
            now()
            , '%Y-%m-%d')
          and
            Bus_MachiningItem.Tenantid = #{tid}
    </select>
    <select id="getSumByGoodsMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT Mat_Goods.GoodsName              AS NAME,
               SUM(Bus_MachiningItem.TaxAmount) AS
                                                   valueb
        FROM Mat_Goods
                 RIGHT OUTER JOIN App_Workgroup
                 RIGHT OUTER JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
                                  ON Mat_Goods.id = Bus_MachiningItem.Goodsid
        WHERE Bus_MachiningItem.Tenantid = #{tenantid}
        GROUP BY Mat_Goods.GoodsName
        ORDER BY valueb desc LIMIT 0,#{PageSize}
    </select>

    <select id="getCountMachItemOnline" resultType="java.lang.Integer">
        SELECT count(1)
        FROM Bus_MachiningItem
        WHERE Bus_MachiningItem.Quantity > Bus_MachiningItem.OutQuantity
          AND Bus_MachiningItem.Closed = 0
          AND Bus_MachiningItem.DisannulMark = 0
          AND Bus_MachiningItem.Tenantid = #{tid}
    </select>
</mapper>
