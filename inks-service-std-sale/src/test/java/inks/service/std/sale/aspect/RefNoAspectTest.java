package inks.service.std.sale.aspect;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.utils.RefNoUtils;
import inks.common.security.service.TokenService;
import inks.service.std.sale.annotation.RefNoGeneration;
import inks.service.std.sale.config.RefNoProperties;
import org.aspectj.lang.JoinPoint;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RefNoAspect 测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class RefNoAspectTest {
    
    @Mock
    private TokenService tokenService;
    
    @Mock
    private RefNoProperties refNoProperties;
    
    @Mock
    private JoinPoint joinPoint;
    
    @InjectMocks
    private RefNoAspect refNoAspect;
    
    private LoginUser mockLoginUser;
    private TestController mockController;
    
    @BeforeEach
    void setUp() {
        mockLoginUser = new LoginUser();
        mockLoginUser.setUserid("test-user-id");
        mockLoginUser.setRealname("测试用户");
        mockLoginUser.setTenantid("test-tenant");
        
        mockController = new TestController();
        
        // 默认配置
        when(refNoProperties.isEnabled()).thenReturn(true);
        when(refNoProperties.isVerboseLogging()).thenReturn(false);
    }
    
    @Test
    void testGenerateRefNo_JsonString() throws Exception {
        // 准备测试数据
        String jsonParam = "{\"name\":\"测试单据\"}";
        Object[] args = {jsonParam};
        
        RefNoGeneration annotation = mock(RefNoGeneration.class);
        when(annotation.billType()).thenReturn("Bus_Test");
        when(annotation.field()).thenReturn("refno");
        when(annotation.paramType()).thenReturn(RefNoGeneration.ParamType.JSON_STRING);
        when(annotation.paramIndex()).thenReturn(0);
        when(annotation.saveToRedis()).thenReturn(true);
        when(annotation.customPrefix()).thenReturn("");
        
        // Mock JoinPoint
        when(joinPoint.getTarget()).thenReturn(mockController);
        when(joinPoint.getArgs()).thenReturn(args);
        
        // Mock TokenService
        when(tokenService.getLoginUser(any())).thenReturn(mockLoginUser);
        
        // Mock RefNoUtils
        try (MockedStatic<RefNoUtils> mockedRefNoUtils = mockStatic(RefNoUtils.class)) {
            mockedRefNoUtils.when(() -> RefNoUtils.generateRefNo(anyString(), anyString(), any(), anyString()))
                    .thenReturn("TEST202312010001");
            
            // 执行测试
            refNoAspect.generateRefNo(joinPoint, annotation);
            
            // 验证
            mockedRefNoUtils.verify(() -> RefNoUtils.generateRefNo(
                    eq("TestController"), 
                    eq("Bus_Test"), 
                    isNull(), 
                    eq("test-tenant")
            ));
            
            mockedRefNoUtils.verify(() -> RefNoUtils.saveRedisRefNo(
                    eq("TEST202312010001"), 
                    eq("TestController"), 
                    eq("test-tenant")
            ));
        }
    }
    
    @Test
    void testGenerateRefNo_Disabled() throws Exception {
        // 禁用功能
        when(refNoProperties.isEnabled()).thenReturn(false);
        
        RefNoGeneration annotation = mock(RefNoGeneration.class);
        
        // 执行测试
        refNoAspect.generateRefNo(joinPoint, annotation);
        
        // 验证没有调用TokenService
        verify(tokenService, never()).getLoginUser(any());
    }
    
    @Test
    void testGenerateRefNo_NoLoginUser() throws Exception {
        // Mock没有登录用户的情况
        when(tokenService.getLoginUser(any())).thenReturn(null);
        when(joinPoint.getTarget()).thenReturn(mockController);
        
        RefNoGeneration annotation = mock(RefNoGeneration.class);
        when(annotation.billType()).thenReturn("Bus_Test");
        
        // 执行测试
        refNoAspect.generateRefNo(joinPoint, annotation);
        
        // 验证没有调用RefNoUtils
        try (MockedStatic<RefNoUtils> mockedRefNoUtils = mockStatic(RefNoUtils.class)) {
            mockedRefNoUtils.verifyNoInteractions();
        }
    }
    
    @Test
    void testGenerateRefNo_CustomPrefix() throws Exception {
        // 准备测试数据
        String jsonParam = "{\"name\":\"测试单据\"}";
        Object[] args = {jsonParam};
        
        RefNoGeneration annotation = mock(RefNoGeneration.class);
        when(annotation.billType()).thenReturn("Bus_Test");
        when(annotation.field()).thenReturn("refno");
        when(annotation.paramType()).thenReturn(RefNoGeneration.ParamType.JSON_STRING);
        when(annotation.paramIndex()).thenReturn(0);
        when(annotation.saveToRedis()).thenReturn(true);
        when(annotation.customPrefix()).thenReturn("CUSTOM");
        
        when(joinPoint.getTarget()).thenReturn(mockController);
        when(joinPoint.getArgs()).thenReturn(args);
        when(tokenService.getLoginUser(any())).thenReturn(mockLoginUser);
        
        try (MockedStatic<RefNoUtils> mockedRefNoUtils = mockStatic(RefNoUtils.class)) {
            mockedRefNoUtils.when(() -> RefNoUtils.generateRefNo(anyString(), anyString(), anyString(), anyString()))
                    .thenReturn("CUSTOM202312010001");
            
            // 执行测试
            refNoAspect.generateRefNo(joinPoint, annotation);
            
            // 验证使用了自定义前缀
            mockedRefNoUtils.verify(() -> RefNoUtils.generateRefNo(
                    eq("TestController"), 
                    eq("Bus_Test"), 
                    eq("CUSTOM"), 
                    eq("test-tenant")
            ));
        }
    }
    
    @Test
    void testGenerateRefNo_NoSaveToRedis() throws Exception {
        // 准备测试数据
        String jsonParam = "{\"name\":\"测试单据\"}";
        Object[] args = {jsonParam};
        
        RefNoGeneration annotation = mock(RefNoGeneration.class);
        when(annotation.billType()).thenReturn("Bus_Test");
        when(annotation.field()).thenReturn("refno");
        when(annotation.paramType()).thenReturn(RefNoGeneration.ParamType.JSON_STRING);
        when(annotation.paramIndex()).thenReturn(0);
        when(annotation.saveToRedis()).thenReturn(false); // 不保存到Redis
        when(annotation.customPrefix()).thenReturn("");
        
        when(joinPoint.getTarget()).thenReturn(mockController);
        when(joinPoint.getArgs()).thenReturn(args);
        when(tokenService.getLoginUser(any())).thenReturn(mockLoginUser);
        
        try (MockedStatic<RefNoUtils> mockedRefNoUtils = mockStatic(RefNoUtils.class)) {
            mockedRefNoUtils.when(() -> RefNoUtils.generateRefNo(anyString(), anyString(), any(), anyString()))
                    .thenReturn("TEST202312010001");
            
            // 执行测试
            refNoAspect.generateRefNo(joinPoint, annotation);
            
            // 验证生成了编码但没有保存到Redis
            mockedRefNoUtils.verify(() -> RefNoUtils.generateRefNo(anyString(), anyString(), any(), anyString()));
            mockedRefNoUtils.verify(() -> RefNoUtils.saveRedisRefNo(anyString(), anyString(), anyString()), never());
        }
    }
    
    /**
     * 测试用的Controller类
     */
    static class TestController {
        private final String moduleCode = "TestController";
        
        public String getModuleCode() {
            return moduleCode;
        }
    }
}
